import { Card7 } from "@/components/custom/cards/Card7";
import { Badge } from "@/components/custom/badges/badges";
import { Eye, Edit } from "lucide-react";
import { OutlinedButton } from "@/components/custom/buttons/buttons";
import TooltipTemp from "@/components/custom/tooltip/TooltipTemp";

export type UserStatus = "active" | "inactive"; // Lowercase 'inactive' for consistency

export interface User {
  employee_no: string | null;
  email: string | null;
  fullnames: string | null;
  company_email?: string | null;
  gender?: string | null;
  department?: string | null;
  designation?: string | null;
  status?: UserStatus | null; // Use UserStatus for type safety
  team?: string | null;
  region?: string | null;
  manager?: string | null;
  phone_number?: string | null;
}

interface UserCardProps {
  user: User;
}

const getUserInitials = (fullnames: string | null): string => {
  if (!fullnames || fullnames.trim() === "") {
    return "??";
  }

  const nameParts = fullnames.trim().split(" ");
  if (nameParts.length === 1) {
    // If only one name, take first two characters
    return nameParts[0].substring(0, 2).toUpperCase();
  }

  // Take first letter of first name and first letter of last name
  const firstInitial = nameParts[0]?.charAt(0) || "";
  const lastInitial = nameParts[nameParts.length - 1]?.charAt(0) || "";

  return (firstInitial + lastInitial).toUpperCase();
};

const getAvatarBackgroundColor = (fullnames: string | null): string => {
  if (!fullnames) return "bg-gray-500";

  // Generate a consistent color based on the name
  const colors = [
    "bg-blue-500",
    "bg-green-500",
    "bg-purple-500",
    "bg-pink-500",
    "bg-indigo-500",
    "bg-yellow-500",
    "bg-red-500",
    "bg-teal-500",
    "bg-orange-500",
    "bg-cyan-500"
  ];

  const hash = fullnames.split("").reduce((acc, char) => acc + char.charCodeAt(0), 0);
  return colors[hash % colors.length];
};

const getDepartmentBadge = (department: string | null): JSX.Element => {
  if (!department || department === "N/A") {
    return (
      <Badge
        variant="outline"
        size="sm"
        className="bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300"
      >
        No Department
      </Badge>
    );
  }

  return (
    <Badge
      variant="secondary"
      size="sm"
      className="bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300"
    >
      {department}
    </Badge>
  );
};

const getDesignationBadge = (designation: string | null): JSX.Element => {
  if (!designation || designation === "N/A") {
    return (
      <Badge
        variant="outline"
        size="sm"
        className="bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300"
      >
        No Designation
      </Badge>
    );
  }

  return (
    <Badge
      variant="primary"
      size="sm"
      className="bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300"
    >
      {designation}
    </Badge>
  );
};

const getStatusBadge = (status: UserStatus | string | null | undefined): JSX.Element => {
  const statusLower = status?.toLowerCase();
  if (statusLower === "active") {
    return (
      <Badge
        variant="primary"
        size="sm"
        className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
      >
        Active
      </Badge>
    );
  } else if (statusLower === "inactive") {
    return (
      <Badge
        variant="destructive"
        size="sm"
        className="bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"
      >
        Inactive
      </Badge>
    );
  } else {
    return (
      <Badge
        variant="outline"
        size="sm"
        className="bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"
      >
        {status || "Unknown"}
      </Badge>
    );
  }
};

export default function UserCard({ user }: UserCardProps) {
  const dropdownItems = [
    {
      label: "Edit",
      onSelect: () => console.log("Edit", user.employee_no),
    },
    {
      label: "Delete",
      onSelect: () => console.log("Delete", user.email),
    },
  ];

  return (
    <Card7
      headerBgClass="bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-blue-900/40 dark:via-indigo-900/40 dark:to-purple-900/40"
      className="bg-white dark:bg-gray-800 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 transform hover:-translate-y-1 h-full flex flex-col"
      dropdownItems={dropdownItems}
      avatarSlot={
        <div className="relative group">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full blur opacity-75 group-hover:opacity-100 transition duration-300"></div>
          <div
            className={`relative h-16 w-16 rounded-full border-2 border-white shadow-lg ring-2 ring-blue-100 dark:ring-blue-800 flex items-center justify-center text-white font-bold text-lg ${getAvatarBackgroundColor(user.fullnames)}`}
            title={user.fullnames ?? "User"}
          >
            {getUserInitials(user.fullnames)}
          </div>
          <div className="absolute -bottom-1 -right-1 transform rotate-12">
            {getStatusBadge(user.status)}
          </div>
        </div>
      }
      titleSlot={
        <div className="text-center space-y-1 px-2">
          <h3 className="font-bold text-gray-900 dark:text-white text-base tracking-tight truncate" title={user.fullnames ?? "Unknown"}>
            {user.fullnames ?? "Unknown"}
          </h3>
          <div className="flex items-center justify-center gap-2">
            <Badge variant="outline" size="sm" className="text-xs font-medium">
              #{user.employee_no ?? "N/A"}
            </Badge>
          </div>
        </div>
      }
      subtitleSlot={
        <div className="text-center space-y-1 px-2">
          <p className="text-sm text-gray-600 dark:text-gray-300 font-medium truncate" title={user.email ?? "N/A"}>
            {user.email ?? "N/A"}
          </p>
          {user.phone_number && (
            <p className="text-xs text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 px-2 py-1 rounded-full inline-block truncate max-w-full">
              📞 {user.phone_number}
            </p>
          )}
        </div>
      }
      pillSlots={
        <div className="w-full flex flex-col items-center justify-center bg-gradient-to-r from-purple-50 via-pink-50 to-rose-50 dark:from-purple-900/30 dark:via-pink-900/30 dark:to-rose-900/30 rounded-lg p-3 gap-3 border border-purple-200 dark:border-purple-700/50 shadow-inner min-h-0">
          <div className="flex flex-col items-center gap-1">
            <span className="text-xs text-gray-600 dark:text-gray-400 font-medium">Department</span>
            {getDepartmentBadge(user.department ?? null)}
          </div>
          <div className="flex flex-col items-center gap-1">
            <span className="text-xs text-gray-600 dark:text-gray-400 font-medium">Designation</span>
            {getDesignationBadge(user.designation ?? null)}
          </div>
        </div>
      }
      actionSlot={
        <div className="flex gap-2 pt-1">
          <TooltipTemp
            content={<p className="text-sm font-medium">View Profile</p>}
            position="top"
          >
            <OutlinedButton
              variant="outline"
              size="sm"
              onClick={() => console.log("View", user.fullnames)}
              className="flex-1 h-8 hover:bg-blue-50 dark:hover:bg-blue-900/30 border-blue-200 dark:border-blue-700 hover:border-blue-400 dark:hover:border-blue-500 transition-all duration-200 group"
            >
              <Eye className="h-3.5 w-3.5 text-blue-600 dark:text-blue-400 group-hover:scale-110 transition-transform duration-200" />
            </OutlinedButton>
          </TooltipTemp>

          <TooltipTemp
            content={<p className="text-sm font-medium">Edit User</p>}
            position="top"
          >
            <OutlinedButton
              variant="outline"
              size="sm"
              onClick={() => console.log("Edit", user.fullnames)}
              className="flex-1 h-8 hover:bg-green-50 dark:hover:bg-green-900/30 border-green-200 dark:border-green-700 hover:border-green-400 dark:hover:border-green-500 transition-all duration-200 group"
            >
              <Edit className="h-3.5 w-3.5 text-green-600 dark:text-green-400 group-hover:scale-110 transition-transform duration-200" />
            </OutlinedButton>
          </TooltipTemp>
        </div>
      }
    />
  );
}