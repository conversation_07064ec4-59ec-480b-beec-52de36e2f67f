import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Screen } from "@/app-components/layout/screen";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import InfoModal from "@/components/custom/modals/InfoModal";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";

import {
  Bell,
  FileText,
  Clock,
  Users,
  UserCheck,
  Briefcase,
  AlertCircle,
  CheckCircle,
  Info,
  AlertTriangle,
  Zap
} from "lucide-react";
import { useLazyGetNotificationsQuery } from "@/redux/slices/services";

interface Notification {
  notification_id: string;
  title: string;
  message: string;
  created_at: string;
  priority: "Low" | "Normal" | "High" | "Urgent";
  notification_type: "Info" | "Warning" | "Error" | "Success" | "Reminder" | "Alert";
  entity_type: string;
  entity_id: string;
  entity_name: string;
  client_status: "Prospect" | "Customer" | "Lead File";
  recipient: string;
  recipient_name: string;
  sender: string;
  sender_name: string;
  is_read: boolean;
  read_at: string | null;
  expires_at: string | null;
  action_url: string | null;
  customer_no: string | null;
  prospect_id: string | null;
  lead_file_no: string | null;
}

type CategoryType = "all" | "customer" | "sales" | "prospects";

export default function NotificationsList() {
  const [trigger, { data: apiResponse, isLoading, isError, error }] = useLazyGetNotificationsQuery();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isInfoModalOpen, setIsInfoModalOpen] = useState(false);
  const [selectedNote, setSelectedNote] = useState<Notification | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [activeCategory, setActiveCategory] = useState<CategoryType>("all");

  // Fetch notifications based on active category
  useEffect(() => {
    const params: any = { page: currentPage };

    if (activeCategory === "customer") {
      params.client_status = "Customer";
    } else if (activeCategory === "sales") {
      params.client_status = "Lead File";
    } else if (activeCategory === "prospects") {
      params.client_status = "Prospect";
    }

    trigger(params);
  }, [trigger, currentPage, activeCategory]);

  // Transform API response to match our interface
  useEffect(() => {
    console.log("Raw API Response:", apiResponse);
    if (apiResponse?.data?.results) {
      const transformedNotifications: Notification[] = apiResponse.data.results.map((item: any) => ({
        notification_id: item.notification_id,
        title: item.title,
        message: item.message || "",
        created_at: item.created_at,
        priority: item.priority || "Normal",
        notification_type: item.notification_type || "Info",
        entity_type: item.entity_type || "",
        entity_id: item.entity_id || "",
        entity_name: item.entity_name || "",
        client_status: item.client_status || "Prospect",
        recipient: item.recipient || "",
        recipient_name: item.recipient_name || "",
        sender: item.sender || "",
        sender_name: item.sender_name || "",
        is_read: item.is_read || false,
        read_at: item.read_at,
        expires_at: item.expires_at,
        action_url: item.action_url,
        customer_no: item.customer_no,
        prospect_id: item.prospect_id,
        lead_file_no: item.lead_file_no,
      }));
      setNotifications((prev) => (currentPage === 1 ? transformedNotifications : [...prev, ...transformedNotifications]));
    }
  }, [apiResponse, currentPage]);

  // Helper functions
  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const timeAgo = (date: string) => {
    const now = new Date();
    const notificationDate = new Date(date);
    const diffInHours = Math.floor((now.getTime() - notificationDate.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return "Just now";
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    return formatDate(date);
  };

  const handleViewDetails = (notification: Notification) => {
    setSelectedNote(notification);
    setIsInfoModalOpen(true);
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "Info": return Info;
      case "Warning": return AlertTriangle;
      case "Error": return AlertCircle;
      case "Success": return CheckCircle;
      case "Reminder": return Clock;
      case "Alert": return Zap;
      default: return Bell;
    }
  };



  const getCategoryCount = (category: CategoryType) => {
    if (category === "all") return notifications.length;

    const statusMap = {
      customer: "Customer",
      sales: "Lead File",
      prospects: "Prospect"
    };

    return notifications.filter(n => n.client_status === statusMap[category]).length;
  };

  const renderNotificationsList = (notificationsList: Notification[]) => {
    if (isLoading) {
      return (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-slate-600 dark:text-slate-400">Loading notifications...</p>
        </div>
      );
    }

    if (isError) {
      return (
        <div className="text-center py-12">
          <AlertCircle className="h-12 w-12 mx-auto text-red-500 mb-4" />
          <h3 className="text-lg font-medium text-red-600 mb-2">Error Loading Notifications</h3>
          <p className="text-slate-600 dark:text-slate-400">
            {error ? JSON.stringify(error) : "Failed to load notifications. Please try again."}
          </p>
        </div>
      );
    }

    if (notificationsList.length === 0) {
      return (
        <div className="text-center py-20">
          <Bell className="h-16 w-16 mx-auto text-slate-300 dark:text-slate-600 mb-4" />
          <h3 className="text-lg font-medium text-slate-700 dark:text-slate-300 mb-2">No notifications found</h3>
          <p className="text-slate-500 dark:text-slate-400">
            {activeCategory === "all"
              ? "You don't have any notifications at the moment."
              : `No notifications found for ${activeCategory}.`}
          </p>
        </div>
      );
    }

    return (
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
        <AnimatePresence>
          {notificationsList.map((notification) => {
            const IconComponent = getNotificationIcon(notification.notification_type);
            return (
              <motion.div
                key={notification.notification_id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                className={`${getPriorityColor(notification.priority)} rounded-lg shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer`}
                onClick={() => handleViewDetails(notification)}
              >
                <div className="p-6">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <div className={`rounded-full p-2 bg-white dark:bg-slate-800 shadow-sm`}>
                        <IconComponent className={`h-5 w-5 ${getIconColor(notification.priority)}`} />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-slate-900 dark:text-slate-100 line-clamp-1">
                          {notification.title}
                        </h3>
                        <p className="text-sm text-slate-600 dark:text-slate-400">
                          {notification.entity_name || notification.entity_type}
                        </p>
                      </div>
                    </div>
                    <Badge className={`${getPriorityBadgeColor(notification.priority)} text-xs`}>
                      {notification.priority}
                    </Badge>
                  </div>

                  <p className="text-sm text-slate-700 dark:text-slate-300 mb-4 line-clamp-2">
                    {notification.message}
                  </p>

                  <div className="flex items-center justify-between text-xs text-slate-500 dark:text-slate-400">
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {timeAgo(notification.created_at)}
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={notification.is_read ? "secondary" : "default"} className="text-xs">
                        {notification.is_read ? "Read" : "Unread"}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {notification.client_status}
                      </Badge>
                    </div>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </AnimatePresence>
      </div>
    );
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case "high":
      case "urgent":
        return "bg-gradient-to-r from-rose-50 to-rose-100 dark:from-rose-900/20 dark:to-rose-800/20 border-l-4 border-rose-500";
      case "normal":
        return "bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 border-l-4 border-blue-500";
      case "low":
        return "bg-gradient-to-r from-emerald-50 to-emerald-100 dark:from-emerald-900/20 dark:to-emerald-800/20 border-l-4 border-emerald-500";
      default:
        return "bg-white dark:bg-slate-800 border-l-4 border-slate-300";
    }
  };

  const getIconColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case "high":
      case "urgent":
        return "text-rose-600 dark:text-rose-400";
      case "normal":
        return "text-blue-600 dark:text-blue-400";
      case "low":
        return "text-emerald-600 dark:text-emerald-400";
      default:
        return "text-slate-600 dark:text-slate-400";
    }
  };

  const getPriorityBadgeColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case "urgent":
        return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
      case "high":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400";
      case "normal":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400";
      case "low":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  const infoFields = selectedNote
    ? [
        { label: "Title", value: selectedNote.title },
        { label: "Message", value: selectedNote.message },
        { label: "Date", value: formatDate(selectedNote.created_at) },
        { label: "Priority", value: selectedNote.priority },
        { label: "Type", value: selectedNote.notification_type },
        { label: "Entity Type", value: selectedNote.entity_type || "N/A" },
        { label: "Entity Name", value: selectedNote.entity_name || "N/A" },
        { label: "Client Status", value: selectedNote.client_status },
        { label: "Recipient", value: selectedNote.recipient_name || selectedNote.recipient },
        { label: "Sender", value: selectedNote.sender_name || selectedNote.sender || "System" },
        { label: "Read Status", value: selectedNote.is_read ? "Read" : "Unread" },
      ]
    : [];

  return (
    <Screen>
      <div className="relative bg-gradient-to-r from-blue-600 to-purple-600 text-white py-8 px-4 sm:px-6 rounded-b-lg shadow-lg">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-3xl font-bold flex items-center gap-3">
            <Bell className="h-8 w-8" />
            Notifications Dashboard
          </h1>
          <p className="mt-2 text-blue-100">Manage and track all notifications across customers, sales, and prospects</p>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 pt-8 pb-20">
        {/* Header Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <div className="bg-white dark:bg-slate-800 rounded-lg p-4 shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-slate-600 dark:text-slate-400">Total</p>
                <p className="text-2xl font-bold text-slate-900 dark:text-slate-100">{notifications.length}</p>
              </div>
              <Bell className="h-8 w-8 text-blue-500" />
            </div>
          </div>
          <div className="bg-white dark:bg-slate-800 rounded-lg p-4 shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-slate-600 dark:text-slate-400">Customers</p>
                <p className="text-2xl font-bold text-slate-900 dark:text-slate-100">{getCategoryCount("customer")}</p>
              </div>
              <Users className="h-8 w-8 text-green-500" />
            </div>
          </div>
          <div className="bg-white dark:bg-slate-800 rounded-lg p-4 shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-slate-600 dark:text-slate-400">Sales</p>
                <p className="text-2xl font-bold text-slate-900 dark:text-slate-100">{getCategoryCount("sales")}</p>
              </div>
              <Briefcase className="h-8 w-8 text-purple-500" />
            </div>
          </div>
          <div className="bg-white dark:bg-slate-800 rounded-lg p-4 shadow-sm border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-slate-600 dark:text-slate-400">Prospects</p>
                <p className="text-2xl font-bold text-slate-900 dark:text-slate-100">{getCategoryCount("prospects")}</p>
              </div>
              <UserCheck className="h-8 w-8 text-orange-500" />
            </div>
          </div>
        </div>

        {/* Tabbed Interface */}
        <Tabs value={activeCategory} onValueChange={(value) => setActiveCategory(value as CategoryType)} className="w-full">
          <TabsList className="grid w-full grid-cols-4 mb-6">
            <TabsTrigger value="all" className="flex items-center gap-2">
              <Bell className="h-4 w-4" />
              All ({notifications.length})
            </TabsTrigger>
            <TabsTrigger value="customer" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Customers ({getCategoryCount("customer")})
            </TabsTrigger>
            <TabsTrigger value="sales" className="flex items-center gap-2">
              <Briefcase className="h-4 w-4" />
              Sales ({getCategoryCount("sales")})
            </TabsTrigger>
            <TabsTrigger value="prospects" className="flex items-center gap-2">
              <UserCheck className="h-4 w-4" />
              Prospects ({getCategoryCount("prospects")})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="mt-6">
            {renderNotificationsList(notifications)}
          </TabsContent>

          <TabsContent value="customer" className="mt-6">
            {renderNotificationsList(notifications.filter(n => n.client_status === "Customer"))}
          </TabsContent>

          <TabsContent value="sales" className="mt-6">
            {renderNotificationsList(notifications.filter(n => n.client_status === "Lead File"))}
          </TabsContent>

          <TabsContent value="prospects" className="mt-6">
            {renderNotificationsList(notifications.filter(n => n.client_status === "Prospect"))}
          </TabsContent>
        </Tabs>

        {/* Load More Button */}
        {apiResponse?.data?.current_page < apiResponse?.data?.last_page && (
          <div className="text-center mt-8">
            <PrimaryButton
              className="px-6 py-3 rounded-lg bg-blue-600 hover:bg-blue-700 text-white transition-colors"
              onClick={() => setCurrentPage((prev) => prev + 1)}
            >
              Load More Notifications
            </PrimaryButton>
          </div>
        )}
      </div>

      {/* Notification Details Modal */}
      <InfoModal
        isOpen={isInfoModalOpen}
        onOpenChange={setIsInfoModalOpen}
        title="Notification Details"
        icon={<FileText className="h-6 w-6 text-blue-500" />}
        fields={infoFields}
      />
    </Screen>
  );
}