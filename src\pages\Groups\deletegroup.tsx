import { useState } from "react";
import ConfirmModal from "@/components/custom/modals/ConfirmationModal";


interface DeleteGroupProps {
  isOpen: boolean;
  onClose: () => void;
  onDelete: () => void;
  group: {
    id: string;
    groupName: string;
    groupHead: string;
    description: string;
  }; 
}

export default function DeleteGroup({
  isOpen,
  onClose,
  onDelete,
  group,
}: DeleteGroupProps) {
  return (
    <ConfirmModal
      isOpen={isOpen}
      onOpenChange={onClose}
      title="Confirm Group Deletion"
      variant="danger"
      message={`Are you sure you want to delete the group "${group.groupName}"? This action cannot be undone.`}
      confirmText="Delete"
      confirmVariant="destructive"
      cancelText="Cancel"
      onConfirm={onDelete}
    />
  );
}