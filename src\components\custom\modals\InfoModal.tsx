import { forwardRef, ReactNode } from "react";
import { Info } from "lucide-react";
import { Button } from "@/components/ui/button";
import BaseModal from "./BaseModal";
import { BaseModalProps } from "./BaseModal";


export type ModalVariant = 
  | "default"
  | "warning"
  | "danger" 
  | "success" 
  | "info";

export interface InfoModalProps extends BaseModalProps {
    fields: {
      label: string;
      value: string | ReactNode;
    }[];
    variant?: ModalVariant;
  }

const InfoModal = forwardRef<HTMLDivElement, InfoModalProps>(
  (
    {
      fields,
      variant = "default",
      icon = variant === "info" ? <Info className="h-5 w-5 text-blue-500" /> : undefined,
      ...props
    },
    ref
  ) => {
    const footer = (
      <Button type="button" onClick={() => props.onOpenChange?.(false)}>
        Close
      </Button>
    );

    return (
      <BaseModal
        ref={ref}
        icon={icon}
        footer={footer}
        {...props}
      >
        <div className="space-y-4 py-2">
          {fields.map((field, index) => (
            <div key={index} className="flex flex-col sm:flex-row sm:gap-4">
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 sm:w-1/3">
                {field.label}
              </dt>
              <dd className="mt-1 text-sm text-gray-900 dark:text-gray-100 sm:mt-0 sm:w-2/3">
                {field.value}
              </dd>
            </div>
          ))}
        </div>
      </BaseModal>
    );
  }
);

InfoModal.displayName = "InfoModal";
export default InfoModal;