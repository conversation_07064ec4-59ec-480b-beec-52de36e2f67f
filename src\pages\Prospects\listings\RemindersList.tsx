import { formatDateTime } from "@/utils/formatDate";
import { useState } from "react";

type Props = {
  data: any;
};

const RemindersList = ({ data }: Props) => {
  const [RemindersModalOpen, setRemindersModalOpen] = useState<any | null>(
    null
  );

  return (
    <div className="grid md:grid-cols-4 sm:grid-cols-2 gap-4 border-t pt-4  ">
      {data?.map((rowData: any) => (
        <div
          key={rowData?.reminder_id}
          className="border-t-8 border-t-yellow-400 bg-yellow-100 rounded-lg dark:hover:bg-gray-800 shadow"
        >
          <div className="flex flex-col ">
            <div className="text-end w-full  px-4">
              <span className="text-[10px] px-2 py-1 rounded bg-orange-300/60">
                {rowData?.reminder_type}
              </span>
            </div>
            <div className="px-4 py-2">
              <h3 className="text-md font-medium capitalize">
                <span className="font-medium text-[10px]">Title</span> <br />
                {rowData?.title ? rowData?.title?.toLowerCase() : "Title"}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                <span className="font-medium text-[10px]">Description</span>{" "}
                <br />
                {rowData?.description}
              </p>
              <p className="text-xs text-blue-500 dark:text-blue-300 border-t my-2 pt-2">
                <span className="font-medium text-[10px]">Created At</span>{" "}
                <br />
                {formatDateTime(rowData?.created_at)}
              </p>
            </div>
            {/* <button
              onClick={() => setRemindersModalOpen(rowData)}
              className="text-blue-500 hover:underline"
            >
              Edit
            </button> */}
          </div>
        </div>
      ))}
    </div>
  );
};

export default RemindersList;
