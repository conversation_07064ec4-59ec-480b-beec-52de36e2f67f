import React, { useState, useCallback } from "react";
import { motion } from "framer-motion";
import { Screen } from "@/app-components/layout/screen";
import { Column } from "./Column";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import { CardType } from "./types";
import { BurnBarrel } from "./BurnBarrell";

const headerVariants = {
  initial: { opacity: 0, y: -20 },
  animate: { opacity: 1, y: 0, transition: { duration: 0.4, ease: "easeOut" } },
};

// Define valid statuses to ensure type safety
type ValidStatus = "PENDING" | "IN_PROGRESS" | "COMPLETED" | "CANCELLED";

const styles = {
  header: [
    "sticky top-0 z-20",
    "flex items-center justify-between",
    "px-6 sm:px-8 md:px-12 py-5",
    "bg-white/80 dark:bg-gray-900/80",
    "backdrop-blur-xl backdrop-saturate-150",
    "border-b border-gray-200/50 dark:border-gray-700/50",
    "shadow-lg shadow-gray-100/50 dark:shadow-black/20",
    "transition-all duration-300 ease-out",
  ].join(" "),

  navigation: [
    "flex items-center gap-3",
    "transition-all duration-200 ease-out",
  ].join(" "),

  boardContainer: [
    "flex w-full gap-6",
    "overflow-x-auto scroll-smooth",
    "p-8 sm:p-10 md:p-12 lg:p-16",
    "bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/40",
    "dark:from-gray-900 dark:via-gray-900/95 dark:to-slate-900",
    "bg-[radial-gradient(circle_at_50%_50%,rgba(59,130,246,0.05),transparent_70%)]",
    "dark:bg-[radial-gradient(circle_at_50%_50%,rgba(99,102,241,0.1),transparent_70%)]",
    "min-h-screen",
  ].join(" "),
};

// Updated column configuration to match VALID_STATUSES
const COLUMNS = [
  {
    title: "Backlog",
    column: "PENDING" as ValidStatus,
    headingColor: "text-gray-600 dark:text-gray-300 font-bold tracking-wide",
    accent: "from-gray-400 to-gray-500",
  },
  {
    title: "To Do",
    column: "PENDING" as ValidStatus, // Using PENDING for consistency; adjust if needed
    headingColor: "text-amber-600 dark:text-amber-400 font-bold tracking-wide",
    accent: "from-amber-400 to-orange-500",
  },
  {
    title: "In Progress",
    column: "IN_PROGRESS" as ValidStatus,
    headingColor: "text-blue-600 dark:text-blue-400 font-bold tracking-wide",
    accent: "from-blue-400 to-indigo-600",
  },
  {
    title: "Complete",
    column: "COMPLETED" as ValidStatus,
    headingColor: "text-emerald-600 dark:text-emerald-400 font-bold tracking-wide",
    accent: "from-emerald-400 to-green-600",
  },
];

export const Board: React.FC = () => {
  const [cards, setCards] = useState<CardType[]>([]); // Assuming DEFAULT_CARDS is empty as shown

  const handleResetBoard = useCallback(() => {
    setCards([]);
  }, []);

  const handleAddColumn = useCallback(() => {
    // TODO: Implement add column functionality
    alert("Add column feature not implemented");
  }, []);

  return (
    <Screen>
      <motion.header
        variants={headerVariants}
        initial="initial"
        animate="animate"
        className={styles.header}
        role="banner"
        aria-label="Kanban board header"
      >
        <nav className={styles.navigation} aria-label="Board actions">
          {/* Add navigation items if needed, e.g., reset button */}
          <PrimaryButton onClick={handleResetBoard}>Reset Board</PrimaryButton>
        </nav>
      </motion.header>

      {/* Modern Trello-like Board */}
      <main className={styles.boardContainer}>
        {COLUMNS.map(({ title, column, headingColor, accent }) => (
          <div key={column} className="relative">
            {/* Modern accent line above each column */}
            <div className={`h-1 w-full bg-gradient-to-r ${accent} rounded-full mb-4 shadow-sm`} />
            <Column
              title={title}
              column={column}
              headingColor={headingColor}
              cards={cards}
              setCards={setCards}
            />
          </div>
        ))}

        {/* Add New Column Button */}
        <div className="w-72 shrink-0">
          <button
            onClick={handleAddColumn}
            className="w-full h-32 border-2 border-dashed border-gray-300 rounded-xl hover:border-gray-400 hover:bg-gray-50 transition-all duration-200 flex flex-col items-center justify-center gap-2 text-gray-500 hover:text-gray-600"
          >
            <svg className="h-8 w-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            <span className="text-sm font-medium">Add another list</span>
          </button>
        </div>

        <BurnBarrel setCards={setCards} />
      </main>
    </Screen>
  );
};