import { Activity } from "lucide-react"

const DashboardTile = ({
    borderClassName,
    baseColor,
    icon,
    title,
    value,
    category,
}: {
    borderClassName: string
    baseColor: string
    icon: React.ReactNode
    title: string
    value: string
    category: string
}) => {
    return (
        <div className={`px-2 py-8 ${borderClassName}`} >
            <div className='flex justify-between'>
                <div className='flex flex-col gap-1'>
                    <p>{title}</p>
                    <p className='font-bold text-3xl'>{value}</p>
                </div>
                <div className={`flex items-center justify-center bg-${baseColor}-100 rounded-full h-12 w-12`}>
                    {icon}
                </div>
            </div>
            <div className='h-10'></div>
            <div className='flex items-center gap-2'>
                <Activity className={`text-${baseColor}-800`} />
                <p className={`text-${baseColor}-700`}>{category}</p>
            </div>
        </div>
    )
}

export default DashboardTile