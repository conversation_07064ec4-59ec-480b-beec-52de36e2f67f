import { useState, useEffect } from "react";
import { AlertCircle, Star, MessageCircle, Sparkles, CheckCircle } from "lucide-react";
import MultiStepModal from "@/components/custom/modals/MultiStepModal";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";

interface FeedbackData {
  subject: string;
  feedback_type: string;
  rating: number;
  message?: string;
}

interface CreateFeedBackModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: FeedbackData) => void;
  initialData?: FeedbackData;
  className?: string;
}

export default function CreateFeedBackModal({
  isOpen,
  onClose,
  onSubmit,
  initialData,
  className,
}: CreateFeedBackModalProps) {
  const { toast } = useToast();
  const isEditMode = !!initialData;
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState<FeedbackData>({
    subject: "",
    feedback_type: "Compliment",
    rating: 0,
    message: "",
  });

  useEffect(() => {
    if (isEditMode && initialData) {
      setFormData(initialData);
    } else {
      setFormData({
        subject: "",
        feedback_type: "Compliment",
        rating: 0,
        message: "",
      });
    }
  }, [isEditMode, initialData]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target;
    setFormData((prev) => ({ ...prev, [id]: value }));
  };

  const handleRadioChange = (field: keyof FeedbackData) => (value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: field === "rating" ? parseInt(value) : value,
    }));
  };

  const handleComplete = () => {
    if (!formData.subject || !formData.feedback_type || formData.rating < 1 || formData.rating > 5) {
      toast({
        title: "Error",
        description: "Please fill all required fields and select a rating between 1 and 5.",
        variant: "destructive",
        duration: 3000,
      });
      return;
    }
    onSubmit(formData);
    setCurrentStep(0);
    setFormData({
      subject: "",
      feedback_type: "Compliment",
      rating: 0,
      message: "",
    });
    onClose();
  };

  return (
    <MultiStepModal
      isOpen={isOpen}
      onOpenChange={(open) => {
        if (!open) {
          setCurrentStep(0);
          setFormData({
            subject: "",
            feedback_type: "Compliment",
            rating: 0,
            message: "",
          });
          onClose();
        }
      }}
      title={isEditMode ? "Edit Feedback" : "Add New Feedback"}
      description={isEditMode ? "Update the feedback details" : "Complete all steps to add new feedback"}
      currentStep={currentStep}
      onStepChange={setCurrentStep}
      onComplete={handleComplete}
      className={className}
      steps={[
        {
          title: "Feedback Details",
          content: (
            <div className="space-y-6 py-4">
              <div className="text-center mb-6">
                <div className="mx-auto w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mb-4 shadow-lg">
                  <MessageCircle className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-gray-800">Share Your Feedback</h3>
                <p className="text-gray-600 text-sm">Help us improve by sharing your thoughts</p>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="subject" className="text-sm font-medium text-gray-700">
                    Subject <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="subject"
                    placeholder="What's your feedback about?"
                    value={formData.subject}
                    onChange={handleInputChange}
                    className="border-2 border-green-200 focus:border-green-500 focus:ring-green-500 rounded-xl"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="message" className="text-sm font-medium text-gray-700">
                    Message (optional)
                  </Label>
                  <Textarea
                    id="message"
                    placeholder="Tell us more about your experience..."
                    value={formData.message || ""}
                    onChange={handleInputChange}
                    rows={4}
                    className="border-2 border-green-200 focus:border-green-500 focus:ring-green-500 rounded-xl resize-none"
                  />
                </div>

                <div className="space-y-3">
                  <Label className="text-sm font-medium text-gray-700">
                    Feedback Type <span className="text-red-500">*</span>
                  </Label>
                  <RadioGroup
                    value={formData.feedback_type}
                    onValueChange={handleRadioChange("feedback_type")}
                    className="grid grid-cols-2 gap-3"
                  >
                    {[
                      { value: "Compliment", emoji: "😊", color: "bg-green-50 border-green-200 text-green-700" },
                      { value: "Complaint", emoji: "😞", color: "bg-red-50 border-red-200 text-red-700" },
                      { value: "Suggestion", emoji: "💡", color: "bg-blue-50 border-blue-200 text-blue-700" },
                      { value: "Question", emoji: "❓", color: "bg-purple-50 border-purple-200 text-purple-700" },
                      { value: "Bug", emoji: "🐛", color: "bg-orange-50 border-orange-200 text-orange-700" },
                      { value: "Other", emoji: "📝", color: "bg-gray-50 border-gray-200 text-gray-700" }
                    ].map((type) => (
                      <div key={type.value} className="flex items-center space-x-3">
                        <RadioGroupItem value={type.value} id={type.value.toLowerCase()} />
                        <Label
                          htmlFor={type.value.toLowerCase()}
                          className={`flex items-center gap-2 px-3 py-2 rounded-lg border cursor-pointer transition-all duration-200 hover:shadow-sm ${
                            formData.feedback_type === type.value ? type.color : 'bg-gray-50 border-gray-200 text-gray-600'
                          }`}
                        >
                          <span>{type.emoji}</span>
                          <span className="text-sm font-medium">{type.value}</span>
                        </Label>
                      </div>
                    ))}
                  </RadioGroup>
                </div>
              </div>
            </div>
          ),
        },
        {
          title: "Rate Your Experience",
          content: (
            <div className="space-y-6 py-4">
              <div className="text-center mb-6">
                <div className="mx-auto w-16 h-16 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-full flex items-center justify-center mb-4 shadow-lg">
                  <Star className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-gray-800">Rate Your Experience</h3>
                <p className="text-gray-600 text-sm">How would you rate your overall experience?</p>
              </div>

              <div className="space-y-4">
                <Label className="text-sm font-medium text-gray-700 text-center block">
                  Rating <span className="text-red-500">*</span>
                </Label>

                {/* Star Rating Display */}
                <div className="flex justify-center items-center gap-2 mb-4">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <button
                      key={star}
                      type="button"
                      onClick={() => setFormData(prev => ({ ...prev, rating: star }))}
                      className="transition-all duration-200 hover:scale-110"
                    >
                      <Star
                        className={`h-8 w-8 ${
                          star <= formData.rating
                            ? 'fill-yellow-400 text-yellow-400'
                            : 'text-gray-300 hover:text-yellow-300'
                        }`}
                      />
                    </button>
                  ))}
                </div>

                {/* Rating Labels */}
                <div className="text-center">
                  <span className="text-lg font-semibold text-gray-700">
                    {formData.rating}/5
                  </span>
                  <p className="text-sm text-gray-500 mt-1">
                    {formData.rating === 0 && "Please select a rating"}
                    {formData.rating === 1 && "Poor - Very unsatisfied"}
                    {formData.rating === 2 && "Fair - Somewhat unsatisfied"}
                    {formData.rating === 3 && "Good - Neutral"}
                    {formData.rating === 4 && "Very Good - Satisfied"}
                    {formData.rating === 5 && "Excellent - Very satisfied"}
                  </p>
                </div>

                {/* Hidden Radio Group for form validation */}
                <RadioGroup
                  value={formData.rating.toString()}
                  onValueChange={handleRadioChange("rating")}
                  className="hidden"
                >
                  {[1, 2, 3, 4, 5].map((value) => (
                    <div key={value}>
                      <RadioGroupItem value={value.toString()} id={`rating-${value}`} />
                    </div>
                  ))}
                </RadioGroup>
              </div>
            </div>
          ),
        },
        {
          title: "Review & Submit",
          content: (
            <div className="py-6">
              <div className="text-center mb-6">
                <div className="mx-auto w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mb-4 shadow-lg">
                  <CheckCircle className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-gray-800">Almost Done!</h3>
                <p className="text-gray-600 text-sm mt-2">
                  Please review your feedback details before submitting.
                </p>
              </div>

              <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl p-6 border border-green-200">
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">Subject</p>
                      <p className="text-gray-600">{formData.subject || "Not specified"}</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">Message</p>
                      <p className="text-gray-600">{formData.message || "No additional message"}</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">Feedback Type</p>
                      <p className="text-gray-600">{formData.feedback_type}</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                    <div>
                      <p className="text-sm font-medium text-gray-700">Rating</p>
                      <div className="flex items-center gap-2">
                        <div className="flex items-center gap-1">
                          {[1, 2, 3, 4, 5].map((star) => (
                            <Star
                              key={star}
                              className={`h-4 w-4 ${
                                star <= formData.rating
                                  ? 'fill-yellow-400 text-yellow-400'
                                  : 'text-gray-300'
                              }`}
                            />
                          ))}
                        </div>
                        <span className="text-gray-600">({formData.rating}/5)</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-6 text-center">
                <div className="flex items-center justify-center gap-2 text-green-600">
                  <Sparkles className="h-4 w-4" />
                  <span className="text-sm font-medium">Ready to submit your feedback!</span>
                </div>
              </div>
            </div>
          ),
        },
      ]}
    />
  );
}