
import SimpleTable, { ColumnDefinitionST } from "@/components/custom/tables/SimpleTable";
import { useState, useEffect } from "react";
import { Search } from "lucide-react";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { LinkButton } from "@/components/custom/buttons/buttons";
import { Link } from "react-router";

const PlotReport = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [searchType, setSearchType] = useState("marketer"); 
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(5);

  
  const [bookingsData] = useState([
    { 
      id: "1",
       clientName:  (
        <Link to={"/customer/id"}>
        <LinkButton
           variant="link"
           className="text-blue-600 hover:text-blue-800 flex items-center gap-1 transition-colors" 
           >mwas</LinkButton>
           </Link>
       ),
        marketer: "<PERSON> Johnson", 
        plotNo: "A-123" 
      },
    
  ]);

 
  const filteredBookings = bookingsData.filter((booking) => {
    if (searchType === "marketer") {
      return booking.marketer.toLowerCase().includes(searchTerm.toLowerCase());
    } else {
      return booking.plotNo.toLowerCase().includes(searchTerm.toLowerCase());
    }
  });

 
  const totalPages = Math.ceil(filteredBookings.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredBookings.slice(indexOfFirstItem, indexOfLastItem);

  
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, searchType]);

  
  const columns: ColumnDefinitionST<typeof bookingsData[0]>[] = [
    {
      key: "clientName",
      header: "Client Name",
      headerClassName: "font-semibold text-gray-700 px-6 py-4 bg-gray-50",
      cellClassName: "px-6 py-4 text-gray-600",
    },
    {
      key: "marketer",
      header: "Marketer",
      headerClassName: "font-semibold text-gray-700 px-6 py-4 bg-gray-50",
      cellClassName: "px-6 py-4 text-gray-600",
    },
    {
      key: "plotNo",
      header: "Plot No",
      headerClassName: "font-semibold text-gray-700 px-6 py-4 bg-gray-50",
      cellClassName: "px-6 py-4 text-gray-600",
    },
   
  ];


  const renderPaginationItems = () => {
    const items = [];

    items.push(
      <PaginationItem key="first">
        <PaginationLink 
          onClick={() => setCurrentPage(1)}
          isActive={currentPage === 1}
        >
          1
        </PaginationLink>
      </PaginationItem>
    );
    
    if (currentPage > 3) {
      items.push(
        <PaginationItem key="ellipsis-1">
          <PaginationEllipsis />
        </PaginationItem>
      );
    }
    
    if (currentPage > 2) {
      items.push(
        <PaginationItem key={currentPage - 1}>
          <PaginationLink 
            onClick={() => setCurrentPage(currentPage - 1)}
          >
            {currentPage - 1}
          </PaginationLink>
        </PaginationItem>
      );
    }
    
    if (currentPage !== 1 && currentPage !== totalPages) {
      items.push(
        <PaginationItem key={currentPage}>
          <PaginationLink 
            isActive
            onClick={() => setCurrentPage(currentPage)}
          >
            {currentPage}
          </PaginationLink>
        </PaginationItem>
      );
    }
    
    if (currentPage < totalPages - 1) {
      items.push(
        <PaginationItem key={currentPage + 1}>
          <PaginationLink 
            onClick={() => setCurrentPage(currentPage + 1)}
          >
            {currentPage + 1}
          </PaginationLink>
        </PaginationItem>
      );
    }
    
    if (currentPage < totalPages - 2) {
      items.push(
        <PaginationItem key="ellipsis-2">
          <PaginationEllipsis />
        </PaginationItem>
      );
    }
    
    if (totalPages > 1) {
      items.push(
        <PaginationItem key="last">
          <PaginationLink 
            onClick={() => setCurrentPage(totalPages)}
            isActive={currentPage === totalPages}
          >
            {totalPages}
          </PaginationLink>
        </PaginationItem>
      );
    }

    return items;
  };

  return (
    
      <div className="min-h-screen space-y-6 py-8">
       
        

        
        <div className="flex flex-col md:flex-row gap-4">
          
          

          
          <div className="relative flex-1 max-w-md">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder={searchType === "marketer" ? "Search by plot no..." : "Search by marketer no ..."}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            />
          </div>
        </div>

        
        <div className="border border-gray-200 rounded-lg shadow-sm overflow-hidden">
          <SimpleTable
            data={currentItems}
            columns={columns}
            containerClassName="bg-white"
            tableClassName="w-full border-collapse"
            tHeaderClassName="border-b border-gray-200"
            tBodyClassName="divide-y divide-gray-100"
            tRowClassName="hover:bg-gray-50 transition-colors"
            headerCellsClassName=""
            bodyCellsClassName=""
            hoverable={true}
            striped={false}
          />
        </div>

       
        {filteredBookings.length > 0 && (
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-500">
              Showing {indexOfFirstItem + 1}-{Math.min(indexOfLastItem, filteredBookings.length)} of {filteredBookings.length} bookings
            </p>
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                    className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                  />
                </PaginationItem>
                
                {renderPaginationItems()}
                
                <PaginationItem>
                  <PaginationNext
                    onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                    className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}
        
       
        {filteredBookings.length === 0 && (
          <div className="text-center py-8">
            <p className="text-gray-500">No bookings found matching your search.</p>
          </div>
        )}
      </div>
    
  );
};

export default PlotReport;