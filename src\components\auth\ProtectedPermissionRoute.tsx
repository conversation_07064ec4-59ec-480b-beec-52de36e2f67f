import React from 'react';
import { Outlet } from 'react-router-dom';
import PermissionGuard from './PermissionGuard';

interface ProtectedPermissionRouteProps {
  requiredPermissions?: number[];
  children?: React.ReactNode;
}

/**
 * A wrapper component that combines authentication and permission checking
 * This can be used within the existing ProtectedRoute component
 * Can be used either as a route wrapper (with Outlet) or as a component wrapper (with children)
 */
const ProtectedPermissionRoute: React.FC<ProtectedPermissionRouteProps> = ({
  requiredPermissions,
  children,
}) => {
  return (
    <PermissionGuard requiredPermissions={requiredPermissions}>
      {children || <Outlet />}
    </PermissionGuard>
  );
};

export default ProtectedPermissionRoute;
