import { DataTable } from "@/components/custom/tables/Table1";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { ColumnDef } from "@tanstack/react-table";

interface Team {
    title: string,
    target: string,
}

const data: Team[] = [
    {
        title: 'Bulls',
        target: '8,600,000'
    }
]


const columns: ColumnDef<Team>[] = [
    {
        accessorKey: 'team',
        header: 'Team',
        cell: info => info.getValue(),
        enableColumnFilter: true,
        filterFn: 'includesString',
    },
    {
        accessorKey: 'monthly_target',
        header: 'M.Target',
        cell: info => info.getValue(),
        enableColumnFilter: true,
        filterFn: 'includesString',
    },
    {
        accessorKey: 'MIB_achieved',
        header: 'MIB Achieved',
        cell: info => info.getValue(),
        enableColumnFilter: true,
        filterFn: 'includesString',
    },
    {
        accessorKey: 'MIB_Perfomance',
        header: 'MIB Perfomance',
        cell: info => info.getValue(),
        enableColumnFilter: true,
        filterFn: 'includesString',
    },
    {
        accessorKey: 'actions',
        header: 'Actions',
        cell: info => {
            const rowData = info.row.original;
            return (
                <div className='flex gap-3'>
                    <button className='border border-purple-900 bg-transparent text-foreground  hover:bg-transparent px-4 py-1.5 rounded-md'>Performance</button>
                    <DropdownMenu>
                        <DropdownMenuTrigger className='border border-purple-900 bg-transparent text-foreground  hover:bg-transparent px-4 py-1.5 rounded-md'>
                            Reports
                        </DropdownMenuTrigger>
                        <DropdownMenuContent className="w-56">
                            <DropdownMenuLabel className='py-3'>Team reports</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Installments Exp Today</DropdownMenuItem>
                            <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Overdue Collections</DropdownMenuItem>
                            <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Below Threshold</DropdownMenuItem>
                            <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Monthly Exp Installments</DropdownMenuItem>
                            <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>New Sales Reports</DropdownMenuItem>
                            <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>MIB Reports</DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            );
        },
        enableColumnFilter: false,
        enableSorting: false,
    },
]
const TeamsTable = ({data}:{data:[]}) => {
    return (
        <div className='border border-purple-900/30 rounded-lg px-4 py-4'>
            <DataTable<Team>
                data={data}
                columns={columns}
                title="Global Office"
                enableSelectColumn={false}
                enableColumnFilters={false}
                enableSorting={true}
                enableToolbar={true}
                tableClassName='border-none'
                containerClassName=' py-2 '
                tHeadClassName='border-t'
                tHeadCellsClassName=" px-2 py-6 "
                tBodyCellsClassName="text-xs  px-2"
                tBodyTrClassName='hover:!bg-transparent'
            />
        </div>
    )
}

export default TeamsTable

