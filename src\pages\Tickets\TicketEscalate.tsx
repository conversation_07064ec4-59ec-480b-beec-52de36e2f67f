import { useState } from "react";
import { AlertCircle } from "lucide-react";
import MultiStepModal from "@/components/custom/modals/MultiStepModal";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface Complaint {
  id: number;
  title: string;
  description: string;
  status?: "Open" | "Escalated" | "Resolved";
}

interface EscalationData {
  reason: string;
  priority: "Low" | "Medium" | "High";
  department: string;
}

interface EscalateComplaintModalProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  onSubmit: (escalationData: EscalationData) => void;
  complaint: Complaint;
}

export default function EscalateComplaintModal({
  isOpen,
  onOpenChange,
  onSubmit,
  complaint,

}: EscalateComplaintModalProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [escalationData, setEscalationData] = useState<EscalationData>({
    reason: "",
    priority: "Low",
    department: "",
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setEscalationData((prev) => ({ ...prev, [id]: value }));
  };

  const handleSelectChange = (value: string) => {
    setEscalationData((prev) => ({ ...prev, priority: value as "Low" | "Medium" | "High" }));
  };

  const handleComplete = () => {
    onSubmit(escalationData);
    setCurrentStep(0);
    setEscalationData({
      reason: "",
      priority: "Low",
      department: "",
    });
  };

  return (
    <MultiStepModal
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      title={`Escalate Complaint: ${complaint.title}`}
      description="Provide details to escalate this complaint"
      currentStep={currentStep}
      onStepChange={setCurrentStep}
      onComplete={handleComplete}
      steps={[
        {
          title: "Escalation Details",
          content: (
            <div className="space-y-4 py-2">
              <div className="space-y-2">
                <Label htmlFor="reason">Reason for Escalation</Label>
                <Input
                  id="reason"
                  placeholder="Enter the reason for escalation"
                  value={escalationData.reason}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="priority">Priority</Label>
                <Select value={escalationData.priority} onValueChange={handleSelectChange}>
                  <SelectTrigger id="priority">
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Low">Low</SelectItem>
                    <SelectItem value="Medium">Medium</SelectItem>
                    <SelectItem value="High">High</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="department">Target Department</Label>
                <Input
                  id="department"
                  placeholder="Enter the target department"
                  value={escalationData.department}
                  onChange={handleInputChange}
                />
              </div>
            </div>
          ),
        },
        {
          title: "Confirmation",
          content: (
            <div className="py-6 text-center">
              <AlertCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium">Ready to Escalate</h3>
              <p className="text-muted-foreground mt-2">
                Please review the escalation details before submitting.
              </p>
            </div>
          ),
        },
      ]}
    />
  );
}