import { Screen } from "@/app-components/layout/screen";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { DataTable } from "@/components/custom/tables/Table1";
import { Button } from "@/components/ui/button";

import { ColumnDef } from "@tanstack/react-table";
import { GitForkIcon, Plus } from "lucide-react";
import { useState } from "react";
import AddTicketSourceModal from "../Modals/AddTicketSourceModal";
import { useFetchTicketSourcesQuery } from "@/redux/slices/tickets";

interface sourceTYpes {
  id: number;
  name: string;
  description: string;
}

const index = () => {
  const [searchValue, setSearchValue] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [showCreateModal, setShowCreateModal] = useState(false);

  const { data: ticketsSources, isLoading: loading } =
    useFetchTicketSourcesQuery({
      search: searchValue,
      page: currentPage,
      page_size: itemsPerPage,
    });

  const columns: ColumnDef<sourceTYpes>[] = [
    {
      accessorKey: "name",
      header: "Name",
      cell: (info) => (
        <span className="font-medium capitalize ">
          {info.getValue() as string}
        </span>
      ),
      enableColumnFilter: false,
    },
    {
      accessorKey: "description",
      header: "Description",
      cell: (info) => (
        <span className="font-medium capitalize">
          {info.getValue() as string}
        </span>
      ),
      enableColumnFilter: false,
    },
    {
      id: "actions",
      header: "Action",
      cell: ({ row }) => {
        const ticketCats = row.original;
        return (
          <div className="flex space-x-2 justify-start">
            {/* <PrimaryButton
                  variant="outline"
                  size="sm"
                  onClick={() => handleViewProspect(prospect.id)}
                  className="bg-white text-blue-500 border-blue-300 hover:bg-blue-50 flex items-center space-x-1"
                >
                  <span title="Edit">
                    <Edit />
                  </span>
                </PrimaryButton> */}
          </div>
        );
      },
      enableColumnFilter: false,
    },
  ];

  return (
    <Screen>
      {/* header */}
      <div className="flex justify-between items-center mb-4">
        <div className="">
          <h1 className="text-3xl font-bold">Ticketing Sources</h1>
          <p>View Ticket Sources</p>
        </div>
        <Button
          variant="default"
          className="flex items-center"
          onClick={() => setShowCreateModal(true)}
        >
          <Plus /> Create Ticket Source
        </Button>
      </div>

      {/* table  */}
      <div className="">
        {loading ? (
          <div className="w-full flex items-center justify-center">
            <SpinnerTemp type="spinner-double" size="md" />
          </div>
        ) : (
          <DataTable<sourceTYpes>
            data={ticketsSources?.data?.results}
            columns={columns}
            enableToolbar={true}
            enablePagination={true}
            enableColumnFilters={true}
            enableSorting={true}
            enablePrintPdf={true}
            enableExportToExcel={true}
            tableClassName="border-collapse"
            tHeadClassName="bg-gray-50"
            tHeadCellsClassName="text-xs uppercase text-gray-600 font-semibold"
            tBodyTrClassName="hover:bg-gray-50"
            tBodyCellsClassName="border-t"
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            itemsPerPage={itemsPerPage}
            setItemsPerPage={setItemsPerPage}
            totalItems={ticketsSources?.data?.total_data || 0}
            searchInput={
              <input
                value={searchValue}
                name="searchValue"
                type="search"
                onChange={(e) => setSearchValue(e.target.value)}
                className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="Search sources..."
              />
            }
          />
        )}
      </div>

      {/* create modal  */}
      {showCreateModal && (
        <AddTicketSourceModal
          isOpen={showCreateModal}
          onOpenChange={setShowCreateModal}
        />
      )}
    </Screen>
  );
};

export default index;
function useFetchTicketsSourcesQuery(arg0: {
  search: string;
  page: number;
  page_size: number;
}): { data: any; isLoading: any } {
  throw new Error("Function not implemented.");
}
