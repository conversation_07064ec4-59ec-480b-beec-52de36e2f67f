import React, { useState } from "react";
import { DropIndicator } from "./DropIndicator";
import { AddCard } from "./AddCard";
import { KanbanCard } from "./KanbanCard";
import { Card4 } from "@/components/custom/cards/Card4";
import { CardType } from "./types";

interface ColumnProps {
  title: string;
  headingColor: string;
  cards: CardType[];
  column: string;
  setCards: React.Dispatch<React.SetStateAction<CardType[]>>;
}

export const Column: React.FC<ColumnProps> = ({ title, headingColor, cards, column, setCards }) => {
  const [active, setActive] = useState(false);

  const handleDragStart = (e: React.DragEvent, card: CardType) => {
    e.dataTransfer.setData("cardId", card.id);
    e.dataTransfer.effectAllowed = "move";
  };

  const handleDragEnd = (e: React.DragEvent) => {
    e.preventDefault();
    setActive(false);
    clearHighlights();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const cardId = e.dataTransfer.getData("cardId");
    setActive(false);
    clearHighlights();

    const indicators = getIndicators();
    const { element } = getNearestIndicator(e, indicators);

    const before = element.dataset.before || "-1";

    if (before !== cardId) {
      let copy = [...cards];
      let cardToTransfer = copy.find((c) => c.id === cardId);
      if (!cardToTransfer) return;
      cardToTransfer = { ...cardToTransfer, column };

      copy = copy.filter((c) => c.id !== cardId);
      const moveToBack = before === "-1";

      if (moveToBack) {
        copy.push(cardToTransfer);
      } else {
        const insertAtIndex = copy.findIndex((el) => el.id === before);
        if (insertAtIndex === -1) return;
        copy.splice(insertAtIndex, 0, cardToTransfer);
      }

      setCards(copy);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "move";
    highlightIndicator(e);
    setActive(true);
  };

  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    setActive(true);
  };

  const clearHighlights = (els?: HTMLElement[]) => {
    const indicators = els || getIndicators();
    indicators.forEach((i) => {
      i.style.opacity = "0";
    });
  };

  const highlightIndicator = (e: React.DragEvent) => {
    const indicators = getIndicators();
    clearHighlights(indicators);
    const el = getNearestIndicator(e, indicators);
    el.element.style.opacity = "1";
  };

  const getNearestIndicator = (e: React.DragEvent, indicators: HTMLElement[]) => {
    const DISTANCE_OFFSET = 50;

    return indicators.reduce(
      (closest, child) => {
        const box = child.getBoundingClientRect();
        const offset = e.clientY - (box.top + DISTANCE_OFFSET);

        if (offset < 0 && offset > closest.offset) {
          return { offset, element: child };
        }
        return closest;
      },
      {
        offset: Number.NEGATIVE_INFINITY,
        element: indicators[indicators.length - 1],
      }
    );
  };

  const getIndicators = () => {
    return Array.from(document.querySelectorAll(`[data-column="${column}"]`)) as HTMLElement[];
  };

  const handleDragLeave = (e: React.DragEvent) => {
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      clearHighlights();
      setActive(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent, card: CardType) => {
    if (e.key === "Enter" || e.key === " ") {
      e.preventDefault();
      setCards((prev) => {
        let copy = [...prev];
        let cardToTransfer = copy.find((c) => c.id === card.id);
        if (!cardToTransfer) return prev;
        cardToTransfer = { ...cardToTransfer, column };
        copy = copy.filter((c) => c.id !== card.id);
        copy.push(cardToTransfer);
        return copy;
      });
    }
  };

  const filteredCards = cards.filter((c) => c.column === column);

  return (
    <div className="w-72 shrink-0">
      <Card4
        title={title}
        description=""
        content={
          <div
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragEnter={handleDragEnter}
            onDragLeave={handleDragLeave}
            className={`min-h-[200px] transition-colors ${active ? "bg-secondary/50" : "bg-secondary"}`}
          >
            {filteredCards.map((c) => (
              <React.Fragment key={c.id}>
                <DropIndicator beforeId={c.id} column={column} />
                <div className="mb-2">
                  <KanbanCard
                    {...c}
                    handleDragStart={handleDragStart}
                    onKeyDown={(e) => handleKeyDown(e, c)}
                    setCards={setCards}
                  />
                </div>
              </React.Fragment>
            ))}
            <DropIndicator beforeId={null} column={column} />
          </div>
        }
        actions={
          <div className="flex flex-row items-center justify-between">
            <span className="text-sm text-muted-foreground">{filteredCards.length}</span>
            <AddCard column={column} setCards={setCards} />
          </div>
        }
        className="bg-secondary"
        headerClassName={`text-lg font-semibold ${headingColor}`}
        contentClassName="p-1"
        footerClassName=""
      />
    </div>
  );
};