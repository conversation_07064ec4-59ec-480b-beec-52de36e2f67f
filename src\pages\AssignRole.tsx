// src/components/permissions/AssignRole.tsx
import React, { useMemo, useCallback } from "react";
import { Screen } from "@/app-components/layout/screen";
import { CheckboxRound } from "@/components/custom/forms/Checkbox";
import SimpleTable, {
  ColumnDefinitionST,
} from "@/components/custom/tables/SimpleTable";
import Tab2 from "@/components/custom/tabs/Tab2";
import {
  Tooltip,
  TooltipProvider,
  TooltipTrigger,
  TooltipContent,
} from "@/components/ui/tooltip";

// — parties —
import {
  useGetUsersQuery,
  useGetGroupsQuery,
  useGetTeamsQuery,
} from "@/redux/slices/user";

// — permission-defs & mappings —
import {
  useGetUserPermissionsQuery,
  useGetGroupPermissionsQuery,
  useGetTeamPermissionsQuery,
  useGetUser2UserPermissionsQuery,
  useCreateUser2UserPermissionMutation,
  useDeleteUser2UserPermissionMutation,
  useGetUserGroup2UserGroupPermissionsQuery,
  useCreateUserGroup2UserGroupPermissionMutation,
  useDeleteUserGroup2UserGroupPermissionMutation,
  useGetTeams2TeamsPermissionsQuery,
  useCreateTeams2TeamsPermissionMutation,
  useDeleteTeams2TeamsPermissionMutation,
} from "@/redux/slices/permissions";

interface UserRow {
  id: number;
  name: string;
  employee_no: string;
  [permCode: string]: boolean | number | string;
}

interface PartyRow {
  id: number;
  name: string;
  [permCode: string]: boolean | number;
}

const AssignRole: React.FC = () => {
  // ── 1) ALL HOOKS AT THE TOP ──────────────────────────────────────────────────

  // parties
  const { data: users = [], isLoading: loadingUsers } = useGetUsersQuery({
    page: 1,
    page_size: 100,
  });

  const { data: groups = [], isLoading: loadingGroups } = useGetGroupsQuery({
    page: 1,
    page_size: 100,
  });

  const { data: teams = [], isLoading: loadingTeams } = useGetTeamsQuery({
    page: 1,
    page_size: 100,
  });

  // permission definitions
  const { data: userPermDefsResponse, isLoading: loadingUserPerms } =
    useGetUserPermissionsQuery({ page: 1, page_size: 100 });
  const userPermDefs = Array.isArray(userPermDefsResponse)
    ? userPermDefsResponse
    : userPermDefsResponse?.results || [];

  const { data: groupPermDefsResponse, isLoading: loadingGroupPerms } =
    useGetGroupPermissionsQuery({ page: 1, page_size: 100 });
  const groupPermDefs = Array.isArray(groupPermDefsResponse)
    ? groupPermDefsResponse
    : groupPermDefsResponse?.results || [];

  const { data: teamPermDefsResponse, isLoading: loadingTeamPerms } =
    useGetTeamPermissionsQuery({ page: 1, page_size: 100 });
  const teamPermDefs = Array.isArray(teamPermDefsResponse)
    ? teamPermDefsResponse
    : teamPermDefsResponse?.results || [];

  // assigned mappings
  const { data: userMaps = [], isLoading: loadingUserMaps } =
    useGetUser2UserPermissionsQuery({ page: 1, page_size: 1000 });

  const { data: groupMaps = [], isLoading: loadingGroupMaps } =
    useGetUserGroup2UserGroupPermissionsQuery({ page: 1, page_size: 1000 });

  const { data: teamMaps = [], isLoading: loadingTeamMaps } =
    useGetTeams2TeamsPermissionsQuery({ page: 1, page_size: 1000 });

  // mutations
  const [addUserMap] = useCreateUser2UserPermissionMutation();
  const [delUserMap] = useDeleteUser2UserPermissionMutation();
  const [addGroupMap] = useCreateUserGroup2UserGroupPermissionMutation();
  const [delGroupMap] = useDeleteUserGroup2UserGroupPermissionMutation();
  const [addTeamMap] = useCreateTeams2TeamsPermissionMutation();
  const [delTeamMap] = useDeleteTeams2TeamsPermissionMutation();

  // ── 2) TOGGLE HANDLERS ─────────────────────────────────────────────────────

  const onToggleUser = useCallback(
    async (employeeNo: string, permId: number, has: boolean) => {
      if (has) {
        const m = userMaps.find(
          (m) => m.user === employeeNo && m.permission === permId
        );
        if (m) await delUserMap(m.id).unwrap();
      } else {
        try {
          await addUserMap({ user: employeeNo, permission: permId }).unwrap();
        } catch (err) {
          console.error(err);
        }
      }
    },
    [userMaps, addUserMap, delUserMap]
  );

  const onToggleGroup = useCallback(
    async (id: number, permId: number, has: boolean) => {
      if (has) {
        const m = groupMaps.find(
          (m) => m.user_group === id && m.permission === permId
        );
        if (m) await delGroupMap(m.id).unwrap();
      } else {
        try {
          await addGroupMap({ user_group: id, permission: permId }).unwrap();
        } catch (err) {
          console.error(err);
        }
      }
    },
    [groupMaps, addGroupMap, delGroupMap]
  );

  const onToggleTeam = useCallback(
    async (teamId: number, permId: number, has: boolean) => {
      if (has) {
        // find by the `team` field, not `user_group`
        const m = teamMaps.find(
          (m) => m.team === teamId && m.permission === permId
        );
        if (m) await delTeamMap(m.id).unwrap();
      } else {
        try {
          // send `team` in the payload
          await addTeamMap({ team: teamId, permission: permId }).unwrap();
        } catch (err) {
          console.error(err);
        }
      }
    },
    [teamMaps, addTeamMap, delTeamMap]
  );

  // ── 3) BUILD COLUMNS & ROWS ────────────────────────────────────────────────

  // ── USERS ──────────────────────────────────────────────────────────────────
  const userColumns: ColumnDefinitionST<UserRow>[] = useMemo(() => {
    const cols: ColumnDefinitionST<UserRow>[] = [
      {
        key: "name",
        header: "Name",
        headerClassName: "px-6 py-4 text-left",
        cellClassName: "px-6 py-3",
      },
    ];

    userPermDefs.forEach((p) => {
      const code = String(p.permission_id);
      cols.push({
        key: code,
        header: code,
        headerClassName: "px-4 py-2 text-center",
        cellClassName: "px-4 py-2 text-center",
        renderCell: (row) => {
          const has = !!row[code];
          return (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex justify-center items-center">
                    <CheckboxRound
                      checked={has}
                      label=""
                      onChange={() =>
                        onToggleUser(row.employee_no, p.permission_id, has)
                      }
                      className="
              accent-green-500       
              hover:accent-green-600  
              transition-transform transform hover:scale-110
            "
                    />
                  </div>
                </TooltipTrigger>
                <TooltipContent side="top">
                  <p>{p.permission_name}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          );
        },
      });
    });

    return cols;
  }, [userPermDefs, onToggleUser]);

  const userRows: UserRow[] = useMemo(
    () =>
      users?.data?.results.map((u: any) => ({
        id: u.id,
        name: u.fullnames,
        employee_no: u.employee_no,
        ...userPermDefs.reduce((acc, p) => {
          acc[String(p.permission_id)] = userMaps.some(
            (m) => m.user === u.employee_no && m.permission === p.permission_id
          );
          return acc;
        }, {} as Record<string, boolean>),
      })),
    [users, userPermDefs, userMaps]
  );

  // ── GROUPS ─────────────────────────────────────────────────────────────────
  const groupColumns: ColumnDefinitionST<PartyRow>[] = useMemo(() => {
    const cols: ColumnDefinitionST<PartyRow>[] = [
      {
        key: "name",
        header: "Name",
        headerClassName: "px-6 py-4 text-left",
        cellClassName: "px-6 py-3",
      },
    ];

    groupPermDefs.forEach((p) => {
      const code = String(p.permission_id);
      cols.push({
        key: code,
        header: code,
        headerClassName: "px-4 py-2 text-center",
        cellClassName: "px-4 py-2 text-center",
        renderCell: (row) => {
          const has = !!row[code];
          return (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex justify-center items-center">
                    <CheckboxRound
                      checked={has}
                      label=""
                      onChange={() =>
                        onToggleGroup(row.id, p.permission_id, has)
                      }
                      className={`
                        transition-transform transform
                        ${has ? "text-green-500" : "text-gray-300"}
                        hover:scale-110
                      `}
                    />
                  </div>
                </TooltipTrigger>
                <TooltipContent side="top">
                  <p>{p.permission_name}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          );
        },
      });
    });

    return cols;
  }, [groupPermDefs, onToggleGroup]);

  const groupRows: PartyRow[] = useMemo(
    () =>
      groups.map((g: any) => {
        const row: any = { id: g.group_id, name: g.name };
        groupPermDefs.forEach((p) => {
          row[String(p.permission_id)] = groupMaps.some(
            (m) =>
              m.user_group === g.group_id && m.permission === p.permission_id
          );
        });
        return row;
      }),
    [groups, groupPermDefs, groupMaps]
  );

  // ── TEAMS ──────────────────────────────────────────────────────────────────
  const teamColumns: ColumnDefinitionST<PartyRow>[] = useMemo(() => {
    const cols: ColumnDefinitionST<PartyRow>[] = [
      {
        key: "name",
        header: "Name",
        headerClassName: "...",
        cellClassName: "...",
      },
    ];

    teamPermDefs.forEach((p) => {
      const code = String(p.permission_id);
      cols.push({
        key: code,
        header: code,
        renderCell: (row) => {
          const has = !!row[code];
          return (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex justify-center">
                    <CheckboxRound
                      checked={has}
                      onChange={() =>
                        onToggleTeam(row.id, p.permission_id, has)
                      }
                      className={`${has ? "accent-green-500" : "accent-gray-300"
                        } transition-transform hover:scale-110`}
                    />
                  </div>
                </TooltipTrigger>
                <TooltipContent side="top">
                  <p>{p.permission_name}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          );
        },
        headerClassName: "px-4 py-2 text-center",
        cellClassName: "px-4 py-2 text-center",
      });
    });

    return cols;
  }, [teamPermDefs, onToggleTeam]);

  const teamRows: PartyRow[] = useMemo(
    () =>
      teams.map((t: any) => {
        const row: any = { id: t.id, name: t.team };
        teamPermDefs.forEach((p) => {
          // again, check .team, not .user_group
          row[String(p.permission_id)] = teamMaps.some(
            (m) => m.team === t.id && m.permission === p.permission_id
          );
        });
        return row;
      }),
    [teams, teamPermDefs, teamMaps]
  );

  // ── LOADING ────────────────────────────────────────────────────────────────
  const anyLoading =
    loadingUsers ||
    loadingGroups ||
    loadingTeams ||
    loadingUserPerms ||
    loadingGroupPerms ||
    loadingTeamPerms ||
    loadingUserMaps ||
    loadingGroupMaps ||
    loadingTeamMaps;

  if (anyLoading) {
    return (
      <Screen>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500" />
        </div>
      </Screen>
    );
  }

  // ── RENDER ────────────────────────────────────────────────────────────────
  const tabs = [
    {
      value: "users",
      title: "Users",
      content: <SimpleTable data={userRows} columns={userColumns} />,
    },
    {
      value: "groups",
      title: "Groups",
      content: <SimpleTable data={groupRows} columns={groupColumns} />,
    },
    {
      value: "teams",
      title: "Teams",
      content: <SimpleTable data={teamRows} columns={teamColumns} />,
    },
  ];

  return (
    <Screen>
      <h2 className="font-bold mb-4">Permission Matrix</h2>
      <Tab2 tabs={tabs} />
    </Screen>
  );
};

export default AssignRole;
