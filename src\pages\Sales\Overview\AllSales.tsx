import { useGetAllSalesQuery } from '@/redux/slices/sales'
import React, { useState } from 'react'
import SalesTable from './SalesTable'
import SpinnerTemp from '@/components/custom/spinners/SpinnerTemp'
import { CircleAlert } from 'lucide-react'

const AllSales = () => {

    //all sales
    const [allSalesItemsPerPage, setAllSalesItemsPerPage] = useState(20)
    const [allSalesCurrentPage, setAllSalesCurrentPage] = useState(1)
    const [universalSearchValue, setuniversalSearchValue] = useState('')

    const { data: allSalesData, isLoading, isFetching } = useGetAllSalesQuery({ page_size: allSalesItemsPerPage,page: allSalesCurrentPage,search: universalSearchValue });

    // Show loader when initially loading or when tab is changing
    const showLoader = isLoading || isFetching;

     const totalItems = allSalesData?.data?.total_data || 0;

    return (
        <>
            <div className='relative'>
                {showLoader ? (
                    <div className="absolute inset-0 z-10 flex pt-16 justify-center bg-white bg-opacity-70">       
                        <SpinnerTemp type="spinner-double" size="md" />
                    </div>
                )
                : (!totalItems || totalItems === 0) ? (
                    <div className='flex flex-col gap-2 justify-center items-center h-64'>
                        <CircleAlert size={45} className='text-muted-foreground'/>
                        <p className="text-center text-xs">No data available.</p>
                        <p className="text-center text-xs">You may not have permission to view this section.</p>
                    </div>
                ) : (
                <SalesTable
                    data={allSalesData}
                    itemsPerPage={allSalesItemsPerPage}
                    setItemsPerPage={setAllSalesItemsPerPage}
                    currentPage={allSalesCurrentPage}
                    setCurrentPage={setAllSalesCurrentPage}
                    SearchComponent={<SearchComponent universalSearchValue={universalSearchValue} setuniversalSearchValue={setuniversalSearchValue} />}
                />
            )}
            </div>
        </>
    )
}

export default AllSales

interface SearchComponentProps {
    universalSearchValue: string,
    setuniversalSearchValue: React.Dispatch<React.SetStateAction<string>>
}

function SearchComponent({ universalSearchValue, setuniversalSearchValue }: SearchComponentProps) {
    return <input
        value={universalSearchValue}
        onChange={e => setuniversalSearchValue(e.target.value)}
        className="px-3 py-2 ml-1 w-full border rounded text-sm focus:outline-none bg-transparent"
        placeholder="Search sales details..."
    />
}












// import { useGetAllSalesQuery } from '@/redux/slices/sales'
// import React, { useState, useEffect } from 'react'
// import SalesTable from './SalesTable'
// import SpinnerTemp from '@/components/custom/spinners/SpinnerTemp'
// import SalesPermissionIndicator from '@/components/sales/SalesPermissionIndicator'
// import { useSalesPermissions } from '@/hooks/useSalesPermissions'

// const AllSales = () => {
//     const { 
//         hasAnySalesAccess,
//         apiParams,
//         userDetails
//     } = useSalesPermissions();

//     //all sales
//     const [allSalesItemsPerPage, setAllSalesItemsPerPage] = useState(20)
//     const [allSalesCurrentPage, setAllSalesCurrentPage] = useState(1)
//     const [universalSearchValue, setuniversalSearchValue] = useState('')

//     // Get API parameters based on permissions
//     const getApiParams = () => {
//         // Use the simplified permission-based API parameters
//         const params = {
//             ...apiParams,
//             page_size: allSalesItemsPerPage,
//             page: allSalesCurrentPage,
//             search: universalSearchValue || undefined
//         };
        
//         return params;
//     };

//     const { data: allSalesData, isLoading, isFetching, refetch } = useGetAllSalesQuery(
//         getApiParams(),
//         {
//             skip: !hasAnySalesAccess // Skip the query if user has no access
//         }
//     );

//     // Refetch data when parameters change
//     useEffect(() => {
//         if (hasAnySalesAccess) {
//             refetch();
//         }
//     }, [allSalesItemsPerPage, allSalesCurrentPage, universalSearchValue, refetch, hasAnySalesAccess]);

//     // Show loader when initially loading or when tab is changing
//     const showLoader = isLoading || isFetching;

//     return (
//         <>
//             <div className="space-y-4">
//                 {/* Show permission indicator */}
//                 <SalesPermissionIndicator />

//                 {/* Only show sales table if user has access */}
//                 {hasAnySalesAccess ? (
//                     <div className='relative'>
//                         {showLoader && (
//                             <div className="absolute inset-0 z-10 flex pt-16 justify-center bg-white bg-opacity-70">       
//                                 <SpinnerTemp type="spinner-double" size="md" />
//                             </div>
//                         )}
//                         <SalesTable
//                             data={allSalesData}
//                             itemsPerPage={allSalesItemsPerPage}
//                             setItemsPerPage={setAllSalesItemsPerPage}
//                             currentPage={allSalesCurrentPage}
//                             setCurrentPage={setAllSalesCurrentPage}
//                             SearchComponent={<SearchComponent universalSearchValue={universalSearchValue} setuniversalSearchValue={setuniversalSearchValue} />}
//                         />
//                     </div>
//                 ) : (
//                     <div className="text-center py-8 text-gray-500">
//                         <p>Contact your administrator to request sales viewing permissions.</p>
//                     </div>
//                 )}
//             </div>
//         </>
//     )
// }

// export default AllSales

// interface SearchComponentProps {
//     universalSearchValue: string,
//     setuniversalSearchValue: React.Dispatch<React.SetStateAction<string>>
// }

// function SearchComponent({ universalSearchValue, setuniversalSearchValue }: SearchComponentProps) {
//     return <input
//         value={universalSearchValue}
//         onChange={e => setuniversalSearchValue(e.target.value)}
//         className="px-3 py-2 ml-1 w-full border rounded text-sm focus:outline-none bg-transparent"
//         placeholder="Search sales details..."
//     />
// }






