import { contentHeader, noAuthHeader } from "@/utils/header";
import { apiSlice } from "../apiSlice";

export const flagsApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getFlags: builder.query({
      query: (params) => ({
        url: "/services/flags/",
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      transformResponse: (response) => {
        const data = response.data || {};
        return {
          results: data.results || [],
          count: data.total_data || 0,
          current_page: data.current_page || 1,
          num_pages: data.last_page || 1,
          total_data: data.total_data || 0,
          per_page: data.per_page || 20
        };
      },
      transformErrorResponse: (error) => {
        console.error("Flags API Error:", error);
        return error;
      },
      providesTags: ["Flags"],
    }),

    getFlagDetails: builder.query({
      query: (flagId) => ({
        url: `/services/flags/${flagId}/`,
        method: "GET",
        headers: noAuthHeader(),
      }),
      providesTags: ["Flags"],
    }),

    createFlag: builder.mutation({
      query: (data) => ({
        url: "/services/flags/",
        method: "POST",
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Flags"],
    }),

    updateFlag: builder.mutation({
      query: ({ flagId, data }) => ({
        url: `/services/flags/${flagId}/`,
        method: "PUT",
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Flags"],
    }),

    partialUpdateFlag: builder.mutation({
      query: (data) => ({
        url: `/services/flags/${data?.flag_id}/`,
        method: "PATCH",
        body: data,
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Flags"],
    }),

    deleteFlag: builder.mutation({
      query: (flagId) => ({
        url: `/services/flags/${flagId}/`,
        method: "DELETE",
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Flags"],
    }),

    resolveFlag: builder.mutation({
      query: ({ flagId, data }) => ({
        url: `/services/flags/${flagId}/resolve/`,
        method: "PATCH",
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Flags"],
    }),
  }),
});

export const {
  useGetFlagsQuery,
  useGetFlagDetailsQuery,
  useCreateFlagMutation,
  useUpdateFlagMutation,
  usePartialUpdateFlagMutation,
  useDeleteFlagMutation,
  useResolveFlagMutation,
} = flagsApiSlice;
