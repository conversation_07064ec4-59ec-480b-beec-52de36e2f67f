import { contentHeader, noAuthHeader } from '@/utils/header';
import { apiSlice } from '../apiSlice';
import { BASE_URL } from '@/config';

// TypeScript interfaces for Logistics entities
export interface Client {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  client_type?: 'client' | 'representative';
  // API response format
  name?: string;
  phone_number?: string;
}

export interface Driver {
  id: number;
  username: string;
  first_name: string;
  last_name: string;
  employee_no: string;
  user_id?: number;
  category?: string;
  office?: string;
  fullnames: string;
  email: string;
  status?: string;
  created_date?: string;
  reset_code?: string;
  reset_code_creation_time?: string;
  user_group?: string;
  is_available?: boolean;
  phone_number?: string;
  personal_email?: string;
  is_marketer?: boolean;
  gender?: string;
  qr_code?: string;
  mkcode: number;
  erp_user_id?: string;
  manager?: string;
  is_driver: boolean;
  department?: string;
  team?: string;
  group?: string;
  groups?: number[];
  user_permissions?: number[];
}

export interface SiteVisit {
  id?: number;
  pickup_time: string;
  pickup_date: string;
  pickup_location: string;
  transport_type?: 'self_drive' | 'own_means' | 'outsourced' | 'company_vehicle';
  special_assignment_destination?: string;
  remarks?: string;
  created_at?: string;
  status:
    | 'Pending'
    | 'Approved'
    | 'In Progress'
    | 'Completed'
    | 'Cancelled'
    | 'Rejected'
    | 'Reviewed';
  marketer: string;
  vehicle?: number;
  project: string;
  driver?: string;
  special_assignment_assigned_to?: string;
  clients?: Client[];
  site_visit_client?: Client[]; // Actual API response format
  is_self_drive?: boolean;
  // Additional client fields that might be included in the response
  site_visist_client__name?: string;
  site_visist_client__phone_number?: string;
  site_visist_client__email?: string;
}

export interface SpecialBooking {
  id?: number;
  reservation_date: string;
  reservation_time: string;
  pickup_location: string;
  destination: string;
  reason?: string;
  remarks?: string;
  created_at?: string;
  status: 'Pending' | 'Approved' | 'In Progress' | 'Completed';
  vehicle: number;
  driver: string;
  assigned_to: string | { dp_id?: string; id?: string; dp_name?: string; name?: string; dep_head_name?: string; head?: string; }; // Can be department ID string or department object
}

export interface Vehicle {
  id?: number;
  make: string;
  model: string;
  body_type: string;
  number_of_seats: number;
  engine_capacity: string;
  vehicle_registration: string;
  passengers_assigned?: boolean;
  status?: 'Available' | 'In Use' | 'Under Maintenance' | 'Out of Service';
  created_at?: string;
  driver: string;
}

export interface VehicleRequest {
  id?: number;
  pickup_time: string;
  pickup_date: string;
  pickup_location: string;
  destination_location: string;
  number_of_passengers: number;
  remarks?: string;
  purpose?: string;
  created_at?: string;
  status?: 'Pending' | 'Approved' | 'In Progress' | 'Trip Completed' | 'Rejected';
  vehicle?: number; // Optional - assigned during approval
  requester: string;
  driver?: string; // Optional - assigned during approval
}

export interface SiteVisitSurvey {
  id?: number;
  booked: boolean;
  visited: boolean;
  amount_reserved?: string | null;
  plot_details?: string | null;
  reason_not_visited?: string | null;
  reason_not_booked?: string | null;
  site_visit: number;
}

export interface LogisticsApiResponse<T> {
  data: {
    results: T[];
    count: number;
    next?: string;
    previous?: string;
  };
}

export interface LogisticsQueryParams {
  page?: number;
  page_size?: number;
  search?: string;
  status?: string;
  driver?: string;
  marketer?: string;
  assigned_to?: string;
  vehicle?: string;
  project?: string;
  pickup_date?: string;
  pickup_date__gte?: string;
  pickup_date__lte?: string;
  pickup_location?: string;
  site_visist_client__name?: string;
  site_visist_client__phone_number?: string;
  site_visist_client__email?: string;
  ordering?: string;
}

export const logisticsApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // —— Site Visits ——
    getSiteVisits: builder.query<LogisticsApiResponse<SiteVisit>, LogisticsQueryParams>({
      query: (params) => ({
        url: `${BASE_URL}/logistics/site-visits`,
        method: 'GET',
        params,
        headers: noAuthHeader(),
      }),
      providesTags: ['Logistics'],
    }),
    getSiteVisit: builder.query<SiteVisit, number>({
      query: (id) => ({
        url: `${BASE_URL}/logistics/site-visits/${id}`,
        method: 'GET',
        headers: noAuthHeader(),
      }),
      providesTags: ['Logistics'],
    }),
    createSiteVisit: builder.mutation<SiteVisit, Partial<SiteVisit>>({
      query: (data) => ({
        url: `${BASE_URL}/logistics/site-visits`,
        method: 'POST',
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ['Logistics'],
    }),
    updateSiteVisit: builder.mutation<SiteVisit, { id: number } & Partial<SiteVisit>>({
      query: ({ id, ...patch }) => ({
        url: `${BASE_URL}/logistics/site-visits/${id}`,
        method: 'PATCH',
        body: patch,
        headers: contentHeader(),
      }),
      invalidatesTags: ['Logistics'],
    }),
    deleteSiteVisit: builder.mutation<void, number>({
      query: (id) => ({
        url: `${BASE_URL}/logistics/site-visits/${id}`,
        method: 'DELETE',
        headers: contentHeader(),
      }),
      invalidatesTags: ['Logistics'],
    }),

    // —— Site Visit "Surveys" (clients) ——
    getSiteVisitsSurveys: builder.query<
      LogisticsApiResponse<{ id: number; site_visit: number; site_visit_client: Client }>,
      { page?: number; page_size?: number; site_visit: number }
    >({
      query: ({ site_visit, page = 1, page_size = 100 }) => ({
        url: `${BASE_URL}/logistics/site-visits-surveys`,
        method: 'GET',
        params: { site_visit, page, page_size },
        headers: noAuthHeader(),
      }),
      providesTags: ['Logistics'],
    }),

    // —— Site Visit Surveys ——
    getSiteVisitSurveys: builder.query<LogisticsApiResponse<SiteVisitSurvey>, LogisticsQueryParams>({
      query: (params) => ({
        url: `${BASE_URL}/logistics/site-visits-surveys`,
        method: 'GET',
        params,
        headers: noAuthHeader(),
      }),
      providesTags: ['Logistics'],
    }),

    // —— Special Bookings ——
    getSpecialBookings: builder.query<LogisticsApiResponse<SpecialBooking>, LogisticsQueryParams>({
      query: (params) => ({
        url: `${BASE_URL}/logistics/special-bookings`,
        method: 'GET',
        params,
        headers: noAuthHeader(),
      }),
      providesTags: ['Logistics'],
    }),
    getSpecialBooking: builder.query<SpecialBooking, number>({
      query: (id) => ({
        url: `${BASE_URL}/logistics/special-bookings/${id}`,
        method: 'GET',
        headers: noAuthHeader(),
      }),
      providesTags: ['Logistics'],
    }),
    createSpecialBooking: builder.mutation<SpecialBooking, Partial<SpecialBooking>>({
      query: (data) => ({
        url: `${BASE_URL}/logistics/special-bookings`,
        method: 'POST',
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ['Logistics'],
    }),
    updateSpecialBooking: builder.mutation<SpecialBooking, { id: number } & Partial<SpecialBooking>>({
      query: ({ id, ...patch }) => ({
        url: `${BASE_URL}/logistics/special-bookings/${id}`,
        method: 'PATCH',
        body: patch,
        headers: contentHeader(),
      }),
      invalidatesTags: ['Logistics'],
    }),
    deleteSpecialBooking: builder.mutation<void, number>({
      query: (id) => ({
        url: `${BASE_URL}/logistics/special-bookings/${id}`,
        method: 'DELETE',
        headers: contentHeader(),
      }),
      invalidatesTags: ['Logistics'],
    }),

    // —— Vehicles ——
    getVehicles: builder.query<LogisticsApiResponse<Vehicle>, LogisticsQueryParams>({
      query: (params) => ({
        url: `${BASE_URL}/logistics/vehicles`,
        method: 'GET',
        params,
        headers: noAuthHeader(),
      }),
      providesTags: ['Logistics'],
    }),
    getVehicle: builder.query<Vehicle, number>({
      query: (id) => ({
        url: `${BASE_URL}/logistics/vehicles/${id}`,
        method: 'GET',
        headers: noAuthHeader(),
      }),
      providesTags: ['Logistics'],
    }),
    createVehicle: builder.mutation<Vehicle, Partial<Vehicle>>({
      query: (data) => ({
        url: `${BASE_URL}/logistics/vehicles`,
        method: 'POST',
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ['Logistics'],
    }),
    updateVehicle: builder.mutation<Vehicle, { id: number } & Partial<Vehicle>>({
      query: ({ id, ...patch }) => ({
        url: `${BASE_URL}/logistics/vehicles/${id}`,
        method: 'PATCH',
        body: patch,
        headers: contentHeader(),
      }),
      invalidatesTags: ['Logistics'],
    }),
    deleteVehicle: builder.mutation<void, number>({
      query: (id) => ({
        url: `${BASE_URL}/logistics/vehicles/${id}`,
        method: 'DELETE',
        headers: contentHeader(),
      }),
      invalidatesTags: ['Logistics'],
    }),

    // —— Vehicle Requests ——
    getVehicleRequests: builder.query<LogisticsApiResponse<VehicleRequest>, LogisticsQueryParams>({
      query: (params) => ({
        url: `${BASE_URL}/logistics/vehicles-requests`,
        method: 'GET',
        params,
        headers: noAuthHeader(),
      }),
      providesTags: ['Logistics'],
    }),
    getVehicleRequest: builder.query<VehicleRequest, number>({
      query: (id) => ({
        url: `${BASE_URL}/logistics/vehicles-requests/${id}`,
        method: 'GET',
        headers: noAuthHeader(),
      }),
      providesTags: ['Logistics'],
    }),
    createVehicleRequest: builder.mutation<VehicleRequest, Partial<VehicleRequest>>({
      query: (data) => ({
        url: `${BASE_URL}/logistics/vehicles-requests`,
        method: 'POST',
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ['Logistics'],
    }),
    updateVehicleRequest: builder.mutation<VehicleRequest, { id: number } & Partial<VehicleRequest>>({
      query: ({ id, ...patch }) => ({
        url: `${BASE_URL}/logistics/vehicles-requests/${id}`,
        method: 'PATCH',
        body: patch,
        headers: contentHeader(),
      }),
      invalidatesTags: ['Logistics'],
    }),
    deleteVehicleRequest: builder.mutation<void, number>({
      query: (id) => ({
        url: `${BASE_URL}/logistics/vehicles-requests/${id}`,
        method: 'DELETE',
        headers: contentHeader(),
      }),
      invalidatesTags: ['Logistics'],
    }),

    // —— Drivers ——
    getDrivers: builder.query<LogisticsApiResponse<Driver>, LogisticsQueryParams>({
      query: (params) => ({
        url: `${BASE_URL}/logistics/drivers`,
        method: 'GET',
        params,
        headers: noAuthHeader(),
      }),
      providesTags: ['Logistics'],
    }),

    createSiteVisitSurvey: builder.mutation<SiteVisitSurvey, Partial<SiteVisitSurvey>>({
      query: (data) => ({
        url: `${BASE_URL}/logistics/site-visits-surveys`,
        method: 'POST',
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ['Logistics'],
    }),

    updateSiteVisitSurvey: builder.mutation<SiteVisitSurvey, { id: number } & Partial<SiteVisitSurvey>>({
      query: ({ id, ...patch }) => ({
        url: `${BASE_URL}/logistics/site-visits-surveys/${id}`,
        method: 'PATCH',
        body: patch,
        headers: contentHeader(),
      }),
      invalidatesTags: ['Logistics'],
    }),
  }),
});

export const {
  useGetSiteVisitsQuery,
  useGetSiteVisitQuery,
  useCreateSiteVisitMutation,
  useUpdateSiteVisitMutation,
  useDeleteSiteVisitMutation,

  // ← newly added hook:
  useGetSiteVisitsSurveysQuery,

  useGetSpecialBookingsQuery,
  useGetSpecialBookingQuery,
  useCreateSpecialBookingMutation,
  useUpdateSpecialBookingMutation,
  useDeleteSpecialBookingMutation,

  useGetVehiclesQuery,
  useGetVehicleQuery,
  useCreateVehicleMutation,
  useUpdateVehicleMutation,
  useDeleteVehicleMutation,

  useGetVehicleRequestsQuery,
  useGetVehicleRequestQuery,
  useCreateVehicleRequestMutation,
  useUpdateVehicleRequestMutation,
  useDeleteVehicleRequestMutation,

  useGetDriversQuery,

  useGetSiteVisitSurveysQuery,
  useCreateSiteVisitSurveyMutation,
  useUpdateSiteVisitSurveyMutation,
} = logisticsApiSlice;