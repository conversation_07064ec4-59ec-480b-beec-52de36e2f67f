import MultiStepModal from "@/components/custom/modals/MultiStepModal";
import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Shield, ShoppingBag, Truck, Users, Building2 } from "lucide-react";

interface AddPermissionProps {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (newPermission: {
    permissionName: string;
    permissionCode: string;
    description: string;
  }) => void;
  isLoading?: boolean;
  category: string;
}

// Sales permissions template data
const salesPermissionsTemplates = [
  // Office-based permissions
  {
    name: "VIEW_SALES_HQ",
    code: "1001",
    description: "Permission to view sales data from HQ office",
    category: "Office"
  },
  {
    name: "VIEW_SALES_KAREN",
    code: "1002",
    description: "Permission to view sales data from KAREN office",
    category: "Office"
  },
  {
    name: "VIEW_SALES_ALL_OFFICES",
    code: "1003",
    description: "Permission to view sales data from all offices",
    category: "Office"
  },
  
  // Marketer-based permissions
  {
    name: "VIEW_SALES_OWN_MARKETER",
    code: "1004",
    description: "Permission to view sales data for the logged-in marketer only",
    category: "Marketer"
  },
  {
    name: "VIEW_SALES_ALL_MARKETERS",
    code: "1005",
    description: "Permission to view sales data for all marketers",
    category: "Marketer"
  },
  
  // Organization team-based permissions
  {
    name: "VIEW_SALES_DIASPORA_TEAM",
    code: "1006",
    description: "Permission to view sales data from DIASPORA team",
    category: "Team"
  },
  {
    name: "VIEW_SALES_DIGITAL_TEAM",
    code: "1007",
    description: "Permission to view sales data from DIGITAL team",
    category: "Team"
  },
  {
    name: "VIEW_SALES_TELEMARKETING_TEAM",
    code: "1008",
    description: "Permission to view sales data from TELEMARKETING team",
    category: "Team"
  },
  {
    name: "VIEW_SALES_OTHER_TEAM",
    code: "1009",
    description: "Permission to view sales data from OTHER team",
    category: "Team"
  },
  {
    name: "VIEW_SALES_ALL_TEAMS",
    code: "1010",
    description: "Permission to view sales data from all teams",
    category: "Team"
  },
  
  // Diaspora region-based permissions
  {
    name: "VIEW_SALES_DIASPORA_REGION",
    code: "1011",
    description: "Permission to view sales data filtered by diaspora region",
    category: "Diaspora"
  },
  {
    name: "VIEW_SALES_ALL_DIASPORA_REGIONS",
    code: "1012",
    description: "Permission to view sales data from all diaspora regions",
    category: "Diaspora"
  }
];

// Logistics permissions template data
const logisticsPermissionsTemplates = [
  {
    name: "BOOK_VISIT",
    code: "201",
    description: "Book Site Visit - Allows user to book a site visit or appointment",
    category: "Logistics"
  },
  {
    name: "COMPLETE_TRIPS",
    code: "202",
    description: "Complete Trips - Allows user to mark trips as completed",
    category: "Logistics"
  },
  {
    name: "CREATE_VEHICLE_REQUEST",
    code: "203",
    description: "Create Vehicle Request - Allows user to request a vehicle",
    category: "Logistics"
  },
  {
    name: "CREATE_SPECIAL_ASSIGNMENT",
    code: "204",
    description: "Create Special Assignment - Allows user to assign or create special assignments",
    category: "Logistics"
  },
  {
    name: "ACCESS_LOGISTICS_DASHBOARD",
    code: "205",
    description: "Access Logistics Dashboard - Allows user to view the logistics dashboard",
    category: "Logistics"
  },
  {
    name: "ACCESS_LOGISTICS_STATISTICS",
    code: "206",
    description: "Access Logistics Statistics - Allows user to view logistics-related statistics",
    category: "Logistics"
  },
  {
    name: "ACCESS_CLIENTS",
    code: "207",
    description: "Access Clients - Allows user to view clients within logistics module",
    category: "Logistics"
  },
  {
    name: "ACCESS_DRIVERS",
    code: "208",
    description: "Access Drivers - Allows user to view and manage drivers",
    category: "Logistics"
  },
  {
    name: "ACCESS_VEHICLES",
    code: "209",
    description: "Access Vehicles - Allows user to view and manage vehicles",
    category: "Logistics"
  },
  {
    name: "ACCESS_LOGISTICS_REPORTS",
    code: "210",
    description: "Access Logistics Reports - Allows user to view and generate logistics reports",
    category: "Logistics"
  },
  {
    name: "ACCESS_SITEVISIT_REPORTS",
    code: "211",
    description: "Access Site Visit Reports - Allows user to view and generate site visit reports",
    category: "Logistics"
  }
];

export default function AddPermission({
  isOpen,
  onClose,
  onAdd,
  isLoading = false,
  category,
}: AddPermissionProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [permissionData, setPermissionData] = useState<{
    permissionName: string;
    permissionCode: string;
    description: string;
  }>({
    permissionName: "",
    permissionCode: "",
    description: "",
  });
  const [permissionType, setPermissionType] = useState<string>("custom");

  useEffect(() => {
    if (isOpen) {
      setCurrentStep(0);
      setPermissionData({
        permissionName: "",
        permissionCode: "",
        description: "",
      });
      setPermissionType("custom");
    }
  }, [isOpen]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setPermissionData((prev) => ({ ...prev, [name]: value }));
  };

  const handleAddPermission = () => {
    onAdd(permissionData);
  };

  const handleTemplateSelect = (template: {
    name: string;
    code: string;
    description: string;
  }) => {
    setPermissionData({
      permissionName: template.name,
      permissionCode: template.code,
      description: template.description,
    });
  };

  const renderTemplateItem = (template: {
    name: string;
    code: string;
    description: string;
    category: string;
  }) => {
    return (
      <div 
        key={template.code}
        className="p-3 border rounded-md hover:bg-gray-50 cursor-pointer transition-colors"
        onClick={() => handleTemplateSelect(template)}
      >
        <div className="flex items-center justify-between">
          <span className="font-medium">{template.name}</span>
          <Badge variant="outline">{template.code}</Badge>
        </div>
        <p className="text-sm text-muted-foreground mt-1">{template.description}</p>
        <Badge 
          variant="secondary" 
          className="mt-2"
        >
          {template.category}
        </Badge>
      </div>
    );
  };

  return (
    <MultiStepModal
      isOpen={isOpen}
      onOpenChange={onClose}
      title={`Add New ${category} Permission`}
      description="Complete all steps to add a new permission"
      currentStep={currentStep}
      onStepChange={setCurrentStep}
      onComplete={handleAddPermission}
      //isCompleteDisabled={isLoading}
      steps={[
        {
          title: "Basic Information",
          content: (
            <div className="space-y-4 py-2">
              <Tabs defaultValue="custom" onValueChange={setPermissionType} className="w-full">
                <TabsList className="grid grid-cols-4 mb-4">
                  <TabsTrigger value="custom" className="flex items-center space-x-2">
                    <Shield className="h-4 w-4" />
                    <span>Custom</span>
                  </TabsTrigger>
                  <TabsTrigger value="sales" className="flex items-center space-x-2">
                    <ShoppingBag className="h-4 w-4" />
                    <span>Sales</span>
                  </TabsTrigger>
                  <TabsTrigger value="logistics" className="flex items-center space-x-2">
                    <Truck className="h-4 w-4" />
                    <span>Logistics</span>
                  </TabsTrigger>
                  <TabsTrigger value="other" className="flex items-center space-x-2">
                    <Building2 className="h-4 w-4" />
                    <span>Other</span>
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="custom">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="permissionName">Permission Name</Label>
                      <Input
                        id="permissionName"
                        name="permissionName"
                        value={permissionData.permissionName}
                        onChange={handleInputChange}
                        placeholder="Enter permission name"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="permissionCode">Permission Code</Label>
                      <Input
                        id="permissionCode"
                        name="permissionCode"
                        value={permissionData.permissionCode}
                        onChange={handleInputChange}
                        placeholder="Enter permission code (e.g., 001)"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        id="description"
                        name="description"
                        value={permissionData.description}
                        onChange={handleInputChange}
                        placeholder="Describe the permission"
                        rows={3}
                      />
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="sales">
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground mb-2">
                      Select a sales permission template:
                    </p>
                    <ScrollArea className="h-[300px] rounded-md border p-2">
                      <div className="space-y-2">
                        {salesPermissionsTemplates.map(renderTemplateItem)}
                      </div>
                    </ScrollArea>
                  </div>
                </TabsContent>

                <TabsContent value="logistics">
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground mb-2">
                      Select a logistics permission template:
                    </p>
                    <ScrollArea className="h-[300px] rounded-md border p-2">
                      <div className="space-y-2">
                        {logisticsPermissionsTemplates.map(renderTemplateItem)}
                      </div>
                    </ScrollArea>
                  </div>
                </TabsContent>

                <TabsContent value="other">
                  <div className="flex items-center justify-center h-[300px]">
                    <div className="text-center">
                      <Users className="h-12 w-12 mx-auto text-muted-foreground" />
                      <p className="mt-2 text-muted-foreground">
                        Additional permission templates will be added here.
                      </p>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>

              {permissionType !== "custom" && (
                <div className="space-y-4 pt-4 border-t">
                  <div className="space-y-2">
                    <Label htmlFor="permissionName">Permission Name</Label>
                    <Input
                      id="permissionName"
                      name="permissionName"
                      value={permissionData.permissionName}
                      onChange={handleInputChange}
                      placeholder="Enter permission name"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="permissionCode">Permission Code</Label>
                    <Input
                      id="permissionCode"
                      name="permissionCode"
                      value={permissionData.permissionCode}
                      onChange={handleInputChange}
                      placeholder="Enter permission code (e.g., 001)"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      name="description"
                      value={permissionData.description}
                      onChange={handleInputChange}
                      placeholder="Describe the permission"
                      rows={3}
                    />
                  </div>
                </div>
              )}
            </div>
          ),
        },
        {
          title: "Review & Confirm",
          content: (
            <div className="space-y-4 py-2">
              <div className="bg-gray-50 p-4 rounded-md space-y-2">
                <div>
                  <span className="font-medium">Permission Name:</span>
                  <span className="ml-2">{permissionData.permissionName}</span>
                </div>
                <div>
                  <span className="font-medium">Permission Code:</span>
                  <span className="ml-2">{permissionData.permissionCode}</span>
                </div>
                <div>
                  <span className="font-medium">Description:</span>
                  <span className="ml-2">{permissionData.description}</span>
                </div>
              </div>
              <p className="text-sm text-gray-600">
                Please review before adding this permission.
              </p>
            </div>
          ),
        },
      ]}
    />
  );
}
