import { Flag, <PERSON><PERSON><PERSON><PERSON>gle, <PERSON><PERSON><PERSON>cle, TrendingUp, Activity, Shield, Clock, Zap } from "lucide-react";
import { motion } from "framer-motion";


interface FlagData {
  flag_id: string;
  flag_type: string;
  title: string;
  description: string;
  severity?: 'Info' | 'Low' | 'Medium' | 'High' | 'Critical';
  status?: 'Active' | 'Under Review' | 'Resolved' | 'Dismissed';
  flagged_by?: string;
  flagged_by_name?: string;
  assigned_to?: string | null;
  assigned_to_display?: string;
  assigned_to_name?: string;
  resolution_notes?: string | null;
  resolved_at?: string | null;
  created_at?: string;
  updated_at?: string;
  created_by?: string;
  is_active?: boolean;
  link_entity_type?: string;
  link_entity_id?: string;
  entity_type?: string;
  entity_id?: string;
  entity_name?: string;
  client_status?: 'Prospect' | 'Customer' | 'Lead File';
  customer_no?: string | null;
  prospect_id?: string | null;
  lead_file_no?: string | null;
  related_entity?: string;
}

export default function FlagsAnalytics({ flags }: { flags: FlagData[] }) {
  const total = flags.length;
  const active = flags.filter(f => f.status === "Active").length;
  const resolved = flags.filter(f => f.status === "Resolved").length;
  const underReview = flags.filter(f => f.status === "Under Review").length;
  const dismissed = flags.filter(f => f.status === "Dismissed").length;

  const critical = flags.filter(f => f.severity === "Critical").length;
  const high = flags.filter(f => f.severity === "High").length;
  const medium = flags.filter(f => f.severity === "Medium").length;
  const low = flags.filter(f => f.severity === "Low").length;
  const info = flags.filter(f => f.severity === "Info").length;

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.1,
        duration: 0.5,
        ease: "easeOut"
      }
    })
  };

  return (
    <div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {/* Total Flags */}
      <motion.div
        custom={0}
        initial="hidden"
        animate="visible"
        variants={cardVariants}
        className="relative overflow-hidden bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl shadow-xl p-6 text-white"
      >
        <div className="absolute top-2 right-2 w-16 h-16 bg-white/10 rounded-full blur-xl"></div>
        <div className="relative z-10">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
              <Flag className="h-6 w-6 text-white" />
            </div>
            <TrendingUp className="h-5 w-5 text-green-200" />
          </div>
          <div className="text-3xl font-bold mb-1">{total}</div>
          <div className="text-green-100 text-sm font-medium">Total Flags</div>
        </div>
      </motion.div>

      {/* Active Flags */}
      <motion.div
        custom={1}
        initial="hidden"
        animate="visible"
        variants={cardVariants}
        className="relative overflow-hidden bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl shadow-xl p-6 text-white"
      >
        <div className="absolute top-2 right-2 w-16 h-16 bg-white/10 rounded-full blur-xl"></div>
        <div className="relative z-10">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
              <AlertTriangle className="h-6 w-6 text-white" />
            </div>
            <Activity className="h-5 w-5 text-orange-200" />
          </div>
          <div className="text-3xl font-bold mb-1">{active}</div>
          <div className="text-orange-100 text-sm font-medium">Active Flags</div>
        </div>
      </motion.div>

      {/* Resolved Flags */}
      <motion.div
        custom={2}
        initial="hidden"
        animate="visible"
        variants={cardVariants}
        className="relative overflow-hidden bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl shadow-xl p-6 text-white"
      >
        <div className="absolute top-2 right-2 w-16 h-16 bg-white/10 rounded-full blur-xl"></div>
        <div className="relative z-10">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
              <CheckCircle className="h-6 w-6 text-white" />
            </div>
            <Shield className="h-5 w-5 text-blue-200" />
          </div>
          <div className="text-3xl font-bold mb-1">{resolved}</div>
          <div className="text-blue-100 text-sm font-medium">Resolved</div>
        </div>
      </motion.div>

      {/* Under Review Flags */}
      <motion.div
        custom={3}
        initial="hidden"
        animate="visible"
        variants={cardVariants}
        className="relative overflow-hidden bg-gradient-to-br from-yellow-500 to-orange-500 rounded-2xl shadow-xl p-6 text-white"
      >
        <div className="absolute top-2 right-2 w-16 h-16 bg-white/10 rounded-full blur-xl"></div>
        <div className="relative z-10">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
              <Clock className="h-6 w-6 text-white" />
            </div>
            <Zap className="h-5 w-5 text-yellow-200" />
          </div>
          <div className="text-3xl font-bold mb-1">{underReview}</div>
          <div className="text-yellow-100 text-sm font-medium">Under Review</div>
        </div>
      </motion.div>
      </div>

      {/* Severity Breakdown */}
    <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border-2 border-blue-100 dark:border-gray-700 p-6 mb-8">
      <h3 className="text-lg font-bold text-gray-800 dark:text-white mb-4 flex items-center gap-2">
        <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
          <TrendingUp className="h-5 w-5 text-white" />
        </div>
        Severity Breakdown
      </h3>
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <div className="text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-xl border border-red-200 dark:border-red-800">
          <div className="text-2xl font-bold text-red-600 dark:text-red-400">{critical}</div>
          <div className="text-sm text-red-500 dark:text-red-400 font-medium">Critical</div>
        </div>
        <div className="text-center p-4 bg-orange-50 dark:bg-orange-900/20 rounded-xl border border-orange-200 dark:border-orange-800">
          <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">{high}</div>
          <div className="text-sm text-orange-500 dark:text-orange-400 font-medium">High</div>
        </div>
        <div className="text-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-xl border border-yellow-200 dark:border-yellow-800">
          <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{medium}</div>
          <div className="text-sm text-yellow-500 dark:text-yellow-400 font-medium">Medium</div>
        </div>
        <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-xl border border-green-200 dark:border-green-800">
          <div className="text-2xl font-bold text-green-600 dark:text-green-400">{low}</div>
          <div className="text-sm text-green-500 dark:text-green-400 font-medium">Low</div>
        </div>
        <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-xl border border-blue-200 dark:border-blue-800">
          <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{info}</div>
          <div className="text-sm text-blue-500 dark:text-blue-400 font-medium">Info</div>
        </div>
      </div>
    </div>
    </div>
  );
}