import { useState, useEffect } from "react";
import {
  FileText,
  Map,
  DollarSign,
  Search,
  ChevronDown,
  FileBarChart,
  CalendarDays,
  CheckCircle2,
  ArrowUpDown,
  Filter,
  Download
} from "lucide-react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardT<PERSON>le, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useGetCustomerSalesQuery } from "@/redux/slices/customersApiSlice";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";
import { Link } from "react-router-dom";

// Define interfaces for the component props and data structures
interface SalesTabProps {
  customerId: string;
}

interface LeadFileItem {
  lead_file_no: string;
  plot: string;
  project: string;
  purchase_price: string;
  selling_price: string;
  total_paid: string;
  balance_lcy: string;
  purchase_type: string;
  marketer: string;
  customer_name: string;
  customer_id: string;
  completion_date: string;
  booking_date: string;
  installment_amount: string;
  no_of_installments: number;
  sale_agreement_sent: string;
  sale_agreement_signed: string;
  lead_file_status_dropped: boolean;
  [key: string]: string | number | boolean | null | undefined;
}

interface SalesData {
  results: LeadFileItem[];
  count: number;
  next: string | null;
  previous: string | null;
}

interface SalesSummary {
  totalSales: number;
  totalValue: number;
  totalPaid: number;
  totalBalance: number;
  completedSales: number;
  activeSales: number;
  avgSaleValue: number;
}

interface FilterState {
  project: string;
  marketer: string;
  purchase_type: string;
  status: string;
}

const CustomerSalesTab = ({ customerId }: SalesTabProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [filters, setFilters] = useState<FilterState>({
    project: "",
    marketer: "",
    purchase_type: "",
    status: ""
  });
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10;

  useEffect(() => {
  const timer = setTimeout(() => {
    setDebouncedSearchTerm(searchTerm);
  }, 500); // 500ms delay

  return () => clearTimeout(timer);
}, [searchTerm]);

useEffect(() => {
  setCurrentPage(1);
}, [debouncedSearchTerm]);

useEffect(() => {
  setIsSearching(true);
  const timer = setTimeout(() => {
    setDebouncedSearchTerm(searchTerm);
    setIsSearching(false);
  }, 500);

  return () => {
    clearTimeout(timer);
    setIsSearching(false);
  };
}, [searchTerm]);

  // Construct query parameters for lead-files endpoint
  const queryParams = {
    customer_id__customer_no: customerId, 
    search: debouncedSearchTerm || undefined,
    project: filters.project || undefined,
    marketer: filters.marketer || undefined,
    purchase_type: filters.purchase_type || undefined,
    page: currentPage,
    page_size: pageSize
  };
  console.log("Query Params:", queryParams);
  console.log("Debounced Search Term:", debouncedSearchTerm); 
  console.log("Current Page:", currentPage);
  console.log("Filters:", filters);
  console.log("Is Searching:", isSearching);
  console.log("Customer ID:", customerId);
  // Fetch customer sales data using lead-files endpoint
  const {
    data: salesData,
    isLoading,
    isError,
    refetch
  } = useGetCustomerSalesQuery(queryParams);
  console.log("Sales Data:", salesData)
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };
  const handleNextPage = () => {
    if (salesData && salesData.next) {
      setCurrentPage(prev => prev + 1);
    }
  };

  const handlePreviousPage = () => {
    if (salesData && salesData.previous && currentPage > 1) {
      setCurrentPage(prev => prev - 1);
    }
  };

  // Compute summary statistics from lead-files API data
  const calculateSummary = (): SalesSummary => {
    if (!salesData || !salesData.results || salesData.results.length === 0) {
      return {
        totalSales: 0,
        totalValue: 0,
        totalPaid: 0,
        totalBalance: 0,
        completedSales: 0,
        activeSales: 0,
        avgSaleValue: 0
      };
    }

    const results = salesData.results;
    const completedSales = results.filter((sale: LeadFileItem) => parseFloat(sale.balance_lcy || "0") === 0).length;
    const activeSales = results.filter((sale: LeadFileItem) => parseFloat(sale.balance_lcy || "0") > 0).length;

    const totalPaid = results.reduce((acc: number, item: LeadFileItem) => acc + parseFloat(item.total_paid || "0"), 0);
    const totalValue = results.reduce((acc: number, item: LeadFileItem) => {
      const purchasePrice = parseFloat(item.purchase_price || "0");
      return acc + purchasePrice;
    }, 0);
    const totalBalance = results.reduce((acc: number, item: LeadFileItem) => acc + parseFloat(item.balance_lcy || "0"), 0);
    const avgSaleValue = results.length > 0 ? totalValue / results.length : 0;

    return {
      totalSales: results.length,
      totalValue,
      totalPaid,
      totalBalance,
      completedSales,
      activeSales,
      avgSaleValue
    };
  };

  const summary = calculateSummary();

  // Determine sale status based on balance and other factors
  const getSaleStatus = (sale: LeadFileItem): string => {
    if (sale.lead_file_status_dropped) return "Dropped";

    const balance = parseFloat(sale.balance_lcy || "0");
    if (balance === 0) return "Completed";
    if (balance > 0) return "Active";

    return "Pending";
  };

  const getStatusColor = (status: string): string => {
    const statusColors: Record<string, string> = {
      "Active": "bg-blue-50 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800",
      "Completed": "bg-green-50 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300 dark:border-green-800",
      "Dropped": "bg-red-50 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-300 dark:border-red-800",
      "Pending": "bg-yellow-50 text-yellow-800 border-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-300 dark:border-yellow-800"
    };

    return statusColors[status] || "bg-gray-50 text-gray-800 border-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-700";
  };

  // Extract unique values for filter dropdowns
  const getUniqueValues = (field: keyof LeadFileItem): string[] => {
    if (!salesData || !salesData.results) return [];
    const uniqueValues = new Set<string>(
      salesData.results
        .map((item: LeadFileItem) => item[field])
        .filter(Boolean)
        .map((value: unknown) => String(value))
    );
    return Array.from(uniqueValues);
  };

  // Format currency values
  const formatCurrency = (value: string | number): string => {
    const numValue = typeof value === 'string' ? parseFloat(value) : value;
    return isNaN(numValue) ? "0" : numValue.toLocaleString();
  };

  // Format date values
  const formatDate = (dateString: string | null): string => {
    if (!dateString) return "N/A";
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return "N/A";
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <SpinnerTemp type="spinner-double" size="lg" />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="p-4">
        <Alert variant="destructive">
          <AlertDescription>
            Error loading sales data. Please try again later.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Sales Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
        <Card className="border-0 shadow-sm bg-white dark:bg-gray-900">
          <CardContent className="p-4">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <p className="text-xs font-medium text-gray-500 dark:text-gray-400">Total Sales Value</p>
                <h3 className="text-lg font-bold mt-1 text-gray-900 dark:text-white">
                  KES {formatCurrency(summary.totalValue)}
                </h3>
                <div className="flex items-center mt-2 text-xs text-gray-500 dark:text-gray-400">
                  <span>{summary.totalSales} Sales</span>
                  <span className="mx-1">•</span>
                  <span>Avg: KES {formatCurrency(summary.avgSaleValue)}</span>
                </div>
              </div>
              <div className="bg-blue-100 p-2 rounded-lg dark:bg-blue-900/30 ml-3">
                <FileBarChart className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm bg-white dark:bg-gray-900">
          <CardContent className="p-4">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <p className="text-xs font-medium text-gray-500 dark:text-gray-400">Total Paid</p>
                <h3 className="text-lg font-bold mt-1 text-gray-900 dark:text-white">
                  KES {formatCurrency(summary.totalPaid)}
                </h3>
                <div className="flex items-center mt-2 text-xs text-gray-500 dark:text-gray-400">
                  <div className="flex items-center text-green-600 dark:text-green-400">
                    <CheckCircle2 className="h-3 w-3 mr-1" />
                    <span>{summary.completedSales} Completed</span>
                  </div>
                </div>
              </div>
              <div className="bg-green-100 p-2 rounded-lg dark:bg-green-900/30 ml-3">
                <DollarSign className="h-4 w-4 text-green-600 dark:text-green-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm bg-white dark:bg-gray-900">
          <CardContent className="p-4">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <p className="text-xs font-medium text-gray-500 dark:text-gray-400">Outstanding Balance</p>
                <h3 className="text-lg font-bold mt-1 text-gray-900 dark:text-white">
                  KES {formatCurrency(summary.totalBalance)}
                </h3>
                <div className="flex items-center mt-2 text-xs text-gray-500 dark:text-gray-400">
                  <span>From {summary.activeSales} active sales</span>
                </div>
              </div>
              <div className="bg-amber-100 p-2 rounded-lg dark:bg-amber-900/30 ml-3">
                <FileText className="h-4 w-4 text-amber-600 dark:text-amber-400" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Sales Table */}
      <Card className="border-0 shadow-sm bg-white dark:bg-gray-900">
        <CardHeader className="border-b bg-gray-50 dark:bg-gray-800/50 dark:border-gray-700 p-4">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <CardTitle className="text-lg font-medium">Sales Records</CardTitle>
            <div className="flex flex-wrap gap-2">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500 dark:text-gray-400" />
                <Input
                  type="search"
                  placeholder="Search records..."
                  className="pl-8 h-9 w-48 md:w-64 bg-secondary"
                  value={searchTerm}
                  onChange={handleSearchChange}
                />
                {isSearching && (
                  <div className="absolute right-2.5 top-2.5">
                    <div className="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
                  </div>
                )}
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="h-9 w-9 p-0 bg-secondary" 
                    title="Export"
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Export Options</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>
                    <FileText className="h-4 w-4 mr-2" />
                    Export as CSV
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <FileText className="h-4 w-4 mr-2" />
                    Export as PDF
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <FileText className="h-4 w-4 mr-2" />
                    Print Records
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="bg-secondary dark:bg-gray-800/50">
                  <TableHead className="font-medium">
                    <div className="flex items-center">
                      Lead File No.
                      <ArrowUpDown className="ml-1 h-4 w-4" />
                    </div>
                  </TableHead>
                  <TableHead className="font-medium">
                    <div className="flex items-center">
                      Plot Number
                      <ArrowUpDown className="ml-1 h-4 w-4" />
                    </div>
                  </TableHead>
                  <TableHead className="font-medium text-right">Purchase Price</TableHead>
                  <TableHead className="font-medium text-right">Total Paid</TableHead>
                  <TableHead className="font-medium text-right">Balance</TableHead>
                  <TableHead className="font-medium">Status</TableHead>
                  <TableHead className="font-medium">Purchase Type</TableHead>
                  <TableHead className="font-medium">Marketer</TableHead>
                  <TableHead className="sr-only">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {salesData && salesData.results && salesData.results.length > 0 ? (
                  salesData.results.map((sale: LeadFileItem) => {
                    const status = getSaleStatus(sale);
                    return (
                      <TableRow key={sale.lead_file_no} className="hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
                        <TableCell className="font-medium text-blue-600 dark:text-blue-400">
                          <Link
                            to={`/sales/sales-card/${sale.lead_file_no}`}
                            className="block font-bold text-blue-700 underline"
                          >
                            {sale.lead_file_no}
                          </Link>
                        </TableCell>
                        <TableCell>{sale.plot}</TableCell>
                        <TableCell className="text-right font-medium">
                          KES {formatCurrency(sale.purchase_price)}
                        </TableCell>
                        <TableCell className="text-right font-medium">
                          KES {formatCurrency(sale.total_paid)}
                        </TableCell>
                        <TableCell className="text-right">
                          {parseFloat(sale.balance_lcy || "0") === 0 ? (
                            <span className="text-green-600 dark:text-green-400 font-medium">Paid</span>
                          ) : (
                            `KES ${formatCurrency(sale.balance_lcy)}`
                          )}
                        </TableCell>
                        <TableCell>
                          <Badge className={`${getStatusColor(status)}`}>
                            {status}
                          </Badge>
                        </TableCell>
                        <TableCell>{sale.purchase_type}</TableCell>
                        <TableCell>{sale.marketer}</TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <ChevronDown className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem>View Details</DropdownMenuItem>
                              <DropdownMenuItem>Record Payment</DropdownMenuItem>
                              <DropdownMenuItem>View Payment History</DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>Generate Statement</DropdownMenuItem>
                              <DropdownMenuItem>View Installments</DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    );
                  })
                ) : (
                  <TableRow>
                    <TableCell colSpan={10} className="h-24 text-center">
                      {isLoading ? "Loading sales records..." : "No sales records found for this customer"}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
        <CardFooter className="flex items-center justify-between border-t p-4 bg-gray-50 dark:bg-gray-800/50 dark:border-gray-700">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Showing <span className="font-medium">{salesData?.results?.length || 0}</span> of{" "}
            <span className="font-medium">{salesData?.total_data || 0}</span> records
          </div>
          <div className="flex gap-1">
            <Button
              variant="outline"
              size="sm"
              disabled={!salesData?.previous}
              onClick={handlePreviousPage}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              disabled={!salesData?.next}
              onClick={handleNextPage}
            >
              Next
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};

export default CustomerSalesTab;