import React, { useState } from 'react';
import { Screen } from '@/app-components/layout/screen';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useSalesPermissions } from '@/hooks/useSalesPermissions';
import { useCustomerPermissions } from '@/hooks/useCustomerPermissions';
import { useProspectPermissions } from '@/hooks/useProspectPermissions';
import { useLogisticsPermissions } from '@/hooks/useLogisticsPermissions';
import { useAuthHook } from '@/utils/useAuthHook';
import { getSalesApiParams } from '@/utils/salesPermissions';
import { getCustomerApiParams } from '@/utils/customerPermissions';
import { getProspectApiParams } from '@/utils/prospectPermissions';

/**
 * Test component to demonstrate how the new permissions system works
 * Shows how each permission type applies a single filter parameter
 */
const PermissionsFilterTest: React.FC = () => {
  const { user_details } = useAuthHook();
  const [activeTab, setActiveTab] = useState('sales');
  
  // Load all permission hooks
  const salesPermissions = useSalesPermissions();
  const customerPermissions = useCustomerPermissions();
  const prospectPermissions = useProspectPermissions();
  const logisticsPermissions = useLogisticsPermissions();

  // Generate API parameters based on user permissions
  const salesParams = getSalesApiParams(user_details);
  const customerParams = getCustomerApiParams(user_details);
  const prospectParams = getProspectApiParams(user_details);

  return (
    <Screen>
      <div className="container mx-auto p-6 space-y-6">
        <div className="prose dark:prose-invert max-w-none">
          <h1>Permissions Filter Test</h1>
          <p>
            This page demonstrates how the new permissions system works for sales, customers, and prospects.
            Each permission type now applies a single filter parameter, similar to how logistics permissions work.
          </p>
        </div>

        <Tabs defaultValue="sales" onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-4 w-full max-w-md">
            <TabsTrigger value="sales">Sales</TabsTrigger>
            <TabsTrigger value="customers">Customers</TabsTrigger>
            <TabsTrigger value="prospects">Prospects</TabsTrigger>
            <TabsTrigger value="logistics">Logistics</TabsTrigger>
          </TabsList>

          {/* Sales Permissions Tab */}
          <TabsContent value="sales" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Sales Permissions</CardTitle>
                <CardDescription>
                  Current permission level: {salesPermissions.permissionLevel}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-lg font-medium">API Parameters</h3>
                  <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-md mt-2">
                    <pre className="text-sm overflow-auto">
                      {JSON.stringify(salesParams, null, 2)}
                    </pre>
                  </div>
                  <p className="text-sm text-gray-500 mt-2">
                    Note: Only one filter parameter is applied based on permission priority.
                  </p>
                </div>

                <div>
                  <h3 className="text-lg font-medium">Active Permissions</h3>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {salesPermissions.userPermissions.length > 0 ? (
                      salesPermissions.userPermissions.map((perm, index) => (
                        <Badge key={index} variant="outline">
                          {perm}
                        </Badge>
                      ))
                    ) : (
                      <span className="text-red-600">No sales permissions</span>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Customers Permissions Tab */}
          <TabsContent value="customers" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Customer Permissions</CardTitle>
                <CardDescription>
                  Current permission level: {customerPermissions.permissionLevel}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-lg font-medium">API Parameters</h3>
                  <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-md mt-2">
                    <pre className="text-sm overflow-auto">
                      {JSON.stringify(customerParams, null, 2)}
                    </pre>
                  </div>
                  <p className="text-sm text-gray-500 mt-2">
                    Note: Only one filter parameter is applied based on permission priority.
                  </p>
                </div>

                <div>
                  <h3 className="text-lg font-medium">Active Permissions</h3>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {customerPermissions.userPermissions.length > 0 ? (
                      customerPermissions.userPermissions.map((perm, index) => (
                        <Badge key={index} variant="outline">
                          {perm}
                        </Badge>
                      ))
                    ) : (
                      <span className="text-red-600">No customer permissions</span>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Prospects Permissions Tab */}
          <TabsContent value="prospects" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Prospect Permissions</CardTitle>
                <CardDescription>
                  Current permission level: {prospectPermissions.permissionLevel}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-lg font-medium">API Parameters</h3>
                  <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-md mt-2">
                    <pre className="text-sm overflow-auto">
                      {JSON.stringify(prospectParams, null, 2)}
                    </pre>
                  </div>
                  <p className="text-sm text-gray-500 mt-2">
                    Note: Only one filter parameter is applied based on permission priority.
                  </p>
                </div>

                <div>
                  <h3 className="text-lg font-medium">Active Permissions</h3>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {prospectPermissions.userPermissions.length > 0 ? (
                      prospectPermissions.userPermissions.map((perm, index) => (
                        <Badge key={index} variant="outline">
                          {perm}
                        </Badge>
                      ))
                    ) : (
                      <span className="text-red-600">No prospect permissions</span>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Logistics Permissions Tab */}
          <TabsContent value="logistics" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Logistics Permissions</CardTitle>
                <CardDescription>
                  Reference implementation that the other modules now follow
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-lg font-medium">Available Permissions</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-2">
                    {Object.entries(logisticsPermissions.LOGISTICS_PERMISSIONS).map(([key, code]) => {
                      const hasPermission = logisticsPermissions.userPermissionCodes.includes(code);
                      return (
                        <div key={key} className={`p-2 rounded ${hasPermission ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'}`}>
                          <span>{key} ({code})</span>
                          <span className="float-right">{hasPermission ? '✓' : '✗'}</span>
                        </div>
                      );
                    })}
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium">Active Permissions</h3>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {logisticsPermissions.userLogisticsPermissions.length > 0 ? (
                      logisticsPermissions.userLogisticsPermissions.map((perm, index) => (
                        <Badge key={index} variant="outline">
                          {perm}
                        </Badge>
                      ))
                    ) : (
                      <span className="text-red-600">No logistics permissions</span>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <Card>
          <CardHeader>
            <CardTitle>Permission System Explanation</CardTitle>
          </CardHeader>
          <CardContent className="prose dark:prose-invert max-w-none">
            <h3>How the New Permission System Works</h3>
            <p>
              The sales, customer, and prospect permission systems have been refactored to work like the logistics permissions:
            </p>
            <ul>
              <li>Each permission now applies a <strong>single filter parameter</strong> to the API request</li>
              <li>If multiple permissions are present, only the highest priority one is used</li>
              <li>Priority order: All access &gt; Office &gt; Team &gt; Marketer &gt; Region</li>
              <li>This prevents conflicting filters that could cancel each other out</li>
            </ul>
            
            <h3>Benefits</h3>
            <ul>
              <li>Simplified permission logic</li>
              <li>Predictable filtering behavior</li>
              <li>Consistent implementation across all modules</li>
              <li>Easier to maintain and extend</li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </Screen>
  );
};

export default PermissionsFilterTest;