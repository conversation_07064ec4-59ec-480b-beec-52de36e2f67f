"use client"
import React from "react";
import { Screen } from "@/app-components/layout/screen";
import SpinnerTemp from "./SpinnerTemp";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { SpinnerType } from "./SpinnerTemp";
// type Props = {
//     isLoading?: boolean
// }


const Spinner: React.FC = ({ }) => {
    const [isLoading, setIsLoading] = React.useState(false);


    const codeSnippetMaker = (type: SpinnerType["type"], inHouse: boolean) => {
        const code = `return (
        <>
        <SpinnerTemp 
        type="${type}"
         size="md"
         speed={1} />
        ...
        </>
        );`;

        const codeInHouse = `return (
            <>
            <SpinnerTemp 
            type="${type}"
             size="md" />
            ...
            </>
            );`;

        if (inHouse) {
            return codeInHouse;
        }
        return code;


        return code;

    }
    return (
        <Screen >
            <div className="demonstrations bg-inherit h-full w-full bg-slate-50 flex flex-col gap-6 p-4 relative">
                <h1 className="absolute top-4 text-lg font-semibold">Spinner Templates Jibambez</h1>
                <div className="absolute right-0">
                <Label className=" my-auto pr-5">Toggle Loaders</Label>
                <Switch className="my-auto"></Switch>
                </div>
                {/* Layer 1 */}
                <div className="layer-1 bg-inherit flex flex-wrap justify-between items-center gap-6 mt-20">
                    
                    <div className="opposing-spinner rounded-lg px-6 py-4 bg-slate-100 w-full sm:w-[48%] lg:w-[30%]">
                        <p className="text-sm font-medium">Opposing Double Spinner</p>
                        <div className="flex h-[20vh] justify-center items-center rounded-md">
                            <SpinnerTemp type="opposing-double-spinner" size="md" />
                        </div>
                        <div className="code tutorial h-fit ">
                            <p>Usage and syntax</p>
                            <pre className="text-sm text-white bg-black p-2 rounded-md mt-2 overflow-x-auto">
                                <code>
                                    {codeSnippetMaker("opposing-double-spinner", true)}
                                </code>
                            </pre>

                        </div>
                    </div>
                    <div className="opposing-double-spinner rounded-lg px-6 py-4 bg-slate-100 w-full sm:w-[48%] lg:w-[30%]">
                        <p className="text-sm font-medium">Opposing Spinner</p>
                        <div className="flex h-[20vh] justify-center items-center rounded-md">
                            <SpinnerTemp />
                        </div>
                        <div className="code tutorial h-fit ">
                            <p>Usage and syntax</p>
                            <pre className="text-sm text-white bg-black p-2 rounded-md mt-2 overflow-x-auto">
                                <code>
                                    {codeSnippetMaker("opposing-spinner", true)}
                                </code>
                            </pre>

                        </div>
                    </div>
                </div>

                {/* Layer 2 */}
                <div className="layer-2 flex flex-wrap justify-between items-center gap-6 mt-8">
                    <div className="opposing-double-spinner rounded-lg px-6 py-4 bg-slate-100 w-full sm:w-[48%] lg:w-[30%]">
                        <p className="text-sm font-medium">Spinner Wheel</p>
                        <div className="flex h-[20vh] justify-center items-center rounded-md">
                            <SpinnerTemp type="spinner-wheel" />
                        </div>
                        <div className="code tutorial h-fit ">
                            <p>Usage and syntax</p>
                            <pre className="text-sm text-white bg-black p-2 rounded-md mt-2 overflow-x-auto">
                                <code>
                                    {codeSnippetMaker("spinner-wheel", true)}
                                </code>
                            </pre>

                        </div>
                    </div>
                    <div className="opposing-double-spinner rounded-lg px-6 py-4 bg-slate-100 w-full sm:w-[48%] lg:w-[30%]">
                        <p className="text-sm font-medium">Double Spinner</p>
                        <div className="flex h-[20vh] justify-center items-center rounded-md">
                            <SpinnerTemp type="spinner-double" />
                        </div>
                        <div className="code tutorial h-fit ">
                            <p>Usage and syntax</p>
                            <pre className="text-sm text-white bg-black p-2 rounded-md mt-2 overflow-x-auto">
                                <code>
                                    {codeSnippetMaker("spinner-double", true)}
                                </code>
                            </pre>

                        </div>
                    </div>
                    <div className="opposing-double-spinner rounded-lg px-6 py-4 bg-slate-100 w-full sm:w-[48%] lg:w-[30%]">
                        <p className="text-sm font-medium">Tail Spinner</p>
                        <div className="flex h-[20vh] justify-center items-center rounded-md">
                            <SpinnerTemp type="tail-spinner" />
                        </div>
                        <div className="code tutorial h-fit ">
                            <p>Usage and syntax</p>
                            <pre className="text-sm text-white bg-black p-2 rounded-md mt-2 overflow-x-auto">
                                <code>
                                    {codeSnippetMaker("tail-spinner", true)}
                                </code>
                            </pre>

                        </div>
                    </div>
                </div>

                {/* Layer 3 */}
                <div className="layer-3 flex flex-wrap justify-between items-center gap-6 mt-8">
                    <div className="opposing-double-spinner rounded-lg px-6 py-4 bg-slate-100 w-full sm:w-[48%] lg:w-[30%]">
                        <p className="text-sm font-medium">Shape Box</p>
                        <div className="flex h-[20vh] justify-center items-center rounded-md">
                            <SpinnerTemp type="shape-box" />
                        </div>
                        <div className="code tutorial h-fit ">
                            <p>Usage and syntax</p>
                            <pre className="text-sm text-white bg-black p-2 rounded-md mt-2 overflow-x-auto">
                                <code>
                                    {codeSnippetMaker("shape-box", true)}
                                </code>
                            </pre>

                        </div>
                    </div>
                    <div className="opposing-double-spinner rounded-lg px-6 py-4 bg-slate-100 w-full sm:w-[48%] lg:w-[30%]">
                        <p className="text-sm font-medium">Clones Box</p>
                        <div className="flex h-[20vh] justify-center items-center rounded-md">
                            <SpinnerTemp type="clones-box" />
                        </div>
                        <div className="code tutorial h-fit ">
                            <p>Usage and syntax</p>
                            <pre className="text-sm text-white bg-black p-2 rounded-md mt-2 overflow-x-auto">
                                <code>
                                    {codeSnippetMaker("clones-box", true)}
                                </code>
                            </pre>

                        </div>
                    </div>
                    <div className="opposing-double-spinner rounded-lg px-6 py-4 bg-slate-100 w-full sm:w-[48%] lg:w-[30%]">
                        <p className="text-sm font-medium">Classic</p>
                        <div className="flex h-[20vh] justify-center items-center rounded-md">
                            <SpinnerTemp type="classic" />
                        </div>
                        <div className="code tutorial h-fit ">
                            <p>Usage and syntax</p>
                            <pre className="text-sm text-white bg-black p-2 rounded-md mt-2 overflow-x-auto">
                                <code>
                                    {codeSnippetMaker("classic", true)}
                                </code>
                            </pre>

                        </div>
                    </div>
                </div>
            </div>


        </Screen>
    );

}
export default Spinner