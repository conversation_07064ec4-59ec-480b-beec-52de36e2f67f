import { contentHeader, noAuthHeader, noContentHeader } from "@/utils/header";
import { apiSlice } from "../apiSlice";

export const customerTicketsApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Customer Tickets CRUD operations
    getCustomerTickets: builder.query({
      query: (params) => ({
        url: "/tickets/",
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      transformResponse: (response) => {
        console.log("Customer Tickets API Response:", response);
        return {
          results: response.data?.results || [],
          count: response.data?.total_data || 0,
          current_page: response.data?.current_page || 1,
          last_page: response.data?.last_page || 1,
          total_data: response.data?.total_data || 0,
          per_page: response.data?.per_page || 20,
        };
      },
      providesTags: ["Tickets"],
    }),

    getCustomerTicketDetails: builder.query({
      query: (id) => ({
        url: `/tickets/${id}/`,
        method: "GET",
        headers: noAuthHeader(),
      }),
      providesTags: ["Tickets"],
    }),

    createCustomerTicket: builder.mutation({
      query: (data) => ({
        url: "/tickets/",
        method: "POST",
        body: data,
        headers: noContentHeader(), // Use noContentHeader for FormData uploads
      }),
      invalidatesTags: ["Tickets"],
    }),

    updateCustomerTicket: builder.mutation({
      query: ({ ticketId, data }) => ({
        url: `/tickets/${ticketId}/`,
        method: "PATCH",
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Tickets"],
    }),

    deleteCustomerTicket: builder.mutation({
      query: (ticketId) => ({
        url: `/tickets/${ticketId}/`,
        method: "DELETE",
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Tickets"],
    }),

    // Customer Ticket Categories
    getCustomerTicketCategories: builder.query({
      query: (params) => ({
        url: "/tickets-categories/",
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      transformResponse: (response) => {
        return {
          results: response.data?.results || [],
          count: response.data?.total_data || 0,
          current_page: response.data?.current_page || 1,
          last_page: response.data?.last_page || 1,
          total_data: response.data?.total_data || 0,
          per_page: response.data?.per_page || 20,
        };
      },
      providesTags: ["Tickets"],
    }),

    // Customer Ticket Sources
    getCustomerTicketSources: builder.query({
      query: (params) => ({
        url: "/tickets-sources/",
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      transformResponse: (response) => {
        return {
          results: response.data?.results || [],
          count: response.data?.total_data || 0,
          current_page: response.data?.current_page || 1,
          last_page: response.data?.last_page || 1,
          total_data: response.data?.total_data || 0,
          per_page: response.data?.per_page || 20,
        };
      },
      providesTags: ["Tickets"],
    }),

    // Customer Ticket Messages
    getCustomerTicketMessages: builder.query({
      query: (params) => ({
        url: "/tickets-messages/",
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      transformResponse: (response) => {
        return {
          results: response.data?.results || [],
          count: response.data?.total_data || 0,
          current_page: response.data?.current_page || 1,
          last_page: response.data?.last_page || 1,
          total_data: response.data?.total_data || 0,
          per_page: response.data?.per_page || 20,
        };
      },
      providesTags: ["Tickets"],
    }),

    createCustomerTicketMessage: builder.mutation({
      query: (data) => ({
        url: "/tickets-messages/",
        method: "POST",
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Tickets"],
    }),

    updateCustomerTicketMessage: builder.mutation({
      query: ({ messageId, data }) => ({
        url: `/tickets-messages/${messageId}/`,
        method: "PATCH",
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Tickets"],
    }),

    deleteCustomerTicketMessage: builder.mutation({
      query: (messageId) => ({
        url: `/tickets-messages/${messageId}/`,
        method: "DELETE",
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Tickets"],
    }),

    // Customer Ticket Attachments
    getCustomerTicketAttachments: builder.query({
      query: (params) => ({
        url: "/tickets-attachments/",
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      transformResponse: (response) => {
        return {
          results: response.data?.results || [],
          count: response.data?.total_data || 0,
          current_page: response.data?.current_page || 1,
          last_page: response.data?.last_page || 1,
          total_data: response.data?.total_data || 0,
          per_page: response.data?.per_page || 20,
        };
      },
      providesTags: ["Tickets"],
    }),

    createCustomerTicketAttachment: builder.mutation({
      query: (data) => ({
        url: "/tickets-attachments/",
        method: "POST",
        body: data,
        headers: noContentHeader(), // For file uploads
      }),
      invalidatesTags: ["Tickets"],
    }),

    deleteCustomerTicketAttachment: builder.mutation({
      query: (attachmentId) => ({
        url: `/tickets-attachments/${attachmentId}/`,
        method: "DELETE",
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Tickets"],
    }),

    // Customer Ticket Action Logs
    getCustomerTicketActionLogs: builder.query({
      query: (params) => ({
        url: "/tickets-action-logs/",
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      transformResponse: (response) => {
        return {
          results: response.data?.results || [],
          count: response.data?.total_data || 0,
          current_page: response.data?.current_page || 1,
          last_page: response.data?.last_page || 1,
          total_data: response.data?.total_data || 0,
          per_page: response.data?.per_page || 20,
        };
      },
      providesTags: ["Tickets"],
    }),

    createCustomerTicketActionLog: builder.mutation({
      query: (data) => ({
        url: "/tickets-action-logs/",
        method: "POST",
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Tickets"],
    }),

    updateCustomerTicketActionLog: builder.mutation({
      query: ({ actionLogId, data }) => ({
        url: `/tickets-action-logs/${actionLogId}/`,
        method: "PATCH",
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Tickets"],
    }),

    deleteCustomerTicketActionLog: builder.mutation({
      query: (actionLogId) => ({
        url: `/tickets-action-logs/${actionLogId}/`,
        method: "DELETE",
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Tickets"],
    }),

    // Customer Ticket Categories CRUD
    createCustomerTicketCategory: builder.mutation({
      query: (data) => ({
        url: "/tickets-categories/",
        method: "POST",
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Tickets"],
    }),

    updateCustomerTicketCategory: builder.mutation({
      query: ({ categoryId, data }) => ({
        url: `/tickets-categories/${categoryId}/`,
        method: "PATCH",
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Tickets"],
    }),

    deleteCustomerTicketCategory: builder.mutation({
      query: (categoryId) => ({
        url: `/tickets-categories/${categoryId}/`,
        method: "DELETE",
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Tickets"],
    }),

    // Customer Ticket Sources CRUD
    createCustomerTicketSource: builder.mutation({
      query: (data) => ({
        url: "/tickets-sources/",
        method: "POST",
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Tickets"],
    }),

    updateCustomerTicketSource: builder.mutation({
      query: ({ sourceId, data }) => ({
        url: `/tickets-sources/${sourceId}/`,
        method: "PATCH",
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Tickets"],
    }),

    deleteCustomerTicketSource: builder.mutation({
      query: (sourceId) => ({
        url: `/tickets-sources/${sourceId}/`,
        method: "DELETE",
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Tickets"],
    }),

    // Get individual ticket attachment details
    getCustomerTicketAttachmentDetails: builder.query({
      query: (id) => ({
        url: `/tickets-attachments/${id}/`,
        method: "GET",
        headers: noAuthHeader(),
      }),
      providesTags: ["Tickets"],
    }),

    updateCustomerTicketAttachment: builder.mutation({
      query: ({ attachmentId, data }) => ({
        url: `/tickets-attachments/${attachmentId}/`,
        method: "PATCH",
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Tickets"],
    }),

    // Get individual action log details
    getCustomerTicketActionLogDetails: builder.query({
      query: (id) => ({
        url: `/tickets-action-logs/${id}/`,
        method: "GET",
        headers: noAuthHeader(),
      }),
      providesTags: ["Tickets"],
    }),

    // Get individual message details
    getCustomerTicketMessageDetails: builder.query({
      query: (id) => ({
        url: `/tickets-messages/${id}/`,
        method: "GET",
        headers: noAuthHeader(),
      }),
      providesTags: ["Tickets"],
    }),

    // Get individual category details
    getCustomerTicketCategoryDetails: builder.query({
      query: (id) => ({
        url: `/tickets-categories/${id}/`,
        method: "GET",
        headers: noAuthHeader(),
      }),
      providesTags: ["Tickets"],
    }),

    // Get individual source details
    getCustomerTicketSourceDetails: builder.query({
      query: (id) => ({
        url: `/tickets-sources/${id}/`,
        method: "GET",
        headers: noAuthHeader(),
      }),
      providesTags: ["Tickets"],
    }),
  }),
});

export const {
  // Customer Tickets
  useGetCustomerTicketsQuery,
  useGetCustomerTicketDetailsQuery,
  useCreateCustomerTicketMutation,
  useUpdateCustomerTicketMutation,
  useDeleteCustomerTicketMutation,

  // Customer Ticket Categories
  useGetCustomerTicketCategoriesQuery,
  useGetCustomerTicketCategoryDetailsQuery,
  useCreateCustomerTicketCategoryMutation,
  useUpdateCustomerTicketCategoryMutation,
  useDeleteCustomerTicketCategoryMutation,

  // Customer Ticket Sources
  useGetCustomerTicketSourcesQuery,
  useGetCustomerTicketSourceDetailsQuery,
  useCreateCustomerTicketSourceMutation,
  useUpdateCustomerTicketSourceMutation,
  useDeleteCustomerTicketSourceMutation,

  // Customer Ticket Messages
  useGetCustomerTicketMessagesQuery,
  useGetCustomerTicketMessageDetailsQuery,
  useCreateCustomerTicketMessageMutation,
  useUpdateCustomerTicketMessageMutation,
  useDeleteCustomerTicketMessageMutation,

  // Customer Ticket Attachments
  useGetCustomerTicketAttachmentsQuery,
  useGetCustomerTicketAttachmentDetailsQuery,
  useCreateCustomerTicketAttachmentMutation,
  useUpdateCustomerTicketAttachmentMutation,
  useDeleteCustomerTicketAttachmentMutation,

  // Customer Ticket Action Logs
  useGetCustomerTicketActionLogsQuery,
  useGetCustomerTicketActionLogDetailsQuery,
  useCreateCustomerTicketActionLogMutation,
  useUpdateCustomerTicketActionLogMutation,
  useDeleteCustomerTicketActionLogMutation,
} = customerTicketsApiSlice;
