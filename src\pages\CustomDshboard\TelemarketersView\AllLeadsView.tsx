import { SetStateAction, useState } from "react";
import BaseModal from "@/components/custom/modals/BaseModal";
import { ColumnDef } from "@tanstack/react-table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Settings } from "lucide-react";
import { DataTable } from "@/components/custom/tables/Table1";

import { PrimaryButton } from "@/components/custom/buttons/buttons";
import AddLeadModal from "./AddLeadModal";

interface Leads {
  name: string;
  phonenumber: string;
  allocatedmarketer: string | null;
  Telemarker: string | null;
}

interface ProspectsTableModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function ProspectsTableModal({ open, onOpenChange }: ProspectsTableModalProps) {
  const [activeTab, setActiveTab] = useState<"allocated" | "unallocated">("allocated");
  const[isAddLeadModalOpen, setIsAddLeadModalOpen] = useState(false); 

  const data: Leads[] = [
    {
      name: "Ken Maish",
      phonenumber: "0712345678",
      allocatedmarketer: "Jane Doe",
      Telemarker: "John Smith",
    },
    {
      name: "Jane Wanjiru",
      phonenumber: "0723456789",
      allocatedmarketer: null,
      Telemarker: null,
    },
    {
      name: "John Kamau",
      phonenumber: "0734567890",
      allocatedmarketer: "Alice Doe",
      Telemarker: "Bob Smith",
    },
  ];

  const filteredData =
    activeTab === "allocated"
      ? data.filter((lead) => lead.allocatedmarketer !== null && lead.allocatedmarketer !== "")
      : data.filter((lead) => lead.allocatedmarketer === null || lead.allocatedmarketer === "");

  const columns: ColumnDef<Leads>[] = [
    {
      accessorKey: "name",
      header: "Name",
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: "phonenumber",
      header: "Phone Number",
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: "allocatedmarketer",
      header: "Allocated Marketer",
      cell: (info) => info.getValue() || "Unallocated",
    },
    {
      accessorKey: "Telemarker",
      header: "Telemarker",
      cell: (info) => info.getValue() || "Unallocated",
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger>
            <Settings size={20} />
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                console.log(`Edit row ${row.original.name}`);
              }}
            >
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                console.log(`Delete row ${row.original.name}`);
              }}
            >
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
    },
  ];

  return (
    <BaseModal
      open={open}
      onOpenChange={onOpenChange}
      title="Leads Management"
      description="View and manage allocated and unallocated leads"
      className="max-w-4xl"
      size="full"
    >
        <div className="flex justify-end mb-4">
            <PrimaryButton onClick={() => setIsAddLeadModalOpen(true)}>
                Add New Lead
            </PrimaryButton>
        </div>
      <div className="p-4 bg-white dark:bg-gray-900 rounded-md">
        <div className="flex space-x-4 mb-4">
          <button
            className={`px-4 py-2 rounded-md ${
              activeTab === "allocated" ? "bg-blue-500 text-white" : "bg-gray-200 text-gray-700"
            }`}
            onClick={() => setActiveTab("allocated")}
          >
            Allocated Leads
          </button>
          <button
            className={`px-4 py-2 rounded-md ${
              activeTab === "unallocated" ? "bg-blue-500 text-white" : "bg-gray-200 text-gray-700"
            }`}
            onClick={() => setActiveTab("unallocated")}
          >
            Unallocated Leads
          </button>
        </div>

        {filteredData.length === 0 ? (
          <div className="text-center text-gray-500 dark:text-gray-400 py-4">
            No {activeTab === "allocated" ? "allocated" : "unallocated"} leads found.
          </div>
        ) : (
          <DataTable<Leads>
            data={filteredData}
            columns={columns}
            title={activeTab === "allocated" ? "Allocated Leads" : "Unallocated Leads"}
            enableExportToExcel={true}
            enablePrintPdf={true}
            enableColumnFilters={true}
            enablePagination={true}
            enableSorting={true}
            enableToolbar={true}
            containerClassName="max-w-full"
            tableClassName="w-full border-collapse border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 text-sm text-left text-gray-700 dark:text-gray-300 shadow-md rounded-lg overflow-hidden"
            tHeadClassName="bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 font-semibold"
            tHeadCellsClassName="px-4 py-2 border-b border-gray-200 dark:border-gray-700"
            tBodyClassName="divide-y divide-gray-200 dark:divide-gray-700"
            tBodyTrClassName="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            tBodyCellsClassName="px-4 py-2"
          />
        )}
        <AddLeadModal
            isOpen={isAddLeadModalOpen}
            onOpenChange={setIsAddLeadModalOpen}
            
            />
                 
      </div>
    </BaseModal>
  );
}