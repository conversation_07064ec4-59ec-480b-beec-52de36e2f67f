import React from 'react';

interface NextOfKinFormProps {
  formData: any;
  setFormData: (data: any) => void;
  onNext: () => void;
  onBack: () => void;
}

const NextOfKinForm: React.FC<NextOfKinFormProps> = ({
  formData,
  setFormData,
  onNext,
  onBack,
}) => {
  const handleInputChange = (field: string, value: string) => {
    setFormData({ ...formData, [field]: value });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onNext();
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold text-gray-800 mb-2">Enter Next of Kin Details</h2>
        <p className="text-gray-600 mb-6">Please provide the details of your next of kin.</p>
      </div>

      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium text-gray-700 mb-4">
            1. Next of Kin Information <span className="text-gray-500 text-sm">- (All Fields are required)</span>
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Full Names (as per ID)
              </label>
              <input
                type="text"
                placeholder="Jane Doe"
                value={formData.nextOfKinName || ''}
                onChange={(e) => handleInputChange('nextOfKinName', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Relationship
              </label>
              <input
                type="text"
                placeholder="Mother"
                value={formData.nextOfKinRelationship || ''}
                onChange={(e) => handleInputChange('nextOfKinRelationship', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Enter Valid Email Address
              </label>
              <input
                type="email"
                placeholder="<EMAIL>"
                value={formData.nextOfKinEmail || ''}
                onChange={(e) => handleInputChange('nextOfKinEmail', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Enter Valid Phone Number
              </label>
              <div className="flex">
                <select className="px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                  <option>🇰🇪</option>
                </select>
                <input
                  type="tel"
                  placeholder="+254 719 116 112"
                  value={formData.nextOfKinPhone || ''}
                  onChange={(e) => handleInputChange('nextOfKinPhone', e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-r-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-between pt-6">
        <button
          type="button"
          onClick={onBack}
          className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
        >
          ← Go Back
        </button>
        <button
          type="submit"
          className="px-6 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors"
        >
          Next Page →
        </button>
      </div>
    </form>
  );
};

export default NextOfKinForm;