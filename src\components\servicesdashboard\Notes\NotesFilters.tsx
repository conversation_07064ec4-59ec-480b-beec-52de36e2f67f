import { useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import {
  Search,
  Filter,
  X,
  Calendar as CalendarIcon,
  Download,
  RefreshCw,
  SlidersHorizontal,
} from "lucide-react";
import { NotesFilters, NOTE_TYPES } from "@/types/notes";
import { format } from "date-fns";

interface NotesFiltersProps {
  filters: NotesFilters;
  onFilterChange: (key: keyof NotesFilters, value: any) => void;
  onRefresh: () => void;
  onExport?: () => void;
  showAdvanced?: boolean;
}

export default function NotesFiltersComponent({
  filters,
  onFilterChange,
  onRefresh,
  onExport,
  showAdvanced = false,
}: NotesFiltersProps) {
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(showAdvanced);
  const [dateFrom, setDateFrom] = useState<Date>();
  const [dateTo, setDateTo] = useState<Date>();

  const handleDateFromChange = (date: Date | undefined) => {
    setDateFrom(date);
    onFilterChange("date_from", date ? format(date, "yyyy-MM-dd") : "");
  };

  const handleDateToChange = (date: Date | undefined) => {
    setDateTo(date);
    onFilterChange("date_to", date ? format(date, "yyyy-MM-dd") : "");
  };

  const clearFilters = () => {
    onFilterChange("search", "");
    onFilterChange("note_type", "");
    onFilterChange("priority", "");
    onFilterChange("status", "");
    onFilterChange("is_private", undefined);
    onFilterChange("is_pinned", undefined);
    onFilterChange("date_from", "");
    onFilterChange("date_to", "");
    setDateFrom(undefined);
    setDateTo(undefined);
  };

  const activeFiltersCount = Object.values(filters).filter(value => 
    value !== "" && value !== undefined && value !== null
  ).length;

  return (
    <Card className="shadow-lg">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Filter className="w-5 h-5" />
            <span>Filters & Search</span>
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="ml-2">
                {activeFiltersCount} active
              </Badge>
            )}
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
            >
              <SlidersHorizontal className="w-4 h-4 mr-1" />
              Advanced
            </Button>
            {activeFiltersCount > 0 && (
              <Button variant="outline" size="sm" onClick={clearFilters}>
                <X className="w-4 h-4 mr-1" />
                Clear
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Basic Filters */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Search</label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search notes..."
                value={filters.search || ""}
                onChange={(e) => onFilterChange("search", e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">Note Type</label>
            <Select value={filters.note_type || ""} onValueChange={(value) => onFilterChange("note_type", value)}>
              <SelectTrigger>
                <SelectValue placeholder="All types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All types</SelectItem>
                {Object.entries(NOTE_TYPES).map(([type, config]) => (
                  <SelectItem key={type} value={type}>
                    <div className="flex items-center space-x-2">
                      <span>{type}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Priority</label>
            <Select value={filters.priority || ""} onValueChange={(value) => onFilterChange("priority", value)}>
              <SelectTrigger>
                <SelectValue placeholder="All priorities" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All priorities</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="low">Low</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Actions</label>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm" onClick={onRefresh}>
                <RefreshCw className="w-4 h-4" />
              </Button>
              {onExport && (
                <Button variant="outline" size="sm" onClick={onExport}>
                  <Download className="w-4 h-4" />
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Advanced Filters */}
        {showAdvancedFilters && (
          <div className="border-t pt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Status</label>
                <Select value={filters.status || ""} onValueChange={(value) => onFilterChange("status", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All statuses</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="finalized">Finalized</SelectItem>
                    <SelectItem value="archived">Archived</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Privacy</label>
                <Select 
                  value={filters.is_private === undefined ? "" : filters.is_private.toString()} 
                  onValueChange={(value) => onFilterChange("is_private", value === "" ? undefined : value === "true")}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All notes" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All notes</SelectItem>
                    <SelectItem value="true">Private only</SelectItem>
                    <SelectItem value="false">Public only</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">From Date</label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-full justify-start text-left font-normal">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateFrom ? format(dateFrom, "PPP") : "Pick a date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={dateFrom}
                      onSelect={handleDateFromChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">To Date</label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-full justify-start text-left font-normal">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateTo ? format(dateTo, "PPP") : "Pick a date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={dateTo}
                      onSelect={handleDateToChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </div>
        )}

        {/* Active Filters Display */}
        {activeFiltersCount > 0 && (
          <div className="border-t pt-4">
            <div className="flex items-center space-x-2 flex-wrap">
              <span className="text-sm font-medium text-gray-600">Active filters:</span>
              {filters.search && (
                <Badge variant="secondary" className="flex items-center space-x-1">
                  <span>Search: {filters.search}</span>
                  <X 
                    className="w-3 h-3 cursor-pointer" 
                    onClick={() => onFilterChange("search", "")}
                  />
                </Badge>
              )}
              {filters.note_type && (
                <Badge variant="secondary" className="flex items-center space-x-1">
                  <span>Type: {filters.note_type}</span>
                  <X 
                    className="w-3 h-3 cursor-pointer" 
                    onClick={() => onFilterChange("note_type", "")}
                  />
                </Badge>
              )}
              {filters.priority && (
                <Badge variant="secondary" className="flex items-center space-x-1">
                  <span>Priority: {filters.priority}</span>
                  <X 
                    className="w-3 h-3 cursor-pointer" 
                    onClick={() => onFilterChange("priority", "")}
                  />
                </Badge>
              )}
              {filters.status && (
                <Badge variant="secondary" className="flex items-center space-x-1">
                  <span>Status: {filters.status}</span>
                  <X 
                    className="w-3 h-3 cursor-pointer" 
                    onClick={() => onFilterChange("status", "")}
                  />
                </Badge>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
