import React from "react";
interface ButtonGroupProps {
    children: React.ReactNode;
    size?: 'sm' | 'md' | 'lg';
    className?: string;
  }
  
  const ButtonGroup = ({ children, size = 'md', className = '' }: ButtonGroupProps) => {
    const sizeClasses = {
      sm: 'gap-2',
      md: 'gap-4',
      lg: 'gap-6',
    };
  
    return (
      <div
        className={`inline-flex items-center ${sizeClasses[size]} ${className}`}
      >
        {children}
      </div>
    );
  };
  export default ButtonGroup;