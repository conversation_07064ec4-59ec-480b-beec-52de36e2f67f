// src/components/AllocateProspects.tsx
import { useState, useEffect } from "react";
import MultiStepModal from "@/components/custom/modals/MultiStepModal";
import { Label } from "@/components/ui/label";
import DropdownButton from "@/components/custom/Dropdowns/dropdown";

interface Prospects {
  id: string;
  Name: string;
  Passport: string;
  KRAPIN: string;
  PhoneNo: string;
  Digital: string;
  Allocatedmarketer: string;
  marketer: string;
  LeadSource: string;
  ProjectOfIntrest: string;
  Message: string;
  Date: Date;
}

interface AllocateProspectsProps {
  isOpen: boolean;
  onClose: () => void;
  prospect: Prospects;
  onAllocate: (prospectId: string, marketer: string) => void;
}

export default function AllocateProspects({
  isOpen,
  onClose,
  prospect,
  onAllocate,
}: AllocateProspectsProps) {
  const [marketer, setMarketer] = useState("");

  // Sample options for dropdown (replace with actual data)
  const marketerOptions = [
    { label: "<PERSON>", onClick: () => setMarketer("<PERSON>") },
    { label: "<PERSON>", onClick: () => setMarketer("<PERSON>") },
    { label: "<PERSON> Johnson", onClick: () => setMarketer("<PERSON>") },
  ];

  useEffect(() => {
    if (isOpen) {
      setMarketer("");
    }
  }, [isOpen]);

  const handleAllocate = () => {
    onAllocate(prospect.id, marketer);
    onClose();
  };

  return (
    <MultiStepModal
      isOpen={isOpen}
      onOpenChange={onClose}
      title="Allocate Lead"
      description="Assign a marketer to the prospect"
      currentStep={0}
      onStepChange={() => {}}
      onComplete={handleAllocate}
      steps={[
        {
          title: "Assign Marketer",
          content: (
            <div className="space-y-4 py-2">
              <div className="space-y-2">
                <Label htmlFor="marketer">Marketer</Label>
                <DropdownButton
                  variant="primary"
                  size="lg"
                  items={marketerOptions}
                  label={marketer || "Select marketer"}
                  fullWidth
                />
              </div>
              <p className="text-sm text-gray-600">Prospect: {prospect.Name}</p>
            </div>
          ),
        },
      ]}
    />
  );
}