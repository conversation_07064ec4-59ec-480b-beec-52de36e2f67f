import { contentHeader, noAuth<PERSON>eader } from "@/utils/header";
import { apiSlice } from "../apiSlice";
import { BASE_URL } from "@/config";

export const permissionsApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // ── Group Permissions ───────────────────────────────────────────────────
    getGroupPermissions: builder.query<
      any,
      { search?: string; ordering?: string; page?: number; page_size?: number }
    >({
      query: (params) => ({
        url: `${BASE_URL}/users/group_permissions`,
        method: "GET",
        params,
        // headers: contentHeader(),
        headers: noAuthHeader(),
      }),
      transformResponse: (raw: any) => {
        // Return full response to preserve pagination metadata
        return raw.data || { results: [], count: 0, next: null, previous: null };
      },
      providesTags: ["Permissions"],
    }),
    getGroupPermission: builder.query<any, number>({
      query: (permission_id) => ({
        url: `${BASE_URL}/users/group_permissions/${permission_id}`,
        method: "GET",
        // headers: contentHeader(),
        headers: noAuthHeader(),
      }),
      transformResponse: (raw: any) => {
        return raw.data?.results ?? [];
      },
      providesTags: ["Permissions"],
    }),
    createGroupPermission: builder.mutation<
      any,
      { permission_id: number; permission_name: string; comments?: string }
    >({
      query: (data) => ({
        url: `${BASE_URL}/users/group_permissions`,
        method: "POST",
        body: data,
        // headers: contentHeader(),
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Permissions"],
    }),
    updateGroupPermission: builder.mutation<
      any,
      { permission_id: number } & Partial<{
        permission_name: string;
        comments: string;
      }>
    >({
      query: ({ permission_id, ...patch }) => ({
        url: `${BASE_URL}/users/group_permissions/${permission_id}`,
        method: "PATCH",
        body: patch,
        // headers: contentHeader(),
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Permissions"],
    }),
    deleteGroupPermission: builder.mutation<any, number>({
      query: (permission_id) => ({
        url: `${BASE_URL}/users/group_permissions/${permission_id}`,
        method: "DELETE",
        // headers: contentHeader(),
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Permissions"],
    }),

    // ── Team Permissions ────────────────────────────────────────────────────
    getTeamPermissions: builder.query<
      any,
      { search?: string; ordering?: string; page?: number; page_size?: number }
    >({
      query: (params) => ({
        url: `${BASE_URL}/users/team_permissions`,
        method: "GET",
        params,
        // headers: contentHeader(),
        headers: noAuthHeader(),
      }),
      transformResponse: (raw: any) => {
        // Return full response to preserve pagination metadata
        return raw.data || { results: [], count: 0, next: null, previous: null };
      },
      providesTags: ["Permissions"],
    }),
    getTeamPermission: builder.query<any, number>({
      query: (permission_id) => ({
        url: `${BASE_URL}/users/team_permissions/${permission_id}`,
        method: "GET",
        // headers: contentHeader(),
        headers: noAuthHeader(),
      }),
      transformResponse: (raw: any) => {
        return raw.data?.results ?? [];
      },
      providesTags: ["Permissions"],
    }),
    createTeamPermission: builder.mutation<
      any,
      { permission_id: number; permission_name: string; comments?: string }
    >({
      query: (data) => ({
        url: `${BASE_URL}/users/team_permissions`,
        method: "POST",
        body: data,
        // headers: contentHeader(),
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Permissions"],
    }),
    updateTeamPermission: builder.mutation<
      any,
      { permission_id: number } & Partial<{
        permission_name: string;
        comments: string;
      }>
    >({
      query: ({ permission_id, ...patch }) => ({
        url: `${BASE_URL}/users/team_permissions/${permission_id}`,
        method: "PATCH",
        body: patch,
        // headers: contentHeader(),
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Permissions"],
    }),
    deleteTeamPermission: builder.mutation<any, number>({
      query: (permission_id) => ({
        url: `${BASE_URL}/users/team_permissions/${permission_id}`,
        method: "DELETE",
        // headers: contentHeader(),
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Permissions"],
    }),

    // ── Department Permissions ──────────────────────────────────────────────
    getDepartmentPermissions: builder.query<
      any[],
      { search?: string; ordering?: string; page?: number; page_size?: number }
    >({
      query: (params) => ({
        url: `${BASE_URL}/users/department_permissions`,
        method: "GET",
        params,
        headers: noAuthHeader(),
      }),
      transformResponse: (raw: any) => {
        return raw.data?.results ?? [];
      },
      providesTags: ["Permissions"],
    }),
    getDepartmentPermission: builder.query<any, number>({
      query: (permission_id) => ({
        url: `${BASE_URL}/users/department_permissions/${permission_id}`,
        method: "GET",
        headers: noAuthHeader(),
      }),
      transformResponse: (raw: any) => {
        return raw.data?.results ?? [];
      },
      providesTags: ["Permissions"],
    }),
    createDepartmentPermission: builder.mutation<
      any,
      { permission_id: number; permission_name: string; comments?: string }
    >({
      query: (data) => ({
        url: `${BASE_URL}/users/department_permissions`,
        method: "POST",
        body: data,
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Permissions"],
    }),
    updateDepartmentPermission: builder.mutation<
      any,
      { permission_id: number } & Partial<{
        permission_name: string;
        comments: string;
      }>
    >({
      query: ({ permission_id, ...patch }) => ({
        url: `${BASE_URL}/users/department_permissions/${permission_id}`,
        method: "PATCH",
        body: patch,
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Permissions"],
    }),
    deleteDepartmentPermission: builder.mutation<any, number>({
      query: (permission_id) => ({
        url: `${BASE_URL}/users/department_permissions/${permission_id}`,
        method: "DELETE",
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Permissions"],
    }),

    // ── Departments ↔ DepartmentPermissions ─────────────────────────────────
    getDepartments2DepartmentsPermissions: builder.query<
      any[],
      {
        search?: string;
        ordering?: string;
        page?: number;
        page_size?: number;
        department?: number;
      }
    >({
      query: (params) => ({
        url: `${BASE_URL}/users/departments_2_departmentspermissions`,
        method: "GET",
        params,
        headers: noAuthHeader(),
      }),
      transformResponse: (raw: any) => raw.data?.results ?? [],
      providesTags: ["Permissions"],
    }),
    getDepartments2DepartmentsPermission: builder.query<any, number>({
      query: (id) => ({
        url: `${BASE_URL}/users/departments_2_departmentspermissions/${id}`,
        method: "GET",
        headers: noAuthHeader(),
      }),
      transformResponse: (raw: any) => raw.data?.results ?? {},
      providesTags: ["Permissions"],
    }),
    createDepartments2DepartmentsPermission: builder.mutation<
      any,
      { department: number; permission: number }
    >({
      query: (data) => ({
        url: `${BASE_URL}/users/departments_2_departmentspermissions`,
        method: "POST",
        body: data,
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Permissions"],
    }),
    updateDepartments2DepartmentsPermission: builder.mutation<
      any,
      { id: number } & Partial<{ department: number; permission: number }>
    >({
      query: ({ id, ...patch }) => ({
        url: `${BASE_URL}/users/departments_2_departmentspermissions/${id}`,
        method: "PATCH",
        body: patch,
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Permissions"],
    }),
    deleteDepartments2DepartmentsPermission: builder.mutation<any, number>({
      query: (id) => ({
        url: `${BASE_URL}/users/departments_2_departmentspermissions/${id}`,
        method: "DELETE",
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Permissions"],
    }),

    // ── Teams ↔ TeamPermissions ─────────────────────────────────────────────
    getTeams2TeamsPermissions: builder.query<
      any[],
      {
        search?: string;
        ordering?: string;
        page?: number;
        page_size?: number;
        team?: number;
      }
    >({
      query: (params) => ({
        url: `${BASE_URL}/users/teams_2_teamspermissions`,
        method: "GET",
        params,
        headers: noAuthHeader(),
      }),
      transformResponse: (raw: any) => raw.data?.results ?? [],
      providesTags: ["Permissions"],
    }),
    getTeams2TeamsPermission: builder.query<any, number>({
      query: (id) => ({
        url: `${BASE_URL}/users/teams_2_teamspermissions/${id}`,
        method: "GET",
        headers: noAuthHeader(),
      }),
      transformResponse: (raw: any) => raw.data?.results ?? {},
      providesTags: ["Permissions"],
    }),
    createTeams2TeamsPermission: builder.mutation<
      any,
      { team: number; permission: number } // <-- changed from `user_group`
    >({
      query: (data) => ({
        url: `${BASE_URL}/users/teams_2_teamspermissions`,
        method: "POST",
        body: data,
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Permissions"],
    }),
    updateTeams2TeamsPermission: builder.mutation<
      any,
      { id: number } & Partial<{ team: number; permission: number }> // <-- patchable fields
    >({
      query: ({ id, ...patch }) => ({
        url: `${BASE_URL}/users/teams_2_teamspermissions/${id}`,
        method: "PATCH",
        body: patch,
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Permissions"],
    }),
    deleteTeams2TeamsPermission: builder.mutation<any, number>({
      query: (id) => ({
        url: `${BASE_URL}/users/teams_2_teamspermissions/${id}`,
        method: "DELETE",
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Permissions"],
    }),

    // ── Users ↔ UserPermissions ─────────────────────────────────────────────
    getUser2UserPermissions: builder.query<
      any[],
      {
        search?: string;
        ordering?: string;
        page?: number;
        page_size?: number;
        user: string;
      }
    >({
      query: (params) => ({
        url: `${BASE_URL}/users/user_2_userpermissions`,
        method: "GET",
        params,
        // headers: contentHeader(),
        headers: noAuthHeader(),
      }),
      transformResponse: (raw: any) => {
        return raw.data?.results ?? [];
      },
      providesTags: ["Permissions"],
    }),
    getUser2UserPermission: builder.query<any, number>({
      query: (id) => ({
        url: `${BASE_URL}/users/user_2_userpermissions/${id}`,
        method: "GET",
        // headers: contentHeader(),
        headers: noAuthHeader(),
      }),
      transformResponse: (raw: any) => {
        return raw.data?.results ?? [];
      },
      providesTags: ["Permissions"],
    }),

    createUser2UserPermission: builder.mutation<
      any,
      { user: string; permission: number }
    >({
      query: (data) => ({
        url: `${BASE_URL}/users/user_2_userpermissions`,
        method: "POST",
        body: data,
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Permissions"],
    }),

    updateUser2UserPermission: builder.mutation<
      any,
      { id: number } & Partial<{ user: string; permission: number }>
    >({
      query: ({ id, ...patch }) => ({
        url: `${BASE_URL}/users/user_2_userpermissions/${id}`,
        method: "PATCH",
        body: patch,
        // headers: contentHeader(),
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Permissions"],
    }),
    deleteUser2UserPermission: builder.mutation<any, number>({
      query: (id) => ({
        url: `${BASE_URL}/users/user_2_userpermissions/${id}`,
        method: "DELETE",
        // headers: contentHeader(),
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Permissions"],
    }),

    // ── User Permissions ────────────────────────────────────────────────────
    getUserPermissions: builder.query<
      any,
      { search?: string; ordering?: string; page?: number; page_size?: number }
    >({
      query: (params) => ({
        url: `${BASE_URL}/users/user_permissions`,
        method: "GET",
        params,
        // headers: contentHeader(),
        headers: noAuthHeader(),
      }),
      transformResponse: (raw: any) => {
        // Return full response to preserve pagination metadata
        return raw.data || { results: [], count: 0, next: null, previous: null };
      },
      providesTags: ["Permissions"],
    }),
    getUserPermission: builder.query<any, number>({
      query: (permission_id) => ({
        url: `${BASE_URL}/users/user_permissions/${permission_id}`,
        method: "GET",
        // headers: contentHeader(),
        headers: noAuthHeader(),
      }),
      transformResponse: (raw: any) => {
        return raw.data?.results ?? [];
      },
      providesTags: ["Permissions"],
    }),
    createUserPermission: builder.mutation<
      any,
      { permission_id: number; permission_name: string; comments?: string }
    >({
      query: (data) => ({
        url: `${BASE_URL}/users/user_permissions`,
        method: "POST",
        body: data,
        // headers: contentHeader(),
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Permissions"],
    }),
    updateUserPermission: builder.mutation<
      any,
      { permission_id: number } & Partial<{
        permission_name: string;
        comments: string;
      }>
    >({
      query: ({ permission_id, ...patch }) => ({
        url: `${BASE_URL}/users/user_permissions/${permission_id}`,
        method: "PATCH",
        body: patch,
        // headers: contentHeader(),
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Permissions"],
    }),
    deleteUserPermission: builder.mutation<any, number>({
      query: (permission_id) => ({
        url: `${BASE_URL}/users/user_permissions/${permission_id}`,
        method: "DELETE",
        // headers: contentHeader(),
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Permissions"],
    }),

    // ── UserGroup ↔ UserGroupPermissions ───────────────────────────────────
    getUserGroup2UserGroupPermissions: builder.query<
      any[],
      {
        search?: string;
        ordering?: string;
        page?: number;
        page_size?: number;
        user_group: number;
      }
    >({
      query: (params) => ({
        url: `${BASE_URL}/users/usergroup_2_usergrouppermissions`,
        method: "GET",
        params,
        // headers: contentHeader(),
        headers: noAuthHeader(),
      }),
      transformResponse: (raw: any) => {
        return raw.data?.results ?? [];
      },
      providesTags: ["Permissions"],
    }),
    getUserGroup2UserGroupPermission: builder.query<any, number>({
      query: (id) => ({
        url: `${BASE_URL}/users/usergroup_2_usergrouppermissions/${id}`,
        method: "GET",
        // headers: contentHeader(),
        headers: noAuthHeader(),
      }),
      transformResponse: (raw: any) => {
        return raw.data?.results ?? [];
      },
      providesTags: ["Permissions"],
    }),
    createUserGroup2UserGroupPermission: builder.mutation<
      any,
      { user_group: number; permission: number }
    >({
      query: (data) => ({
        url: `${BASE_URL}/users/usergroup_2_usergrouppermissions`,
        method: "POST",
        body: data,
        // headers: contentHeader(),
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Permissions"],
    }),
    updateUserGroup2UserGroupPermission: builder.mutation<
      any,
      { id: number } & Partial<{ user_group: number; permission: number }>
    >({
      query: ({ id, ...patch }) => ({
        url: `${BASE_URL}/users/usergroup_2_usergrouppermissions/${id}`,
        method: "PATCH",
        body: patch,
        // headers: contentHeader(),
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Permissions"],
    }),
    deleteUserGroup2UserGroupPermission: builder.mutation<any, number>({
      query: (id) => ({
        url: `${BASE_URL}/users/usergroup_2_usergrouppermissions/${id}`,
        method: "DELETE",
        // headers: contentHeader(),
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Permissions"],
    }),
  }),
});

export const {
  useGetGroupPermissionsQuery,
  useLazyGetGroupPermissionsQuery,
  useGetGroupPermissionQuery,
  useCreateGroupPermissionMutation,
  useUpdateGroupPermissionMutation,
  useDeleteGroupPermissionMutation,

  useGetTeamPermissionsQuery,
  useGetTeamPermissionQuery,
  useCreateTeamPermissionMutation,
  useUpdateTeamPermissionMutation,
  useDeleteTeamPermissionMutation,
  useLazyGetTeamPermissionsQuery,

  useGetDepartmentPermissionsQuery,
  useGetDepartmentPermissionQuery,
  useCreateDepartmentPermissionMutation,
  useUpdateDepartmentPermissionMutation,
  useDeleteDepartmentPermissionMutation,
  useLazyGetDepartmentPermissionsQuery,

  useGetDepartments2DepartmentsPermissionsQuery,
  useGetDepartments2DepartmentsPermissionQuery,
  useCreateDepartments2DepartmentsPermissionMutation,
  useUpdateDepartments2DepartmentsPermissionMutation,
  useDeleteDepartments2DepartmentsPermissionMutation,

  useGetTeams2TeamsPermissionsQuery,
  useGetTeams2TeamsPermissionQuery,
  useCreateTeams2TeamsPermissionMutation,
  useUpdateTeams2TeamsPermissionMutation,
  useDeleteTeams2TeamsPermissionMutation,

  useGetUser2UserPermissionsQuery,
  useGetUser2UserPermissionQuery,
  useCreateUser2UserPermissionMutation,
  useUpdateUser2UserPermissionMutation,
  useDeleteUser2UserPermissionMutation,

  useGetUserPermissionsQuery,
  useGetUserPermissionQuery,
  useCreateUserPermissionMutation,
  useUpdateUserPermissionMutation,
  useDeleteUserPermissionMutation,
  useLazyGetUserPermissionsQuery,

  useGetUserGroup2UserGroupPermissionsQuery,
  useGetUserGroup2UserGroupPermissionQuery,
  useCreateUserGroup2UserGroupPermissionMutation,
  useUpdateUserGroup2UserGroupPermissionMutation,
  useDeleteUserGroup2UserGroupPermissionMutation,
} = permissionsApiSlice;
