import  { forwardRef, FormEvent } from "react";
import { Button } from "@/components/ui/button";
import BaseModal from "./BaseModal";
import { BaseModalProps } from "./BaseModal";

export interface FormModalProps extends BaseModalProps {
    onSubmit?: (data: any) => void | Promise<void>;
    isLoading?: boolean;
    submitText?: string;
    cancelText?: string;
    hideCancel?: boolean;
  }
const FormModal = forwardRef<HTMLDivElement, FormModalProps>(
  (
    {
      onSubmit,
      isLoading = false,
      submitText = "Save",
      cancelText = "Cancel",
      hideCancel = false,
      children,
      ...props
    },
    ref
  ) => {
    const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      if (onSubmit) {
        const formData = new FormData(e.currentTarget);
        const data = Object.fromEntries(formData.entries());
        await onSubmit(data);
        if (props.onOpenChange) {
          props.onOpenChange(false);
        }
      }
    };

    const footer = (
      <>
        {!hideCancel && (
          <Button
            type="button"
            variant="outline"
            onClick={() => props.onOpenChange?.(false)}
            disabled={isLoading}
          >
            {cancelText}
          </Button>
        )}
        <Button type="submit" form="form-modal" disabled={isLoading}>
          {isLoading ? "Saving..." : submitText}
        </Button>
      </>
    );

    return (
      <BaseModal ref={ref} footer={footer} {...props}>
        <form id="form-modal" onSubmit={handleSubmit} className="space-y-4">
          {children}
        </form>
      </BaseModal>
    );
  }
);

FormModal.displayName = "FormModal";
export default FormModal;