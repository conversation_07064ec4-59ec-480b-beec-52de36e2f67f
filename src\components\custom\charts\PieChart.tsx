import {
  Pie<PERSON>hart as Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  ResponsiveContainer
} from 'recharts';

type Props = {
  data: {
    name: string;
    value: number;
  }[];
  dataKey?: string;
  nameKey?: string;
  showTooltip?: boolean;
  showLegend?: boolean;
};

const COLORS = [
  'hsl(var(--chart-1))',
  'hsl(var(--chart-2))',
  'hsl(var(--chart-3))',
  'hsl(var(--chart-4))',
  'hsl(var(--chart-5))'
];

const renderCustomizedLabel = ({ 
  cx, 
  cy, 
  midAngle, 
  innerRadius, 
  outerRadius, 
  percent, 
  name 
}: any) => {
  const RADIAN = Math.PI / 180;
  const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
  const x = cx + radius * Math.cos(-midAngle * RADIAN);
  const y = cy + radius * Math.sin(-midAngle * RADIAN);

  return (
    <text 
      x={x} 
      y={y} 
      fill="hsl(var(--background))" 
      textAnchor={x > cx ? 'start' : 'end'} 
      dominantBaseline="central"
      fontSize={12}
      fontWeight="bold"
    >
      {`${(percent * 100).toFixed(0)}%`}
    </text>
  );
};

const PieChart = ({
  data,
  dataKey = "value",
  nameKey = "name",
  showTooltip = true,
  showLegend = true
}: Props) => {
  return (
    <div className="w-full h-80">
      <ResponsiveContainer width="100%" height="100%">
        <RechartsPieChart>
          {showTooltip && (
            <Tooltip 
              contentStyle={{ 
                backgroundColor: 'hsl(var(--card))', 
                border: '1px solid hsl(var(--border))',
                borderRadius: '0.5rem',
                color: 'hsl(var(--card-foreground))'
              }} 
              formatter={(value: number) => [`${value}`, 'Value']}
            />
          )}
          {showLegend && (
            <Legend 
              layout="vertical" 
              verticalAlign="middle" 
              align="right"
            />
          )}
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={renderCustomizedLabel}
            outerRadius={80}
            innerRadius={30}
            fill="hsl(var(--primary))"
            dataKey={dataKey}
            nameKey={nameKey}
            animationDuration={1500}
            animationBegin={200}
          >
            {data.map((entry, index) => (
              <Cell 
                key={`cell-${index}`} 
                fill={COLORS[index % COLORS.length]} 
                className="hover:opacity-80 transition-opacity"
              />
            ))}
          </Pie>
        </RechartsPieChart>
      </ResponsiveContainer>
    </div>
  );
};

export default PieChart; 