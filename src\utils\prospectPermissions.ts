/**
 * Prospect permissions utility functions
 * These functions handle the logic for filtering prospect data based on user permissions
 * Simplified to follow the logistics permissions pattern
 */
import { UserDetails } from '@/redux/authSlice';

// Prospect permission codes as defined in the system
export const PROSPECT_PERMISSION_CODES = {
  // Single filter permissions - only one of these should be applied at a time
  VIEW_PROSPECT_HQ: 3001,
  VIEW_PROSPECT_KAREN: 3002,
  VIEW_PROSPECT_ALL_OFFICES: 3003,
  VIEW_PROSPECT_OWN_MARKETER: 3004,
  VIEW_PROSPECT_ALL_MARKETERS: 3005,
  VIEW_PROSPECT_DIASPORA_TEAM: 3006,
  VIEW_PROSPECT_DIGITAL_TEAM: 3007,
  VIEW_PROSPECT_TELEMARKETING_TEAM: 3008,
  VIEW_PROSPECT_OTHER_TEAM: 3009,
  VIEW_PROSPECT_ALL_TEAMS: 3010,
  VIEW_PROSPECT_DIASPORA_REGION: 3011,
  VIEW_PROSPECT_ALL_DIASPORA_REGIONS: 3012,
} as const;

/**
 * Permission descriptions for documentation and UI display
 */
export const PROSPECT_PERMISSION_DESCRIPTIONS = {
  [PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_HQ]: 
    'View prospects from HQ office',
  [PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_KAREN]: 
    'View prospects from Karen office',
  [PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_ALL_OFFICES]: 
    'View prospects from all offices',
  [PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_OWN_MARKETER]: 
    'View only prospects assigned to the current user',
  [PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_ALL_MARKETERS]: 
    'View prospects assigned to all marketers',
  [PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_DIASPORA_TEAM]: 
    'View prospects from Diaspora team',
  [PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_DIGITAL_TEAM]: 
    'View prospects from Digital team',
  [PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_TELEMARKETING_TEAM]: 
    'View prospects from Telemarketing team',
  [PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_OTHER_TEAM]: 
    'View prospects from Other teams',
  [PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_ALL_TEAMS]: 
    'View prospects from all teams',
  [PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_DIASPORA_REGION]: 
    'View prospects from user\'s diaspora region only',
  [PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_ALL_DIASPORA_REGIONS]: 
    'View prospects from all diaspora regions',
} as const;

/**
 * Check if user has a specific prospect permission
 */
export const hasProspectPermission = (
  userPermissions: any[],
  permissionCode: number
): boolean => {
  // Extract permissions from potentially nested arrays if needed
  let flatPermissions: (number | string | { permission?: number | string; code?: number | string; id?: number | string })[] = [];
  
  // If we have nested arrays, flatten them
  if (userPermissions.some(p => Array.isArray(p))) {
    userPermissions.forEach(p => {
      if (Array.isArray(p)) {
        flatPermissions = [...flatPermissions, ...p];
      } else {
        flatPermissions.push(p);
      }
    });
  } else {
    flatPermissions = userPermissions;
  }
  
  // Convert all permissions to numbers to ensure consistent comparison
  const normalizedPermissions = flatPermissions.map(p => {
    if (typeof p === 'string') {
      return parseInt(p, 10);
    } else if (typeof p === 'object' && p !== null) {
      // First try to get the permission field (from API response)
      if (p.permission !== undefined) {
        return typeof p.permission === 'string' ? parseInt(p.permission, 10) : p.permission;
      }
      // Then try code or id (from other sources)
      const code = p.code !== undefined ? p.code : p.id;
      return typeof code === 'string' ? parseInt(code, 10) : code;
    }
    return p;
  });
  
  return normalizedPermissions.includes(permissionCode);
};

/**
 * Get the appropriate API parameters based on user's prospect permissions
 * Simplified to only apply a single filter parameter based on priority
 */
export const getProspectApiParams = (
  userDetails: UserDetails | null,
  baseParams: Record<string, any> = {},
  user2userPermissions: any[] = []
): Record<string, any> => {
  // Extract permissions from user2user permissions
  const userPermissions: (number | string | { permission?: number | string; code?: number | string; id?: number | string })[] = [];
  
  // Add permissions from user2user permissions API
  user2userPermissions.forEach((p: any) => {
    if (p.permission !== undefined) {
      userPermissions.push({ permission: p.permission });
    }
  });

  // If no user2user permissions found, check legacy user_permissions as fallback
  if (userPermissions.length === 0 && userDetails?.user_permissions) {
    userDetails.user_permissions.forEach((p: any) => {
      if (Array.isArray(p)) {
        // If it's an array, add each item
        p.forEach((subP: any) => {
          userPermissions.push(subP);
        });
      } else if (typeof p === 'object') {
        // If it's an object, extract permission, code or id
        userPermissions.push(p);
      } else {
        // Otherwise add as is
        userPermissions.push(p);
      }
    });
  }

  // If still no permissions, return empty result set
  if (userPermissions.length === 0) {
    return { ...baseParams, no_access: true };
  }

  // Convert all permissions to numbers to ensure consistent comparison
  const normalizedPermissions = userPermissions.map(p => {
    if (typeof p === 'string') {
      return parseInt(p, 10);
    } else if (typeof p === 'object' && p !== null) {
      // First try to get the permission field (from API response)
      if (p.permission !== undefined) {
        return typeof p.permission === 'string' ? parseInt(p.permission, 10) : p.permission;
      }
      // Then try code or id (from other sources)
      const code = p.code !== undefined ? p.code : p.id;
      return typeof code === 'string' ? parseInt(code, 10) : code;
    }
    return p;
  });

  const params = { ...baseParams };

  // Apply a single filter parameter based on priority
  // Priority order: All access > Office > Team > Marketer > Region
  
  // Check for all access first
  if (normalizedPermissions.includes(PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_ALL_OFFICES)) {
    params.OFFICE = 'ALL';
    return params;
  }
  
  if (normalizedPermissions.includes(PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_ALL_TEAMS)) {
    params.ORGANIZATION_TEAM = 'ALL';
    return params;
  }
  
  if (normalizedPermissions.includes(PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_ALL_MARKETERS)) {
    params.MARKETER_EMPLOYEE_NO = 'ALL';
    return params;
  }
  
  if (normalizedPermissions.includes(PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_ALL_DIASPORA_REGIONS)) {
    params.DIASPORA_REGION = 'ALL';
    return params;
  }
  
  // Check for specific office access
  if (normalizedPermissions.includes(PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_HQ)) {
    params.OFFICE = 'HQ';
    return params;
  }
  
  if (normalizedPermissions.includes(PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_KAREN)) {
    params.OFFICE = 'KAREN';
    return params;
  }
  
  // Check for specific team access
  if (normalizedPermissions.includes(PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_DIASPORA_TEAM)) {
    params.ORGANIZATION_TEAM = 'DIASPORA';
    return params;
  }
  
  if (normalizedPermissions.includes(PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_DIGITAL_TEAM)) {
    params.ORGANIZATION_TEAM = 'DIGITAL';
    return params;
  }
  
  if (normalizedPermissions.includes(PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_TELEMARKETING_TEAM)) {
    params.ORGANIZATION_TEAM = 'TELEMARKETING';
    return params;
  }
  
  if (normalizedPermissions.includes(PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_OTHER_TEAM)) {
    params.ORGANIZATION_TEAM = 'OTHER';
    return params;
  }
  
  // Check for marketer-specific access
  if (normalizedPermissions.includes(PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_OWN_MARKETER)) {
    params.MARKETER_EMPLOYEE_NO = userDetails.employee_no || '';
    return params;
  }
  
  // Check for region-specific access
  if (normalizedPermissions.includes(PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_DIASPORA_REGION)) {
    params.DIASPORA_REGION = userDetails.diaspora_region || 'USER_REGION';
    return params;
  }

  // If no prospect permissions are found, restrict access
  const hasProspectAccess = Object.values(PROSPECT_PERMISSION_CODES).some(code =>
    normalizedPermissions.includes(code)
  );

  if (!hasProspectAccess) {
    params.no_access = true;
  }

  return params;
};

/**
 * Check if user has any prospect viewing permissions
 */
export const hasAnyProspectPermission = (userPermissions: any[]): boolean => {
  // Extract permissions from potentially nested arrays if needed
  let flatPermissions: (number | string | { permission?: number | string; code?: number | string; id?: number | string })[] = [];
  
  // If we have nested arrays, flatten them
  if (userPermissions.some(p => Array.isArray(p))) {
    userPermissions.forEach(p => {
      if (Array.isArray(p)) {
        flatPermissions = [...flatPermissions, ...p];
      } else {
        flatPermissions.push(p);
      }
    });
  } else {
    flatPermissions = userPermissions;
  }
  
  // Convert all permissions to numbers to ensure consistent comparison
  const normalizedPermissions = flatPermissions.map(p => {
    if (typeof p === 'string') {
      return parseInt(p, 10);
    } else if (typeof p === 'object' && p !== null) {
      // First try to get the permission field (from API response)
      if (p.permission !== undefined) {
        return typeof p.permission === 'string' ? parseInt(p.permission, 10) : p.permission;
      }
      // Then try code or id (from other sources)
      const code = p.code !== undefined ? p.code : p.id;
      return typeof code === 'string' ? parseInt(code, 10) : code;
    }
    return p;
  });
  
  // Check if any of the user's permissions match any of the prospect permission codes
  return Object.values(PROSPECT_PERMISSION_CODES).some(code =>
    normalizedPermissions.includes(code)
  );
};

/**
 * Get user's prospect permission level description
 */
export const getProspectPermissionLevel = (userPermissions: any[]): string => {
  // Extract permissions from potentially nested arrays if needed
  let flatPermissions: (number | string | { code?: number | string; id?: number | string })[] = [];
  
  // If we have nested arrays, flatten them
  if (userPermissions.some(p => Array.isArray(p))) {
    userPermissions.forEach(p => {
      if (Array.isArray(p)) {
        flatPermissions = [...flatPermissions, ...p];
      } else {
        flatPermissions.push(p);
      }
    });
  } else {
    flatPermissions = userPermissions;
  }
  
  // Convert all permissions to numbers to ensure consistent comparison
  const normalizedPermissions = flatPermissions.map(p => {
    if (typeof p === 'string') {
      return parseInt(p, 10);
    } else if (typeof p === 'object' && p !== null) {
      const code = p.code !== undefined ? p.code : p.id;
      return typeof code === 'string' ? parseInt(code, 10) : code;
    }
    return p;
  });

  // Check for all access first
  if (normalizedPermissions.includes(PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_ALL_OFFICES)) {
    return 'All Offices';
  }
  
  if (normalizedPermissions.includes(PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_ALL_TEAMS)) {
    return 'All Teams';
  }
  
  if (normalizedPermissions.includes(PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_ALL_MARKETERS)) {
    return 'All Marketers';
  }
  
  if (normalizedPermissions.includes(PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_ALL_DIASPORA_REGIONS)) {
    return 'All Diaspora Regions';
  }
  
  // Check for specific permissions
  if (normalizedPermissions.includes(PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_HQ)) {
    return 'HQ Office Only';
  }
  
  if (normalizedPermissions.includes(PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_KAREN)) {
    return 'Karen Office Only';
  }
  
  if (normalizedPermissions.includes(PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_DIASPORA_TEAM)) {
    return 'Diaspora Team Only';
  }
  
  if (normalizedPermissions.includes(PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_DIGITAL_TEAM)) {
    return 'Digital Team Only';
  }
  
  if (normalizedPermissions.includes(PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_TELEMARKETING_TEAM)) {
    return 'Telemarketing Team Only';
  }
  
  if (normalizedPermissions.includes(PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_OTHER_TEAM)) {
    return 'Other Team Only';
  }
  
  if (normalizedPermissions.includes(PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_OWN_MARKETER)) {
    return 'Own Prospects Only';
  }
  
  if (normalizedPermissions.includes(PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_DIASPORA_REGION)) {
    return 'User Diaspora Region Only';
  }

  return 'No Access';
};