import { OfferLetterDetailResponse, OfferLetterCategory } from '@/pages/OfferLetter/api/offerLetterApi';

// Utility function to format currency
export const formatCurrency = (amount: string | number): string => {
  return new Intl.NumberFormat('en-KE', {
    style: 'currency',
    currency: 'KES'
  }).format(typeof amount === 'string' ? parseFloat(amount) : amount);
};

// Generate PDF content for offer letter
export const generateOfferLetterPDF = (
  offerLetter: OfferLetterCategory,
  detailsData?: OfferLetterDetailResponse
) => {
  // Create a new window for printing
  const printWindow = window.open('', '_blank');
  if (!printWindow) {
    alert('Please allow popups to download the PDF');
    return;
  }

  const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Offer Letter - ${offerLetter.plot_number}</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          margin: 20px;
          color: #333;
          line-height: 1.6;
        }
        .header {
          text-align: center;
          border-bottom: 2px solid #2563eb;
          padding-bottom: 20px;
          margin-bottom: 30px;
        }
        .header h1 {
          color: #2563eb;
          margin: 0;
        }
        .section {
          margin-bottom: 30px;
        }
        .section h2 {
          color: #1f2937;
          border-bottom: 1px solid #e5e7eb;
          padding-bottom: 10px;
        }
        .info-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 20px;
          margin-bottom: 20px;
        }
        .info-item {
          display: flex;
          justify-content: space-between;
          padding: 8px 0;
          border-bottom: 1px solid #f3f4f6;
        }
        .info-label {
          font-weight: bold;
          color: #6b7280;
        }
        .info-value {
          color: #1f2937;
        }
        .payment-cards {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 15px;
          margin: 20px 0;
        }
        .payment-card {
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          padding: 15px;
          text-align: center;
        }
        .payment-card h3 {
          margin: 0 0 10px 0;
          color: #6b7280;
          font-size: 14px;
        }
        .payment-card .amount {
          font-size: 18px;
          font-weight: bold;
          color: #2563eb;
        }
        .customer-section {
          background: #f9fafb;
          padding: 20px;
          border-radius: 8px;
          margin: 20px 0;
        }
        .member-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 15px;
          margin-top: 15px;
        }
        .member-card {
          background: white;
          border: 1px solid #e5e7eb;
          border-radius: 6px;
          padding: 15px;
        }
        .status-badge {
          display: inline-block;
          padding: 4px 12px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: bold;
        }
        .status-completed {
          background: #dcfce7;
          color: #166534;
        }
        .status-active {
          background: #fef3c7;
          color: #92400e;
        }
        @media print {
          body { margin: 0; }
          .no-print { display: none; }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>Offer Letter Details</h1>
        <p>Plot ${detailsData?.plot_number || offerLetter.plot_number} • ${detailsData?.lead_file || offerLetter.lead_file}</p>
        <span class="status-badge ${(detailsData?.is_completed ?? offerLetter.is_completed) ? 'status-completed' : 'status-active'}">
          ${(detailsData?.is_completed ?? offerLetter.is_completed) ? 'Completed' : 'Active'}
        </span>
      </div>

      <div class="section">
        <h2>Basic Information</h2>
        <div class="info-grid">
          <div>
            <div class="info-item">
              <span class="info-label">Plot Number:</span>
              <span class="info-value">${detailsData?.plot_number || offerLetter.plot_number}</span>
            </div>
            <div class="info-item">
              <span class="info-label">Booking ID:</span>
              <span class="info-value">${detailsData?.booking_id || offerLetter.booking_id}</span>
            </div>
            <div class="info-item">
              <span class="info-label">Lead File:</span>
              <span class="info-value">${detailsData?.lead_file || offerLetter.lead_file}</span>
            </div>
          </div>
          <div>
            <div class="info-item">
              <span class="info-label">Customer Type:</span>
              <span class="info-value">${(detailsData?.customer_type || offerLetter.customer_type).charAt(0).toUpperCase() + (detailsData?.customer_type || offerLetter.customer_type).slice(1)}</span>
            </div>
            <div class="info-item">
              <span class="info-label">Date:</span>
              <span class="info-value">${new Date(detailsData?.date || offerLetter.date).toLocaleDateString()}</span>
            </div>
            ${detailsData?.step ? `
            <div class="info-item">
              <span class="info-label">Step:</span>
              <span class="info-value">Step ${detailsData.step}</span>
            </div>
            ` : ''}
          </div>
        </div>
      </div>

      ${detailsData?.payment_plan && detailsData.payment_plan.length > 0 ? `
      <div class="section">
        <h2>Payment Plan</h2>
        ${detailsData.payment_plan.map(plan => `
          <div style="margin-bottom: 20px;">
            <h3>Plot: ${plan.plot_no}</h3>
            <div class="payment-cards">
              <div class="payment-card">
                <h3>Total Price</h3>
                <div class="amount">${formatCurrency(plan.total_cash_price)}</div>
              </div>
              <div class="payment-card">
                <h3>Deposit</h3>
                <div class="amount">${formatCurrency(plan.deposit)}</div>
              </div>
              <div class="payment-card">
                <h3>Monthly Payment</h3>
                <div class="amount">${formatCurrency(plan.monthly_installments)}</div>
              </div>
              <div class="payment-card">
                <h3>Installments</h3>
                <div class="amount">${plan.no_of_instalments} ${plan.no_of_instalments === 1 ? 'Payment' : 'Months'}</div>
              </div>
            </div>
          </div>
        `).join('')}
      </div>
      ` : ''}

      ${detailsData?.individuals && detailsData.individuals.length > 0 ? `
      <div class="section">
        <h2>Individual Customer</h2>
        ${detailsData.individuals.map(individual => `
          <div class="customer-section">
            <h3>${individual.first_name} ${individual.last_name}</h3>
            <div class="info-grid">
              <div>
                <div class="info-item">
                  <span class="info-label">National ID:</span>
                  <span class="info-value">${individual.national_id}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">KRA PIN:</span>
                  <span class="info-value">${individual.KRA_Pin}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Date of Birth:</span>
                  <span class="info-value">${new Date(individual.DOB).toLocaleDateString()}</span>
                </div>
              </div>
              <div>
                <div class="info-item">
                  <span class="info-label">Phone:</span>
                  <span class="info-value">${individual.country_code || ''} ${individual.phone}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Email:</span>
                  <span class="info-value">${individual.email}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Location:</span>
                  <span class="info-value">${individual.city}, ${individual.country}</span>
                </div>
              </div>
            </div>
          </div>
        `).join('')}
      </div>
      ` : ''}

      ${detailsData?.partners && detailsData.partners.length > 0 ? `
      <div class="section">
        <h2>Partner Customers</h2>
        ${detailsData.partners.map((partner, index) => `
          <div class="customer-section">
            <h3>Partner ${index + 1}: ${partner.first_name} ${partner.last_name}</h3>
            <div class="info-grid">
              <div>
                <div class="info-item">
                  <span class="info-label">National ID:</span>
                  <span class="info-value">${partner.national_id}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Phone:</span>
                  <span class="info-value">${partner.country_code} ${partner.phone}</span>
                </div>
              </div>
              <div>
                <div class="info-item">
                  <span class="info-label">Email:</span>
                  <span class="info-value">${partner.email}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Location:</span>
                  <span class="info-value">${partner.city}, ${partner.country}</span>
                </div>
              </div>
            </div>
          </div>
        `).join('')}
      </div>
      ` : ''}

      ${detailsData?.companies && detailsData.companies.length > 0 ? `
      <div class="section">
        <h2>Company Customer</h2>
        ${detailsData.companies.map(company => `
          <div class="customer-section">
            <h3>${company.company_name}</h3>
            <div class="info-grid">
              <div>
                <div class="info-item">
                  <span class="info-label">Registration No:</span>
                  <span class="info-value">${company.company_registration_number}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">KRA PIN:</span>
                  <span class="info-value">${company.company_kra}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Phone:</span>
                  <span class="info-value">${company.phone}</span>
                </div>
              </div>
              <div>
                <div class="info-item">
                  <span class="info-label">Email:</span>
                  <span class="info-value">${company.email}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Address:</span>
                  <span class="info-value">${company.address}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Location:</span>
                  <span class="info-value">${company.city}, ${company.country}</span>
                </div>
              </div>
            </div>
            ${company.directors && company.directors.length > 0 ? `
            <h4>Directors</h4>
            <div class="member-grid">
              ${company.directors.map(director => `
                <div class="member-card">
                  <h5>${director.first_name} ${director.last_name}</h5>
                  <p><strong>ID:</strong> ${director.national_id}</p>
                  <p><strong>Phone:</strong> ${director.phone}</p>
                  <p><strong>Email:</strong> ${director.email}</p>
                </div>
              `).join('')}
            </div>
            ` : ''}
          </div>
        `).join('')}
      </div>
      ` : ''}

      ${detailsData?.groups && detailsData.groups.length > 0 ? `
      <div class="section">
        <h2>Group Customer</h2>
        ${detailsData.groups.map(group => `
          <div class="customer-section">
            <h3>${group.group_name}</h3>
            <div class="info-grid">
              <div>
                <div class="info-item">
                  <span class="info-label">Group Code:</span>
                  <span class="info-value">${group.group_code}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">KRA PIN:</span>
                  <span class="info-value">${group.Group_KRA_PIN}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Phone:</span>
                  <span class="info-value">${group.group_phone}</span>
                </div>
              </div>
              <div>
                <div class="info-item">
                  <span class="info-label">Email:</span>
                  <span class="info-value">${group.group_email}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Location:</span>
                  <span class="info-value">${group.Group_city}, ${group.Group_country}</span>
                </div>
              </div>
            </div>
            ${group.members && group.members.length > 0 ? `
            <h4>Group Members (${group.members.length})</h4>
            <div class="member-grid">
              ${group.members.map(member => `
                <div class="member-card">
                  <h5>${member.first_name} ${member.last_name}</h5>
                  <p><strong>Member ID:</strong> ${member.member_id}</p>
                  <p><strong>National ID:</strong> ${member.national_id}</p>
                  <p><strong>Phone:</strong> ${member.country_codes} ${member.phone}</p>
                  <p><strong>Email:</strong> ${member.email}</p>
                </div>
              `).join('')}
            </div>
            ` : ''}
          </div>
        `).join('')}
      </div>
      ` : ''}

      ${detailsData?.terms_conditions && detailsData.terms_conditions.length > 0 ? `
      <div class="section">
        <h2>Terms & Conditions</h2>
        ${detailsData.terms_conditions.map(terms => `
          <div class="customer-section">
            <div class="info-item">
              <span class="info-label">Acceptance Date:</span>
              <span class="info-value">${new Date(terms.acceptance_date).toLocaleDateString()}</span>
            </div>
            <div style="margin-top: 15px; padding: 15px; background: white; border-radius: 6px;">
              <p>${terms.content}</p>
            </div>
          </div>
        `).join('')}
      </div>
      ` : ''}

      <div class="section no-print" style="text-align: center; margin-top: 40px;">
        <button onclick="window.print()" style="background: #2563eb; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer; margin-right: 10px;">Print PDF</button>
        <button onclick="window.close()" style="background: #6b7280; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer;">Close</button>
      </div>
    </body>
    </html>
  `;

  printWindow.document.write(htmlContent);
  printWindow.document.close();
  
  // Auto-focus the print window
  printWindow.focus();
};

// Export offer letters data to CSV
export const exportOfferLettersToCSV = (offerLetters: OfferLetterCategory[]) => {
  const headers = [
    'Plot Number',
    'Booking ID',
    'Lead File',
    'Customer Type',
    'Status',
    'Date'
  ];

  const csvContent = [
    headers.join(','),
    ...offerLetters.map(letter => [
      letter.plot_number,
      letter.booking_id,
      letter.lead_file,
      letter.customer_type,
      letter.is_completed ? 'Completed' : 'Active',
      new Date(letter.date).toLocaleDateString()
    ].join(','))
  ].join('\n');

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', `offer-letters-${new Date().toISOString().split('T')[0]}.csv`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};
