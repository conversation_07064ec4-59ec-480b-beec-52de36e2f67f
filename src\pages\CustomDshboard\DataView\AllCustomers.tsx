import BaseModal from "@/components/custom/modals/BaseModal";
import { ColumnDef } from "@tanstack/react-table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Settings, Plus } from "lucide-react";
import { DataTable } from "@/components/custom/tables/Table1";
import { useState } from "react";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import AddDigitalLeadModal from "../DigitalView/NewLead";

interface DigitalLeads {
  customerNo: string;
  name: string;
  nationalIdOrPassportNo: string;
  kraPin: string;
  phoneno: string;
  primaryEmail: string;
  allocatedmarketer: string | null;
  date: string;
  status: "pending" | "active" | "closed";
}

interface ProspectsTableModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function DataLeadsTableModal({ open, onOpenChange }: ProspectsTableModalProps) {
  const [activeTab, setActiveTab] = useState<"allocated" | "unallocated">("allocated");
  const [isAddLeadModalOpen, setIsAddLeadModalOpen] = useState(false);

  const data: DigitalLeads[] = [
    {
      customerNo: "CUST001",
      name: "Ken Maish",
      nationalIdOrPassportNo: "12345678",
      kraPin: "A123456789B",
      phoneno: "0712345678",
      primaryEmail: "<EMAIL>",
      allocatedmarketer: "Jane Doe",
      date: "2025-05-29",
      status: "active",
    },
    {
      customerNo: "CUST002",
      name: "Jane Wanjiru",
      nationalIdOrPassportNo: "87654321",
      kraPin: "B987654321A",
      phoneno: "0723456789",
      primaryEmail: "<EMAIL>",
      allocatedmarketer: null,
      date: "2025-05-28",
      status: "pending",
    },
    {
      customerNo: "CUST003",
      name: "John Kamau",
      nationalIdOrPassportNo: "45678912",
      kraPin: "C456789123D",
      phoneno: "0734567890",
      primaryEmail: "<EMAIL>",
      allocatedmarketer: "Alice Doe",
      date: "2025-05-27",
      status: "closed",
    },
  ];

  const filteredData =
    activeTab === "allocated"
      ? data.filter((lead) => lead.allocatedmarketer !== null && lead.allocatedmarketer !== "")
      : data.filter((lead) => lead.allocatedmarketer === null || lead.allocatedmarketer === "");

  const columns: ColumnDef<DigitalLeads>[] = [
    {
      accessorKey: "customerNo",
      header: "Customer No",
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: "name",
      header: "Name",
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: "nationalIdOrPassportNo",
      header: "National ID/Passport No",
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: "kraPin",
      header: "KRA PIN",
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: "phoneno",
      header: "Phone No",
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: "primaryEmail",
      header: "Primary Email",
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: "date",
      header: "Date Added",
      cell: (info) =>
        new Date(info.getValue() as string).toLocaleDateString("en-US", {
          month: "short",
          day: "numeric",
          year: "numeric",
        }),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: (info) => {
        const status = info.getValue() as string;
        const statusStyles: { [key: string]: string } = {
          pending: "bg-yellow-100 text-yellow-800",
          active: "bg-green-100 text-green-800",
          closed: "bg-red-100 text-red-800",
        };
        return (
          <span
            className={`px-2 py-1 rounded-full text-xs font-semibold ${
              statusStyles[status] || "bg-gray-100 text-gray-800"
            }`}
          >
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </span>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger>
            <Settings size={20} className="text-gray-500 hover:text-gray-700" />
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                console.log(`View details for ${row.original.name}`, row.original);
              }}
            >
              View Details
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                console.log(`Edit row ${row.original.name}`);
              }}
            >
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                console.log(`Delete row ${row.original.name}`);
              }}
            >
              Delete
            </DropdownMenuItem>
            {row.original.allocatedmarketer === null && (
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  console.log(`Allocate lead ${row.original.name}`);
                }}
              >
                Allocate
              </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      ),
    },
  ];

  return (
    <BaseModal
      open={open}
      onOpenChange={onOpenChange}
      title="Leads Management"
      description="View and manage allocated and unallocated leads"
      className="max-w-4xl"
      size="full"
    >
      <div className="flex justify-end mb-4">
        <PrimaryButton onClick={() => setIsAddLeadModalOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Add Lead
        </PrimaryButton>
      </div>
      <div className="p-4 bg-white dark:bg-gray-900 rounded-md border-2 border-indigo-500 dark:border-indigo-400">
        <div className="flex space-x-4 mb-4">
          <button
            className={`px-4 py-2 rounded-md ${
              activeTab === "allocated"
                ? "bg-indigo-500 text-white"
                : "bg-gray-200 text-gray-700"
            }`}
            onClick={() => setActiveTab("allocated")}
          >
            Allocated Leads
          </button>
          <button
            className={`px-4 py-2 rounded-md ${
              activeTab === "unallocated"
                ? "bg-indigo-500 text-white"
                : "bg-gray-200 text-gray-700"
            }`}
            onClick={() => setActiveTab("unallocated")}
          >
            Unallocated Leads
          </button>
        </div>

        {filteredData.length === 0 ? (
          <div className="text-center text-gray-500 dark:text-gray-400 py-4">
            No {activeTab === "allocated" ? "allocated" : "unallocated"} leads found.
          </div>
        ) : (
          <DataTable<DigitalLeads>
            data={filteredData}
            columns={columns}
            title={activeTab === "allocated" ? "Allocated Leads" : "Unallocated Leads"}
            enableExportToExcel={true}
            enablePrintPdf={true}
            enableColumnFilters={true}
            enablePagination={true}
            enableSorting={true}
            enableToolbar={true}
            containerClassName="max-w-full"
            tableClassName="w-full border-collapse border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 text-sm text-left text-gray-700 dark:text-gray-300 shadow-md rounded-lg overflow-hidden"
            tHeadClassName="bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 font-semibold"
            tHeadCellsClassName="px-4 py-2 border-b border-gray-200 dark:bordertheaderCellsClassName="px-4 py-2"
            tBodyClassName="divide-y divide-gray-200 dark:divide-gray-700"
            tBodyTrClassName="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            tBodyCellsClassName="px-4 py-2"
          />
        )}
      </div>
      <AddDigitalLeadModal
        isOpen={isAddLeadModalOpen}
        onOpenChange={setIsAddLeadModalOpen}
      />
    </BaseModal>
  );
}