import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Code, Eye } from 'lucide-react';

type Props = {
  code: string;
  preview?: React.ReactNode;
  title?: string;
};

const CodeDisplay = ({ code, preview, title }: Props) => {
  const [showCode, setShowCode] = useState(false);

  const copyToClipboard = () => {
    navigator.clipboard.writeText(code);
  };

  return (
    <div className="mt-4">
      <div className="flex items-center justify-between mb-2">
        {title && <span className="text-sm text-muted-foreground">{title}</span>}
        <div className="flex gap-2">
          <Button
            onClick={() => setShowCode(!showCode)}
            variant="outline"
            size="sm"
            className="text-xs flex items-center gap-1"
          >
            {showCode ? <Eye size={14} /> : <Code size={14} />}
            {showCode ? 'Show Preview' : 'Show Code'}
          </Button>
          
          {showCode && (
            <Button 
              onClick={copyToClipboard} 
              variant="outline"
              size="sm"
              className="text-xs"
            >
              Copy
            </Button>
          )}
        </div>
      </div>
      
      {showCode ? (
        <div className="relative">
          <pre className="p-4 bg-muted rounded-md overflow-x-auto text-xs font-mono">
            <code>{code}</code>
          </pre>
        </div>
      ) : (
        <div className="bg-background border rounded-md p-4">
          {preview}
        </div>
      )}
    </div>
  );
};

export default CodeDisplay; 