import * as React from "react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { CalendarIcon } from "lucide-react";

interface SingleDatePickerProps {
  label?: string;
  value?: Date;
  onChange?: (date: Date) => void;
  fromYear?: number;
  toYear?: number;
  disablePastDates?: boolean;
}

export function SingleDatePicker({
  label = "Pick a date",
  value,
  onChange,
  fromYear = 2020,
  toYear = 2030,
  disablePastDates = false,
}: SingleDatePickerProps) {
  const [selectedDate, setSelectedDate] = React.useState<Date | undefined>(
    value
  );

  React.useEffect(() => {
    if (selectedDate && onChange) {
      onChange(selectedDate);
    }
  }, [selectedDate, onChange]);

  // Get today's date for comparison
  const today = new Date();
  today.setHours(0, 0, 0, 0); // Reset time to start of day

  return (
    <div className="flex flex-col gap-1 w-full ">
      {label && <label className="text-sm font-medium">{label}</label>}

      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className="flex items-center justify-start text-left font-normal border border-accent  dark:border-white/40"
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {selectedDate ? selectedDate.toLocaleDateString() : "Select date"}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            mode="single"
            selected={selectedDate}
            onSelect={setSelectedDate}
            captionLayout="dropdown"
            fromYear={fromYear}
            toYear={toYear}
            disabled={disablePastDates ? (date) => date < today : undefined}
            initialFocus
          />
        </PopoverContent>
      </Popover>
    </div>
  );
}
