import { contentHeader, noAuthHeader, noContentHeader } from "@/utils/header";
import { apiSlice } from "../apiSlice";

export const ticketsApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    fetchTicketsCategories: builder.query({
      query: (params) => ({
        url: "/tickets-categories",
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      providesTags: ["Tickets"],
    }),

    createTicketsCategories: builder.mutation({
      query: (data) => ({
        url: "/tickets-categories/",
        method: "POST",
        body: data,
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Tickets"],
    }),

    fetchTickets: builder.query({
      query: (params) => ({
        url: "/tickets",
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      providesTags: ["Tickets"],
    }),

    fetchTicketDetails: builder.query({
      query: (id) => ({
        url: "/tickets/" + id,
        method: "GET",
        headers: noAuthHeader(),
      }),
      providesTags: ["Tickets"],
    }),

    createTicket: builder.mutation({
      query: (data) => ({
        url: "/tickets/",
        method: "POST",
        body: data,
        headers: noContentHeader(),
      }),
      invalidatesTags: ["Tickets"],
    }),

    updateTicket: builder.mutation({
      query: (data) => ({
        url: "/tickets/" + data.id + "/",
        method: "PATCH",
        body: data,
        headers: noContentHeader(),
      }),
      invalidatesTags: ["Tickets"],
    }),

    fetchTicketLogs: builder.query({
      query: (params) => ({
        url: "/tickets-action-logs",
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      providesTags: ["Tickets"],
    }),

    fetchTicketSources: builder.query({
      query: (params) => ({
        url: "/tickets-sources",
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      providesTags: ["Tickets"],
    }),

    createTicketSource: builder.mutation({
      query: (data) => ({
        url: "/tickets-sources/",
        method: "POST",
        body: data,
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Tickets"],
    }),

    fetchTicketMessages: builder.query({
      query: (params) => ({
        url: "/tickets-messages",
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      providesTags: ["Tickets"],
    }),

    createTicketMessage: builder.mutation({
      query: (data) => ({
        url: "/tickets-messages/",
        method: "POST",
        body: data,
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Tickets"],
    }),
  }),
});

export const {
  useFetchTicketsQuery,
  useLazyFetchTicketsQuery,
  useCreateTicketMutation,
  useFetchTicketDetailsQuery,
  useUpdateTicketMutation,

  useCreateTicketsCategoriesMutation,
  useFetchTicketsCategoriesQuery,
  useLazyFetchTicketsCategoriesQuery,

  useFetchTicketLogsQuery,

  useCreateTicketSourceMutation,
  useFetchTicketSourcesQuery,
  useLazyFetchTicketSourcesQuery,

  useFetchTicketMessagesQuery,
  useCreateTicketMessageMutation,
} = ticketsApiSlice;
