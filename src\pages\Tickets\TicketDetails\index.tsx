import { Screen } from "@/app-components/layout/screen";
import {
  useFetchTicketDetailsQuery,
  useFetchTicketMessagesQuery,
  useUpdateTicketMutation,
} from "@/redux/slices/tickets";
import { useLocation } from "react-router-dom";
import TicketDetailsSideBar from "./TicketDetailsSideBar";
import { useAuthHook } from "@/utils/useAuthHook";
import MessageBox from "../TicketMessages/MessageBox";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { ChevronDown, ChevronUp, Edit } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import CustomSelectField from "@/components/CustomSelectField";
import { useLazyGetUsersQuery } from "@/redux/slices/user";
import { toast } from "sonner";
import { formatDateTime } from "@/utils/formatDate";

type Props = {};

const index = ({}: Props) => {
  const location = useLocation();
  const ticketId = location.pathname.split("/")[2];
  const { user_details } = useAuthHook();
  const [expanded, setExpanded] = useState(false);
  const [expandPriority, setExpandPriority] = useState(false);
  const [status, setStatus] = useState("");
  const [newUser, setNewUser] = useState("");
  const [priority, setPriority] = useState("");

  const currentUser = user_details?.employee_no
    ? user_details?.employee_no
    : "";

  const { data: ticket, isLoading: tloading } =
    useFetchTicketDetailsQuery(ticketId);
  const { data: messages, isLoading: mloading } = useFetchTicketMessagesQuery({
    ticket: ticketId,
  });
  const [fetchUsers, { data: usersList, isLoading }] = useLazyGetUsersQuery();
  const [editStatus, { isLoading: sUpdating }] = useUpdateTicketMutation();

  const handleStatusChange = async () => {
    const isEscalatedUser = Array.isArray(ticket?.escalations)
      ? ticket.escalations.some((e: any) => e.user === currentUser)
      : false;

    if (currentUser !== ticket.user && !isEscalatedUser) {
      toast.error("Only the creator or escalated to person can edit");
      return;
    }

    let formData: any = {
      id: ticketId,
    };

    if (status) {
      formData = { ...formData, status };
    }

    if (priority) {
      formData = { ...formData, priority };
    }

    if (newUser) {
      formData = { ...formData, user: newUser };
    }
    try {
      const res = await editStatus(formData).unwrap();
      if (res) {
        toast.success("Ticket status updated successfully");
      }
    } catch (error: any) {
      console.log("Error: ", error);
      if (error?.data) {
        toast.error(`${error?.data?.error}`);
      } else {
        toast.error(`Error updating ticket status`);
      }
      return;
    }
  };

  return (
    <Screen>
      <div className="min-h-screen flex flex-col">
        <div className="grid lg:grid-cols-5 md:grid-cols-4 sm:grid-cols-1">
          <div className=" w-full border-r shrink-0 md:h-[calc(100vh-44px)] overflow-auto">
            {tloading ? (
              <div className="w-full flex items-center justify-center">
                <SpinnerTemp type="spinner-double" size="sm" />
              </div>
            ) : (
              ticket && <TicketDetailsSideBar ticket={ticket} />
            )}
            {/* PropectDetails {prospect_id} */}
          </div>

          <div className="lg:col-span-3 md:col-span-2 sm:col-span-1 lg:px-4 md:px-2 flex flex-1 flex-col bg-gray-50 md:h-[calc(100vh-44px)] overflow-hidden">
            <div className="flex-1 overflow-auto dark:bg-gray-900">
              <div className="py-4 px-2 md:py-6 mx-auto space-y-4">
                {/* Body sections */}
                <div className="py-4 px-5 rounded border border-secondary bg-secondary shadow-lg">
                  <p className=" font-bold">Ticket Descriptions</p>
                  <p className="text-sm">{ticket?.description}</p>
                </div>

                <MessageBox
                  ticket={ticket}
                  currentUser={currentUser}
                  messages={messages}
                  mloading={mloading}
                />

                {/* <TicketDetailsBody prospect_id={prospect_id} /> */}
              </div>
            </div>
          </div>
          <div className="p-4 w-full border-l shrink-0 md:h-[calc(100vh-44px)] overflow-auto">
            <p className="text-xl font-bold">Ticket Actions</p>

            {/* update Status  */}
            <div className=" mt-3">
              <div
                className="flex shadow items-center justify-between cursor-pointer group transition-colors bg-accent/50 hover:bg-accent dark:hover:bg-gray-800 p-2 rounded-md"
                onClick={() => setExpanded(!expanded)}
              >
                <div className="flex items-center text-sm font-medium">
                  <Edit className="h-4 w-4 mr-2 text-blue-500" />
                  <span className="text-blue-500">Update Ticket Status</span>
                </div>
                <Button
                  onClick={() => setExpanded(!expanded)}
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 opacity-70 group-hover:opacity-100"
                >
                  {expanded ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </Button>
              </div>
              {expanded && (
                <div className="p-2 bg-accent">
                  <div className="space-y-2 flex flex-col">
                    <label className="px-1 text-xs">
                      {" "}
                      Current Status{" "}
                      <span className="text-destructive">{ticket?.status}</span>
                    </label>
                    <select
                      className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                      name="status"
                      value={status}
                      onChange={(e) => setStatus(e.target.value)}
                      required
                    >
                      <option value="" className="text-gray-400">
                        Select Status
                      </option>
                      <option value="open">open</option>
                      <option value="in_progress">In progress</option>
                      <option value="resolved">resolved</option>
                      <option value="closed">closed</option>
                      <option value="escalated">escalate</option>
                    </select>
                  </div>
                  {status == "escalated" && (
                    <div className="space-y-2 flex flex-col my-4">
                      <label className="px-1 text-xs">Escalate to:</label>
                      <CustomSelectField
                        valueField="employee_no"
                        labelField="fullnames"
                        data={usersList?.data?.results}
                        queryFunc={fetchUsers}
                        setValue={setNewUser}
                        useSearchField={true}
                      />
                    </div>
                  )}
                  {sUpdating ? (
                    <SpinnerTemp type="spinner-double" size="sm" />
                  ) : (
                    <Button
                      onClick={handleStatusChange}
                      variant="default"
                      className="w-full my-3"
                    >
                      Submit
                    </Button>
                  )}
                </div>
              )}
            </div>

            {/* update priority  */}
            <div className=" mt-3">
              <div
                className="flex shadow items-center justify-between cursor-pointer group transition-colors bg-amber-300/50 hover:bg-amber-300 dark:hover:bg-gray-800 p-2 rounded-md"
                onClick={() => setExpandPriority(!expandPriority)}
              >
                <div className="flex items-center text-sm font-medium">
                  <Edit className="h-4 w-4 mr-2 text-amber-700" />
                  <span className="text-amber-700">Update Ticket Priority</span>
                </div>
                <Button
                  onClick={() => setExpandPriority(!expandPriority)}
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 opacity-70 group-hover:opacity-100"
                >
                  {expandPriority ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </Button>
              </div>
              {expandPriority && (
                <div className="p-2 bg-amber-300">
                  <div className="space-y-2 flex flex-col">
                    <label className="px-1 text-xs">
                      {" "}
                      Current Priority:{" "}
                      <b className="text-destructive capitalize">
                        {ticket?.priority}
                      </b>
                    </label>
                    <select
                      className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                      name="priority"
                      value={priority}
                      onChange={(e) => setPriority(e.target.value)}
                      required
                    >
                      <option value="" className="text-gray-400">
                        Select Priority
                      </option>
                      <option value="low">Low</option>
                      <option value="medium">Medium</option>
                      <option value="high">High</option>
                      <option value="urgent">Urgent</option>
                    </select>
                  </div>
                  {sUpdating ? (
                    <SpinnerTemp type="spinner-double" size="sm" />
                  ) : (
                    <Button
                      onClick={handleStatusChange}
                      variant="default"
                      className="w-full my-3"
                    >
                      Submit
                    </Button>
                  )}
                </div>
              )}
            </div>

            <p className="text-xl font-bold mt-5">Ticket Escalations</p>
            <div className="mt-3 shadow p-2">
              <ul className="list list-inside text-sm">
                {ticket?.escalations?.map((es: any) => (
                  <li
                    key={es?.id}
                    className="capitalize bg-green-200 p-2 rounded my-1"
                  >
                    <span className="flex flex-col">
                      {es?.user_name?.toLowerCase()}
                      <small>{formatDateTime(es?.created_at)}</small>
                    </span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </Screen>
  );
};

export default index;
