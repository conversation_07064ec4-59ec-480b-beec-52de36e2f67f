import React, { ReactNode } from 'react';
// import { useFormContext } from './FormContext';
import { cn } from '@/lib/utils';

interface FormStepProps {
  children: ReactNode;
  title?: string;
  description?: string;
  className?: string;
}

const FormStep: React.FC<FormStepProps> = ({ 
  children, 
  title, 
  description,
  className 
}) => {
  return (
    <div className={cn("space-y-6", className)}>
      {title && (
        <div className="space-y-1">
          <h3 className="text-lg font-medium">{title}</h3>
          {description && <p className="text-sm text-muted-foreground">{description}</p>}
        </div>
      )}
      <div>{children}</div>
    </div>
  );
};

export default FormStep;