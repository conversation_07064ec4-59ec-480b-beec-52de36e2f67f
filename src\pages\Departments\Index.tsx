import { Screen } from "@/app-components/layout/screen";
import {
  OutlinedButton,
  PrimaryButton,
} from "@/components/custom/buttons/buttons";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { DataTable } from "@/components/custom/tables/Table1";
import { useGetDepartmentsQuery } from "@/redux/slices/user";
import { ColumnDef } from "@tanstack/react-table";
import { motion } from "framer-motion";
import { KeyRound } from "lucide-react";
import { useEffect, useState } from "react";
import DepartmentPermissionsModal from "./DepartmentPermissionsModal";

interface DepartmentTypes {
  dp_id: number;
  dp_name: string;
  dep_head_name?: string;
  dep_head_code?: string;
  inactive?: boolean;
}

const Index = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [departmentPermissionsData, setDepartmentPermissionsData] =
    useState<DepartmentTypes | null>(null);

  const { data: departments, isLoading: loadingDepartments } = useGetDepartmentsQuery({
    page: currentPage,
    page_size: itemsPerPage,
  });

  const [cols, setCols] = useState<any>([]);

  useEffect(() => {
    setCols(columns);
  }, [loadingDepartments]);

  const columns: ColumnDef<DepartmentTypes>[] = [
    {
      accessorKey: "dp_name",
      header: "Department Name",
      cell: (info) => (info.getValue() as string) || "N/A",
      enableColumnFilter: false,
    },
    {
      accessorKey: "dep_head_name",
      header: "Department Head Name",
      cell: (info) => (info.getValue() as string) || "N/A",
      enableColumnFilter: false,
    },
    {
      accessorKey: "dep_head_code",
      header: "Department Head Code",
      cell: (info) => (info.getValue() as string) || "N/A",
      enableColumnFilter: false,
    },
    {
      id: "actions",
      header: "Action",
      cell: ({ row }) => {
        const department = row.original;
        return (
          <div className="flex space-x-2 justify-center">
            <PrimaryButton
              variant="outline"
              size="sm"
              onClick={() => setDepartmentPermissionsData(department)}
              className="bg-white text-blue-500 border-blue-300 hover:bg-blue-50 flex items-center space-x-1"
            >
              <span title="View Permissions">
                <KeyRound />
              </span>
            </PrimaryButton>
          </div>
        );
      },
      enableColumnFilter: false,
    },
  ];

  return (
    <Screen>
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-3xl font-bold text-gray-800">Departments</h1>
        </div>
        <div className="space-y-4">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-x-auto"
          >
            {loadingDepartments ? (
              <div className="w-full flex items-center justify-center">
                <SpinnerTemp type="spinner-double" size="md" />
              </div>
            ) : !departments || departments?.length === 0 ? (
              <p className="px-2">No Departments found</p>
            ) : (
              <DataTable<DepartmentTypes>
                data={departments}
                columns={cols}
                enableToolbar={true}
                enableExportToExcel={true}
                enablePagination={true}
                enableColumnFilters={true}
                enableSorting={true}
                enablePrintPdf={true}
                tableClassName="border-collapse"
                tHeadClassName="bg-gray-50"
                tHeadCellsClassName="text-xs uppercase text-gray-600 font-semibold"
                tBodyTrClassName="hover:bg-gray-50"
                tBodyCellsClassName="border-t"
                currentPage={currentPage}
                setCurrentPage={setCurrentPage}
                itemsPerPage={itemsPerPage}
                setItemsPerPage={setItemsPerPage}
                totalItems={departments?.length || 0}
              />
            )}
          </motion.div>
        </div>

        {departmentPermissionsData && (
          <DepartmentPermissionsModal
            isOpen={!!departmentPermissionsData}
            onClose={() => setDepartmentPermissionsData(null)}
            department={departmentPermissionsData}
          />
        )}
      </motion.div>
    </Screen>
  );
};

export default Index;
