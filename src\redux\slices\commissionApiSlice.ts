import { noAuthHeader } from "@/utils/header";
import { apiSlice } from "../apiSlice";

// Types for Commission Headers and Lines
export interface CommissionHeader {
  id: number;
  period_start_date: string;
  period_end_date: string;
  role: string;
  Deposit_amount: number;
  deposit_perc: number;
  deposit_commission: number;
  installment_amount: number;
  installment_perc: number;
  installment_commission: number;
  Total_commission: number;
  Tl_gained_comm_from_members: number;
  rm_achieved_MIB: number;
  rm_commission_rate: number;
  rm_commission_amount: number;
  commisison_payable_TL: number;
  emp_no_id: string;
}

export interface CommissionLine {
  id: number;
  period_start_date: string;
  period_end_date: string;
  transaction_date: string;
  plot_number: string;
  new_deposits_collected: number;
  installments_collected: number;
  marketer_no_id: string;
}

export interface CommissionHeaderApiResponse {
  Title: string;
  "Total Results": number;
  count: number;
  num_pages: number;
  current_page: number;
  results: CommissionHeader[];
}

export interface CommissionLineApiResponse {
  Title: string;
  "Total Results": number;
  count: number;
  num_pages: number;
  current_page: number;
  results: CommissionLine[];
}

// API Slice
export const commissionApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getCommissionHeaders: builder.query<CommissionHeaderApiResponse, { MARKETING_PERIOD: string; MARKETER_EMPLOYEE_NO: string; page?: number; page_size?: number }>({
      query: ({ MARKETING_PERIOD, MARKETER_EMPLOYEE_NO, page = 1, page_size = 20 }) => ({
        url: `/commission-headers-report/`,
        method: "GET",
        headers: noAuthHeader(),
        params: {
          MARKETING_PERIOD,
          MARKETER_EMPLOYEE_NO,
          page,
          page_size,
        },
      }),
    }),
    getCommissionLines: builder.query<CommissionLineApiResponse, { MARKETING_PERIOD: string; MARKETER_EMPLOYEE_NO: string; page?: number; page_size?: number }>({
      query: ({ MARKETING_PERIOD, MARKETER_EMPLOYEE_NO, page = 1, page_size = 20 }) => ({
        url: `/commission-lines-report/`,
        method: "GET",
        headers: noAuthHeader(),
        params: {
          MARKETING_PERIOD,
          MARKETER_EMPLOYEE_NO,
          page,
          page_size,
        },
      }),
    }),

    // Individual Marketer Commission Headers
    getIndividualCommissionHeaders: builder.query<any, {
      emp_no?: string;
      period_start_date?: string;
      period_end_date?: string;
      role?: string;
      search?: string;
      ordering?: string;
      page?: number;
      page_size?: number;
    }>({
      query: (params = {}) => {
        const queryParams = {
          ...params,
          page: params.page || 1,
          page_size: params.page_size || 20,
        };
        console.log("🔍 COMMISSION HEADERS API CALL:", {
          url: `/commission-headers/`,
          params: queryParams
        });
        return {
          url: `/commission-headers/`,
          method: "GET",
          headers: noAuthHeader(),
          params: queryParams,
        };
      },
      transformResponse: (response: any) => {
        console.log("🔍 COMMISSION HEADERS API RAW RESPONSE:", response);
        console.log("🔍 Headers Response type:", typeof response);
        console.log("🔍 Headers Response keys:", Object.keys(response || {}));
        console.log("🔍 Headers Response.data:", response?.data);
        console.log("🔍 Headers Response.data.results:", response?.data?.results);
        console.log("🔍 Headers Response.data.results length:", response?.data?.results?.length);
        console.log("🔍 Headers Response.message:", response?.message);

        // The API returns data in response.data structure
        const transformedResponse = {
          ...response,
          data: response.data || response
        };

        console.log("🔍 HEADERS TRANSFORMED RESPONSE:", transformedResponse);
        return transformedResponse;
      },
      transformErrorResponse: (error) => {
        console.error("Individual Commission Headers API Error:", error);
        return error;
      },
      providesTags: ["Sales"],
    }),

    // Individual Marketer Commission Lines
    getIndividualCommissionLines: builder.query<any, {
      marketer_no?: string;
      period_start_date?: string;
      period_end_date?: string;
      plot_number?: string;
      search?: string;
      ordering?: string;
      page?: number;
      page_size?: number;
    }>({
      query: (params = {}) => {
        const queryParams = {
          ...params,
          page: params.page || 1,
          page_size: params.page_size || 20,
        };
        console.log("🔍 COMMISSION LINES API CALL:", {
          url: `/commission-lines/`,
          params: queryParams
        });
        return {
          url: `/commission-lines/`,
          method: "GET",
          headers: noAuthHeader(),
          params: queryParams,
        };
      },
      transformResponse: (response: any) => {
        console.log("🔍 COMMISSION LINES API RAW RESPONSE:", response);
        console.log("🔍 Response type:", typeof response);
        console.log("🔍 Response keys:", Object.keys(response || {}));
        console.log("🔍 Response.data:", response?.data);
        console.log("🔍 Response.data.results:", response?.data?.results);
        console.log("🔍 Response.data.results length:", response?.data?.results?.length);
        console.log("🔍 Response.message:", response?.message);

        // The API returns data in response.data structure
        const transformedResponse = {
          ...response,
          data: response.data || response
        };

        console.log("🔍 TRANSFORMED RESPONSE:", transformedResponse);
        return transformedResponse;
      },
      transformErrorResponse: (error) => {
        console.error("Individual Commission Lines API Error:", error);
        return error;
      },
      providesTags: ["Sales"],
    }),
  }),
});

export const {
  useGetCommissionHeadersQuery,
  useGetCommissionLinesQuery,
  useGetIndividualCommissionHeadersQuery,
  useGetIndividualCommissionLinesQuery,
} = commissionApiSlice;





