import Select from "react-tailwindcss-select";

type Props = {
  data: any;
  value: any | null;
  setValue: (value: any) => void;
  loading: boolean;
  isClearable: boolean;
  isMultiple: boolean;
  isDisabled: boolean;
  isSearchable: boolean;
};

const Multiselect = ({
  data,
  value,
  setValue,
  loading,
  isClearable,
  isDisabled,
  isMultiple,
  isSearchable,
}: Props) => {
  return (
    <Select
      primaryColor="emerald"
      loading={loading}
      value={value}
      onChange={(value) => setValue(value)}
      options={data}
      isClearable={isClearable}
      isDisabled={isDisabled}
      isMultiple={isMultiple}
      isSearchable={isSearchable}
    />
  );
};

export default Multiselect;
