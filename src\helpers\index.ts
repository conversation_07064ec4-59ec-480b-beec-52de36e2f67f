export const getInputColorClassNames = (color?: string) => {
  switch (color) {
    case "destructive":
      return "accent-destructive focus:ring-destructive ";
    case "primary":
      return "accent-primary focus:ring-primary  ";
    case "success":
      return "accent-success focus:ring-success ";
    case "accent":
      return "accent-accent focus:ring-accent";
    default:
      return "accent-gray-600 focus:ring-gray-500 ";
  }
};

export const getCustomCheckboxInputColorClassNames = (color?: string) => {
  switch (color) {
    case "destructive":
      return "checked:bg-destructive checked:border-bg-destructive checked:focus:ring-destructive checked:accent-white";
    case "primary":
      return "checked:bg-primary checked:border-bg-primary checked:focus:ring-primary ";
    case "success":
      return "checked:bg-success checked:border-bg-success checked:focus:ring-success";
    case "accent":
      return "checked:bg-accent checked:border-bg-accent checked:focus:ring-accent";
    default:
      return "checked:bg-gray-500 checked:border-bg-gray-500 checked:focus:ring-gray-500";
  }
};

export const getBgDynamicColor = (color?: string) => {
  switch (color) {
    case "destructive":
      return "bg-destructive";
    case "primary":
      return "bg-primary";
    case "success":
      return "bg-success";
    case "accent":
      return "bg-accent";
    default:
      return "accent-gray-600";
  }
};
