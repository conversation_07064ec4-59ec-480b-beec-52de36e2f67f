import BaseModal from "@/components/custom/modals/BaseModal";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { CheckCircle, Circle, Edit } from "lucide-react";
import { useState } from "react";
import NotificationForm from "../forms/NotificationForm";

type Props = {
  data: any;
};

const NotificationsList = ({ data }: Props) => {
  const [NotificationsModalOpen, setNotificationsModalOpen] = useState<
    any | null
  >(null);
  return (
    <div className="border  rounded">
      <Table>
        <TableHeader className="bg-accent">
          <TableRow className="!font-bold">
            <TableHead className="!font-bold">Is Read</TableHead>
            <TableHead className="!font-bold">Type</TableHead>
            <TableHead className="!font-bold">Title</TableHead>
            <TableHead className="!font-bold">Message</TableHead>
            <TableHead className="!font-bold">Priority</TableHead>
            <TableHead className="!font-bold">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data?.map((rowData: any) => (
            <TableRow
              key={rowData?.notification_id}
              className="hover:!bg-transparent bg-white dark:bg-transparent dark:hover:!bg-gray-300/10"
            >
              <TableCell className="!py-2.5 text-xs">
                {rowData?.is_read ? (
                  <CheckCircle className="text-primary" />
                ) : (
                  <Circle className="text-dark" />
                )}
              </TableCell>
              <TableCell className="!py-2.5 text-xs">
                {rowData?.notification_type}
              </TableCell>
              <TableCell className="!py-2.5 text-xs">
                {rowData?.title}
              </TableCell>
              <TableCell className="!py-2.5 text-xs">
                {rowData?.message}
              </TableCell>
              <TableCell className="!py-2.5 text-xs">
                <Badge variant="default" className="px-4">
                  {rowData?.priority}
                </Badge>
              </TableCell>
              <TableCell className="!py-2.5 text-xs">
                <Edit
                  className="cursor-pointer text-blue-500 hover:text-blue-700"
                  onClick={() => setNotificationsModalOpen(rowData)}
                />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      <BaseModal
        isOpen={!!NotificationsModalOpen}
        onOpenChange={() => setNotificationsModalOpen(null)}
        title="Notifications"
        description="Manage your Notifications here"
      >
        <NotificationForm updateData={NotificationsModalOpen} />
      </BaseModal>
    </div>
  );
};

export default NotificationsList;
