import { Screen } from "@/app-components/layout/screen"

import NavTab1 from "@/components/custom/tabs/NavTab1";
import VehicleDetails from "./VehicleDetails";
import VehicleRequests from "./VehicleRequests";
import VehicleRequestForm from "./VehicleRequestForm";

const Vehicles = () => {
  const tabs = [
    {
      label: "Vehicles",
      content: <VehicleDetails />,
      value: "vehicle-details",
      title: "Vehicle Management",
    },
    {
      label: "Vehicle Requests",
      content: <VehicleRequests/>,
      value: "vehicle-requests",
      title: "Vehicle Requests",
    },
    {
      label: "Request Vehicle",
      content: (
        <div className="p-6">
          <div className="max-w-2xl mx-auto">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Request a Vehicle</h2>
              <p className="text-gray-600">Fill out the form below to request a vehicle for your trip</p>
            </div>
            <VehicleRequestForm mode="create" />
          </div>
        </div>
      ),
      value: "request-vehicle",
      title: "Request Vehicle",
    },
  ]

  return (
    <Screen>
      <div className=" !m-0 min-h-screen w-full border rounded">
        <div className=' px-6 py-5 border-b bg-gradient-to-r from-primary/5 to-primary/10'>
          <div className="flex items-center justify-between">
            <div>
              <h2 className=' font-bold text-2xl text-gray-900'>Vehicle Management</h2>
              <p className="text-gray-600 mt-1">Manage vehicles, requests, and transportation logistics</p>
            </div>
          </div>
        </div>
        <div className=''>
          <NavTab1
            tabs={tabs}
            TabsListClassName='!bg-transparent rounded-none !border-b'
            TabsTriggerClassName='md:!px-16  !rounded-none data-[state=active]:!bg-primary data-[state=active]:!text-primary-foreground '
          />
        </div>

      </div>
    </Screen>
  )
}

export default Vehicles
