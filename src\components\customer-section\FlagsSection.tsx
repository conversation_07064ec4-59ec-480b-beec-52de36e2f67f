import React from 'react';
import { Flag, PlusCircle, ChevronUp, ChevronDown, AlertTriangle, Eye, MoreHorizontal } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Skeleton } from '@/components/ui/skeleton';

interface FlagsSectionProps {
  expanded: boolean;
  onToggle: () => void;
  items: any[];
  loading?: boolean;
  error?: any;
  onAdd: () => void;
  onOpenList: () => void;
  renderFlagTypeBadge: (type: string) => JSX.Element;
  renderSeverityBadge: (severity: string) => JSX.Element;
  renderStatusBadge: (status: string) => JSX.Element;
  timeAgo: (dateString: string) => string;
}

const FlagsSection: React.FC<FlagsSectionProps> = ({
  expanded,
  onToggle,
  items,
  loading = false,
  error = false,
  onAdd,
  onOpenList,
  renderFlagTypeBadge,
  renderSeverityBadge,
  renderStatusBadge,
  timeAgo,
}) => {
  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'Critical':
        return <AlertTriangle className="h-3 w-3 text-red-500" />;
      case 'Error':
        return <AlertTriangle className="h-3 w-3 text-orange-500" />;
      case 'Warning':
        return <AlertTriangle className="h-3 w-3 text-yellow-500" />;
      default:
        return <Flag className="h-3 w-3 text-blue-500" />;
    }
  };

  return (
    <div className="space-y-2">
      <div
        className="flex items-center justify-between cursor-pointer group transition-colors hover:bg-gray-50 dark:hover:bg-gray-800 p-2 rounded-md"
        onClick={onToggle}
      >
        <div className="flex items-center text-sm font-medium">
          <Flag className="h-4 w-4 mr-2 text-orange-500" />
          <span>Flags</span>
          <Badge variant="outline" className="ml-2">
            {loading ? "..." : items.length}
          </Badge>
        </div>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0 opacity-70 group-hover:opacity-100">
          {expanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
        </Button>
      </div>
      
      {expanded && (
        <div className="pl-6 space-y-3">
          {loading ? (
            <div className="space-y-2">
              {[1, 2, 3].map((i) => (
                <div key={i} className="p-3 border rounded-lg">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-5 w-16" />
                    </div>
                    <Skeleton className="h-3 w-full" />
                    <div className="flex items-center gap-2">
                      <Skeleton className="h-5 w-16" />
                      <Skeleton className="h-5 w-20" />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : error ? (
            <div className="text-sm text-red-500 text-center py-4">
              Failed to load flags
            </div>
          ) : items.length === 0 ? (
            <div className="text-sm text-muted-foreground text-center py-4">
              No flags found
            </div>
          ) : (
            <div className="space-y-2">
              {items.slice(0, 3).map((flag) => (
                <div
                  key={flag.flag_id}
                  className="p-3 border rounded-lg hover:shadow-sm transition-shadow bg-white dark:bg-gray-900"
                >
                  <div className="space-y-2">
                    <div className="flex items-start justify-between gap-2">
                      <div className="flex items-center gap-2 min-w-0 flex-1">
                        {getSeverityIcon(flag.severity)}
                        <h4 className="font-medium text-sm truncate">
                          {flag.title}
                        </h4>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                            <MoreHorizontal className="h-3 w-3" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>
                            <Eye className="h-3 w-3 mr-2" />
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            Resolve Flag
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                    
                    <p className="text-xs text-muted-foreground line-clamp-2">
                      {flag.description}
                    </p>
                    
                    <div className="flex items-center gap-2 flex-wrap">
                      {renderFlagTypeBadge(flag.flag_type)}
                      {renderSeverityBadge(flag.severity)}
                      {renderStatusBadge(flag.status)}
                    </div>
                    
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span>
                        By {flag.flagged_by_name || 'Unknown'}
                      </span>
                      <span>
                        {timeAgo(flag.created_at)}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
              
              {items.length > 3 && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full text-xs"
                  onClick={onOpenList}
                >
                  View all {items.length} flags
                </Button>
              )}
            </div>
          )}
          
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              size="sm" 
              className="flex-1 text-xs bg-gray-50 hover:bg-gray-100 dark:bg-gray-800 dark:hover:bg-gray-700 flex gap-1"
              onClick={onAdd}
            >
              <PlusCircle className="h-3 w-3" />
              <span>Add flag</span>
            </Button>
            
            {items.length > 0 && (
              <Button 
                variant="ghost" 
                size="sm" 
                className="text-xs"
                onClick={onOpenList}
              >
                <Eye className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default FlagsSection;