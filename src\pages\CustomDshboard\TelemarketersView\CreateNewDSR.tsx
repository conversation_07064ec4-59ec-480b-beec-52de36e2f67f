import { useState } from "react";
import MultiStepModal from "@/components/custom/modals/MultiStepModal";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Check } from "lucide-react";

export default function AddRegionModal({ isOpen, onOpenChange }: { isOpen: boolean; onOpenChange: (open: boolean) => void }) {
  const [leadDetails, setLeadDetails] = useState({
    name: "",
    phoneNumber: "",
    email: "",
    regionDescription: "", 
    allocatedMarketer: "",
    telemarketersRegion: "", 
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setLeadDetails((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = () => {
    console.log("New Lead Details:", leadDetails);
    onOpenChange(false); // Close the modal after submission
  };

  return (
    <MultiStepModal
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      title="Add New Lead"
      description="Complete the following steps to add a new lead"
      size="lg"
      steps={[
        
        {
          title: "Region and Allocation",
          content: (
            <div className="space-y-6 py-4">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100">Assign Region and Marketers</h3>
              <div className="space-y-2">
                <Label htmlFor="lead-region-description">Region Description</Label>
                <Input
                  id="lead-region-description"
                  name="regionDescription"
                  value={leadDetails.regionDescription}
                  onChange={handleInputChange}
                  placeholder="Enter region description"
                  className="border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="allocated-marketer">Allocated Marketer</Label>
                <Input
                  id="allocated-marketer"
                  name="allocatedMarketer"
                  value={leadDetails.allocatedMarketer}
                  onChange={handleInputChange}
                  placeholder="Enter marketer name"
                  className="border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="telemarketers-region">Telemarketer's Region</Label>
                <Input
                  id="telemarketers-region"
                  name="telemarketersRegion"
                  value={leadDetails.telemarketersRegion}
                  onChange={handleInputChange}
                  placeholder="Enter telemarketer's region"
                  className="border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          ),
        },
        {
          title: "Confirmation",
          content: (
            <div className="py-6 text-center">
              <div className="w-16 h-16 bg-green-100 dark:bg-green-900/50 rounded-full flex items-center justify-center mx-auto mb-4">
                <Check className="h-8 w-8 text-green-600 dark:text-green-400" />
              </div>
              <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-100">Lead Added Successfully!</h3>
              <p className="text-gray-500 dark:text-gray-400 mt-2 mb-6">
                The new lead has been successfully added to the system.
              </p>
              <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700 text-left max-w-md mx-auto">
                <h4 className="text-sm font-medium mb-2 text-gray-800 dark:text-gray-100">Lead Details:</h4>
                <ul className="list-disc ml-5 text-sm text-gray-600 dark:text-gray-300">
                  <li>Name: {leadDetails.name}</li>
                  <li>Phone Number: {leadDetails.phoneNumber}</li>
                  <li>Email: {leadDetails.email}</li>
                  <li>Region Description: {leadDetails.regionDescription}</li>
                  <li>Allocated Marketer: {leadDetails.allocatedMarketer}</li>
                  <li>Telemarketer's Region: {leadDetails.telemarketersRegion}</li>
                </ul>
              </div>
            </div>
          ),
        },
      ]}
       // Ensure the modal triggers handleSubmit on completion
    />
  );
}