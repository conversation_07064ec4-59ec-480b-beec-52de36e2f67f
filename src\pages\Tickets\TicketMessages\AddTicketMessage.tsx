import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { Button } from "@/components/ui/button";
import { useCreateTicketMessageMutation } from "@/redux/slices/tickets";
import { Send } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

interface Props {
  ticket: string;
  currentUser: string;
}

const AddTicketMessage = ({ ticket, currentUser }: Props) => {
  const [message, setMessage] = useState("");

  const [createMessage, { isLoading: creating }] =
    useCreateTicketMessageMutation();

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!message) {
      toast.error("Message field are required");
    }

    try {
      const res = await createMessage({
        ticket,
        message,
        sender: currentUser,
      }).unwrap();
      if (res) {
        toast.success("Message posted successfully");
        setMessage("");
      }
    } catch (error: any) {
      console.log("Error: ", error);
      if (error?.data) {
        toast.error(`${error?.data?.error}`);
      } else {
        toast.error(`Error posting ticket message`);
      }
      return;
    }
  };
  return (
    <form onSubmit={onSubmit} className="">
      <div className="text-sm">
        <div className="flex items-center justify-center gap-2">
          <div className="w-[90%]">
            <input
              className="px-4 py-3 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              name="message"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Enter Message"
              required
            />
          </div>
          <div className="">
            {creating ? (
              <SpinnerTemp type="spinner-double" size="sm" />
            ) : (
              <div>
                <Button type="submit" size="icon" className="">
                  <Send />
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </form>
  );
};

export default AddTicketMessage;
