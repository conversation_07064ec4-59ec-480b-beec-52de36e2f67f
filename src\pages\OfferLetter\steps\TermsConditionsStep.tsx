import React, { useState, useEffect } from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { FileText, Shield, AlertCircle } from 'lucide-react';
import { OfferLetterData } from '../OfferLetter';
import { useFormContext } from '@/components/custom/forms/FormContext';

interface TermsConditionsStepProps {
  data: OfferLetterData;
  onUpdate: (data: Partial<OfferLetterData>) => void;
}

const TermsConditionsStep: React.FC<TermsConditionsStepProps> = ({ data, onUpdate }) => {
  const [activeTab, setActiveTab] = useState<'terms' | 'privacy'>('terms');
  const { setStepValidator } = useFormContext();

  // Set up validation for this step
  useEffect(() => {
    const validateStep = () => {
      return !!(
        data.terms_accepted &&
        data.privacyAccepted
      );
    };

    setStepValidator(5, validateStep); // Step 5 is the terms and conditions step
  }, [data.terms_accepted, data.privacyAccepted, setStepValidator]);

  const handleCheckboxChange = (field: keyof OfferLetterData, checked: boolean) => {
    onUpdate({ [field]: checked });
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header Section */}
      <div className="bg-white border-b border-gray-100">
        <div className="max-w-6xl mx-auto px-6 lg:px-8">
          <div className="flex justify-between items-center py-8">
            <div className="flex items-center">
              <img
                src="/optiven-logo.png"
                alt="Optiven Logo"
                className="h-12 w-auto"
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                  e.currentTarget.nextElementSibling.style.display = 'flex';
                }}
              />
              <div className="hidden h-12 w-40 bg-green-600 rounded-lg flex items-center justify-center shadow-sm">
                <span className="text-white font-bold text-xl">OPTIVEN</span>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <div className="text-sm text-gray-500 font-medium">
                Step 5 of 6
              </div>
              <div className="w-16 h-2 bg-gray-200 rounded-full">
                <div className="w-14 h-2 bg-green-600 rounded-full"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-5xl mx-auto px-6 lg:px-8 py-16">
        {/* Welcome Section */}
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold text-gray-900 mb-6 leading-tight">
            Terms & Conditions
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Please read and accept the following terms and conditions to proceed with your land purchase
          </p>
        </div>

        {/* Terms and Conditions Content */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
          <div className="p-10">
            <div className="flex items-center gap-4 mb-10">
              <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                <FileText className="w-6 h-6 text-blue-600" />
              </div>
              <h2 className="text-3xl font-bold text-gray-900">Legal Documents</h2>
            </div>

            {/* Document Tabs */}
            <div className="flex space-x-2 mb-10 bg-gray-100 p-2 rounded-xl">
              <button
                onClick={() => setActiveTab('terms')}
                className={`flex-1 py-4 px-6 rounded-lg text-lg font-semibold transition-all duration-300 ${
                  activeTab === 'terms'
                    ? 'bg-white text-gray-900 shadow-lg'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                <FileText className="w-5 h-5 inline mr-3" />
                Terms & Conditions
              </button>
              <button
                onClick={() => setActiveTab('privacy')}
                className={`flex-1 py-4 px-6 rounded-lg text-lg font-semibold transition-all duration-300 ${
                  activeTab === 'privacy'
                    ? 'bg-white text-gray-900 shadow-lg'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                <Shield className="w-5 h-5 inline mr-3" />
                Privacy Policy
              </button>
            </div>

            {/* Terms Content */}
            {activeTab === 'terms' && (
              <div className="space-y-8">
                <ScrollArea className="h-[500px] w-full border-2 rounded-xl p-8 bg-gray-50">
                  <div className="space-y-6 text-lg text-gray-700">
                    <h3 className="text-2xl font-bold text-gray-900">OPTIVEN LAND PURCHASE AGREEMENT</h3>

                    <div className="space-y-6">
                      <div>
                        <h4 className="text-xl font-bold text-gray-900 mb-3">1. PROPERTY DESCRIPTION</h4>
                        <p className="leading-relaxed">The property being purchased is located in {data.project_name}, Plot Number {data.plot_number}, with the specifications as outlined in the offer letter.</p>
                      </div>

                      <div>
                        <h4 className="text-xl font-bold text-gray-900 mb-3">2. PURCHASE PRICE AND PAYMENT TERMS</h4>
                        <p className="leading-relaxed">The total purchase price and payment schedule are as agreed upon in the payment plan section of this offer letter. All payments must be made according to the specified timeline.</p>
                      </div>

                      <div>
                        <h4 className="text-xl font-bold text-gray-900 mb-3">3. TITLE AND OWNERSHIP</h4>
                        <p className="leading-relaxed">Upon completion of all payments, the buyer will receive a clean title deed for the property. The title transfer process will be initiated within 30 days of final payment.</p>
                      </div>

                      <div>
                        <h4 className="text-xl font-bold text-gray-900 mb-3">4. DEVELOPMENT AND INFRASTRUCTURE</h4>
                        <p className="leading-relaxed">Optiven commits to providing basic infrastructure including access roads, water points, and security as outlined in the project specifications.</p>
                      </div>

                      <div>
                        <h4 className="text-xl font-bold text-gray-900 mb-3">5. DEFAULT AND REMEDIES</h4>
                        <p className="leading-relaxed">In case of payment default exceeding 90 days, Optiven reserves the right to cancel this agreement and retain payments made as liquidated damages.</p>
                      </div>

                      <div>
                        <h4 className="text-xl font-bold text-gray-900 mb-3">6. FORCE MAJEURE</h4>
                        <p className="leading-relaxed">Neither party shall be liable for delays or failures in performance resulting from acts beyond their reasonable control.</p>
                      </div>

                      <div>
                        <h4 className="text-xl font-bold text-gray-900 mb-3">7. GOVERNING LAW</h4>
                        <p className="leading-relaxed">This agreement shall be governed by the laws of Kenya and any disputes shall be resolved through arbitration.</p>
                      </div>
                    </div>
                  </div>
                </ScrollArea>
              </div>
            )}

            {/* Privacy Policy Content */}
            {activeTab === 'privacy' && (
              <div className="space-y-8">
                <ScrollArea className="h-[500px] w-full border-2 rounded-xl p-8 bg-gray-50">
                  <div className="space-y-6 text-lg text-gray-700">
                    <h3 className="text-2xl font-bold text-gray-900">PRIVACY POLICY</h3>

                    <div className="space-y-6">
                      <div>
                        <h4 className="text-xl font-bold text-gray-900 mb-3">1. INFORMATION COLLECTION</h4>
                        <p className="leading-relaxed">We collect personal information necessary for processing your land purchase, including contact details, identification, and financial information.</p>
                      </div>

                      <div>
                        <h4 className="text-xl font-bold text-gray-900 mb-3">2. USE OF INFORMATION</h4>
                        <p className="leading-relaxed">Your information is used solely for processing your land purchase, communication, and legal compliance. We do not sell or share your data with third parties.</p>
                      </div>

                      <div>
                        <h4 className="text-xl font-bold text-gray-900 mb-3">3. DATA SECURITY</h4>
                        <p className="leading-relaxed">We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.</p>
                      </div>

                      <div>
                        <h4 className="text-xl font-bold text-gray-900 mb-3">4. RETENTION</h4>
                        <p className="leading-relaxed">We retain your information for as long as necessary to fulfill the purposes outlined in this policy and as required by law.</p>
                      </div>

                      <div>
                        <h4 className="text-xl font-bold text-gray-900 mb-3">5. YOUR RIGHTS</h4>
                        <p className="leading-relaxed">You have the right to access, correct, or delete your personal information. Contact us to exercise these rights.</p>
                      </div>
                    </div>
                  </div>
                </ScrollArea>
              </div>
            )}

            {/* Acceptance Checkboxes */}
            <div className="mt-12 space-y-6 p-8 bg-gray-50 rounded-xl border-2 border-gray-200">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">Agreement Acceptance</h3>

              <div className="flex items-start space-x-4">
                <Checkbox
                  id="terms-acceptance"
                  checked={data.terms_accepted || false}
                  onCheckedChange={(checked) => handleCheckboxChange('terms_accepted', checked as boolean)}
                  className="mt-2 w-5 h-5"
                />
                <Label htmlFor="terms-acceptance" className="text-lg text-gray-700 cursor-pointer leading-relaxed">
                  I have read, understood, and agree to the Terms & Conditions outlined above. I acknowledge that this constitutes a legally binding agreement.
                </Label>
              </div>

              <div className="flex items-start space-x-4">
                <Checkbox
                  id="privacy-acceptance"
                  checked={data.privacyAccepted || false}
                  onCheckedChange={(checked) => handleCheckboxChange('privacyAccepted', checked as boolean)}
                  className="mt-2 w-5 h-5"
                />
                <Label htmlFor="privacy-acceptance" className="text-lg text-gray-700 cursor-pointer leading-relaxed">
                  I acknowledge that I have read and understood the Privacy Policy and consent to the collection and use of my personal information as described.
                </Label>
              </div>

              {(!data.terms_accepted || !data.privacyAccepted) && (
                <div className="flex items-center space-x-3 text-amber-600 bg-amber-50 p-6 rounded-xl border border-amber-200">
                  <AlertCircle className="w-6 h-6" />
                  <span className="text-lg">Please accept both the Terms & Conditions and Privacy Policy to continue.</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TermsConditionsStep;
