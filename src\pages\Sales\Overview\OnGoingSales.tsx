import React, { useState } from 'react'
import SalesTable from './SalesTable';
import { useGetOngoingSalesQuery } from '@/redux/slices/sales';
import SpinnerTemp from '@/components/custom/spinners/SpinnerTemp';
import { CircleAlert } from 'lucide-react';

const OnGoingSales = () => {
    const [universalSearchValue, setuniversalSearchValue] = useState('')

    // ongoing sales
    const [onGoingSalesItemsPerPage, setOnGoingSalesItemsPerPage] = useState(20)
    const [onGoingSalesCurrentPage, setOnGoingSalesCurrentPage] = useState(1)

    const { data: ongoingSalesData, isLoading, isFetching } = useGetOngoingSalesQuery({ page_size: onGoingSalesItemsPerPage, page: onGoingSalesCurrentPage, search: universalSearchValue });

    // Show loader when initially loading or when tab is changing
    const showLoader = isLoading || isFetching;

    const totalItems = ongoingSalesData?.data?.total_data || 0;

    return (
        <>
            <div className='relative'>
                {showLoader ? (
                    <div className="absolute inset-0 z-10 flex pt-16 justify-center bg-white bg-opacity-70">
                        <SpinnerTemp type="spinner-double" size="md" />
                    </div>
                ) : (!totalItems || totalItems === 0) ? (
                <div className='flex flex-col gap-2 justify-center items-center h-64'>
                    <CircleAlert size={45} className='text-muted-foreground' />
                    <p className="text-center text-xs">No data available.</p>
                    <p className="text-center text-xs">You may not have permission to view this section.</p>
                </div>
                ) : (
                <SalesTable
                    data={ongoingSalesData}
                    itemsPerPage={onGoingSalesItemsPerPage}
                    setItemsPerPage={setOnGoingSalesItemsPerPage}
                    currentPage={onGoingSalesCurrentPage}
                    setCurrentPage={setOnGoingSalesCurrentPage}
                    SearchComponent={<SearchComponent universalSearchValue={universalSearchValue} setuniversalSearchValue={setuniversalSearchValue} />}
                />
               )}
            </div>
        </>
    )
}

export default OnGoingSales

interface SearchComponentProps {
    universalSearchValue: string,
    setuniversalSearchValue: React.Dispatch<React.SetStateAction<string>>
}

function SearchComponent({ universalSearchValue, setuniversalSearchValue }: SearchComponentProps) {
    return <input
        value={universalSearchValue}
        onChange={e => setuniversalSearchValue(e.target.value)}
        className="px-3 py-2 ml-1 w-full border rounded text-sm focus:outline-none bg-transparent"
        placeholder="Search sales details..."
    />
}




// import React, { useState } from 'react'
// import SalesTable from './SalesTable';
// import { useGetOngoingSalesQuery } from '@/redux/slices/sales';
// import SpinnerTemp from '@/components/custom/spinners/SpinnerTemp';
// import { useSalesPermissions } from '@/hooks/useSalesPermissions';

// interface SalesComponentProps {
//     data: any;
//     itemsPerPage: number;
//     setItemsPerPage: (items: number) => void;
//     currentPage?: number;
//     setCurrentPage?: (page: number) => void;
// }

// interface Sale {
//     lead_file_no: string
//     customer_name: string
//     plot: string
//     selling_price: string
//     total_paid: string
//     balance_lcy: string
//     project: string
//     purchase_type: string
// }


// const OnGoingSales = () => {
//     const [universalSearchValue, setuniversalSearchValue] = useState('')

//     // Get sales permissions
//     const { 
//         hasAnySalesAccess,
//         apiParams,
//         userDetails
//     } = useSalesPermissions();

//     // ongoing sales
//     const [onGoingSalesItemsPerPage, setOnGoingSalesItemsPerPage] = useState(20)
//     const [onGoingSalesCurrentPage, setOnGoingSalesCurrentPage] = useState(1)

//     // Get API parameters based on permissions
//     const getApiParams = () => {
//         // Use the simplified permission-based API parameters
//         return {
//             ...apiParams,
//             page_size: onGoingSalesItemsPerPage,
//             page: onGoingSalesCurrentPage,
//             search: universalSearchValue || undefined
//         };
//     };

//     const { data: ongoingSalesData, isLoading, isFetching } = useGetOngoingSalesQuery(
//         getApiParams(),
//         {
//             skip: !hasAnySalesAccess // Skip the query if user has no access
//         }
//     );

//     // Show loader when initially loading or when tab is changing
//     const showLoader = isLoading || isFetching;

//     return (
//         <>
//             {hasAnySalesAccess ? (
//                 <div className='relative'>
//                     {showLoader && (
//                         <div className="absolute inset-0 z-10 flex pt-16 justify-center bg-white bg-opacity-70">
//                             <SpinnerTemp type="spinner-double" size="md" />
//                         </div>
//                     )}
//                     <SalesTable
//                         data={ongoingSalesData}
//                         itemsPerPage={onGoingSalesItemsPerPage}
//                         setItemsPerPage={setOnGoingSalesItemsPerPage}
//                         currentPage={onGoingSalesCurrentPage}
//                         setCurrentPage={setOnGoingSalesCurrentPage}
//                         SearchComponent={<SearchComponent universalSearchValue={universalSearchValue} setuniversalSearchValue={setuniversalSearchValue} />}
//                     />
//                 </div>
//             ) : (
//                 <div className="text-center py-8 text-gray-500">
//                     <p>Contact your administrator to request sales viewing permissions.</p>
//                 </div>
//             )}
//         </>
//     )
// }

// export default OnGoingSales

// interface SearchComponentProps {
//     universalSearchValue: string,
//     setuniversalSearchValue: React.Dispatch<React.SetStateAction<string>>
// }

// function SearchComponent({ universalSearchValue, setuniversalSearchValue }: SearchComponentProps) {
//     return <input
//         value={universalSearchValue}
//         onChange={e => setuniversalSearchValue(e.target.value)}
//         className="px-3 py-2 ml-1 w-full border rounded text-sm focus:outline-none bg-transparent"
//         placeholder="Search sales details..."
//     />
// }