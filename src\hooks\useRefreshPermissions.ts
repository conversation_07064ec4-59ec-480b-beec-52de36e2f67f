import { useCallback } from 'react';
import PermissionsService from '@/services/permissionsService';

/**
 * Hook to refresh user permissions from the server
 * This ensures that permission changes are reflected immediately in the application
 */
export const useRefreshPermissions = () => {
  /**
   * Fetch the latest user permissions from the server and update the Redux store
   */
  const refreshPermissions = useCallback(async () => {
    return await PermissionsService.refreshPermissions();
  }, []);

  return { refreshPermissions };
};