import { useState, useEffect, useMemo } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { DataTable } from "@/components/custom/tables/Table1";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { Search } from "lucide-react";
import { useGetAllCustomersQuery } from "@/redux/slices/customersApiSlice";
import { useCustomerPermissions } from "@/hooks/useCustomerPermissions";
import CustomerPermissionIndicator from "@/components/customer/CustomerPermissionIndicator";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";

// Import the shared Customer interface
import { Customer } from "@/components/customer-section/CustomerInfoHeader";

const Customers = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  
  // Get customer permissions
  const {
    hasAnyCustomerAccess,
    apiParams,
    userDetails
  } = useCustomerPermissions();

  // Create query parameters with permission filters
  const queryParams = {
    page: currentPage,
    page_size: itemsPerPage,
    ordering: "customer_name",
      search: searchTerm,
    // TEMPORARY FIX: Remove apiParams as the API doesn't support OFFICE parameter
    // ...apiParams
  };
  
  console.log('All Customers API params:', queryParams);
  console.log('All Customers Permission debugging:', {
    hasAnyCustomerAccess,
    apiParams,
    userPermissions: hasAnyCustomerAccess ? 'User has access' : 'User has NO access',
    userDetails
  });
  
  // Fetch customers data with permission-based parameters
  const {
    data: customersData,
    isLoading,
    isError,
    error,
    refetch,
    isFetching
  } = useGetAllCustomersQuery(queryParams, {
    skip: !hasAnyCustomerAccess, // Skip the query if user has no access
  });

  // Test query without permissions for debugging
  const testQueryParams = {
    page: currentPage,
    page_size: itemsPerPage,
    ordering: "customer_name",
    search: searchTerm,
  };

  const {
    data: testCustomersData,
    isLoading: testIsLoading,
    isError: testIsError,
    error: testError,
  } = useGetAllCustomersQuery(testQueryParams, {
    skip: false // Always run this test
  });

  // Test with a very simple query to see if there's any data at all
  const {
    data: simpleTestData,
    isLoading: simpleTestLoading,
    isError: simpleTestError,
    error: simpleTestErrorData,
  } = useGetAllCustomersQuery({
    page: 1,
    page_size: 100  // Try to get more data
  }, {
    skip: false
  });

  // Test with official API parameters only (no OFFICE parameter)
  const {
    data: officialTestData,
    isLoading: officialTestLoading,
    isError: officialTestError,
    error: officialTestErrorData,
  } = useGetAllCustomersQuery({
    page: 1,
    page_size: 20,
    ordering: "customer_name",
    search: ""
    // NO OFFICE parameter - use only official API parameters
  }, {
    skip: false
  });

  useEffect(() => {
    if (isError) {
      console.error("Error fetching customers:", error);
    }
  }, [isError, error]);

  // Log the API response
  useEffect(() => {
    console.log('All Customers API Response:', {
      customersData,
      isLoading,
      isError,
      error,
      customersCount: customersData?.results?.length || 0,
      totalData: customersData?.total_data || 0
    });
  }, [customersData, isLoading, isError, error]);

  // Log test query response
  useEffect(() => {
    console.log('All Customers Test Query Response (No Permissions):', {
      testCustomersData,
      testIsLoading,
      testIsError,
      testError,
      testCustomersCount: testCustomersData?.results?.length || 0,
      testTotalData: testCustomersData?.total_data || 0
    });
  }, [testCustomersData, testIsLoading, testIsError, testError]);

  // Log simple test query response
  useEffect(() => {
    console.log('Simple Test Query Response:', {
      simpleTestData,
      simpleTestLoading,
      simpleTestError,
      simpleTestErrorData,
      simpleTestCount: simpleTestData?.results?.length || 0,
      simpleTestTotal: simpleTestData?.total_data || 0
    });
  }, [simpleTestData, simpleTestLoading, simpleTestError, simpleTestErrorData]);

  // Log official API test query response
  useEffect(() => {
    console.log('Official API Test Query Response (No OFFICE param):', {
      officialTestData,
      officialTestLoading,
      officialTestError,
      officialTestErrorData,
      officialTestCount: officialTestData?.results?.length || 0,
      officialTestTotal: officialTestData?.total_data || 0
    });
  }, [officialTestData, officialTestLoading, officialTestError, officialTestErrorData]);

  // Reset to first page when search term changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  const handleViewCustomer = (customerNo: string) => {
    console.log(`View customer ${customerNo} details`);
  };

  // Map API response fields to Customer interface fields
  const mapApiResponseToCustomer = (apiCustomer: any): Customer => {
    // Helper function to clean and validate string values
    const cleanString = (value: any): string => {
      if (!value) return "";
      const cleaned = String(value).trim();
      return cleaned === "" ? "" : cleaned;
    };

    return {
      id: apiCustomer.id || "",
      customer_no: apiCustomer.customer_no || "", // API uses customer_id, not customer_no
      name: cleanString(apiCustomer.customer_name),
      email: cleanString(apiCustomer.primary_email),
      phone: cleanString(apiCustomer.phone), // API uses phone_number, not phone
      position: cleanString(apiCustomer.position),
      company: cleanString(apiCustomer.company),
      nationalId: cleanString(apiCustomer.national_id),
      passportNo: cleanString(apiCustomer.passport_no),
      kraPin: cleanString(apiCustomer.kra_pin),
      dob: cleanString(apiCustomer.dob),
      gender: cleanString(apiCustomer.gender),
      maritalStatus: cleanString(apiCustomer.marital_status),
      alternativePhone: cleanString(apiCustomer.alternative_phone),
      alternativeEmail: cleanString(apiCustomer.alternative_email),
      address: cleanString(apiCustomer.address),
      customerType: cleanString(apiCustomer.customer_type) as "Individual" | "Group" | undefined,
      countryOfResidence: cleanString(apiCustomer.country_of_residence),
      dateOfRegistration: cleanString(apiCustomer.date_of_registration),
      otp: cleanString(apiCustomer.otp),
      otpGeneratedAt: cleanString(apiCustomer.otp_generated_at),
      leadSource: apiCustomer.lead_source || 0,
      marketer: cleanString(apiCustomer.marketer), // API uses marketer_name, not marketer
      plotNumbers: cleanString(apiCustomer.plot_numbers), // API uses plot_numbers
    };
  };

  const columns: ColumnDef<Customer>[] = [
    {
      accessorKey: "customer_no",
      header: "Customer No",
      cell: (info) => {
        const rowData = info.row.original;
        const customerNo = (info.getValue() as string) || "N/A";
        return (
          <Link
            to={`/customer/${rowData.customer_no}`}
            className='block font-bold text-blue-700 underline'
          >
            {customerNo}
          </Link>
        );
      },
      enableColumnFilter: true,
    },
    {
      accessorKey: "name",
      header: "Customer Name",
      cell: (info) => {
        const rowData = info.row.original;
        const name = (info.getValue() as string) || "N/A";
        return (
          <Link
            to={`/customer/${rowData.customer_no}`}
            className='block font-medium hover:text-blue-700 hover:underline'
          >
            {name}
          </Link>
        );
      },
      enableColumnFilter: true,
    },
    {
      accessorKey: "phone",
      header: "Phone Number",
      cell: (info) => (
        <span className="font-medium">
          {(info.getValue() as string) || "N/A"}
        </span>
      ),
      enableColumnFilter: true,
    },
    {
      accessorKey: "email",
      header: "Primary Email",
      cell: (info) => (info.getValue() as string) || "N/A",
      enableColumnFilter: true,
    },
    {
      accessorKey: "marketer",
      header: "Marketer",
      cell: (info) => (info.getValue() as string) || "N/A",
      enableColumnFilter: true,
    },
    {
      id: "actions",
      header: "Action",
      cell: ({ row }) => {
        const customer = row.original;
        return (
          <div className="flex space-x-2 justify-center">
            <Link to={`/customer/${customer.customer_no}`}>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleViewCustomer(customer.customer_no)}
                className="bg-white text-blue-500 border-blue-300 hover:bg-blue-50 flex items-center space-x-1"
              >
                <span>View</span>
              </Button>
            </Link>
          </div>
        );
      },
      enableColumnFilter: false,
    },
  ];

  // Get customers data array
  const customersArray = customersData?.results || [];

  // Transform API response data to match our Customer interface
  // Since we're using server-side search, we don't need client-side filtering
  const transformedCustomers = customersArray.map(mapApiResponseToCustomer);

  // Debug: Log the first few transformed customers
  useEffect(() => {
    if (transformedCustomers.length > 0) {
      console.log('Transformed customers sample:', transformedCustomers.slice(0, 3));
      console.log('Raw API customers sample:', customersArray.slice(0, 3));
    }
  }, [transformedCustomers, customersArray]);

  // Remove client-side filtering since server-side search is handling it
  // const filteredCustomers = useMemo(() => {
  //   if (!searchTerm) return transformedCustomers;
  //   return transformedCustomers.filter(...)
  // }, [transformedCustomers, searchTerm]);

  return (
    <div className="space-y-6">
      {/* Permission Indicator */}
      {/* <CustomerPermissionIndicator /> */}
      
      {/* Main Table Card */}
      {hasAnyCustomerAccess ? (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
      

          <div className="p-6 relative">
            {(isLoading || isFetching) ? (
              <div className="absolute inset-0 z-10 flex pt-16 justify-center bg-white bg-opacity-70">
                <SpinnerTemp type="spinner-double" size="md" />
              </div>
            ) : null}
            
            {isError ? (
              <div className="text-center py-12">
                <div className="text-red-500 text-lg font-medium">
                  Failed to load customers
                </div>
                <p className="text-gray-600 dark:text-gray-400 mt-2">
                  Please try again later.
                </p>
                <Button onClick={() => refetch()} className="mt-4">
                  Retry
                </Button>
              </div>
            ) : transformedCustomers.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-gray-500 text-lg font-medium">
                  {searchTerm
                    ? "No customers found matching your search"
                    : "No customers found"}
                </div>
                <p className="text-gray-400 mt-2">
                  {searchTerm
                    ? "Try adjusting your search terms"
                    : "Customers will appear here once they are added to the system"}
                </p>
              </div>
            ) : (
              <DataTable<Customer>
                data={transformedCustomers}
                columns={columns}
                enableToolbar
                enableExportToExcel
                enablePrintPdf
                enablePagination
                enableColumnFilters
                enableSorting
                enableSelectColumn
                title="All Customers List"
                currentPage={currentPage}
                setCurrentPage={setCurrentPage}
                itemsPerPage={itemsPerPage}
                setItemsPerPage={setItemsPerPage}
                totalItems={customersData?.total_data || 0}
                tBodyCellsClassName="border-t !p-2"
                searchInput={<SearchComponent universalSearchValue={searchTerm} setuniversalSearchValue={setSearchTerm} />}
            />
            )}
          </div>
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-8 text-center">
          <div className="text-red-500 text-lg font-medium mb-2">
            Access Restricted
          </div>
          <p className="text-gray-600 dark:text-gray-400">
            You don't have permission to view customer data. Contact your administrator to request access.
          </p>
        </div>
      )}
    </div>
  );
}

export default Customers

interface SearchComponentProps {
    universalSearchValue: string,
    setuniversalSearchValue: React.Dispatch<React.SetStateAction<string>>
}

function SearchComponent({ universalSearchValue, setuniversalSearchValue }: SearchComponentProps) {
    return <input
        value={universalSearchValue}
        onChange={e => setuniversalSearchValue(e.target.value)}
        className="px-3 py-2 ml-1 w-full border rounded text-sm focus:outline-none bg-transparent"
        placeholder="Search customer details..."
    />
}
