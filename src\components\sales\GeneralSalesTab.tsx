import { BadgeCheck, DollarSign, FileBar<PERSON>hart } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "../ui/badge";
import { useSalesStore } from "@/zustand/useSaleStore";
import { formatNumberWithCommas } from "@/utils/salesDataFormatter";
import PaymentInstallments from "@/pages/Sales/Overview/PaymentInstallments";
import { ScrollArea } from "../ui/scroll-area";

const GenaralSalesTab = () => {
  const salesData = useSalesStore((state) => state.salesData);
  const {
    lead_file_no,
    lead_file_status_dropped,
    purchase_price,
    selling_price,
    balance_lcy,
    customer_name,
    purchase_type,
    commission_threshold,
    deposit_threshold,
    discount,
    completion_date,
    no_of_installments,
    installment_amount,
    sale_agreement_sent,
    sale_agreement_signed,
    total_paid,
    transfer_cost_charged,
    transfer_cost_paid,
    overpayments,
    refunds,
    refundable_amount,
    penalties_accrued,
    booking_id,
    booking_date,
    additional_deposit_date,
    title_status,
    credit_officer_id,
    plot,
    project,
    marketer,
    customer_id,
    customer_lead_source,
    cat_lead_source,
  } = salesData;

  return (
    <div className="space-y-6 w-full h-full">
      <div className="grid grid-cols-1 [@media(min-width:1200px)]:grid-cols-3 gap-3">
        <div className="flex justify-between items-center shadow-lg shadow-primary/20 px-3 py-8 rounded-md">
          <div>
            <p className="text-xs mb-2">Purchase price</p>
            <p className="text-1xl font-bold">
              {typeof purchase_price !== "undefined" ? "Ksh" : "Not"}
            </p>
            <p className="text-2xl font-bold">
              {typeof purchase_price !== "undefined"
                ? formatNumberWithCommas(purchase_price)
                : "Declared"}
            </p>
            {discount != null && Number(discount) > 0 && (
              <>
                <p className="text-xs mt-3">Discount</p>
                <p className="text-sm mt-1 font-semibold">Ksh {formatNumberWithCommas(discount)}</p>
              </>
            )}
          </div>
          <div className="h-fit bg-blue-100 p-3 rounded-full dark:bg-blue-900/30">
            <FileBarChart className=" text-blue-600 dark:text-blue-400" />
          </div>
        </div>

        <div className="flex justify-between items-center shadow-lg shadow-primary/20 px-3 py-8 rounded-md">
          <div>
            <p className="text-xs mb-2">Total paid</p>
            <p className="text-1xl font-bold">
              {typeof total_paid !== "undefined" ? "Ksh" : "Not"}
            </p>
            <p className="text-2xl font-bold">
              {typeof total_paid !== "undefined"
                ? formatNumberWithCommas(total_paid)
                : "Declared"}
            </p>
          </div>
          <div className="h-fit bg-blue-100 p-3 rounded-full dark:bg-blue-900/30">
            <DollarSign className="h-6 w-6 text-green-600 dark:text-green-400" />
          </div>
        </div>

        <div className="flex justify-between items-center shadow-lg shadow-primary/20 px-3 py-8 rounded-md">
          <div>
            <p className="text-xs mb-2">Total balance</p>
            <p className="text-1xl font-bold">
              {typeof total_paid !== "undefined" ? "Ksh" : "Not"}
            </p>
            <p className="text-2xl font-bold">
              {typeof total_paid !== "undefined"
                ? formatNumberWithCommas(balance_lcy)
                : "Declared"}
            </p>
          </div>
          <div className="h-fit bg-red-100 p-3 rounded-full dark:bg-red-900/30">
            <BadgeCheck className="h-6 w-6 text-gred-600 dark:text-gred-400" />
          </div>
        </div>
      </div>

      <div className="">
        <p className="font-bold text-[15px] mb-2">Payment installments</p>
        <PaymentInstallments plot_no={plot} />

      </div>
    </div>
  );
};

export default GenaralSalesTab;
