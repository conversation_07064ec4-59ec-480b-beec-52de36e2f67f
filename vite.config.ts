import path from "path";
import react from "@vitejs/plugin-react";
import { defineConfig } from "vite";

const host = process.env.VITE_HOST_URL;
const port = parseInt(process.env.VITE_HOST_PORT || "5179");

export default defineConfig({
  plugins: [react()],
  server: {
    host,
    port,
    allowedHosts: ["crm2.0.optiven.co.ke", "temp.crm.optiven.co.ke" , "engage360.optiven.co.ke"],
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  worker: {
    format: "es", // Use ES modules in workers
  },
});