
import React, { useState, ReactNode } from 'react';
import { CalendarIcon, X, Download } from 'lucide-react';
import { apiSlice } from '@/redux/apiSlice';
import { DateRange } from 'react-day-picker';
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover';
import { Button } from '../ui/button';
import { format } from 'date-fns';
import { Calendar } from '../ui/calendar';
import SpinnerTemp from '../custom/spinners/SpinnerTemp';
import Paginator from '../custom/tables/Paginator';
import pdfMake from "pdfmake/build/pdfmake";
import pdfFonts from "pdfmake/build/vfs_fonts";
import { useGetVehiclesQuery, useGetSpecialBookingsQuery } from '@/redux/slices/logistics';

// Initialize pdfMake fonts
pdfMake.vfs = pdfFonts.vfs;

// PDF Generation Utility for Logistics Reports
const generateLogisticsReportPDF = (data: any, reportTitle: string, reportType: string, vehiclesData?: any) => {
  const results = data?.results || data?.data?.results || [];

  if (!results?.length) {
    console.warn('No data available for PDF generation');
    return;
  }

  // Log PDF generation summary for vehicle debugging
  console.group('📄 PDF GENERATION - Complete Data Analysis');
  console.log('📊 Report Details:', {
    title: reportTitle,
    type: reportType,
    recordCount: results.length,
    vehiclesAvailable: vehiclesData?.data?.results?.length || 0
  });
  console.log('📋 Raw Data Structure:', data);
  console.log('📊 Results Array:', results);
  console.log('📊 First 3 Records:', results.slice(0, 3));

  // Analyze vehicle data in the results
  const vehicleAnalysis = results.map((item: any, index: number) => ({
    index,
    id: item.id,
    hasVehicle: !!item.vehicle,
    hasVehicleId: !!item.vehicle_id,
    hasVehicleRegistration: !!item.vehicle_registration,
    vehicleType: typeof item.vehicle,
    vehicle: item.vehicle,
    vehicle_registration: item.vehicle_registration
  }));

  console.log('🚗 Vehicle Data Analysis for PDF:', vehicleAnalysis);
  console.groupEnd();

  // Helper functions
  const formatDate = (dateString: string) => {
    if (!dateString) return '-';
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return dateString;
    }
  };

  const formatTime = (timeString: string) => {
    if (!timeString) return '-';
    try {
      const time = timeString.includes('T') ? new Date(timeString) : new Date(`2000-01-01T${timeString}`);
      return time.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      });
    } catch {
      return timeString;
    }
  };

  const getClientCount = (item: any) => {
    // For approved-site-visits API response
    if (item.client_count && typeof item.client_count === 'number') {
      return item.client_count.toString();
    }
    // For standard site visits API response
    if (item.site_visit_client && Array.isArray(item.site_visit_client)) {
      return item.site_visit_client.length.toString();
    }
    if (item.clients && Array.isArray(item.clients)) {
      return item.clients.length.toString();
    }
    // If clients is a string (like "Repher Mukonzo (+254719463209)"), extract count
    if (typeof item.clients === 'string') {
      // Count the number of phone numbers in the string (assuming each client has a phone number in parentheses)
      const phoneMatches = item.clients.match(/\(\+\d+\)/g);
      return phoneMatches ? phoneMatches.length.toString() : '1'; // Default to 1 if no phone pattern found
    }
    return '0';
  };

  // Enhanced helper function to get vehicle information
  const getVehicleInfo = (visit: any) => {
    const vehicles = vehiclesData?.data?.results || [];

    // Enhanced debug logging for approved site visits and special assignments
    console.group(`🚗 VEHICLE FETCH DEBUG - Visit ID: ${visit.id}`);
    console.log('📋 Raw Visit Data:', visit);
    console.log('🚙 Available Vehicles Count:', vehicles.length);
    console.log('🔍 Vehicle Fields Analysis:', {
      vehicle: visit.vehicle,
      vehicle_type: typeof visit.vehicle,
      vehicle_id: visit.vehicle_id,
      vehicle_make: visit.vehicle_make,
      vehicle_model: visit.vehicle_model,
      vehicle_registration: visit.vehicle_registration,
      is_special_assignment: visit.is_special_assignment,
      transport_type: visit.transport_type,
      pickup_location: visit.pickup_location
    });

    // Log first few vehicles for reference
    if (vehicles.length > 0) {
      console.log('🚗 Sample Vehicles in Database:', vehicles.slice(0, 3).map(v => ({
        id: v.id,
        make: v.make,
        model: v.model,
        registration: v.vehicle_registration,
        status: v.status
      })));
    }

    // Priority 1: Check for direct vehicle_registration field first
    if (visit.vehicle_registration) {
      const make = visit.vehicle_make || '';
      const model = visit.vehicle_model || '';
      const registration = visit.vehicle_registration;
      const result = `${make} ${model} - ${registration}`.trim();
      console.log('✅ PRIORITY 1 - Direct vehicle_registration found:', {
        make, model, registration, result
      });
      console.groupEnd();
      return result;
    }

    // Priority 2: If visit has vehicle object, use it directly
    if (visit.vehicle && typeof visit.vehicle === 'object') {
      const make = visit.vehicle.make || '';
      const model = visit.vehicle.model || '';
      const registration = visit.vehicle.vehicle_registration || visit.vehicle.registration || '';
      const result = `${make} ${model} - ${registration}`.trim() || 'Vehicle assigned';
      console.log('✅ PRIORITY 2 - Vehicle object found:', {
        vehicleObject: visit.vehicle,
        make, model, registration, result
      });
      console.groupEnd();
      return result;
    }

    // Priority 3: If visit has vehicle ID (number or string), find it in vehicles list
    if (visit.vehicle && (typeof visit.vehicle === 'number' || typeof visit.vehicle === 'string')) {
      const vehicleId = typeof visit.vehicle === 'string' ? parseInt(visit.vehicle, 10) : visit.vehicle;
      console.log('🔍 PRIORITY 3 - Vehicle ID lookup attempt:', {
        originalVehicle: visit.vehicle,
        parsedVehicleId: vehicleId,
        isValidNumber: !isNaN(vehicleId)
      });

      if (!isNaN(vehicleId)) {
        const vehicleData = vehicles.find((v: any) => v.id === vehicleId);
        console.log('🔍 Vehicle ID search result:', {
          searchingForId: vehicleId,
          found: !!vehicleData,
          vehicleData: vehicleData
        });

        if (vehicleData) {
          const make = vehicleData.make || '';
          const model = vehicleData.model || '';
          const registration = vehicleData.vehicle_registration || vehicleData.registration || '';
          const result = `${make} ${model} - ${registration}`.trim() || 'Vehicle assigned';
          console.log('✅ PRIORITY 3 - Vehicle ID lookup success:', {
            vehicleId, vehicleData, make, model, registration, result
          });
          console.groupEnd();
          return result;
        } else {
          console.log('❌ PRIORITY 3 - Vehicle ID not found in vehicles list');
        }
      }
    }

    // Priority 4: Check for vehicle_id field
    if (visit.vehicle_id) {
      const vehicleId = typeof visit.vehicle_id === 'string' ? parseInt(visit.vehicle_id, 10) : visit.vehicle_id;
      console.log('🔍 PRIORITY 4 - vehicle_id field lookup:', {
        vehicle_id: visit.vehicle_id,
        parsedId: vehicleId,
        isValidNumber: !isNaN(vehicleId)
      });

      if (!isNaN(vehicleId)) {
        const vehicleData = vehicles.find((v: any) => v.id === vehicleId);
        if (vehicleData) {
          const make = vehicleData.make || '';
          const model = vehicleData.model || '';
          const registration = vehicleData.vehicle_registration || vehicleData.registration || '';
          const result = `${make} ${model} - ${registration}`.trim() || 'Vehicle assigned';
          console.log('✅ PRIORITY 4 - vehicle_id lookup success:', {
            vehicleId, vehicleData, make, model, registration, result
          });
          console.groupEnd();
          return result;
        } else {
          console.log('❌ PRIORITY 4 - vehicle_id not found in vehicles list');
        }
      }
    }

    // Priority 5: Fallback to individual fields
    const make = visit.vehicle_make || '';
    const model = visit.vehicle_model || '';
    const registration = visit.vehicle_registration || '';

    console.log('🔍 PRIORITY 5 - Individual fields check:', {
      vehicle_make: visit.vehicle_make,
      vehicle_model: visit.vehicle_model,
      vehicle_registration: visit.vehicle_registration,
      hasAnyField: !!(make || model || registration)
    });

    if (make || model || registration) {
      const result = `${make} ${model} - ${registration}`.trim();
      console.log('✅ PRIORITY 5 - Individual fields found:', { make, model, registration, result });
      console.groupEnd();
      return result;
    }

    // Priority 6: Check if vehicle is a string (might be registration directly)
    if (visit.vehicle && typeof visit.vehicle === 'string') {
      console.log('✅ PRIORITY 6 - Vehicle string found:', visit.vehicle);
      console.groupEnd();
      return visit.vehicle;
    }

    // Priority 7: Check for any field that might contain registration
    const possibleRegistrationFields = [
      'registration',
      'reg_number',
      'plate_number',
      'license_plate'
    ];

    console.log('🔍 PRIORITY 7 - Alternative registration fields check:', {
      fieldsToCheck: possibleRegistrationFields,
      availableFields: possibleRegistrationFields.map(field => ({ field, value: visit[field] }))
    });

    for (const field of possibleRegistrationFields) {
      if (visit[field]) {
        console.log(`✅ PRIORITY 7 - Registration found in ${field}:`, visit[field]);
        console.groupEnd();
        return visit[field];
      }
    }

    console.log('❌ NO VEHICLE INFORMATION FOUND - All priorities exhausted for visit:', visit.id);
    console.log('📋 Final visit data summary:', {
      id: visit.id,
      type: visit.is_special_assignment ? 'Special Assignment' : 'Site Visit',
      transport_type: visit.transport_type,
      pickup_location: visit.pickup_location,
      allVehicleRelatedFields: Object.keys(visit).filter(key =>
        key.toLowerCase().includes('vehicle') ||
        key.toLowerCase().includes('registration') ||
        key.toLowerCase().includes('plate')
      ).reduce((obj, key) => ({ ...obj, [key]: visit[key] }), {})
    });
    console.groupEnd();
    return '-';
  };

  // Create table headers based on report type
  let headers: string[] = [];
  let tableBody: any[][] = [];

  if (reportType === 'approved-site-visits' || reportType === 'chauffeur-itinerary') {
    headers = ['Date', 'Time', 'Type', 'Marketer', 'No. of Clients', 'Project/Destination', 'Driver', 'Vehicle', 'Pickup Location', 'Status', 'Remarks'];

    tableBody = results.map((item: any) => [
      formatDate(item.pickup_date || item.reservation_date),
      formatTime(item.pickup_time || item.reservation_time),
      item.is_special_assignment ? 'Special Assignment' : 'Site Visit',
      item.marketer || item.marketer_name || (typeof item.assigned_to === 'object' ? (item.assigned_to?.dp_name || item.assigned_to?.name || 'Unknown Department') : item.assigned_to) || '-',
      item.is_special_assignment ? '-' : getClientCount(item),
      item.project || item.project_name || item.destination || item.special_assignment_destination || '-',
      item.driver || item.driver_name || '-',
      getVehicleInfo(item),
      item.pickup_location || '-',
      item.status || '-',
      item.remarks || item.reason || '-'
    ]);
  } else if (reportType === 'site-visits-summary') {
    headers = ['Marketer', 'Project', 'Total Visits', 'Approved', 'Completed', 'In Progress', 'Pending', 'Cancelled', 'Rejected'];

    // Process summary data
    const summaryMap = new Map();
    results.forEach((visit: any) => {
      const marketer = visit.marketer || 'Unknown';
      const project = visit.project || 'Unknown';
      const key = `${marketer}-${project}`;

      if (!summaryMap.has(key)) {
        summaryMap.set(key, {
          marketer_name: marketer,
          project_name: project,
          total_visits: 0,
          pending: 0,
          approved: 0,
          in_progress: 0,
          completed: 0,
          cancelled: 0,
          rejected: 0,
        });
      }

      const summary = summaryMap.get(key);
      summary.total_visits++;

      const status = visit.status?.toLowerCase() || 'unknown';
      switch (status) {
        case 'pending': summary.pending++; break;
        case 'approved': summary.approved++; break;
        case 'in progress': summary.in_progress++; break;
        case 'completed': summary.completed++; break;
        case 'cancelled': summary.cancelled++; break;
        case 'rejected': summary.rejected++; break;
      }
    });

    const summaryResults = Array.from(summaryMap.values());
    tableBody = summaryResults.map((item: any) => [
      item.marketer_name,
      item.project_name,
      item.total_visits.toString(),
      item.approved.toString(),
      item.completed.toString(),
      item.in_progress.toString(),
      item.pending.toString(),
      item.cancelled.toString(),
      item.rejected.toString()
    ]);
  } else {
    // Default format for other report types
    headers = ['Date', 'Marketer', 'Project', 'Location', 'Status', 'Remarks'];
    tableBody = results.map((item: any) => [
      formatDate(item.pickup_date),
      item.marketer || item.marketer_name || '-',
      item.project || item.project_name || '-',
      item.pickup_location || '-',
      item.status || '-',
      item.remarks || '-'
    ]);
  }

  // Log table body for debugging
  console.group('📊 PDF TABLE BODY DEBUG');
  console.log('Headers:', headers);
  console.log('Table Body Length:', tableBody.length);
  console.log('First 3 Table Rows:', tableBody.slice(0, 3));
  console.log('Last 3 Table Rows:', tableBody.slice(-3));
  console.groupEnd();

  // Define custom column widths based on report type
  let tableWidths: any[] = [];

  if (reportType === 'approved-site-visits' || reportType === 'chauffeur-itinerary') {
    // Custom widths for approved site visits report to accommodate all fields
    // Headers: ['Date', 'Time', 'Type', 'Marketer', 'No. of Clients', 'Project/Destination', 'Driver', 'Vehicle', 'Pickup Location', 'Status', 'Remarks']
    tableWidths = [
      60,   // Date - fixed width
      50,   // Time - fixed width
      80,   // Type - fixed width
      80,   // Marketer - fixed width
      50,   // No. of Clients - fixed width
      100,  // Project/Destination - wider for longer names
      80,   // Driver - fixed width
      90,   // Vehicle - wider for make/model/registration
      100,  // Pickup Location - wider for addresses
      60,   // Status - fixed width
      '*'   // Remarks - flexible width for remaining space
    ];
  } else if (reportType === 'site-visits-summary') {
    // Custom widths for summary report
    tableWidths = [
      100,  // Marketer
      100,  // Project
      50,   // Total Visits
      50,   // Approved
      50,   // Completed
      50,   // In Progress
      50,   // Pending
      50,   // Cancelled
      50    // Rejected
    ];
  } else {
    // Default equal widths for other report types
    tableWidths = Array(headers.length).fill('*');
  }

  // Create PDF document definition with enhanced styling and colors
  const docDefinition: import('pdfmake/interfaces').TDocumentDefinitions = {
    content: [
      // Header with gradient-like background effect
      {
        table: {
          widths: ['*'],
          body: [
            [{
              text: reportTitle,
              style: 'header',
              alignment: 'center',
              fillColor: '#16a34a', // Green primary color
              color: 'white',
              margin: [10, 15, 10, 15]
            }]
          ]
        },
        layout: 'noBorders',
        margin: [0, 0, 0, 20]
      },

      // Company info section with colored background
      {
        table: {
          widths: ['50%', '50%'],
          body: [
            [
              {
                text: 'Optiven Limited\nLogistics Department',
                style: 'companyInfo',
                fillColor: '#fef7ed', // Light brown background
                margin: [10, 8, 10, 8]
              },
              {
                text: `Generated on: ${new Date().toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}`,
                style: 'dateInfo',
                alignment: 'right',
                fillColor: '#fef7ed', // Light brown background
                margin: [10, 8, 10, 8]
              }
            ]
          ]
        },
        layout: {
          hLineWidth: function () { return 0; },
          vLineWidth: function () { return 0; }
        },
        margin: [0, 0, 0, 15]
      },

      // Summary section with colored background
      {
        table: {
          widths: ['*'],
          body: [
            [{
              text: `📊 Complete Dataset: ${results.length} Records | Report Type: ${reportTitle} | All Data Included`,
              style: 'summaryInfo',
              fillColor: '#f0fdf4', // Light green background
              margin: [10, 8, 10, 8],
              alignment: 'center'
            }]
          ]
        },
        layout: 'noBorders',
        margin: [0, 0, 0, 20]
      },

      // Main data table with enhanced styling
      {
        table: {
          headerRows: 1,
          widths: tableWidths,
          keepWithHeaderRows: 1, // Keep header with content
          dontBreakRows: false, // Allow row breaks across pages
          body: [
            headers.map(header => ({
              text: header,
              style: 'tableHeader',
              fillColor: '#16a34a', // Green header background
              color: 'white'
            })),
            ...tableBody.map((row, index) =>
              row.map((cell, cellIndex) => ({
                text: cell || '-', // Ensure no null/undefined values
                style: 'tableCell',
                fillColor: index % 2 === 0 ? '#fef7ed' : 'white', // Light brown alternating rows
                // Enable text wrapping for longer content columns
                ...(reportType === 'approved-site-visits' || reportType === 'chauffeur-itinerary') &&
                   (cellIndex === 5 || cellIndex === 7 || cellIndex === 8 || cellIndex === 10) && // Project, Vehicle, Pickup Location, Remarks
                   { noWrap: false }
              }))
            )
          ]
        },
        layout: {
          hLineWidth: function (i: number, node: any) {
            return i === 0 || i === node.table.body.length ? 2 : 1;
          },
          vLineWidth: function (i: number, node: any) {
            return i === 0 || i === node.table.widths.length ? 2 : 1;
          },
          hLineColor: function (i: number, node: any) {
            return i === 0 || i === node.table.body.length ? '#16a34a' : '#d97706'; // Green and brown borders
          },
          vLineColor: function (i: number, node: any) {
            return i === 0 || i === node.table.widths.length ? '#16a34a' : '#d97706'; // Green and brown borders
          }
        },
        // Allow table to span multiple pages
      },

      // Footer with colored background
      {
        table: {
          widths: ['*'],
          body: [
            [{
              text: '🚗 Optiven Logistics - Driving Excellence in Transportation Services',
              style: 'footer',
              fillColor: '#fef7ed', // Light brown footer background
              margin: [10, 8, 10, 8],
              alignment: 'center'
            }]
          ]
        },
        layout: 'noBorders',
        margin: [0, 20, 0, 0]
      }
    ],
    styles: {
      header: {
        fontSize: 22, // Increased for better visibility
        bold: true,
        color: 'white'
      },
      companyInfo: {
        fontSize: 13, // Increased for better visibility
        color: '#92400e', // Brown color
        bold: true
      },
      dateInfo: {
        fontSize: 12, // Increased for better visibility
        color: '#92400e', // Brown color
        italics: true,
        bold: true // Made bold for better visibility
      },
      summaryInfo: {
        fontSize: 14, // Increased for better visibility
        bold: true,
        color: '#16a34a' // Green color
      },
      tableHeader: {
        fontSize: (reportType === 'approved-site-visits' || reportType === 'chauffeur-itinerary') ? 10 : 12, // Smaller for wide tables
        bold: true,
        color: 'white',
        alignment: 'center',
        margin: [2, 6, 2, 6] // Reduced padding for wide tables
      },
      tableCell: {
        fontSize: (reportType === 'approved-site-visits' || reportType === 'chauffeur-itinerary') ? 9 : 11, // Smaller for wide tables
        color: '#374151',
        bold: false, // Keep table cells normal weight for readability
        alignment: 'left',
        margin: [2, 4, 2, 4] // Reduced padding for wide tables
      },
      footer: {
        fontSize: 12, // Increased for better visibility
        italics: true,
        bold: true, // Made bold for better visibility
        color: '#92400e' // Brown color
      }
    },
    defaultStyle: {
      fontSize: 10, // Reduced for better fit
      lineHeight: 1.3, // Reduced for better fit
      bold: false
    },
    pageOrientation: 'landscape',
    pageMargins: [15, 15, 15, 15], // Further reduced margins for more content space
    pageSize: (reportType === 'approved-site-visits' || reportType === 'chauffeur-itinerary') ? 'A3' : 'A4', // Use A3 for wider tables
    // Allow content to flow across multiple pages without restrictions
    pageBreakBefore: function() {
      return false; // Never force page breaks
    }
  };

  // Generate and download PDF
  const fileName = `${reportTitle.replace(/\s+/g, '-').toLowerCase()}-${new Date().toISOString().split('T')[0]}.pdf`;
  pdfMake.createPdf(docDefinition).download(fileName);
};

// Type definitions
interface ApiParams {
    [key: string]: string | number | boolean | undefined | null;
}

interface ApiQueryArgs {
    url: string;
    params?: ApiParams;
}

interface ApiError {
    message?: string;
    data?: {
        message?: string;
    };
}

export interface TableColumn<T = any> {
    key: keyof T | string;
    title: string;
    width?: string;
    render?: (value: any, row: T, index: number) => ReactNode;
    headerClassName?: string;
    cellClassName?: string;
    headerStyle?: React.CSSProperties;
    cellStyle?: React.CSSProperties;
}

interface DataTableProps<T = any> {
    data?: T[];
    columns: TableColumn<T>[];
    isLoading?: boolean;
    error?: ApiError | null;
    isFetching?: boolean;
    emptyMessage?: string;
    loadingMessage?: string;
    refreshingMessage?: string;
    className?: string;
    tableClassName?: string;
    headerClassName?: string;
    rowClassName?: string;
    cellClassName?: string;
}

type ModalSize = 'sm' | 'md' | 'lg' | 'xl' | 'full';

interface LazyModalProps<T = any> {
    isOpen: boolean;
    onClose: () => void;
    title: string;
    url: string;
    params?: ApiParams;
    TableComponent?: React.ComponentType<DataTableProps<T>>;
    columns: TableColumn<T>[];
    size?: ModalSize;
    enableBackendSearch?: boolean,
    enableDateFilter?: boolean,
    FilterComponents?: ReactNode;
}


const extendedApiSlice = apiSlice.injectEndpoints({
    endpoints: (builder) => ({
        getDynamicData: builder.query<any, ApiQueryArgs>({
            query: ({ url, params = {} }) => {
                const searchParams = new URLSearchParams();
                Object.entries(params).forEach(([key, value]) => {
                    if (value !== undefined && value !== null) {
                        searchParams.append(key, String(value));
                    }
                });

                const queryString = searchParams.toString();
                return {
                    url: `${url}${queryString ? `?${queryString}` : ''}`,
                    method: 'GET' as const,
                };
            },
            // providesTags: (result, error, { url }) => [
            //   { type: 'DynamicData', id: url }
            // ],
            transformResponse: (response: any) => {
                // Handle nested data structure from logistics API
                if (response?.data?.results) {
                    return {
                        results: response.data.results,
                        count: response.data.count || response.data.results.length,
                        next: response.data.next,
                        previous: response.data.previous,
                        current_page: response.data.current_page || 1,
                        num_pages: response.data.num_pages || 1,
                        per_page: response.data.per_page || response.data.results.length
                    };
                }
                // Handle direct results array
                if (response?.results) {
                    return response;
                }
                // Handle direct array response
                if (Array.isArray(response)) {
                    return {
                        results: response,
                        count: response.length,
                        current_page: 1,
                        num_pages: 1,
                        per_page: response.length
                    };
                }
                return response;
            },
            transformErrorResponse: (response: any): ApiError => {
                console.error('Dynamic Data Query Error:', response);
                return response.data || { message: 'An error occurred' };
            },
        }),
    }),
    overrideExisting: false,
});

export const { useGetDynamicDataQuery } = extendedApiSlice;


// Generic DataTable Component
const DataTable = <T extends Record<string, any>>({
    data = [],
    columns,
    isLoading = false,
    error = null,
    isFetching = false,
    emptyMessage = "No data available",
    loadingMessage = "Loading...",
    refreshingMessage = "Refreshing...",
    className = "",
    tableClassName = "",
    headerClassName = "",
    rowClassName = "",
    cellClassName = ""
}: DataTableProps<T>): JSX.Element => {
    // Loading state
    if (isLoading || isFetching) {
        return (
            <div className={`flex justify-center items-center h-64 ${className}`}>
                <SpinnerTemp type="spinner-double" size="md" />
                <span className="ml-2 text-gray-600">
                    {isLoading ? loadingMessage : refreshingMessage}
                </span>
            </div>
        );
    }

    // Error state
    if (error) {
        return (
            <div className={`bg-red-50 border border-red-200 rounded-md p-4 ${className}`}>
                <p className="text-red-800">
                    Error loading data: {error.message || error.data?.message || 'Unknown error'}
                </p>
            </div>
        );
    }

    // Empty state
    if (!data || data.length === 0) {
        return (
            <div className={`flex justify-center items-center h-64 ${className}`}>
                {emptyMessage}
            </div>
        );
    }

    // Data table
    return (
        <div className={`overflow-x-auto ${className}`}>
            <table className={`min-w-full bg-white dark:bg-gray-800  ${tableClassName}`}>
                <thead className={` ${headerClassName}`}>
                    <tr>
                        {columns.map((column) => (
                            <th
                                key={String(column.key)}
                                className={`align-top px-2 py-4 text-left text-xs font-bold uppercase tracking-wider border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 ${column.headerClassName || ''}`}
                                style={column.headerStyle}
                            >
                                {column.title}
                            </th>
                        ))}
                    </tr>
                </thead>
                <tbody className="divide-y  divide-gray-200 dark:divide-gray-700">
                    {data.map((row, index) => (
                        <tr
                            key={row.id || index}
                            className={`hover:bg-gray-50 ${rowClassName}`}
                        >
                            {columns.map((column) => (
                                <td
                                    key={String(column.key)}
                                    className={`px-2 py-3 whitespace-nowrap text-sm text-gray-900 ${cellClassName} ${column.cellClassName || ''}`}
                                    style={column.cellStyle}
                                >
                                    {column.render
                                        ? column.render(row[column.key as keyof T], row, index)
                                        : row[column.key as keyof T]}
                                </td>
                            ))}
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
};

// Lazy Modal Component
const LazyModal = <T extends Record<string, any>>({
    isOpen,
    onClose,
    title,
    url,
    params = {},
    TableComponent = DataTable,
    columns,
    size = 'lg',
    enableBackendSearch = false,
    enableDateFilter= false,
    FilterComponents,
}: LazyModalProps<T>): JSX.Element | null => {

    const [date, setDate] = useState<DateRange>({
        from: new Date(new Date().setDate(new Date().getDate() - 7)),
        to: new Date(),
    });
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage, setItemsPerPage] = useState(20);

    // search 
    const [universalSearchValue, setuniversalSearchValue] = useState('')

    // Use the injected endpoint with proper date filtering
    const { data, isLoading, error, isFetching } = useGetDynamicDataQuery(
        {
            url,
            params: {
                ...params,
                // Only include date params if date filter is enabled and both dates are selected
                ...(enableDateFilter && date.from && date.to && {
                    start_date: formatDateForAPI(date.from),
                    end_date: formatDateForAPI(date.to),
                }),
                page_size: itemsPerPage,
                page: currentPage,
                search: universalSearchValue,
            }
        },
        {
            skip: !isOpen, // Skip the query when modal is closed
            refetchOnMountOrArgChange: true,
            refetchOnFocus: false,
            refetchOnReconnect: true,
        }
    );

    if (!isOpen) return null;

    const sizeClasses: Record<ModalSize, string> = {
        sm: 'max-w-md',
        md: 'max-w-lg',
        lg: 'max-w-4xl',
        xl: 'max-w-6xl',
        full: 'max-w-full mx-4'
    };

    return (
        <div className="fixed inset-0 z-50 overflow-y-auto">
            {/* Backdrop */}
            <div
                className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
                onClick={onClose}
            />

            {/* Modal */}
            <div className="flex min-h-full items-center justify-center p-4">
                <div
                    className={`relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full ${sizeClasses[size]} max-h-[90vh] overflow-hidden`}
                    onClick={(e: React.MouseEvent) => e.stopPropagation()}
                >
                    {/* Header */}
                    <div className="flex items-center justify-between p-4 border-b border-gray-200">
                        <h2 className="text-xl font-semibold text-gray-900">
                            {title}
                        </h2>
                        <button
                            onClick={onClose}
                            className="text-gray-400 hover:text-gray-600 transition-colors"
                            type="button"
                        >
                            <X size={24} />
                        </button>
                    </div>

                    {/* Content */}
                    <div className="px-6 py-2 overflow-y-auto max-h-[calc(90vh-120px)]">



                        <div className='flex gap-4 pb-1 border-b border-gray-200 dark:border-gray-700 '>
                            {/* search  */}
                            {enableBackendSearch && (
                                <input
                                    value={universalSearchValue}
                                    onChange={e => setuniversalSearchValue(e.target.value)}
                                    className="px-3 py-3 ml-1 w-full border rounded-lg text-sm focus:outline-none bg-transparent"
                                    placeholder="Search..."
                                />
                            )}

                            {FilterComponents}

                            {/* date filter  */}
                            {enableDateFilter && (
                                <Popover>
                                    <PopoverTrigger asChild className="hover:!bg-none ml-auto border border-foreground/40 rounded-lg px-3">
                                        <div className="flex items-center gap-2">
                                            <CalendarIcon size={20} className="hidden md:inline" />
                                            <Button
                                                variant="ghost"
                                                className="hover:!bg-transparent hover:text-muted-foreground justify-start text-left font-normal !p-0 focus:outline-none rounded-none whitespace-nowrap overflow-hidden text-ellipsis"
                                                size="sm"
                                            >
                                                {date?.from ? (
                                                    date.to ? (
                                                        <>
                                                            {format(date.from, "LLL dd, y")} -{" "}
                                                            {format(date.to, "LLL dd, y")}
                                                        </>
                                                    ) : (
                                                        format(date.from, "LLL dd, y")
                                                    )
                                                ) : (
                                                    <span className="text-xs text-muted-foreground">Filter by date...</span>
                                                )}
                                            </Button>
                                        </div>
                                    </PopoverTrigger>
                                    <PopoverContent className="w-auto p-0" align="start">
                                        <Calendar
                                            initialFocus
                                            mode="range"
                                            defaultMonth={date?.from}
                                            selected={date}
                                            onSelect={(selectedDate) => {
                                                setDate(selectedDate ?? { from: new Date(), to: new Date() });
                                            }}
                                            numberOfMonths={1}
                                        />
                                        <div className="flex items-center justify-between px-3 pb-2 hover:!bg-none">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => {
                                                    setDate({
                                                        from: new Date(new Date().setDate(new Date().getDate() - 7)),
                                                        to: new Date(),
                                                    });
                                                }}
                                            >
                                                Reset
                                            </Button>
                                        </div>
                                    </PopoverContent>
                                </Popover>
                            )}
                        </div>

                        <TableComponent
                            data={data?.results || []}
                            columns={columns}
                            isLoading={isLoading}
                            //   error={error}
                            isFetching={isFetching}
                        />

                        <Paginator
                            currentPage={currentPage}
                            setCurrentPage={setCurrentPage}
                            itemsPerPage={itemsPerPage}
                            setItemsPerPage={setItemsPerPage}
                            totalItems={data?.count}
                            className='!font-bold border-t border-gray-200 dark:border-gray-700 '
                        />
                    </div>
                </div>
            </div>
        </div>
    );
};

// Report Configuration Interface
export interface ReportConfig {
    moduleId: string;
    endpoint: string;
    params?: ApiParams;
    title: string;
    componentType: string;
    initialDateRange?: { from?: Date; to?: Date };
}

// Component Registry
const componentRegistry: Record<string, React.ComponentType<any>> = {};

// Register Report Component Function
export const registerReportComponent = (key: string, component: React.ComponentType<any>) => {
    componentRegistry[key] = component;
};

// Get Registered Component Function
const getRegisteredComponent = (key: string): React.ComponentType<any> | null => {
    return componentRegistry[key] || null;
};

// Helper function to format date without timezone issues
const formatDateForAPI = (date: Date): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
};

// Custom Report Modal Component
interface ReportsModalProps {
    config: ReportConfig;
    isOpen: boolean;
    onClose: () => void;
    customComponent?: React.ComponentType<any>;
}

export const ReportsModal: React.FC<ReportsModalProps> = ({
    config,
    isOpen,
    onClose,
    customComponent
}) => {
    const [date, setDate] = useState<DateRange>({
        from: config.initialDateRange?.from,
        to: config.initialDateRange?.to,
    });
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage, setItemsPerPage] = useState(1000); // Increased to get all data

    // Update date when config changes (when modal opens with new date range)
    React.useEffect(() => {
        if (config.initialDateRange) {
            setDate({
                from: config.initialDateRange.from,
                to: config.initialDateRange.to,
            });
        }
    }, [config.initialDateRange]);

    // Get the registered component or use the custom component
    const ReportComponent = customComponent || getRegisteredComponent(config.componentType);

    // Build query parameters with proper date filtering
    const queryParams = React.useMemo(() => {
        const params: any = {
            ...config.params,
            page_size: itemsPerPage,
            page: currentPage
        };

        // Add date filtering if dates are selected
        if (date.from && date.to) {
            const fromDateStr = formatDateForAPI(date.from);
            const toDateStr = formatDateForAPI(date.to);

            if (config.componentType === 'approved-site-visits') {
                params.start_date = fromDateStr;
                params.end_date = toDateStr;
            } else {
                params.pickup_date__gte = fromDateStr;
                params.pickup_date__lte = toDateStr;
            }

            console.log('🔍 Query Parameters for', config.componentType, ':', params);
            console.log('🔍 Date range (fixed timezone issue):', { from: fromDateStr, to: toDateStr });
        }

        return params;
    }, [config.params, config.componentType, date.from, date.to, itemsPerPage, currentPage]);

    // Use the injected endpoint with proper date filtering
    const { data, isLoading, error, isFetching } = useGetDynamicDataQuery(
        {
            url: config.endpoint,
            params: queryParams
        },
        {
            skip: !isOpen, // Skip the query when modal is closed
            refetchOnMountOrArgChange: true,
            refetchOnFocus: false,
            refetchOnReconnect: true,
        }
    );

    // Fetch vehicles data for proper vehicle information display
    const { data: vehiclesData, isLoading: vehiclesLoading } = useGetVehiclesQuery({
        page: 1,
        page_size: 1000,
    }, {
        skip: !isOpen, // Skip when modal is closed
    });

    // Build special assignments query parameters
    const specialAssignmentsParams = React.useMemo(() => {
        const params: any = {
            status: 'Approved',
            page: 1,
            page_size: 1000
        };

        // Add date filtering if dates are selected
        if (date.from && date.to) {
            const fromDateStr = formatDateForAPI(date.from);
            const toDateStr = formatDateForAPI(date.to);

            params.reservation_date__gte = fromDateStr;
            params.reservation_date__lte = toDateStr;

            console.log('🔍 Special Assignments Query Parameters:', params);
            console.log('🔍 Special Assignments Date range (fixed timezone issue):', { from: fromDateStr, to: toDateStr });
        }

        return params;
    }, [date.from, date.to]);

    // Fetch special assignments for approved site visits reports
    const { data: specialAssignmentsData } = useGetSpecialBookingsQuery(specialAssignmentsParams, {
        skip: !isOpen || config.componentType !== 'approved-site-visits', // Only fetch for approved site visits
    });



    // Debug log vehicles data
    React.useEffect(() => {
        if (vehiclesData && !vehiclesLoading) {
            console.log('Vehicles data loaded:', {
                count: vehiclesData?.data?.results?.length || 0,
                vehicles: vehiclesData?.data?.results?.slice(0, 3) // Log first 3 vehicles for debugging
            });
        }
    }, [vehiclesData, vehiclesLoading]);

    // Debug log date filtering parameters
    React.useEffect(() => {
        if (isOpen && date.from && date.to) {
            const fromDateStr = formatDateForAPI(date.from);
            const toDateStr = formatDateForAPI(date.to);

            console.group('🔍 ReportsModal Date Filtering Debug');
            console.log('Report Type:', config.componentType);
            console.log('Date Range (fixed timezone issue):', {
                from: fromDateStr,
                to: toDateStr
            });

            if (config.componentType === 'approved-site-visits') {
                console.log('Using approved-site-visits date parameters:', {
                    start_date: fromDateStr,
                    end_date: toDateStr
                });
            } else {
                console.log('Using standard date parameters:', {
                    pickup_date__gte: fromDateStr,
                    pickup_date__lte: toDateStr
                });
            }
            console.groupEnd();
        }
    }, [isOpen, date.from, date.to, config.componentType]);

    // Debug log the actual data structure when it changes
    React.useEffect(() => {
        if (data && !isLoading) {
            const results = data?.results || data?.data?.results || [];
            console.group('🔍 Site Visits API Response Debug');
            console.log('Total results:', results.length);
            console.log('Date range filter:', date.from && date.to ? {
                from: formatDateForAPI(date.from),
                to: formatDateForAPI(date.to)
            } : 'No date filter');

            if (results.length > 0) {
                console.log('Sample dates in results:', results.slice(0, 5).map((item: any) => ({
                    id: item.id || item.site_visit_id,
                    pickup_date: item.pickup_date,
                    status: item.status,
                    type: 'Site Visit'
                })));

                // Check if any results are outside the date range
                if (date.from && date.to) {
                    const fromDate = formatDateForAPI(date.from);
                    const toDate = formatDateForAPI(date.to);
                    const outsideRange = results.filter((item: any) => {
                        const itemDate = item.pickup_date?.slice(0, 10); // Extract just the date part
                        return itemDate && (itemDate < fromDate || itemDate > toDate);
                    });

                    if (outsideRange.length > 0) {
                        console.warn('⚠️ Found results outside date range:', outsideRange.length);
                        console.log('Sample outside range:', outsideRange.slice(0, 3).map((item: any) => ({
                            id: item.id,
                            pickup_date: item.pickup_date,
                            expected_range: `${fromDate} to ${toDate}`
                        })));
                    }
                }
            }
            console.groupEnd();
        }
    }, [data, isLoading, date.from, date.to]);

    // Debug log special assignments data
    React.useEffect(() => {
        if (specialAssignmentsData && config.componentType === 'approved-site-visits') {
            const results = specialAssignmentsData?.data?.results || [];
            console.group('🔍 Special Assignments API Response Debug');
            console.log('Total results:', results.length);
            console.log('Date range filter:', date.from && date.to ? {
                from: formatDateForAPI(date.from),
                to: formatDateForAPI(date.to)
            } : 'No date filter');

            if (results.length > 0) {
                console.log('Sample dates in results:', results.slice(0, 5).map((item: any) => ({
                    id: item.id,
                    reservation_date: item.reservation_date,
                    status: item.status,
                    type: 'Special Assignment'
                })));

                // Check if any results are outside the date range
                if (date.from && date.to) {
                    const fromDate = formatDateForAPI(date.from);
                    const toDate = formatDateForAPI(date.to);
                    const outsideRange = results.filter((item: any) => {
                        const itemDate = item.reservation_date?.slice(0, 10); // Extract just the date part
                        return itemDate && (itemDate < fromDate || itemDate > toDate);
                    });

                    if (outsideRange.length > 0) {
                        console.warn('⚠️ Found special assignments outside date range:', outsideRange.length);
                        console.log('Sample outside range:', outsideRange.slice(0, 3).map((item: any) => ({
                            id: item.id,
                            reservation_date: item.reservation_date,
                            expected_range: `${fromDate} to ${toDate}`
                        })));
                    }
                }
            }
            console.groupEnd();
        }
    }, [specialAssignmentsData, config.componentType, date.from, date.to]);

    // For approved-site-visits, combine site visits with special assignments
    const filteredData = React.useMemo(() => {
        if (config.componentType === 'approved-site-visits') {
            let siteVisits = data?.results || data?.data?.results || [];
            let specialAssignments = specialAssignmentsData?.data?.results || [];

            // Apply client-side date filtering as backup (in case API filtering doesn't work)
            if (date.from && date.to) {
                const fromDateStr = formatDateForAPI(date.from);
                const toDateStr = formatDateForAPI(date.to);

                console.group('🔄 Client-side Date Filtering');
                console.log('Date range (fixed timezone issue):', { from: fromDateStr, to: toDateStr });
                console.log('Before filtering - Site visits:', siteVisits.length, 'Special assignments:', specialAssignments.length);

                // Filter site visits by pickup_date
                siteVisits = siteVisits.filter((sv: any) => {
                    const pickupDate = sv.pickup_date;
                    if (!pickupDate) return false;
                    // Ensure we're comparing date strings in YYYY-MM-DD format
                    const pickupDateStr = pickupDate.slice(0, 10); // Extract just the date part
                    return pickupDateStr >= fromDateStr && pickupDateStr <= toDateStr;
                });

                // Filter special assignments by reservation_date
                specialAssignments = specialAssignments.filter((sa: any) => {
                    const reservationDate = sa.reservation_date;
                    if (!reservationDate) return false;
                    // Ensure we're comparing date strings in YYYY-MM-DD format
                    const reservationDateStr = reservationDate.slice(0, 10); // Extract just the date part
                    return reservationDateStr >= fromDateStr && reservationDateStr <= toDateStr;
                });

                console.log('After filtering - Site visits:', siteVisits.length, 'Special assignments:', specialAssignments.length);
                console.groupEnd();
            }

            console.group('🔄 ReportsModal - Data Combination Debug');
            console.log('Site Visits Data:', {
                count: siteVisits.length,
                sampleDates: siteVisits.slice(0, 3).map((sv: any) => ({
                    id: sv.id || sv.site_visit_id,
                    pickup_date: sv.pickup_date,
                    status: sv.status
                }))
            });
            console.log('Special Assignments Data:', {
                count: specialAssignments.length,
                sampleDates: specialAssignments.slice(0, 3).map((sa: any) => ({
                    id: sa.id,
                    reservation_date: sa.reservation_date,
                    status: sa.status
                }))
            });

            // Transform special assignments to match site visit structure
            const transformedSpecialAssignments = specialAssignments.map((assignment: any) => ({
                ...assignment,
                pickup_date: assignment.reservation_date,
                pickup_time: assignment.reservation_time,
                project: assignment.destination,
                marketer: typeof assignment.assigned_to === 'object'
                    ? (assignment.assigned_to?.dp_name || assignment.assigned_to?.name || 'Unknown Department')
                    : assignment.assigned_to || 'Unknown',
                is_special_assignment: true,
            }));

            // Combine and return
            const combinedResults = [...siteVisits, ...transformedSpecialAssignments];

            console.log('Final Combined Results:', {
                siteVisits: siteVisits.length,
                specialAssignments: transformedSpecialAssignments.length,
                total: combinedResults.length,
                dateRange: date.from && date.to ? {
                    from: formatDateForAPI(date.from),
                    to: formatDateForAPI(date.to)
                } : 'No date filter'
            });
            console.groupEnd();

            return {
                ...data,
                results: combinedResults,
                count: combinedResults.length
            };
        }

        // For other reports, use the API data directly
        return data;
    }, [data, specialAssignmentsData, config.componentType, date.from, date.to]);

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-50 overflow-y-auto">
            {/* Backdrop */}
            <div
                className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
                onClick={onClose}
            />

            {/* Modal */}
            <div className="flex min-h-full items-center justify-center p-4">
                <div
                    className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden"
                    onClick={(e: React.MouseEvent) => e.stopPropagation()}
                >
                    {/* Header */}
                    <div className="flex items-center justify-between p-4 border-b border-gray-200">
                        <h2 className="text-xl font-semibold text-gray-900">
                            {config.title}
                        </h2>
                        <div className="flex items-center gap-2">
                            {/* PDF Download Button */}
                            {filteredData && !isLoading && !error && (
                                <Button
                                    onClick={() => generateLogisticsReportPDF(filteredData, config.title, config.componentType, vehiclesData)}
                                    variant="outline"
                                    size="sm"
                                    className="flex items-center gap-2 hover:bg-primary hover:text-white transition-colors"
                                >
                                    <Download size={16} />
                                    Download PDF
                                </Button>
                            )}
                            <button
                                onClick={onClose}
                                className="text-gray-400 hover:text-gray-600 transition-colors"
                                type="button"
                            >
                                <X size={24} />
                            </button>
                        </div>
                    </div>

                    {/* Content */}
                    <div className="px-6 py-2 overflow-y-auto max-h-[calc(90vh-120px)]">
                        {/* Date filter */}
                        <div className='flex pb-1 border-b border-gray-200 dark:border-gray-700'>
                            <Popover>
                                <PopoverTrigger asChild className="hover:!bg-none ml-auto">
                                    <div className="flex items-center gap-2">
                                        <CalendarIcon size={20} className="hidden md:inline" />
                                        <Button
                                            variant="ghost"
                                            className="hover:!bg-transparent hover:text-muted-foreground justify-start text-left font-normal !p-0 border-b border-foreground/40 focus:outline-none rounded-none whitespace-nowrap overflow-hidden text-ellipsis"
                                            size="sm"
                                        >
                                            {date?.from ? (
                                                date.to ? (
                                                    <>
                                                        {format(date.from, "LLL dd, y")} -{" "}
                                                        {format(date.to, "LLL dd, y")}
                                                    </>
                                                ) : (
                                                    format(date.from, "LLL dd, y")
                                                )
                                            ) : (
                                                <span className="text-xs text-muted-foreground">Filter by date...</span>
                                            )}
                                        </Button>
                                    </div>
                                </PopoverTrigger>
                                <PopoverContent className="w-auto p-0" align="start">
                                    <Calendar
                                        initialFocus
                                        mode="range"
                                        defaultMonth={date?.from}
                                        selected={date}
                                        onSelect={(selectedDate) => {
                                            setDate(selectedDate ?? { from: new Date(), to: new Date() });
                                        }}
                                        numberOfMonths={1}
                                    />
                                    <div className="flex items-center justify-between px-3 pb-2 hover:!bg-none">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => {
                                                setDate({
                                                    from: undefined,
                                                    to: undefined,
                                                });
                                            }}
                                        >
                                            Reset
                                        </Button>
                                    </div>
                                </PopoverContent>
                            </Popover>
                        </div>

                        {/* Date Filter Status */}
                        {date.from && date.to && (
                            <div className="bg-green-50 border border-green-200 rounded-md p-3 mb-4">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                        <CalendarIcon size={16} className="text-green-600" />
                                        <span className="text-sm font-medium text-green-800">
                                            Date Filter Active: {format(date.from, "MMM dd, yyyy")} - {format(date.to, "MMM dd, yyyy")}
                                            {config.initialDateRange?.from && config.initialDateRange?.to && (
                                                <span className="ml-2 text-xs text-green-600">(Pre-selected from main page)</span>
                                            )}
                                        </span>
                                    </div>
                                    <div className="text-sm text-green-600">
                                        {(() => {
                                            const results = filteredData?.results || filteredData?.data?.results || [];
                                            const originalResults = data?.results || data?.data?.results || [];
                                            const siteVisits = results.filter((item: any) => !item.is_special_assignment).length;
                                            const specialAssignments = results.filter((item: any) => item.is_special_assignment).length;

                                            // For approved-site-visits, the total should include both site visits and special assignments
                                            const totalCount = config.componentType === 'approved-site-visits'
                                                ? results.length  // Use filtered results length which includes both types
                                                : originalResults.length;

                                            return (
                                                <div className="space-y-1">
                                                    <div>{results.length} of {totalCount} records</div>
                                                    {config.componentType === 'approved-site-visits' && (
                                                        <div className="text-xs">
                                                            {siteVisits} Site Visits • {specialAssignments} Special Assignments
                                                        </div>
                                                    )}
                                                </div>
                                            );
                                        })()}
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* Report Content */}
                        {isLoading || isFetching ? (
                            <div className="flex justify-center items-center h-64">
                                <SpinnerTemp type="spinner-double" size="md" />
                                <span className="ml-2 text-gray-600">
                                    {isLoading ? "Loading..." : "Refreshing..."}
                                </span>
                            </div>
                        ) : error ? (
                            <div className="bg-red-50 border border-red-200 rounded-md p-4">
                                <p className="text-red-800">
                                    Error loading data: {'Error fetching data, please try again later.'}
                                </p>
                            </div>
                        ) : ReportComponent ? (
                            <ReportComponent data={filteredData} />
                        ) : (
                            <div className="flex justify-center items-center h-64">
                                <p className="text-gray-500">No report component found for: {config.componentType}</p>
                            </div>
                        )}

                        {/* Pagination */}
                        {filteredData && !isLoading && !error && (
                            <Paginator
                                currentPage={currentPage}
                                setCurrentPage={setCurrentPage}
                                itemsPerPage={itemsPerPage}
                                setItemsPerPage={setItemsPerPage}
                                totalItems={filteredData?.count || (filteredData?.results?.length || filteredData?.data?.results?.length || 0)}
                                className='!font-bold border-t border-gray-200 dark:border-gray-700'
                            />
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default LazyModal;