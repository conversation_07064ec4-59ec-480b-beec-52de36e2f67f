import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { useAddEngagementMutation } from "@/redux/slices/services";
import { usePartialUpdateEngagementMutation } from "@/redux/slices/engagementsApiSlice";
import { useLazyGetUsersQuery } from "@/redux/slices/user";
import { useState } from "react";
import CustomSelectField from "@/components/CustomSelectField";

type Props = {
  prospect_id?: string;
  employee_no?: string;
  onOpenChange?: (e: boolean) => void;
  updateData?: any;
};

const EngagementForm = ({ prospect_id, onOpenChange, updateData }: Props) => {
  const [assignTo, setAssignTo] = useState("");
  const [addEngagement, { isLoading: submitting }] = useAddEngagementMutation();
  const [updateEngagement, { isLoading: updating }] =
    usePartialUpdateEngagementMutation();
  const [fetchUsers, { data: userData, isLoading: usersLoading }] =
    useLazyGetUsersQuery({});
  // form schema
  const formSchema = z.object({
    engagement_type: z
      .string()
      .min(1, { message: "Engagement Type is required." }),
    subject: z.string().min(1, { message: "subject is required." }),
    description: z.string().min(1, { message: "description is required." }),
    status: z.string().min(1, { message: "status is required." }),
    scheduled_at: z.string().min(1, { message: "Schedule Time is required." }),
    notes: z.string().optional(),
    follow_up_required: z.boolean().default(false),
    follow_up_date: z.string().optional(),
  });

  // instantiate react hook form
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      engagement_type: updateData?.engagement_type || "",
      subject: updateData?.subject || "",
      description: updateData?.description || "",
      status: updateData?.status || "",
      scheduled_at: updateData?.scheduled_at || "",
      notes: updateData?.notes || "",
      follow_up_required: updateData?.follow_up_required || false,
      follow_up_date: updateData?.follow_up_date || "",
    },
  });

  const handleEngagementSubmit = async (data: any) => {
    let formData = updateData
      ? { ...data, engagement_id: updateData?.engagement_id }
      : {
          ...data,
          prospect_id: prospect_id,
          client_status: "prospect",
          assigned_to: assignTo,
        };
    console.log("formData", formData);
    try {
      let res;
      if (updateData) {
        res = await updateEngagement(formData).unwrap();
      } else {
        res = await addEngagement(formData).unwrap();
      }
      if (res?.engagement_id) {
        toast.success(
          `Engagement ${updateData ? "updated" : "added"} successfully`
        );
        onOpenChange && onOpenChange(false);
      } else {
        toast.error("Failed to add");
        return;
      }
    } catch (error: any) {
      console.log("error", error);
      toast.error(error?.data ? error?.data?.error : "Somethe went wrong");
    }
  };

  return (
    <div className="">
      <form onSubmit={handleSubmit(handleEngagementSubmit)}>
        <div className="space-y-2">
          <div>
            <label className="ml-1 text-sm" htmlFor="assignTo">
              Assign To
            </label>
            <div className="space-y-2 flex flex-col my-4">
              <CustomSelectField
                valueField="employee_no"
                labelField="fullnames"
                data={userData?.data?.results}
                queryFunc={fetchUsers}
                setValue={setAssignTo}
                loader={usersLoading}
                useSearchField={true}
              />
            </div>
          </div>
          <div>
            <label className="ml-1 text-sm">Engagement Type*</label>
            <div className="relative pt-1">
              <select
                {...register("engagement_type")}
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              >
                <option value="Call">Call</option>
                <option value="Email">Email</option>
                <option value="Meeting">Meeting</option>
                <option value="SMS">SMS</option>
                <option value="Chat">Chat</option>
                <option value="Visit">Visit</option>
                <option value="Event">Event</option>
                <option value="Follow-up">Follow-up</option>
              </select>
            </div>
            {errors.engagement_type && (
              <p className="text-red-500 text-xs pl-3">
                {errors.engagement_type.message}
              </p>
            )}
          </div>

          <div>
            <label className="ml-1 text-sm">Subject*</label>
            <div className="relative pt-1">
              <input
                {...register("subject")}
                placeholder="Subject"
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              />
            </div>
            {errors.subject && (
              <p className="text-red-500 text-xs pl-3">
                {errors.subject.message}
              </p>
            )}
          </div>

          <div>
            <label className="ml-1 text-sm">Descriptions*</label>
            <div className="relative pt-1">
              <textarea
                {...register("description")}
                rows={3}
                placeholder="Description"
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              ></textarea>
            </div>
            {errors.description && (
              <p className="text-red-500 text-xs pl-3">
                {errors.description.message}
              </p>
            )}
          </div>

          <div>
            <label className="ml-1 text-sm">Status</label>
            <div className="relative pt-1">
              <select
                {...register("status")}
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              >
                <option value="Scheduled">Scheduled</option>
                <option value="In Progress">In Progress</option>
                <option value="Completed">Completed</option>
                <option value="Cancelled">Cancelled</option>
                <option value="Rescheduled">Rescheduled</option>
              </select>
            </div>
            {errors.status && (
              <p className="text-red-500 text-xs pl-3">
                {errors.status.message}
              </p>
            )}
          </div>

          <div>
            <label className="ml-1 text-sm">Scheduled At</label>
            <div className="relative pt-1">
              <input
                type="datetime-local"
                {...register("scheduled_at")}
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              />
            </div>
            {errors.scheduled_at && (
              <p className="text-red-500 text-xs pl-3">
                {errors.scheduled_at.message}
              </p>
            )}
          </div>

          <div className="py-1">
            <div className="flex items-center gap-2 pl-2 border py-2 rounded my-3">
              <div className="relative">
                <input type="checkbox" {...register("follow_up_required")} />
              </div>
              <label className="ml-1 text-sm">Follow Up Required</label>
              {errors.follow_up_required && (
                <p className="text-red-500 text-xs pl-3">
                  {errors.follow_up_required.message}
                </p>
              )}
            </div>
          </div>

          {watch().follow_up_required && (
            <div>
              <label className="ml-1 text-sm">Follow Up Date</label>
              <div className="relative pt-1">
                <input
                  type="date"
                  {...register("follow_up_date")}
                  placeholder="Your Email"
                  className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
                />
              </div>
              {errors.follow_up_date && (
                <p className="text-red-500 text-xs pl-3">
                  {errors.follow_up_date.message}
                </p>
              )}
            </div>
          )}

          <div>
            <label className="ml-1 text-sm">Notes</label>
            <div className="relative pt-1">
              <textarea
                {...register("notes")}
                rows={3}
                placeholder="Notes"
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              ></textarea>
            </div>
            {errors.notes && (
              <p className="text-red-500 text-xs pl-3">
                {errors.notes.message}
              </p>
            )}
          </div>

          <div className="w-full">
            {submitting || updating ? (
              <SpinnerTemp type="spinner-double" size="sm" />
            ) : (
              <Button type="submit" className="w-full">
                Submit
              </Button>
            )}
          </div>
        </div>
      </form>
    </div>
  );
};

export default EngagementForm;
