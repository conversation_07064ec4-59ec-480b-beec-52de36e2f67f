import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { formatDateTime } from "@/utils/formatDate";

type Props = {
  data: any;
};

const ComplaintsList = ({ data }: Props) => {
  return (
    <div className="border  rounded">
      <Table>
        <TableHeader className="bg-accent">
          <TableRow className="!font-bold">
            <TableHead className="!font-bold">Title</TableHead>
            <TableHead className="!font-bold">Category</TableHead>
            <TableHead className="!font-bold">Priority</TableHead>
            <TableHead className="!font-bold">Status</TableHead>
            <TableHead className="!font-bold">Date</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data?.map((rowData: any) => (
            <TableRow
              key={rowData?.complaint_id}
              className="hover:!bg-transparent bg-white dark:bg-transparent dark:hover:!bg-gray-300/10"
            >
              <TableCell className="!py-2.5 text-xs">
                {rowData?.title}
              </TableCell>
              <TableCell className="!py-2.5 text-xs">
                {rowData?.category}
              </TableCell>
              <TableCell className="!py-2.5 text-xs">
                <Badge variant="secondary" className="px-4">
                  {rowData?.priority}
                </Badge>
              </TableCell>
              <TableCell className="!py-2.5 text-xs">
                <Badge variant="default" className="px-4">
                  {rowData?.status}
                </Badge>
              </TableCell>
              <TableCell className="!py-2.5 text-xs">
                {formatDateTime(rowData?.created_at)}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default ComplaintsList;
