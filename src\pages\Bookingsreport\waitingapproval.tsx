import { LinkButton } from "@/components/custom/buttons/buttons";
import SimpleTable, { ColumnDefinitionST } from "@/components/custom/tables/SimpleTable";
import { FileText, CircleDot } from "lucide-react";

const WaitingApprovalBookingsReport = () => {
  const WaitingApproval = [
    {
      id: 1,
      name: "<PERSON><PERSON><PERSON><PERSON>",
      status: "received",
      type: "Waiting",
      attachments: (
        <LinkButton
          variant="link"
          className="text-blue-600 hover:text-blue-800 flex items-center gap-1 transition-colors"
        >
          <FileText className="w-4 h-4" />
          Attachment
        </LinkButton>
      ),
    },
    {
      id: 2, 
      name: "<PERSON><PERSON>",
      status: "received",
      type:"waiting",
      attachments: (
        <LinkButton
          variant="link"
          className="text-blue-600 hover:text-blue-800 flex items-center gap-1 transition-colors"
        >
          <FileText className="w-4 h-4" />
          Attachment
        </LinkButton>
      ),
    },
  ];

  const columns: ColumnDefinitionST<typeof WaitingApproval[0]>[] = [
    {
      key: "id",
      header: "ID",
      headerClassName: "font-semibold text-gray-700 px-6 py-4 bg-gray-50",
      cellClassName: "px-6 py-4 text-gray-600",
    },
    {
      key: "name",
      header: "Name",
      headerClassName: "font-semibold text-gray-700 px-6 py-4 bg-gray-50",
      cellClassName: "px-6 py-4 text-gray-600",
    },
    {
      key: "attachments",
      header: "Attachments",
      headerClassName: "font-semibold text-gray-700 px-6 py-4 bg-gray-50",
      cellClassName: "px-6 py-4",
    },
    {
        key: "type",
        header: "type",
        headerClassName: "font-semibold text-gray-700 px-6 py-4 bg-gray-50",
        cellClassName: "px-6 py-4",
      },
    {
      key: "status",
      header: "Status",
      headerClassName: "font-semibold text-gray-700 px-6 py-4 bg-gray-50",
      cellClassName: "px-6 py-4",
      renderCell: (row) => (
        <span
          className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium gap-1 ${
            row.status === "received"
              ? "bg-purple-100 text-purple-800"
              : row.status === "Completed"
              ? "bg-green-100 text-green-800"
              : row.status === "Pending"
              ? "bg-yellow-100 text-yellow-800"
              : "bg-red-100 text-red-800"
          }`}
        >
          <CircleDot className="w-4 h-4" />
          {row.status.charAt(0).toUpperCase() + row.status.slice(1)} {/* Capitalize status */}
        </span>
      ),
    },
  ];

  return (
    <div >
      <h2 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-2">
        
        
      </h2>
      <SimpleTable
        data={WaitingApproval}
        columns={columns}
        containerClassName="rounded-lg overflow-hidden border border-gray-200"
        tableClassName="min-w-full border-collapse bg-white"
        hoverable={true}
        striped={true}
        tRowClassName="transition-colors duration-150"
      />
    </div>
  );
};

export default WaitingApprovalBookingsReport;