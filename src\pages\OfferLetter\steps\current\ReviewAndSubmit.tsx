import React from 'react';

interface ReviewAndSubmitProps {
  formData: any;
  onBack: () => void;
  onSubmit: () => void;
}

const ReviewAndSubmit: React.FC<ReviewAndSubmitProps> = ({
  formData,
  onBack,
  onSubmit,
}) => {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit();
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold text-gray-800 mb-2">Review & Submit</h2>
        <p className="text-gray-600 mb-6">Please review your information before submitting your application.</p>
      </div>

      <div className="space-y-6">
        <div className="bg-gray-50 p-6 rounded-lg">
          <h3 className="text-lg font-medium text-gray-700 mb-4">Personal Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div><strong>Name:</strong> {formData.firstName} {formData.lastName}</div>
            <div><strong>Email:</strong> {formData.email}</div>
            <div><strong>Phone:</strong> {formData.phone}</div>
            <div><strong>National ID:</strong> {formData.nationalId}</div>
            <div><strong>Country:</strong> {formData.country}</div>
            <div><strong>City:</strong> {formData.city}</div>
          </div>
        </div>

        <div className="bg-gray-50 p-6 rounded-lg">
          <h3 className="text-lg font-medium text-gray-700 mb-4">Next of Kin Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div><strong>Name:</strong> {formData.nextOfKinName}</div>
            <div><strong>Relationship:</strong> {formData.nextOfKinRelationship}</div>
            <div><strong>Email:</strong> {formData.nextOfKinEmail}</div>
            <div><strong>Phone:</strong> {formData.nextOfKinPhone}</div>
          </div>
        </div>

        <div className="bg-gray-50 p-6 rounded-lg">
          <h3 className="text-lg font-medium text-gray-700 mb-4">Payment Plans</h3>
          <div className="space-y-2 text-sm">
            {formData.selectedPayments?.fo196 && (
              <div><strong>Plot FO196:</strong> {formData.selectedPayments.fo196}</div>
            )}
            {formData.selectedPayments?.fg226 && (
              <div><strong>Plot FG226:</strong> {formData.selectedPayments.fg226}</div>
            )}
          </div>
        </div>

        <div className="bg-green-50 p-6 rounded-lg">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked
              disabled
              className="text-green-500"
            />
            <span className="text-green-700">Terms and Conditions accepted</span>
          </div>
        </div>
      </div>

      <div className="flex justify-between pt-6">
        <button
          type="button"
          onClick={onBack}
          className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
        >
          ← Go Back
        </button>
        <button
          type="submit"
          className="px-6 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors"
        >
          Submit Application →
        </button>
      </div>
    </form>
  );
};

export default ReviewAndSubmit;