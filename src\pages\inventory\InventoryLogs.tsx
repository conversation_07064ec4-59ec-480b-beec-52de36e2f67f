import { Screen } from "@/app-components/layout/screen";
import BaseModal from "@/components/custom/modals/BaseModal";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { DataTable } from "@/components/custom/tables/Table1";
import { But<PERSON> } from "@/components/ui/button";
import { useGetInventoryLogsQuery } from "@/redux/slices/projects";
import { formatDateTime } from "@/utils/formatDate";
import { ColumnDef } from "@tanstack/react-table";
import { useState } from "react";

type Props = {};

interface LogTypes {
  id: number | string;
  system_level: string;
  user: string;
  action: string;
  module: string;
  message: string;
  timestamp: string;
}

const InventoryLogs = ({}: Props) => {
  const { data: logs, isLoading } = useGetInventoryLogsQuery({});
  const [isLogModalOpen, setIsLogModalOpen] = useState(false);
  const [openLog, setOpenLog] = useState<LogTypes | undefined>();

  const handleViewLog = (rowData: LogTypes) => {
    setOpenLog(rowData);
    setIsLogModalOpen(true);
  };

  const columns: ColumnDef<LogTypes>[] = [
    {
      accessorKey: "system_level",
      header: "System Level",
      cell: (info) => info.getValue(),
      enableColumnFilter: false,
    },
    {
      accessorKey: "user",
      header: "Action By",
      cell: (info) => info.getValue(),
      enableColumnFilter: false,
    },
    {
      accessorKey: "module",
      header: "Module",
      cell: (info) => info.getValue(),
      enableColumnFilter: false,
    },
    {
      accessorKey: "action",
      header: "Action Type",
      cell: (info) => (
        <span
          className={`${
            info?.getValue() === "Create"
              ? "bg-primary text-white"
              : info?.getValue() === "Edit"
              ? "bg-blue-400 text-white"
              : "bg-destructive text-white"
          } text-center px-2 pt-1 pb-1.5 rounded-full`}
        >
          {info?.getValue() as string}
        </span>
      ),
      enableColumnFilter: false,
    },
    {
      accessorKey: "message",
      header: "Task",
      cell: (info) => `${(info.getValue() as string)?.substring(0, 50)}...`,
      enableColumnFilter: false,
    },
    {
      accessorKey: "timestamp",
      header: "Time",
      cell: (info) => formatDateTime(info.getValue()),
      enableColumnFilter: false,
    },
    {
      accessorKey: "id",
      header: "Action",
      cell: ({ row }) => {
        const a = row.original;
        return (
          <>
            <Button variant="outline" onClick={() => handleViewLog(a)}>
              View
            </Button>
          </>
        );
      },
      enableColumnFilter: false,
      enableSorting: false,
    },
  ];

  return (
    <Screen>
      <div className="min-h-screen space-y-4 p-4">
        <div className=" rounded-lg shadow-sm border">
          <div className="p-4 border-b">
            <h1 className="text-xl md:text-2xl font-semibold">
              Inventory Logs
            </h1>
          </div>
          <div className="p-4">
            {isLoading ? (
              <div className="flex justify-center items-center h-12">
                <SpinnerTemp type="spinner-double" size="md" />
              </div>
            ) : (
              <>
                <DataTable<LogTypes>
                  data={logs?.data?.results || []}
                  columns={columns}
                  title="Inventory Logs"
                  enableToolbar={true}
                  enableExportToExcel={true}
                  enablePagination={true}
                  enablePrintPdf={true}
                  enableFullScreenToggle={true}
                  enableColumnControl={false}
                  enableSorting={true}
                  enableSelectColumn={true}
                  enableSelectToolbar={false}
                  enableSelectToolbarButtonExportToExcel={true}
                  enableSelectToolbarButtonPrintToPdf={true}
                  containerClassName=""
                  tableClassName=""
                  tHeadClassName="bg-secondary"
                  tHeadCellsClassName="text-primary"
                  tBodyClassName=""
                  tBodyTrClassName=""
                  tBodyCellsClassName=""
                />

                {isLogModalOpen && openLog && (
                  <BaseModal
                    size="md"
                    isOpen={isLogModalOpen}
                    description="Log Details View"
                    onOpenChange={() => {
                      setOpenLog(undefined);
                      setIsLogModalOpen(false);
                    }}
                    title="Log Details"
                  >
                    <div className="grid md:grid-cols-2 sm:grid-cols-1 gap-3">
                      <div className="flex flex-col ">
                        <p className="font-bold text-sm">Action Level</p>
                        <p className="text-sm">{openLog?.system_level}</p>
                      </div>
                      <div className="flex flex-col ">
                        <p className="font-bold text-sm">Action By</p>
                        <p className="text-sm">{openLog?.user}</p>
                      </div>
                      <div className="flex flex-col ">
                        <p className="font-bold text-sm">Module</p>
                        <p className="text-sm">{openLog?.module}</p>
                      </div>
                      <div className="flex flex-col ">
                        <p className="font-bold text-sm">Time</p>
                        <p className="text-sm">
                          {formatDateTime(openLog?.timestamp)}
                        </p>
                      </div>
                      <div className="flex flex-col ">
                        <p className="font-bold text-sm">Action Type</p>
                        <p className="text-sm py-2">
                          <span
                            className={`${
                              openLog?.action === "Create"
                                ? "text-primary border-primary"
                                : openLog?.action === "Edit"
                                ? "text-blue-400 border-blue-400"
                                : "text-destructive border-destructive"
                            } text-center px-2 pt-1 pb-1.5 rounded-full border`}
                          >
                            {openLog?.action}
                          </span>
                        </p>
                      </div>
                      <div
                        className={`${
                          openLog?.action === "Create"
                            ? "bg-primary/30"
                            : openLog?.action === "Edit"
                            ? "bg-blue-400/30"
                            : "bg-destructive/30"
                        } flex flex-col md:col-span-2 sm:col-span-1 border rounded p-2`}
                      >
                        <p className="font-bold text-sm">Action Performed</p>
                        <p className="text-sm">{openLog?.message}</p>
                      </div>
                    </div>
                  </BaseModal>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </Screen>
  );
};

export default InventoryLogs;
