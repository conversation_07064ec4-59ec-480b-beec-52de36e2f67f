import React, { useState, use<PERSON>emo } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>ontent, 
  <PERSON>alog<PERSON>eader, 
  DialogTitle,
  DialogDescription
} from '@/components/ui/dialog';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  GitGraph,
  BarChart3,
  Layers,
  Search,
  Download,
  RefreshCw,
  Users,
  DollarSign,
  Percent,
  Building2,
  UserCheck,
  TrendingUp,
  ArrowUpDown,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight
} from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  useGetLeadSourceQuery, 
  useGetLeadSourceCategoriesQuery, 
  use<PERSON>etLeadSourceSubCategoriesQuery 
} from '@/redux/slices/propects';

// Type definitions
interface LeadSource {
  id: number;
  name: string;
  description?: string;
  ref_code: string;
  sales?: number;
  active_leads?: number;
  dormant_leads?: number;
  ongoing_sales?: number;
  completed_sales?: number;
  dropped_sales?: number;
  lead_source_category?: {
    id: number;
    name: string;
  };
  lead_source_subcategory?: {
    id: number;
    name: string;
  };
}

interface Category {
  id: number;
  name: string;
  description?: string;
}

interface SubCategory {
  id: number;
  name: string;
  description?: string;
  manager_name?: string;
  manager?: string;
  lead_source_category: number;
}

interface PaginationData {
  current_page: number;
  last_page: number;
  per_page: number;
  total_data: number;
  links: {
    next: string | null;
    previous: string | null;
  };
}

interface DataModalsProps {
  leadSourcesOpen: boolean;
  setLeadSourcesOpen: (open: boolean) => void;
  leadCategoriesOpen: boolean;
  setLeadCategoriesOpen: (open: boolean) => void;
  leadSubCategoriesOpen: boolean;
  setLeadSubCategoriesOpen: (open: boolean) => void;
}

const DataModals = ({
  leadSourcesOpen,
  setLeadSourcesOpen,
  leadCategoriesOpen,
  setLeadCategoriesOpen,
  leadSubCategoriesOpen,
  setLeadSubCategoriesOpen
}: DataModalsProps) => {
  // State for filters and search
  const [sourceSearch, setSourceSearch] = useState('');
  const [categorySearch, setCategorySearch] = useState('');
  const [subCategorySearch, setSubCategorySearch] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState<'name' | 'sales' | 'leads' | 'conversion'>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // Pagination state
  const [sourcePage, setSourcePage] = useState(1);
  const [categoryPage, setCategoryPage] = useState(1);
  const [subCategoryPage, setSubCategoryPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  // Build query parameters
  const buildSourceParams = () => ({
    page: sourcePage,
    page_size: pageSize,
    search: sourceSearch || undefined,
    ordering: sortOrder === 'desc' ? `-${sortBy}` : sortBy
  });

  const buildCategoryParams = () => ({
    page: categoryPage,
    page_size: pageSize,
    search: categorySearch || undefined
  });

  const buildSubCategoryParams = () => ({
    page: subCategoryPage,
    page_size: pageSize,
    search: subCategorySearch || undefined,
    lead_source_category: selectedCategory !== 'all' ? selectedCategory : undefined
  });

  // Fetch data with pagination
  const { 
    data: leadSourcesResponse, 
    isLoading: sourcesLoading, 
    error: sourcesError,
    refetch: refetchSources
  } = useGetLeadSourceQuery(buildSourceParams());
  
  const { 
    data: categoriesResponse, 
    isLoading: categoriesLoading, 
    error: categoriesError,
    refetch: refetchCategories
  } = useGetLeadSourceCategoriesQuery(buildCategoryParams());
  
  const { 
    data: subCategoriesResponse, 
    isLoading: subCategoriesLoading, 
    error: subCategoriesError,
    refetch: refetchSubCategories
  } = useGetLeadSourceSubCategoriesQuery(buildSubCategoryParams());
  
  // Extract data and pagination info from API responses
  const leadSources: LeadSource[] = leadSourcesResponse?.data?.results || [];
  const sourcesPagination: PaginationData | null = leadSourcesResponse?.data || null;
  
  const categories: Category[] = categoriesResponse?.data?.results || [];
  const categoriesPagination: PaginationData | null = categoriesResponse?.data || null;
  
  const subCategories: SubCategory[] = subCategoriesResponse?.data?.results || [];
  const subCategoriesPagination: PaginationData | null = subCategoriesResponse?.data || null;
  
  // For calculating stats, we need all categories data (not paginated)
  const { data: allCategoriesResponse } = useGetLeadSourceCategoriesQuery({ page_size: 1000 });
  const allCategories: Category[] = allCategoriesResponse?.data?.results || [];
  
  // For calculating category stats, we need all sources data
  const { data: allSourcesResponse } = useGetLeadSourceQuery({ page_size: 1000 });
  const allSources: LeadSource[] = allSourcesResponse?.data?.results || [];
  
  // Calculate metrics
  const getTotalLeads = (source: LeadSource): number => {
    return (source.active_leads || 0) + (source.dormant_leads || 0);
  };
  
  const getConversionRate = (source: LeadSource): number => {
    const totalLeads = getTotalLeads(source);
    if (totalLeads === 0) return 0;
    return ((source.sales || 0) / totalLeads) * 100;
  };

  const getPerformanceStatus = (conversionRate: number) => {
    if (conversionRate >= 10) return { status: 'excellent', color: 'bg-green-100 text-green-800' };
    if (conversionRate >= 5) return { status: 'good', color: 'bg-blue-100 text-blue-800' };
    if (conversionRate >= 1) return { status: 'average', color: 'bg-yellow-100 text-yellow-800' };
    return { status: 'poor', color: 'bg-red-100 text-red-800' };
  };

  // Get category statistics
  const getCategoryStats = (categoryId: number) => {
    const categorySources = allSources.filter((source: LeadSource) => 
      source.lead_source_category?.id === categoryId
    );
    
    const totalSales = categorySources.reduce((sum: number, source: LeadSource) => sum + (source.sales || 0), 0);
    const totalLeads = categorySources.reduce((sum: number, source: LeadSource) => sum + getTotalLeads(source), 0);
    const avgConversion = totalLeads > 0 ? (totalSales / totalLeads) * 100 : 0;
    
    return {
      sources: categorySources.length,
      totalSales,
      totalLeads,
      avgConversion
    };
  };

  const handleSort = (column: 'name' | 'sales' | 'leads' | 'conversion') => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
    setSourcePage(1); // Reset to first page when sorting
  };

  // Handle search with debounce effect
  const handleSourceSearch = (value: string) => {
    setSourceSearch(value);
    setSourcePage(1); // Reset to first page when searching
  };

  const handleCategorySearch = (value: string) => {
    setCategorySearch(value);
    setCategoryPage(1);
  };

  const handleSubCategorySearch = (value: string) => {
    setSubCategorySearch(value);
    setSubCategoryPage(1);
  };

  const handleCategoryFilter = (value: string) => {
    setSelectedCategory(value);
    setSubCategoryPage(1);
  };

  // Pagination component
  const PaginationControls = ({ 
    pagination, 
    currentPage, 
    onPageChange 
  }: { 
    pagination: PaginationData | null; 
    currentPage: number; 
    onPageChange: (page: number) => void; 
  }) => {
    if (!pagination) return null;

    const { current_page, last_page, total_data, per_page } = pagination;
    const startItem = (current_page - 1) * per_page + 1;
    const endItem = Math.min(current_page * per_page, total_data);

    return (
      <div className="flex items-center justify-between px-2 py-4">
        <div className="flex items-center space-x-2 text-sm text-gray-700">
          <span>
            Showing {startItem.toLocaleString()} to {endItem.toLocaleString()} of{' '}
            {total_data.toLocaleString()} results
          </span>
        </div>
        
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-700">Rows per page:</span>
            <Select 
              value={pageSize.toString()} 
              onValueChange={(value) => {
                setPageSize(parseInt(value));
                onPageChange(1);
              }}
            >
              <SelectTrigger className="w-20 h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="20">20</SelectItem>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="100">100</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex items-center space-x-1">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(1)}
              disabled={current_page === 1}
              className="h-8 w-8 p-0"
            >
              <ChevronsLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(current_page - 1)}
              disabled={current_page === 1}
              className="h-8 w-8 p-0"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            
            <div className="flex items-center space-x-1">
              <span className="text-sm text-gray-700">
                Page {current_page} of {last_page}
              </span>
            </div>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(current_page + 1)}
              disabled={current_page === last_page}
              className="h-8 w-8 p-0"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(last_page)}
              disabled={current_page === last_page}
              className="h-8 w-8 p-0"
            >
              <ChevronsRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    );
  };

  // Loading skeleton for tables
  const renderTableSkeleton = (rows: number, cols: number) => {
    return Array.from({ length: rows }).map((_, i) => (
      <TableRow key={i}>
        {Array.from({ length: cols }).map((_, j) => (
          <TableCell key={j}>
            <Skeleton className="h-4 w-full" />
          </TableCell>
        ))}
      </TableRow>
    ));
  };

  return (
    <>
      {/* Lead Sources Modal */}
      <Dialog open={leadSourcesOpen} onOpenChange={setLeadSourcesOpen}>
        <DialogContent className="max-w-7xl max-h-[90vh] overflow-auto">
          <DialogHeader className="pb-6">
            <DialogTitle className="flex items-center space-x-3 text-2xl font-bold text-gray-800">
              <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                <GitGraph className="w-6 h-6 text-white" />
              </div>
              <span>Lead Sources Performance</span>
            </DialogTitle>
            <DialogDescription className="text-gray-600 text-lg">
              Comprehensive analysis of all lead sources and their conversion metrics
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-6">
            {/* Search and Filters */}
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <Search className="w-4 h-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                  <Input 
                    placeholder="Search sources..." 
                    className="pl-10 w-64"
                    value={sourceSearch}
                    onChange={(e) => handleSourceSearch(e.target.value)}
                  />
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm" className="flex items-center space-x-2">
                  <Download className="w-4 h-4" />
                  <span>Export</span>
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="flex items-center space-x-2"
                  onClick={() => refetchSources()}
                >
                  <RefreshCw className="w-4 h-4" />
                  <span>Refresh</span>
                </Button>
              </div>
            </div>

            {sourcesError ? (
              <div className="bg-red-50 border-2 border-red-200 p-6 rounded-xl text-center">
                <div className="text-red-600 font-medium text-lg">Failed to load lead sources data</div>
                <p className="text-red-500 mt-2">Please try refreshing the page</p>
              </div>
            ) : (
              <div className="border rounded-lg">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead 
                        className="cursor-pointer hover:bg-gray-50"
                        onClick={() => handleSort('name')}
                      >
                        <div className="flex items-center space-x-1">
                          <span>Source Name</span>
                          <ArrowUpDown className="w-4 h-4" />
                        </div>
                      </TableHead>
                      <TableHead>Reference Code</TableHead>
                      <TableHead 
                        className="cursor-pointer hover:bg-gray-50 text-right"
                        onClick={() => handleSort('sales')}
                      >
                        <div className="flex items-center justify-end space-x-1">
                          <span>Sales</span>
                          <ArrowUpDown className="w-4 h-4" />
                        </div>
                      </TableHead>
                      <TableHead className="text-right">Total Leads</TableHead>
                      <TableHead className="text-right">Conversion Rate</TableHead>
                      <TableHead>Performance</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Sub-Category</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {sourcesLoading ? renderTableSkeleton(pageSize, 8) : (
                      leadSources.map((source: LeadSource) => {
                        const totalLeads = getTotalLeads(source);
                        const conversionRate = getConversionRate(source);
                        const performance = getPerformanceStatus(conversionRate);
                        
                        return (
                          <TableRow key={source.id} className="hover:bg-gray-50">
                            <TableCell className="font-medium">
                              <div>
                                <div className="font-semibold text-gray-900">{source.name}</div>
                                {source.description && (
                                  <div className="text-sm text-gray-500 mt-1">{source.description}</div>
                                )}
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge variant="secondary" className="text-xs">
                                {source.ref_code}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="space-y-1">
                                <div className="font-semibold text-blue-600">{source.sales || 0}</div>
                                <div className="text-xs text-gray-500">
                                  O: {source.ongoing_sales || 0} | C: {source.completed_sales || 0} | D: {source.dropped_sales || 0}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="space-y-1">
                                <div className="font-semibold">{totalLeads}</div>
                                <div className="text-xs text-gray-500">
                                  A: {source.active_leads || 0} | D: {source.dormant_leads || 0}
                                </div>
                              </div>
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="font-semibold">{conversionRate.toFixed(1)}%</div>
                            </TableCell>
                            <TableCell>
                              <Badge className={performance.color}>
                                {performance.status}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <Badge variant="outline" className="text-xs">
                                {source.lead_source_category?.name || 'N/A'}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <Badge variant="outline" className="text-xs">
                                {source.lead_source_subcategory?.name || 'N/A'}
                              </Badge>
                            </TableCell>
                          </TableRow>
                        );
                      })
                    )}
                  </TableBody>
                </Table>
                
                <PaginationControls 
                  pagination={sourcesPagination}
                  currentPage={sourcePage}
                  onPageChange={setSourcePage}
                />
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Lead Categories Modal */}
      <Dialog open={leadCategoriesOpen} onOpenChange={setLeadCategoriesOpen}>
        <DialogContent className="max-w-6xl max-h-[85vh] overflow-auto">
          <DialogHeader className="pb-6">
            <DialogTitle className="flex items-center space-x-3 text-2xl font-bold text-gray-800">
              <div className="p-2 bg-gradient-to-r from-green-500 to-blue-600 rounded-lg">
                <BarChart3 className="w-6 h-6 text-white" />
              </div>
              <span>Lead Source Categories</span>
            </DialogTitle>
            <DialogDescription className="text-gray-600 text-lg">
              Overview of lead source categories with performance metrics
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-6">
            {/* Search */}
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
              <div className="relative">
                <Search className="w-4 h-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                <Input 
                  placeholder="Search categories..." 
                  className="pl-10 w-64"
                  value={categorySearch}
                  onChange={(e) => handleCategorySearch(e.target.value)}
                />
              </div>
              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm" className="flex items-center space-x-2">
                  <Download className="w-4 h-4" />
                  <span>Export</span>
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="flex items-center space-x-2"
                  onClick={() => refetchCategories()}
                >
                  <RefreshCw className="w-4 h-4" />
                  <span>Refresh</span>
                </Button>
              </div>
            </div>

            {categoriesError ? (
              <div className="bg-red-50 border-2 border-red-200 p-6 rounded-xl text-center">
                <div className="text-red-600 font-medium text-lg">Failed to load categories data</div>
                <p className="text-red-500 mt-2">Please try refreshing the page</p>
              </div>
            ) : (
              <div className="border rounded-lg">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Category Name</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead className="text-center">Sources</TableHead>
                      <TableHead className="text-right">Total Sales</TableHead>
                      <TableHead className="text-right">Total Leads</TableHead>
                      <TableHead className="text-right">Avg. Conversion</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {categoriesLoading ? renderTableSkeleton(pageSize, 6) : (
                      categories.map((category: Category) => {
                        const stats = getCategoryStats(category.id);
                        
                        return (
                          <TableRow key={category.id} className="hover:bg-gray-50">
                            <TableCell className="font-medium">
                              <div className="flex items-center space-x-2">
                                <div className="w-3 h-3 bg-gradient-to-r from-green-400 to-blue-500 rounded-full" />
                                <span className="font-semibold text-gray-900">{category.name}</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="text-sm text-gray-600 max-w-xs">
                                {category.description || 'No description available'}
                              </div>
                            </TableCell>
                            <TableCell className="text-center">
                              <Badge variant="secondary" className="font-medium">
                                {stats.sources}
                              </Badge>
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex items-center justify-end space-x-1">
                                <DollarSign className="w-4 h-4 text-blue-500" />
                                <span className="font-semibold text-blue-600">{stats.totalSales}</span>
                              </div>
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex items-center justify-end space-x-1">
                                <Users className="w-4 h-4 text-purple-500" />
                                <span className="font-semibold text-purple-600">{stats.totalLeads}</span>
                              </div>
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex items-center justify-end space-x-1">
                                <Percent className="w-4 h-4 text-gray-500" />
                                <span className="font-semibold">{stats.avgConversion.toFixed(1)}%</span>
                              </div>
                            </TableCell>
                          </TableRow>
                        );
                      })
                    )}
                  </TableBody>
                </Table>
                
                <PaginationControls 
                  pagination={categoriesPagination}
                  currentPage={categoryPage}
                  onPageChange={setCategoryPage}
                />
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Lead Sub-Categories Modal */}
      <Dialog open={leadSubCategoriesOpen} onOpenChange={setLeadSubCategoriesOpen}>
        <DialogContent className="max-w-7xl max-h-[90vh] overflow-auto">
          <DialogHeader className="pb-6">
            <DialogTitle className="flex items-center space-x-3 text-2xl font-bold text-gray-800">
              <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg">
                <Layers className="w-6 h-6 text-white" />
              </div>
              <span>Lead Source Sub-Categories</span>
            </DialogTitle>
            <DialogDescription className="text-gray-600 text-lg">
              Detailed breakdown of sub-categories with their managers and parent categories
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-6">
            {/* Search and Filters */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 p-4 bg-gray-50 rounded-xl">
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <Search className="w-4 h-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                  <Input 
                    placeholder="Search sub-categories..." 
                    className="pl-10 w-64"
                    value={subCategorySearch}
                    onChange={(e) => handleSubCategorySearch(e.target.value)}
                  />
                </div>
                <Select value={selectedCategory} onValueChange={handleCategoryFilter}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Filter by Category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {allCategories.map((cat: Category) => (
                      <SelectItem key={cat.id} value={cat.id.toString()}>
                        {cat.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm" className="flex items-center space-x-2">
                  <Download className="w-4 h-4" />
                  <span>Export</span>
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="flex items-center space-x-2"
                  onClick={() => refetchSubCategories()}
                >
                  <RefreshCw className="w-4 h-4" />
                  <span>Refresh</span>
                </Button>
              </div>
            </div>

            {subCategoriesError ? (
              <div className="bg-red-50 border-2 border-red-200 p-6 rounded-xl text-center">
                <div className="text-red-600 font-medium text-lg">Failed to load sub-categories data</div>
                <p className="text-red-500 mt-2">Please try refreshing the page</p>
              </div>
            ) : (
              <div className="border rounded-lg">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Sub-Category Name</TableHead>
                      <TableHead>Parent Category</TableHead>
                      <TableHead>Manager</TableHead>
                      <TableHead>Manager Code</TableHead>
                      <TableHead className="text-center">Active Sources</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {subCategoriesLoading ? renderTableSkeleton(pageSize, 5) : (
                      subCategories.map((subCategory: SubCategory) => {
                        const parentCategory = allCategories.find((cat: Category) => cat.id === subCategory.lead_source_category);
                        const sourcesInSubCategory = allSources.filter((source: LeadSource) => 
                          source.lead_source_subcategory?.id === subCategory.id
                        );
                        
                        return (
                        <TableRow key={subCategory.id} className="hover:bg-gray-50">
                        <TableCell className="font-medium">
                            <div className="flex items-center space-x-2">
                            <div className="w-3 h-3 bg-gradient-to-r from-purple-400 to-pink-500 rounded-full" />
                            <div>
                                <div className="font-semibold text-gray-900">{subCategory.name}</div>
                                {subCategory.description && (
                                <div className="text-xs text-gray-500 mt-1">{subCategory.description}</div>
                                )}
                            </div>
                            </div>
                        </TableCell>
                        <TableCell>
                            <Badge variant="outline" className="text-xs">
                            {parentCategory?.name || 'N/A'}
                            </Badge>
                        </TableCell>
                        <TableCell>
                            <div className="flex items-center space-x-2">
                            <UserCheck className="w-4 h-4 text-purple-500" />
                            <span className="text-sm font-medium">
                                {subCategory.manager_name || 'No Manager Assigned'}
                            </span>
                            </div>
                        </TableCell>
                        <TableCell>
                            {subCategory.manager ? (
                            <Badge variant="secondary" className="text-xs">
                                {subCategory.manager}
                            </Badge>
                            ) : (
                            <span className="text-gray-400 text-sm">-</span>
                            )}
                        </TableCell>
                        <TableCell className="text-center">
                            <div className="flex items-center justify-center space-x-1">
                            <Building2 className="w-4 h-4 text-gray-500" />
                            <Badge variant="secondary" className="font-medium">
                                {sourcesInSubCategory.length}
                            </Badge>
                            </div>
                        </TableCell>
                        </TableRow>
                        )})
                    )}
                  </TableBody>
                </Table>
                
                <PaginationControls 
                  pagination={subCategoriesPagination}
                  currentPage={subCategoryPage}
                  onPageChange={setSubCategoryPage}
                />
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default DataModals;