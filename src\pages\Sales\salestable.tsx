import { useState, useEffect, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { RefreshCw } from "lucide-react";

import { Button } from "@/components/ui/button";
import { DataTable } from "@/components/custom/tables/Table1";
import { Screen } from "@/app-components/layout/screen";
import { getColumns } from "./Column";
import { salesData } from "./Salesdata";
import { SearchComponent } from "./search";
import { TableSkeleton } from "./tableskeleton";
// Import TabSale
import { Sales } from "./type";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import TabSale from "./tabsale";

const SalesPage = () => {
  const [searchValue, setSearchValue] = useState("");
  const [transactions, setTransactions] = useState<Sales[]>(salesData);
  const [activeStatus, setActiveStatus] = useState<
    "ALL" | "ACTIVE" | "DROPPED"
  >("ALL");
  const [isLoading, setIsLoading] = useState(true);

  // Simulate loading state
  useEffect(() => {
    const timer = setTimeout(() => setIsLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  const handleCloseTransaction = (txnId: string) => {
    setTransactions((prevTransactions) =>
      prevTransactions.map((txn) =>
        txn.id === txnId ? { ...txn, status: "DROPPED" as const } : txn
      )
    );
    console.log(`Transaction ${txnId} marked as closed`);
  };

  const handleRefresh = () => {
    setSearchValue("");
    setActiveStatus("ALL");
    setTransactions(salesData);
  };

  // Filter transactions based on search and status
  const filteredTransactions = useMemo(() => {
    return transactions.filter((txn) => {
      const matchesStatus =
        activeStatus === "ALL" || txn.status === activeStatus;
      const matchesSearch = searchValue
        ? Object.values(txn).some((value) =>
            value.toString().toLowerCase().includes(searchValue.toLowerCase())
          )
        : true;
      return matchesStatus && matchesSearch;
    });
  }, [transactions, activeStatus, searchValue]);

  return (
    <Screen>
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-800 dark:text-white">
            All Sales
          </h1>
          <SearchComponent
            searchValue={""}
            setSearchValue={function (value: string): void {
              throw new Error("Function not implemented.");
            }}
          >
            <input
              type="text"
              placeholder="Search..."
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              className="border border-gray-300 rounded-md p-2 w-full sm:w-1/3"
            />
            <Button
              variant="outline"
              size="sm"
              className="ml-2"
              onClick={handleRefresh}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>
          </SearchComponent>
        </div>

        {/* Replace SummaryCard and StatusToggleButtons with TabSale */}
        <TabSale
          transactions={transactions}
          activeStatus={activeStatus}
          setActiveStatus={setActiveStatus}
        />
      </motion.div>
    </Screen>
  );
};

export default SalesPage;
