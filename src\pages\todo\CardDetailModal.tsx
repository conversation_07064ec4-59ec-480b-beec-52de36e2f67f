import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  X,
  Edit3,
  Trash2,
  User,
  Tag,
  Calendar,
  Paperclip,
  Send,
  Link,
  Archive,
} from "lucide-react";
import { SonnerToaster, toast as sonnerToast } from "sonner";
import { Button } from "@/components/ui/button";
import { ToDo } from "./types";

interface CardDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  card: ToDo;
  onDelete?: (cardId: string) => void;
}

export const CardDetailModal: React.FC<CardDetailModalProps> = ({ isOpen, onClose, card, onDelete }) => {
  const [newComment, setNewComment] = useState("");

  const handleClose = () => {
    sonnerToast.info("Modal closed", {
      description: "Card details modal has been closed.",
      position: "top-center",
      duration: 4000,
    });
    onClose();
  };

  const handleDelete = () => {
    if (!onDelete || !card.id) return;

    const toastId = sonnerToast.warning(`Delete task "${card.title || "this task"}"?`, {
      description: "This action cannot be undone.",
      position: "top-center",
      duration: 10000,
      action: {
        label: "Confirm",
        onClick: () => {
          onDelete(card.id.toString());
          sonnerToast.success(`Task "${card.title || "Task"}" deleted`, {
            description: "Task was deleted successfully.",
            position: "top-center",
            duration: 4000,
          });
          sonnerToast.dismiss(toastId);
          onClose();
        },
      },
      closeButton: true,
    });
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={handleClose}
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          />
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
          >
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Edit3 className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-900">{card.title}</h2>
                  <p className="text-sm text-gray-500">
                    Status: <span className="font-medium">{card.status_display || card.status}</span>
                  </p>
                </div>
              </div>
              <button
                onClick={handleClose}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            <div className="flex">
              <div className="flex-1 p-6 overflow-y-auto max-h-[calc(90vh-80px)]">
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
                  <div>
                    <span className="text-xs text-gray-500">Priority</span>
                    <div className="font-semibold">{card.priority_display || card.priority || "Not set"}</div>
                  </div>
                  {(card.due_date || card.dueDate) && (
                    <div>
                      <span className="text-xs text-gray-500">Due Date</span>
                      <div className="font-semibold">
                        {card.due_date || new Date(card.dueDate!).toISOString().split("T")[0]}
                      </div>
                    </div>
                  )}
                  {(card.due_time || card.dueDate) && (
                    <div>
                      <span className="text-xs text-gray-500">Due Time</span>
                      <div className="font-semibold">
                        {card.due_time ||
                          new Date(card.dueDate!).toLocaleTimeString("en-US", {
                            hour12: false,
                            hour: "2-digit",
                            minute: "2-digit",
                            second: "2-digit",
                          })}
                      </div>
                    </div>
                  )}
                  <div>
                    <span className="text-xs text-gray-500">Assignee</span>
                    <div className="font-semibold">{card.assigned_to || (card as any).assignee || "Unassigned"}</div>
                  </div>
                  <div>
                    <span className="text-xs text-gray-500">Created By</span>
                    <div className="font-semibold">{card.created_by || "Unknown"}</div>
                  </div>
                  {card.labels && card.labels.length > 0 && (
                    <div>
                      <span className="text-xs text-gray-500">Labels</span>
                      <div className="flex flex-wrap gap-1">
                        {card.labels.map((label) => (
                          <span
                            key={label.id}
                            className="px-2 py-1 text-xs font-semibold rounded"
                            style={{ backgroundColor: label.color, color: "#fff" }}
                          >
                            {label.name}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
                <div className="mb-6">
                  <h3 className="text-sm font-semibold text-gray-900 mb-2">Description</h3>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <p className="text-sm text-gray-700 leading-relaxed">
                      {card.description || "No description provided."}
                    </p>
                    {!card.description && (
                      <p className="text-xs text-gray-500 mt-1 italic">
                        (This field was empty in the submitted payload)
                      </p>
                    )}
                  </div>
                </div>
                <div className="mb-6">
                  <h3 className="text-sm font-semibold text-gray-900 mb-3">Activity</h3>
                  <div className="flex gap-3 mb-4">
                    <div className="w-8 h-8 rounded-full bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center text-white text-xs font-semibold">
                      {(card.assigned_to || (card as any).assignee)
                        ? typeof (card.assigned_to || (card as any).assignee) === "string"
                          ? (card.assigned_to || (card as any).assignee).split("/").pop()?.substring(0, 2).toUpperCase() || "U"
                          : (card.assigned_to || (card as any).assignee)?.fullnames?.split(" ").map((n: string) => n[0]).join("") || "U"
                        : card.created_by
                        ? typeof card.created_by === "string"
                          ? card.created_by.split("/").pop()?.substring(0, 2).toUpperCase() || "U"
                          : card.created_by.fullnames?.split(" ").map((n: string) => n[0]).join("") || "U"
                        : "U"}
                    </div>
                    <div className="flex-1">
                      <textarea
                        value={newComment}
                        onChange={(e) => setNewComment(e.target.value)}
                        placeholder="Write a comment..."
                        className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        rows={3}
                      />
                      <div className="flex items-center justify-between mt-2">
                        <div className="flex items-center gap-2">
                          <button className="p-1 text-gray-400 hover:text-gray-600 transition-colors">
                            <Paperclip className="h-4 w-4" />
                          </button>
                          <button className="p-1 text-gray-400 hover:text-gray-600 transition-colors">
                            <Link className="h-4 w-4" />
                          </button>
                        </div>
                        <button
                          disabled={!newComment.trim()}
                          className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white text-sm font-medium rounded-lg transition-colors"
                        >
                          <Send className="h-3 w-3" /> Comment
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="w-64 bg-gray-50 p-6 border-l border-gray-200">
                <h3 className="text-sm font-semibold text-gray-900 mb-4">Actions</h3>
                <div className="space-y-2">
                  <button className="w-full flex items-center gap-3 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                    <User className="h-4 w-4" /> Members
                  </button>
                  <button className="w-full flex items-center gap-3 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                    <Tag className="h-4 w-4" /> Labels
                  </button>
                  <button className="w-full flex items-center gap-3 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                    <Calendar className="h-4 w-4" /> Due Date
                  </button>
                  <button className="w-full flex items-center gap-3 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                    <Paperclip className="h-4 w-4" /> Attachment
                  </button>
                </div>
                <hr className="my-4" />
                <div className="space-y-2">
                  <button className="w-full flex items-center gap-3 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                    <Archive className="h-4 w-4" /> Archive
                  </button>
                  <button
                    onClick={handleDelete}
                    className="w-full flex items-center gap-3 px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                  >
                    <Trash2 className="h-4 w-4" /> Delete
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};