import * as React from "react"
import { Check, X } from "lucide-react"

export const IconSwitch = ({ checked, onChange }: { checked: boolean; onChange: () => void }) => (
  <label className="relative inline-flex items-center cursor-pointer">
    <input type="checkbox" className="sr-only peer" checked={checked} onChange={onChange} />
    <div className="w-11 h-6 bg-gray-300 rounded-full peer peer-checked:bg-green-500 transition-all flex items-center justify-between px-1">
      <Check className="text-white w-4 h-4" />
      <X className="text-white w-4 h-4" />
    </div>
    <div className="absolute left-0.5 top-0.5 w-5 h-5 bg-white rounded-full shadow-md transform peer-checked:translate-x-5 transition-transform"></div>
  </label>
)