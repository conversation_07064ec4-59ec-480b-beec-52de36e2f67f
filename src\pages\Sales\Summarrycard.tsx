
import { motion } from "framer-motion";

import { Card6 } from "@/components/custom/cards/Card6";
import { MonitorCheck, PersonStandingIcon } from "lucide-react";
import { Sales } from "./type";

interface SummaryCardProps {
  transactions: Sales[];
}

export function SummaryCard({ transactions }: SummaryCardProps) {
  const totalSales = transactions.length;
  const activeSales = transactions.filter((txn) => txn.status === "ACTIVE").length;
  const droppedSales = transactions.filter((txn) => txn.status === "DROPPED").length;

  return (
    <motion.div
      className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6"
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
    >
      <Card6
        title="Total Sales"
        value={totalSales.toString()}
        icon={MonitorCheck}
        change=""
        changeLabel=""
        cardBg="bg-blue-100"
       
      />
      <Card6
        title="Active Sales"
        value={activeSales.toString()}
        icon={PersonStandingIcon}
        change=""
        changeLabel=""
        cardBg="bg-green-100"
       
      />
      <Card6
        title="Dropped Sales"
        value={droppedSales.toString()}
        icon={PersonStandingIcon}
        change=""
        changeLabel=""
        
        
        cardBg="bg-red-100"
      />
    </motion.div>)}
