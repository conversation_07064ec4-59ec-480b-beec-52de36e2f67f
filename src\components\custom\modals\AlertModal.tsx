import  { forwardRef, ReactNode } from "react";
import { AlertTriangle, AlertCircle, CheckCircle, Info } from "lucide-react";
import { Button } from "@/components/ui/button";
import BaseModal from "./BaseModal";
import { cn } from "@/lib/utils";
import { BaseModalProps } from "./BaseModal";

const variantIcons = {
  default: null,
  warning: <AlertTriangle className="h-5 w-5 text-amber-500" />,
  danger: <AlertCircle className="h-5 w-5 text-red-500" />,
  success: <CheckCircle className="h-5 w-5 text-green-500" />,
  info: <Info className="h-5 w-5 text-blue-500" />,
};

const variantClasses = {
  default: "",
  warning: "border-l-4 border-amber-500 bg-amber-50 dark:bg-amber-950/10 px-4 py-3",
  danger: "border-l-4 border-red-500 bg-red-50 dark:bg-red-950/10 px-4 py-3",
  success: "border-l-4 border-green-500 bg-green-50 dark:bg-green-950/10 px-4 py-3",
  info: "border-l-4 border-blue-500 bg-blue-50 dark:bg-blue-950/10 px-4 py-3",
};

export interface AlertModalProps extends Omit<BaseModalProps, "children"> {
    variant?: ModalVariant;
    message: ReactNode;
    buttonText?: string;
    onButtonClick?: () => void;
  }

  export type ModalVariant = 
  | "default"
  | "warning"
  | "danger" 
  | "success" 
  | "info";

const AlertModal = forwardRef<HTMLDivElement, AlertModalProps>(
  (
    {
      variant = "info",
      message,
      buttonText = "OK",
      onButtonClick,
      icon = variantIcons[variant],
      ...props
    },
    ref
  ) => {
    const handleButtonClick = () => {
      onButtonClick?.();
      if (props.onOpenChange) {
        props.onOpenChange(false);
      }
    };

    const footer = (
      <Button type="button" onClick={handleButtonClick}>
        {buttonText}
      </Button>
    );

    return (
      <BaseModal
        ref={ref}
        icon={icon}
        footer={footer}
        {...props}
      >
        <div className={cn("my-2", variantClasses[variant])}>
          {message}
        </div>
      </BaseModal>
    );
  }
);

AlertModal.displayName = "AlertModal";
export default AlertModal;