# Permissions Loading System

This document explains how to use the permissions loading system to ensure that permissions are loaded before rendering content in your components.

## Overview

The permissions loading system provides several ways to ensure that permissions are loaded before rendering content:

1. **PermissionsLoader Component**: A component that loads permissions before rendering its children
2. **withPreloadedPermissions HOC**: A higher-order component that wraps a component and provides preloaded permissions
3. **usePermissionsLoader Hook**: A hook that can be used in any component to load permissions

## Using the PermissionsLoader Component

The `PermissionsLoader` component is the simplest way to ensure permissions are loaded before rendering content. It will show a loading indicator while permissions are being loaded, and then render its children once permissions are loaded.

```tsx
import { PermissionsLoader } from '@/components/permissions/PermissionsLoader';
import { useSalesPermissions } from '@/hooks/useSalesPermissions';

const MyComponent = () => {
  // This will only be called after permissions are loaded
  const salesPermissions = useSalesPermissions();

  return (
    <PermissionsLoader
      onPermissionsLoaded={() => {
        // console.log('Permissions loaded successfully');
      }}
    >
      {/* This content will only be rendered after permissions are loaded */}
      <div>
        <h1>Sales Permissions</h1>
        <p>Has Any Sales Access: {salesPermissions.hasAnySalesAccess ? 'Yes' : 'No'}</p>
        <p>Permission Level: {salesPermissions.permissionLevel}</p>
      </div>
    </PermissionsLoader>
  );
};
```

## Using the withPreloadedPermissions HOC

The `withPreloadedPermissions` HOC is useful when you want to wrap an entire component and provide it with preloaded permissions. This is especially useful for page components.

```tsx
import withPreloadedPermissions from '@/components/hoc/withPreloadedPermissions';

interface MyComponentProps {
  salesPermissions?: any;
  customerPermissions?: any;
  prospectPermissions?: any;
}

const MyComponent: React.FC<MyComponentProps> = ({
  salesPermissions,
  customerPermissions,
  prospectPermissions
}) => {
  // Permissions are already loaded and provided as props
  const { hasAnySalesAccess, permissionLevel } = salesPermissions || {};

  return (
    <div>
      <h1>Sales Permissions</h1>
      <p>Has Any Sales Access: {hasAnySalesAccess ? 'Yes' : 'No'}</p>
      <p>Permission Level: {permissionLevel}</p>
    </div>
  );
};

// Wrap the component with the HOC to preload permissions
export default withPreloadedPermissions(MyComponent);
```

## Using the usePermissionsLoader Hook

The `usePermissionsLoader` hook is useful when you want more control over the loading process. It provides a loading state and a function to reload permissions.

```tsx
import { usePermissionsLoader } from '@/hooks/usePermissionsLoader';
import { useSalesPermissions } from '@/hooks/useSalesPermissions';

const MyComponent = () => {
  const { isLoading, reloadPermissions } = usePermissionsLoader(() => {
    // console.log('Permissions loaded successfully');
  });

  // This will be called regardless of loading state
  const salesPermissions = useSalesPermissions();

  if (isLoading) {
    return <div>Loading permissions...</div>;
  }

  return (
    <div>
      <h1>Sales Permissions</h1>
      <p>Has Any Sales Access: {salesPermissions.hasAnySalesAccess ? 'Yes' : 'No'}</p>
      <p>Permission Level: {salesPermissions.permissionLevel}</p>
      <button onClick={reloadPermissions}>Reload Permissions</button>
    </div>
  );
};
```

## Using the usePreloadPermissions Hook

The `usePreloadPermissions` hook is useful when you want to preload all permissions at once and have access to them in a single hook.

```tsx
import { usePreloadPermissions } from '@/hooks/usePreloadPermissions';

const MyComponent = () => {
  const {
    isLoading,
    salesPermissions,
    customerPermissions,
    prospectPermissions,
    refreshPermissions
  } = usePreloadPermissions();

  if (isLoading) {
    return <div>Loading permissions...</div>;
  }

  return (
    <div>
      <h1>Sales Permissions</h1>
      <p>Has Any Sales Access: {salesPermissions.hasAnySalesAccess ? 'Yes' : 'No'}</p>
      <p>Permission Level: {salesPermissions.permissionLevel}</p>
      
      <h1>Customer Permissions</h1>
      <p>Has Any Customer Access: {customerPermissions.hasAnyCustomerAccess ? 'Yes' : 'No'}</p>
      <p>Permission Level: {customerPermissions.permissionLevel}</p>
      
      <h1>Prospect Permissions</h1>
      <p>Has Any Prospect Access: {prospectPermissions.hasAnyProspectAccess ? 'Yes' : 'No'}</p>
      <p>Permission Level: {prospectPermissions.permissionLevel}</p>
      
      <button onClick={refreshPermissions}>Refresh Permissions</button>
    </div>
  );
};
```

## Best Practices

1. **Use PermissionsLoader for simple cases**: When you just need to ensure permissions are loaded before rendering content, use the `PermissionsLoader` component.

2. **Use withPreloadedPermissions for page components**: When you have a page component that needs access to permissions, use the `withPreloadedPermissions` HOC.

3. **Use usePermissionsLoader for custom loading logic**: When you need more control over the loading process, use the `usePermissionsLoader` hook.

4. **Use usePreloadPermissions for access to all permissions**: When you need access to all permissions in a single hook, use the `usePreloadPermissions` hook.

5. **Avoid multiple permission refreshes**: The permission hooks automatically refresh permissions when they mount, so avoid using multiple hooks in the same component unless necessary.

6. **Consider caching**: Permissions are cached in the Redux store, so you don't need to reload them on every page navigation.