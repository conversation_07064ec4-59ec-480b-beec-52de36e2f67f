import { useState, useEffect, useMemo } from "react";
import { ColumnDef } from "@tanstack/react-table";
import { DataTable } from "@/components/custom/tables/Table1";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { useGetAllCustomersQuery } from "@/redux/slices/customersApiSlice";
import { Search } from "lucide-react";
import { useCustomerPermissions } from "@/hooks/useCustomerPermissions";

// Define interface for the All Customers API response
interface AllCustomersResponse {
  id: string;
  customer_name: string;
  customer_no: string;
  customer_type: string;
  phone: string;
  email: string;
  marketer: string;
  total_balance: number;
}

// Import the shared Customer interface for compatibility
import { Customer } from "@/components/customer-section/CustomerInfoHeader";

const CompletedCustomers = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);

  // Use server-side search by passing searchTerm to the API
  
  // Get customer permissions
  const { hasAnyCustomerAccess, apiParams } = useCustomerPermissions();

  // Create query parameters (removed permission filters as API doesn't support OFFICE parameter)
  const queryParams = {
    category: "COMPLETED", // Filter for completed customers
    page: currentPage,
    page_size: itemsPerPage,
    search: searchTerm, // Server-side search
  };
  
  // Fetch completed customers data with permission checking
  const {
    data: customersData,
    isLoading,
    isError,
    error,
    refetch,
  } = useGetAllCustomersQuery(queryParams, {
    skip: !hasAnyCustomerAccess // Skip the query if user has no access
  });

  useEffect(() => {
    if (isError) {
      console.error("Error fetching completed customers:", error);
    }
  }, [isError, error]);

  // Reset to first page when search term changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  const handleViewCustomer = (customerNo: string) => {
    console.log(`View customer ${customerNo} details`);
  };

  // Map All Customers API response fields to Customer interface fields
  const mapApiResponseToCustomer = (apiCustomer: AllCustomersResponse): Customer => {
    return {
      id: apiCustomer.id || "",
      customer_no: apiCustomer.customer_no,
      name: apiCustomer.customer_name,
      email: apiCustomer.email || "",
      phone: apiCustomer.phone || "",
      position: "", // Not available in API
      company: "", // Not available in API
      nationalId: "", // Not available in API
      passportNo: "", // Not available in API
      kraPin: "", // Not available in API
      dob: "", // Not available in API
      gender: "", // Not available in API
      maritalStatus: "", // Not available in API
      alternativePhone: "", // Not available in API
      alternativeEmail: "", // Not available in API
      address: "", // Not available in API
      customerType: (apiCustomer.customer_type === "Individual" || apiCustomer.customer_type === "Group")
        ? apiCustomer.customer_type
        : undefined,
      countryOfResidence: "", // Not available in API
      dateOfRegistration: "", // Not available in API
      otp: "", // Not available in API
      otpGeneratedAt: "", // Not available in API
      leadSource: 0, // Not available in API
      marketer: apiCustomer.marketer || "",
    };
  };

  const columns: ColumnDef<Customer>[] = [
    {
      accessorKey: "customer_no",
      header: "Customer No",
      cell: (info) => {
        const rowData = info.row.original;
        return (
          <Link
            to={`/customer/${rowData.customer_no}`}
            className='block font-bold text-blue-700 underline'
          >
            {info.getValue() as string}
          </Link>
        );
      },
      enableColumnFilter: true,
    },
    {
      accessorKey: "name", // Changed from customer_name to name
      header: "Customer Name",
      cell: (info) => {
        const rowData = info.row.original;
        return (
          <Link
            to={`/customer/${rowData.customer_no}`}
            className='block font-medium hover:text-blue-700 hover:underline'
          >
            {info.getValue() as string}
          </Link>
        );
      },
      enableColumnFilter: true,
    },
    {
      accessorKey: "phone",
      header: "Phone Number",
      cell: (info) => (
        <span className="font-medium">
          {(info.getValue() as string) || "N/A"}
        </span>
      ),
      enableColumnFilter: true,
    },
    {
      accessorKey: "email", // Changed from primary_email to email
      header: "Primary Email",
      cell: (info) => (info.getValue() as string) || "N/A",
      enableColumnFilter: true,
    },
    {
      accessorKey: "marketer",
      header: "Marketer",
      cell: (info) => (info.getValue() as string) || "N/A",
      enableColumnFilter: true,
    },
    {
      accessorKey: "customerType", // Changed from customer_type to customerType
      header: "Customer Type",
      cell: (info) => info.getValue() as string,
      enableColumnFilter: true,
    },
    {
      id: "total_balance",
      header: "Total Balance",
      cell: ({ row }) => {
        // Access the original data to get total_balance
        const customersArray: AllCustomersResponse[] = Array.isArray(customersData?.results)
          ? customersData.results
          : [];
        const originalCustomer = customersArray.find(
          (c: AllCustomersResponse) => c.customer_no === row.original.customer_no
        );
        const balance = originalCustomer?.total_balance || 0;
        return (
          <span className="font-medium text-green-600">
            KES {balance.toLocaleString()}
          </span>
        );
      },
      enableColumnFilter: false,
    },
    {
      id: "actions",
      header: "Action",
      cell: ({ row }) => {
        const customer = row.original;
        return (
          <div className="flex space-x-2 justify-center">
            <Link to={`/customer/${customer.customer_no}`}>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleViewCustomer(customer.customer_no)}
                className="bg-white text-blue-500 border-blue-300 hover:bg-blue-50 flex items-center space-x-1"
              >
                <span>View</span>
              </Button>
            </Link>
          </div>
        );
      },
      enableColumnFilter: false,
    },
  ];

  // Get customers data array
  const customersArray: AllCustomersResponse[] = Array.isArray(customersData?.results)
    ? customersData.results
    : [];

  // Transform API response data to match our Customer interface
  // Since we're using server-side search, we don't need client-side filtering
  const transformedCustomers = customersArray.map(mapApiResponseToCustomer);

  // Remove client-side filtering since server-side search is handling it
  // const filteredCustomers = useMemo(() => {
  //   if (!searchTerm) return transformedCustomers;
  //   return transformedCustomers.filter(...)
  // }, [transformedCustomers, searchTerm]);

  return (
    <div className="space-y-6">
      {/* Main Table Card */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
        <div className="p-6">
          {!hasAnyCustomerAccess ? (
            <div className="text-center py-12 text-gray-500">
              <p>Contact your administrator to request customer viewing permissions.</p>
            </div>
          ) : isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-4 text-gray-600 dark:text-gray-400">
                  Loading customers...
                </p>
              </div>
            </div>
          ) : isError ? (
            <div className="text-center py-12">
              <div className="text-red-500 text-lg font-medium">
                Failed to load customers
              </div>
              <p className="text-gray-600 dark:text-gray-400 mt-2">
                Please try again later.
              </p>
              <Button onClick={() => refetch()} className="mt-4">
                Retry
              </Button>
            </div>
          ) : transformedCustomers.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-500 text-lg font-medium">
                {searchTerm
                  ? "No customers found matching your search"
                  : "No customers found"}
              </div>
              <p className="text-gray-400 mt-2">
                {searchTerm
                  ? "Try adjusting your search terms"
                  : "Customers will appear here once they are added to the system"}
              </p>
            </div>
          ) : (
            <DataTable<Customer>
              data={transformedCustomers}
              columns={columns}
              enableToolbar
              enableExportToExcel
              enablePrintPdf
              enablePagination
              enableColumnFilters
              enableSorting
              enableSelectColumn
              title="Completed Customers List"
              currentPage={currentPage}
              setCurrentPage={setCurrentPage}
              itemsPerPage={itemsPerPage}
              setItemsPerPage={setItemsPerPage}
              totalItems={customersData?.total_data || 0}
              tBodyCellsClassName="border-t !p-2"
              searchInput={<SearchComponent universalSearchValue={searchTerm} setuniversalSearchValue={setSearchTerm} />}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default CompletedCustomers;

interface SearchComponentProps {
    universalSearchValue: string,
    setuniversalSearchValue: React.Dispatch<React.SetStateAction<string>>
}

function SearchComponent({ universalSearchValue, setuniversalSearchValue }: SearchComponentProps) {
    return <input
        value={universalSearchValue}
        onChange={e => setuniversalSearchValue(e.target.value)}
        className="px-3 py-2 ml-1 w-full border rounded text-sm focus:outline-none bg-transparent"
        placeholder="Search customer details..."
    />
}