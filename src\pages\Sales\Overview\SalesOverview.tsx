import { Screen } from '@/app-components/layout/screen'
import NavTab1 from '@/components/custom/tabs/NavTab1'
import AllSales from './AllSales'
import CompletedSales from './CompletedSales'
import OnGoingSales from './OnGoingSales'
import DroppedSales from './DroppedSales'

const SalesOverview = () => {

  const tabs = [
    {
      label: "All Sales",
      content: <AllSales />,
      value: "all-sales",
      title: "All Sale",
    },
    {
      label: "Ongoing Sales",
      content: <OnGoingSales />,
      value: "on-going-sales",
      title: "Ongoing Sales",
    },
    {
      label: "Completed Sales",
      content: <CompletedSales />,
      value: "completed-sales",
      title: "Completed Sales",
    },
    {
      label: "Dropped Sales",
      content: <DroppedSales />,
      value: "dropped-sales",
      title: "Dropped Sales",
    },
  ]

  return (
    <Screen>
      <div className=" !m-0 min-h-screen w-full border rounded-2xl">
        <div className='px-3 py-5 border-b flex justify-between items-center'>
          <h2 className='font-bold text-lg'>Sales</h2>
        </div>
          <NavTab1
            tabs={tabs}
            TabsListClassName='!bg-transparent rounded-none !border-b'
            TabsTriggerClassName='md:!px-16  !rounded-none data-[state=active]:!bg-primary data-[state=active]:!text-primary-foreground '
          />
      </div>
      <div className='h-10'></div>
    </Screen>
  )
}

export default SalesOverview

