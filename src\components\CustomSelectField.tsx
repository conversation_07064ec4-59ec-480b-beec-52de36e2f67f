import { Loader2, X } from "lucide-react";
import React, { useEffect, useState } from "react";

type Props = {
  useSearchField?: boolean;
  valueField: any; // name of field from api to use as value in option
  setValue: (e: any) => void; // onchange function
  labelField: any; // name of the field from api to display
  data: any; // retrieve data
  queryFunc: (e: any) => void; // function to
  loader?: boolean;
  isMultiple?: boolean; // if true, allows multiple selection
  // setLabel: (e: any) => void; // onchange function
};

const CustomSelectField = ({
  setValue,
  useSearchField,
  valueField,
  labelField,
  data,
  loader,
  queryFunc,
  isMultiple,
}: Props) => {
  const [showDataList, setDataList] = useState(false);
  const [inputVal, setInputVal] = useState("");
  const [selectedItems, setSelectedItems] = useState<any[]>([]);

  //   useEffect(() => {
  //     setDataList(true);
  //   }, []);

  const handleSearchChange = (value: string) => {
    const searchField = useSearchField ? "search" : labelField;
    queryFunc({ [searchField]: value });
    setInputVal(value);
    setDataList(true);
  };

  const handleItemSelect = (item: string) => {
    if (isMultiple) {
      // Avoid duplicates
      const exists = selectedItems.find(
        (i) => i[valueField] === item[valueField]
      );
      if (!exists) {
        const newItems = [...selectedItems, item];
        setSelectedItems(newItems);
        setValue(newItems.map((i) => i[valueField]));
      }
      setInputVal("");
    } else {
      setSelectedItems([item]);
      setValue(item[valueField]);
      setInputVal(item[labelField]);
      setDataList(false);
    }
  };

  const handleRemove = (val: any) => {
    const filtered = selectedItems.filter(
      (item) => item[valueField] !== val[valueField]
    );
    setSelectedItems(filtered);
    setValue(filtered.map((i) => i[valueField]));
  };

  return (
    <div className="relative w-full">
      <div className="flex flex-wrap gap-1 border px-2 py-1 rounded-md focus-within:ring-2 focus-within:ring-primary">
        {isMultiple &&
          selectedItems.map((item, idx) => (
            <span
              key={idx}
              className="flex items-center bg-blue-100 text-blue-800 rounded-full px-2 py-1 text-sm"
            >
              {item[labelField]}
              <X
                className="ml-1 cursor-pointer w-4 h-4"
                onClick={() => handleRemove(item)}
              />
            </span>
          ))}
        <input
          value={inputVal}
          type="search"
          onChange={(e) => handleSearchChange(e.target.value)}
          className="flex-1 min-w-[80px] border-0 focus:outline-none bg-transparent py-1"
          placeholder="Search..."
        />
      </div>

      {loader && (
        <div className="max-h-[10vh] overflow-auto px-6 py-1 dark:bg-black bg-white shadow absolute z-10 w-full">
          <Loader2 className="animate-spin" />
        </div>
      )}

      {showDataList && inputVal.length > 0 && (
        <ul className="max-h-[20vh] overflow-auto px-6 py-1 dark:bg-black bg-white shadow absolute z-10 w-full">
          {data?.map((d: any, i: number) => (
            <li
              key={i}
              className="cursor-pointer border-b capitalize p-2"
              onClick={() => handleItemSelect(d)}
            >
              {d[labelField]?.toLowerCase()}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default CustomSelectField;
