import React from 'react';
import { DataTable } from './Table1';
import { Button } from './Button'; // Assuming a Button component exists
import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';
import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';

pdfMake.vfs = pdfFonts.pdfMake.vfs;

interface ReportTableProps<T> {
    data: T[];
    columns: any[];
    title?: string;
    enableExportToCsv?: boolean;
    enableExportToPdf?: boolean;
}

export function ReportTable<T>({
    data,
    columns,
    title = 'Report Table',
    enableExportToCsv = true,
    enableExportToPdf = true,
}: ReportTableProps<T>) {
    const exportToCsv = () => {
        const headers = columns.map((col) => col.header);
        const csvRows = data.map((row) =>
            columns.map((col) => row[col.id] || '').join(',')
        );
        const csvContent = [headers.join(','), ...csvRows].join('\n');
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        saveAs(blob, `${title}.csv`);
    };

    const exportToPdf = () => {
        const headers = columns.map((col) => col.header);
        const body = [
            headers,
            ...data.map((row) =>
                columns.map((col) => row[col.id] || '')
            ),
        ];

        const docDefinition = {
            content: [
                { text: title, style: 'header', alignment: 'center' },
                {
                    table: {
                        headerRows: 1,
                        widths: Array(headers.length).fill('*'),
                        body,
                    },
                },
            ],
            styles: {
                header: {
                    fontSize: 18,
                    bold: true,
                    marginBottom: 10,
                },
            },
        };

        pdfMake.createPdf(docDefinition).download(`${title}.pdf`);
    };

    return (
        <div>
            <div className="flex justify-end gap-2 mb-4">
                {enableExportToCsv && (
                    <Button onClick={exportToCsv}>Export to CSV</Button>
                )}
                {enableExportToPdf && (
                    <Button onClick={exportToPdf}>Export to PDF</Button>
                )}
            </div>
            <DataTable
                data={data}
                columns={columns}
                title={title}
                enableExportToExcel={false}
                enablePrintPdf={false}
                enableToolbar={true}
                enablePagination={true}
            />
        </div>
    );
}