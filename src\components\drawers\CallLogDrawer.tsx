import { useState } from "react";
import { useForm } from "react-hook-form";
import DrawerModal from "../custom/modals/DrawerModal";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { 
  Form, 
  FormControl, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface CallLogDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  customerId: string;
  customerPhone: string;
}

interface CallLogFormValues {
  callType: string;
  duration: string;
  notes: string;
  outcome: string;
}

const CallLogDrawer = ({ isOpen, onClose, customerId, customerPhone }: CallLogDrawerProps) => {
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();
  
  const form = useForm<CallLogFormValues>({
    defaultValues: {
      callType: 'outbound',
      duration: '',
      notes: '',
      outcome: ''
    }
  });

  const handleSubmit = async (data: CallLogFormValues) => {
    setIsSaving(true);
    
    try {
      
      console.log("Logging call:", data, "for customer:", customerId);
      
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast({
        title: "Call logged",
        description: "The call has been logged successfully."
      });
      
      onClose();
      form.reset();
    } catch (error) {
      console.error("Error logging call:", error);
      toast({
        title: "Error",
        description: "There was a problem logging the call.",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <DrawerModal
      isOpen={isOpen}
      onOpenChange={onClose}
      title="Log Call"
      description={`Phone: ${customerPhone}`}
      size="md"
    >
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4 sm:space-y-6">
          <FormField
            control={form.control}
            name="callType"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Call Type</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select call type" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="outbound">Outbound</SelectItem>
                    <SelectItem value="inbound">Inbound</SelectItem>
                    <SelectItem value="missed">Missed</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="duration"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Duration (minutes)</FormLabel>
                <FormControl>
                  <Input 
                    type="number"
                    placeholder="e.g., 15" 
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="outcome"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Outcome</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select outcome" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="follow-up-required">Follow-up Required</SelectItem>
                    <SelectItem value="no-answer">No Answer</SelectItem>
                    <SelectItem value="left-voicemail">Left Voicemail</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="notes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Call Notes</FormLabel>
                <FormControl>
                  <Textarea 
                    placeholder="Add notes about the call..." 
                    className="min-h-[150px]"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <div className="flex flex-col sm:flex-row justify-end gap-3 pt-4 border-t">
            <Button type="button" variant="outline" onClick={onClose} className="w-full sm:w-auto">
              Cancel
            </Button>
            <Button type="submit" disabled={isSaving} className="w-full sm:w-auto">
              {isSaving ? "Saving..." : "Log Call"}
            </Button>
          </div>
        </form>
      </Form>
    </DrawerModal>
  );
};

export default CallLogDrawer;