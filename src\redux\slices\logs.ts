import { contentHeader } from "@/utils/header";
import { apiSlice } from "../apiSlice";

export const logsApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getLogs: builder.query({
      query: (params) => ({
        url: "/log",
        method: "GET",
        params: params,
        headers: contentHeader(),
      }),
      providesTags: ["Logs"],
    }),
  }),
});

export const { useGetLogsQuery } = logsApiSlice;
