import React from 'react';
import { Tabs, <PERSON><PERSON><PERSON>ist, TabsTrigger, TabsContent } from '@/components/ui/tabs';

interface TabItem {
  value: string;
  title: string | React.ReactNode;
  icon?: React.ReactNode;
  content: React.ReactNode;
}

interface CustomTabsProps {
  tabs: TabItem[];
  defaultValue?: string;
  className?: string;
  TabsListClassName?: string;
  TabsTriggerClassName?: string;
  TabsContentClassName?: string;
}

const Tab1 = ({ tabs, defaultValue, className,TabsListClassName,TabsTriggerClassName,TabsContentClassName }: CustomTabsProps) => {
  const activeDefault = defaultValue || (tabs.length > 0 ? tabs[0].value : "");

  return (
    <Tabs defaultValue={activeDefault} className={`w-full ${className} `}>
      <TabsList className={`!bg-background !flex !flex-nowrap justify-start h-auto p-0  ${TabsListClassName} `}>
        {tabs.map((tab) => (
          <TabsTrigger key={tab.value} value={tab.value} className={`!flex-none  border-none !rounded-sm cursor-pointer px-2 py-2 sm:px-8 sm:py-4 data-[state=active]:!bg-accent !text-foreground  ${TabsTriggerClassName}`}>
            <span className="mr-1 [&_svg:not([class*='size-'])]:size-4 hidden sm:block">{tab.icon}</span>
            {typeof tab.title === 'string' ? tab.title : tab.title}
          </TabsTrigger>
        ))}
      </TabsList>

      {tabs.map((tab) => (
        <TabsContent key={tab.value} value={tab.value} className={`transform transition-all duration-800 origin-bottom animate-in fade-in slide-in-from-bottom-8 ${TabsContentClassName}`}>
          {tab.content}
        </TabsContent>
      ))}
    </Tabs>
  );
};

export default Tab1;