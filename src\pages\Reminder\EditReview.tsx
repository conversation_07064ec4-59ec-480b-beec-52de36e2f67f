import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import BaseModal from "@/components/custom/modals/BaseModal";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import { Button } from "@/components/ui/button";
import {
  Bell,
  BellOff,
  Clock,
  AlertTriangle,
  AlertCircle,
  Circle,
  CheckCircle2,
  Tag,
  Repeat,
  Zap
} from "lucide-react";

interface EditEventModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  eventToEdit: {
    id: string;
    title: string;
    description: string;
    time: string;
    date: Date;
    status: string;
    reminder_type?: string;
    priority?: string;
    advance_notice_minutes?: number;
    repeat_pattern?: string;
    tags?: string;
    snoozed?: boolean;
    snoozeUntil?: Date;
    is_active?: boolean;
  } | null;
  setEventToEdit: (event: any) => void;
  handleSaveEvent: () => void;
}

export default function EditEventModal({
  isOpen,
  onOpenChange,
  eventToEdit,
  setEventToEdit,
  handleSaveEvent,
}: EditEventModalProps) {

  const getPriorityIcon = (priority?: string) => {
    switch (priority) {
      case 'Critical': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'High': return <AlertCircle className="h-4 w-4 text-orange-500" />;
      case 'Normal': return <Circle className="h-4 w-4 text-blue-500" />;
      case 'Low': return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      default: return <Circle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getPriorityColor = (priority?: string) => {
    switch (priority) {
      case 'Critical': return 'border-red-200 bg-red-50';
      case 'High': return 'border-orange-200 bg-orange-50';
      case 'Normal': return 'border-blue-200 bg-blue-50';
      case 'Low': return 'border-green-200 bg-green-50';
      default: return 'border-gray-200 bg-gray-50';
    }
  };
  
  const handleSnoozeToggle = () => {
    if (!eventToEdit) return;
    
    if (eventToEdit.snoozed) {
      // Turn off snooze
      setEventToEdit({ 
        ...eventToEdit, 
        snoozed: false, 
        snoozeUntil: undefined 
      });
    } else {
      // Default snooze for 1 hour from now
      const snoozeUntil = new Date();
      snoozeUntil.setHours(snoozeUntil.getHours() + 1);
      
      setEventToEdit({ 
        ...eventToEdit, 
        snoozed: true, 
        snoozeUntil: snoozeUntil 
      });
    }
  };
  
  const handleSnoozeTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!eventToEdit || !eventToEdit.snoozed) return;
    
    try {
      const snoozeDate = new Date(e.target.value);
      setEventToEdit({
        ...eventToEdit,
        snoozeUntil: snoozeDate
      });
    } catch (error) {
      console.error("Invalid date format");
    }
  };
  
  const formatDateTimeLocal = (date?: Date) => {
    if (!date) return "";
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day}T${hours}:${minutes}`;
  };

  return (
    <BaseModal
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      title="✏️ Edit Reminder"
      description="Update your reminder details"
      footer={
        <>
          <PrimaryButton variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </PrimaryButton>
          <PrimaryButton
            onClick={handleSaveEvent}
            className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800"
          >
            <Bell className="h-4 w-4 mr-2" />
            Save Changes
          </PrimaryButton>
        </>
      }
    >
      {eventToEdit && (
        <div className="space-y-6 py-4">
          {/* Basic Information Card */}
          <Card className="border-green-200 bg-gradient-to-br from-green-50 to-emerald-50">
            <CardContent className="p-4 space-y-4">
              <div className="flex items-center gap-2 mb-3">
                <Bell className="h-5 w-5 text-green-600" />
                <h3 className="font-semibold text-green-800">Basic Information</h3>
              </div>

              {/* Title */}
              <div className="space-y-2">
                <Label htmlFor="title" className="text-sm font-medium text-gray-700">
                  Title *
                </Label>
                <Input
                  id="title"
                  value={eventToEdit.title}
                  onChange={(e) => setEventToEdit({ ...eventToEdit, title: e.target.value })}
                  className="border-green-200 focus:border-green-400 focus:ring-green-200"
                  placeholder="What would you like to be reminded about?"
                />
              </div>

              {/* Time */}
              <div className="space-y-2">
                <Label htmlFor="time" className="text-sm font-medium text-gray-700">
                  Time *
                </Label>
                <Input
                  id="time"
                  type="time"
                  value={eventToEdit.time}
                  onChange={(e) => setEventToEdit({ ...eventToEdit, time: e.target.value })}
                  className="border-green-200 focus:border-green-400 focus:ring-green-200"
                />
              </div>

              {/* Description */}
              <div className="space-y-2">
                <Label htmlFor="description" className="text-sm font-medium text-gray-700">
                  Description
                </Label>
                <Textarea
                  id="description"
                  value={eventToEdit.description}
                  onChange={(e) => setEventToEdit({ ...eventToEdit, description: e.target.value })}
                  className="border-green-200 focus:border-green-400 focus:ring-green-200 min-h-[80px]"
                  placeholder="Add more details about this reminder..."
                />
              </div>
            </CardContent>
          </Card>

          {/* Settings Card */}
          <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-indigo-50">
            <CardContent className="p-4 space-y-4">
              <div className="flex items-center gap-2 mb-3">
                <Zap className="h-5 w-5 text-blue-600" />
                <h3 className="font-semibold text-blue-800">Reminder Settings</h3>
              </div>

              <div className="grid grid-cols-2 gap-4">
                {/* Reminder Type */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">Type</Label>
                  <Select
                    value={eventToEdit.reminder_type || "General"}
                    onValueChange={(value) =>
                      setEventToEdit({ ...eventToEdit, reminder_type: value })
                    }
                  >
                    <SelectTrigger className="border-blue-200 focus:border-blue-400">
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="General">📋 General</SelectItem>
                      <SelectItem value="Task">✅ Task</SelectItem>
                      <SelectItem value="Appointment">📅 Appointment</SelectItem>
                      <SelectItem value="Meeting">🤝 Meeting</SelectItem>
                      <SelectItem value="Birthday">🎂 Birthday</SelectItem>
                      <SelectItem value="Anniversary">💝 Anniversary</SelectItem>
                      <SelectItem value="Payment">💳 Payment</SelectItem>
                      <SelectItem value="Health">🏥 Health</SelectItem>
                      <SelectItem value="Other">📌 Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Priority */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">Priority</Label>
                  <Select
                    value={eventToEdit.priority || "Normal"}
                    onValueChange={(value) =>
                      setEventToEdit({ ...eventToEdit, priority: value })
                    }
                  >
                    <SelectTrigger className={`border-blue-200 focus:border-blue-400 ${getPriorityColor(eventToEdit.priority)}`}>
                      <div className="flex items-center gap-2">
                        {getPriorityIcon(eventToEdit.priority)}
                        <SelectValue placeholder="Select priority" />
                      </div>
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Low">
                        <div className="flex items-center gap-2">
                          <CheckCircle2 className="h-4 w-4 text-green-500" />
                          Low
                        </div>
                      </SelectItem>
                      <SelectItem value="Normal">
                        <div className="flex items-center gap-2">
                          <Circle className="h-4 w-4 text-blue-500" />
                          Normal
                        </div>
                      </SelectItem>
                      <SelectItem value="High">
                        <div className="flex items-center gap-2">
                          <AlertCircle className="h-4 w-4 text-orange-500" />
                          High
                        </div>
                      </SelectItem>
                      <SelectItem value="Critical">
                        <div className="flex items-center gap-2">
                          <AlertTriangle className="h-4 w-4 text-red-500" />
                          Critical
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                {/* Advance Notice */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">Advance Notice</Label>
                  <Select
                    value={(eventToEdit.advance_notice_minutes || 30).toString()}
                    onValueChange={(value) =>
                      setEventToEdit({ ...eventToEdit, advance_notice_minutes: parseInt(value) })
                    }
                  >
                    <SelectTrigger className="border-blue-200 focus:border-blue-400">
                      <SelectValue placeholder="Select notice time" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="5">5 minutes</SelectItem>
                      <SelectItem value="15">15 minutes</SelectItem>
                      <SelectItem value="30">30 minutes</SelectItem>
                      <SelectItem value="60">1 hour</SelectItem>
                      <SelectItem value="120">2 hours</SelectItem>
                      <SelectItem value="1440">1 day</SelectItem>
                      <SelectItem value="10080">1 week</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Repeat Pattern */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">Repeat</Label>
                  <Select
                    value={eventToEdit.repeat_pattern || "None"}
                    onValueChange={(value) =>
                      setEventToEdit({ ...eventToEdit, repeat_pattern: value })
                    }
                  >
                    <SelectTrigger className="border-blue-200 focus:border-blue-400">
                      <div className="flex items-center gap-2">
                        <Repeat className="h-4 w-4 text-blue-500" />
                        <SelectValue placeholder="Select repeat" />
                      </div>
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="None">🚫 None</SelectItem>
                      <SelectItem value="Daily">📅 Daily</SelectItem>
                      <SelectItem value="Weekly">📆 Weekly</SelectItem>
                      <SelectItem value="Monthly">🗓️ Monthly</SelectItem>
                      <SelectItem value="Yearly">📋 Yearly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Tags */}
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">Tags</Label>
                <div className="flex items-center gap-2">
                  <Tag className="h-4 w-4 text-blue-500" />
                  <Input
                    value={eventToEdit.tags || ""}
                    onChange={(e) =>
                      setEventToEdit({ ...eventToEdit, tags: e.target.value })
                    }
                    className="border-blue-200 focus:border-blue-400 focus:ring-blue-200"
                    placeholder="Add tags separated by commas (e.g., work, important, meeting)"
                  />
                </div>
              </div>

              {/* Status */}
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">Status</Label>
                <Select
                  value={eventToEdit.status || "Active"}
                  onValueChange={(value) => setEventToEdit({ ...eventToEdit, status: value })}
                >
                  <SelectTrigger className="border-blue-200 focus:border-blue-400">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Active">🟢 Active</SelectItem>
                    <SelectItem value="Completed">✅ Completed</SelectItem>
                    <SelectItem value="Snoozed">😴 Snoozed</SelectItem>
                    <SelectItem value="Cancelled">❌ Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Snooze Section */}
          <Card className="border-amber-200 bg-gradient-to-br from-amber-50 to-yellow-50">
            <CardContent className="p-4 space-y-4">
              <div className="flex items-center gap-2 mb-3">
                <Clock className="h-5 w-5 text-amber-600" />
                <h3 className="font-semibold text-amber-800">Snooze Options</h3>
              </div>

              <div className="flex items-center gap-3">
                <Button
                  type="button"
                  variant={eventToEdit.snoozed ? "default" : "outline"}
                  size="sm"
                  className={eventToEdit.snoozed ?
                    "bg-amber-500 hover:bg-amber-600 text-white" :
                    "text-amber-600 border-amber-300 hover:bg-amber-50"}
                  onClick={handleSnoozeToggle}
                >
                  {eventToEdit.snoozed ?
                    <Bell className="h-4 w-4 mr-2" /> :
                    <BellOff className="h-4 w-4 mr-2" />
                  }
                  {eventToEdit.snoozed ? "Snoozed" : "Enable Snooze"}
                </Button>

                {eventToEdit.snoozed && (
                  <div className="flex-1 flex items-center gap-2">
                    <Clock className="h-4 w-4 text-amber-500" />
                    <Input
                      type="datetime-local"
                      value={formatDateTimeLocal(eventToEdit.snoozeUntil)}
                      onChange={handleSnoozeTimeChange}
                      className="flex-1 border-amber-200 focus:border-amber-400 focus:ring-amber-200"
                    />
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </BaseModal>
  );
}