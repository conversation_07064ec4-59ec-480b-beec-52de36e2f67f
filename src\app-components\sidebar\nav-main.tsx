import React, { useEffect, useState } from "react";
import { ChevronRight, type LucideIcon } from "lucide-react";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarMenuSub,
  SidebarMenuSubItem,
  SidebarMenuSubButton,
} from "@/components/ui/sidebar";
import {  useLocation } from "react-router-dom";
import PermissionAwareLink from "@/components/navigation/PermissionAwareLink";
import useNavStore from "@/zustand/useNavStore";

export type SubSubMenuItem = {
  title: string;
  url: string;
};

export type SubMenuItem = {
  title: string;
  url: string;
  isActive?: boolean;
  items?: SubSubMenuItem[];
};

export type MenuItem = {
  title: string;
  url: string;
  icon?: LucideIcon;
  isActive?: boolean;
  items?: SubMenuItem[];
};

export function NavMain({
  label,
  items,
  open,
  onToggle,
}: {
  label: string;
  items: MenuItem[];
  open: boolean;
  onToggle: () => void;
}) {

  const { levelOne, setLevelOneActive, levelOneActive, setLevelTwo, levelTwoActive, setLevelTwoActive } = useNavStore() as {
    levelOne: string | null;
    setLevelOneActive: (value: string | null) => void;
    levelOneActive: string | null;
    setLevelTwo: (value: string | null) => void;
    levelTwoActive: string | null;
    setLevelTwoActive: (value: string | null) => void;
  }


  return (
    <SidebarGroup>
      <Collapsible open={open} onOpenChange={onToggle}>
        <CollapsibleTrigger asChild>
          <SidebarGroupLabel className={`flex items-center justify-between text-xs uppercase text-sidebar-foreground/70 px-4 py-3 cursor-pointer hover:bg-sidebar-accent hover:text-sidebar-accent-foreground ${levelOneActive === label && "font-bold text-sm"}`}>
            {label}
            <ChevronRight
              className={`w-4 h-4 transition-transform duration-200 ${open ? "rotate-90" : ""
                }`}
            />
          </SidebarGroupLabel>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <SidebarMenu className="space-y-1 text-sm">
            {items.map((item) =>
              item.items && item.items.length > 0 ? (
                <TopLevelWithChildren key={item.title} item={item} />
              ) : (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    data-active={item.isActive}
                    className={`flex items-center py-3 px-4 text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground data-[active=true]:bg-sidebar-primary data-[active=true]:text-sidebar-primary-foreground ${levelTwoActive === item.title && "font-bold text-md"}`}
                    onClick={() => {
                      setLevelTwo(item.title)
                      setLevelTwoActive(item.title)
                      setLevelOneActive(label)
                    }}
                  >
                    <PermissionAwareLink to={item.url} className="flex items-center w-full">
                      {item.icon && (
                        <item.icon className="w-5 h-5 text-sidebar-foreground/70 shrink-0" />
                      )}
                      <span className="ml-3 truncate">{item.title}</span>
                    </PermissionAwareLink>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              )
            )}
          </SidebarMenu>
        </CollapsibleContent>
      </Collapsible>
    </SidebarGroup>
  );
}

function TopLevelWithChildren({ item }: { item: MenuItem }) {
  const [openMenu, setOpenMenu] = React.useState<string | null>(
    item.isActive ? item.title : null
  );
  const [openSub, setOpenSub] = React.useState<string | null>(
    item.items?.find((si) => si.isActive)?.title || null
  );

  const { levelOne, setLevelOneActive, levelTwo, setLevelTwo, levelTwoActive, setLevelTwoActive, levelThree, setLevelThree, levelThreeActive, setLevelThreeActive, } = useNavStore() as {
    levelOne: string | null;
    setLevelOneActive: (value: string | null) => void;
    levelTwo: string | null;
    setLevelTwo: (value: string | null) => void;
    levelTwoActive: string | null;
    setLevelTwoActive: (value: string | null) => void;
    levelThree: string | null;
    setLevelThree: (value: string | null) => void;
    levelThreeActive: string | null;
    setLevelThreeActive: (value: string | null) => void;
  };

  return (
    <Collapsible
      asChild
      open={levelTwo === item.title}
      onOpenChange={() => {
        setLevelTwo(levelTwo === item.title ? null : item.title)
        // setOpenMenu((prev) => (prev === item.title ? null : item.title))
      }
      }
      className="group/collapsible"
    >
      <SidebarMenuItem>
        <CollapsibleTrigger asChild>
          <SidebarMenuButton className={`flex items-center py-3 px-4 text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground ${levelTwoActive === item.title && "font-bold text-md"}`}>
            {item.icon && (
              <item.icon className="w-5 h-5 text-sidebar-foreground/70 shrink-0" />
            )}
            <span className="ml-3 truncate">{item.title}</span>
            <ChevronRight className="ml-auto text-sidebar-foreground/70 transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
          </SidebarMenuButton>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <SidebarMenuSub className="space-y-1 pl-8 py-1">
            {item.items!.map((sub) =>
              sub.items && sub.items.length > 0 ? (
                <SecondLevelWithChildren
                  key={sub.title}
                  subItem={sub}
                  isOpen={levelThree === sub.title}
                  onOpenChange={(isOpen) => {
                    setLevelThree(levelThree === sub.title ? null : sub.title)
                    // setOpenSub(isOpen ? sub.title : null)
                  }
                  }
                />
              ) : (
                <SidebarMenuSubItem key={sub.title}>
                  <SidebarMenuSubButton
                    asChild
                    className={`block text-sidebar-foreground/70 hover:text-sidebar-foreground px-2 py-1 rounded-md truncate ${levelThreeActive === sub.title && "font-bold text-md"}`}
                    onClick={() => {
                      setLevelThree(sub.title)
                      setLevelThreeActive(sub.title)
                      setLevelTwoActive(item.title)
                      setLevelOneActive(levelOne)
                    }}
                  >
                    <PermissionAwareLink to={sub.url}>{sub.title}</PermissionAwareLink>
                  </SidebarMenuSubButton>
                </SidebarMenuSubItem>
              )
            )}
          </SidebarMenuSub>
        </CollapsibleContent>
      </SidebarMenuItem>
    </Collapsible>
  );
}

function SecondLevelWithChildren({
  subItem,
  isOpen,
  onOpenChange,
}: {
  subItem: SubMenuItem;
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
}) {
//   const location = useLocation();
  
//   const [activeTab, setActiveTab] = useState('');
//   useEffect(()=>{
// setActiveTab(location.pathname+location.hash)
//   },[location])
//  console.log(location)
//   const [activeTab, setActiveTab] = useState('');
// location.pathname+location.hash
//   // Extract hash from URL and set active tab
//   useEffect(() => {
//     const hash = location.hash.replace('#', '');
//     if (hash && tabs.some(tab => tab.value === hash)) {
//       setActiveTab(hash);
//     }
//   }, [location.hash]);
// console.log('fffffffffff'+location.hash)
// /mybookings#MPESA
  const { levelOne, setLevelOneActive, levelThreeActive, setLevelThreeActive, levelThree, setLevelThree, levelTwo, setLevelTwoActive } = useNavStore() as {
    levelOne: string | null;
    setLevelOneActive: (value: string | null) => void;
    levelThreeActive: string | null;
    setLevelThreeActive: (value: string | null) => void;
    levelThree: string | null;
    setLevelThree: (value: string | null) => void;
    levelTwo: string | null;
    setLevelTwoActive: (value: string | null) => void;
  };

  return (
    <SidebarMenuSubItem>
      <Collapsible
        open={isOpen}
        onOpenChange={onOpenChange}
        className="w-full"
      >
        <CollapsibleTrigger className={`flex items-center justify-between w-full text-sidebar-foreground/70 hover:text-sidebar-foreground px-2 py-1 rounded-md truncate ${levelThreeActive === subItem.title && "font-bold text-md"}`}>
          <span>{subItem.title}</span>
          <ChevronRight
            className={`w-4 h-4 text-sidebar-foreground/70 transition-transform duration-200 ${isOpen ? "rotate-90" : ""
              }`}
          />
        </CollapsibleTrigger>
        <CollapsibleContent>
          <div className="pl-4 mt-1 space-y-1">
            {subItem.items!.map((third) => (
              <PermissionAwareLink
                key={third.title}
                to={third.url}
                className={`block text-sm text-sidebar-foreground/70 hover:text-sidebar-foreground px-2 py-1 rounded-md truncate ${location.pathname+location.hash === third.url && "font-bold text-md"}`}
                onClick={() => {
                  setLevelThreeActive(subItem.title)
                  setLevelTwoActive(levelTwo)
                  setLevelOneActive(levelOne)
                }}
              >
                {third.title}
              </PermissionAwareLink>
            ))}
          </div>
        </CollapsibleContent>
      </Collapsible>
    </SidebarMenuSubItem>
  );
}
