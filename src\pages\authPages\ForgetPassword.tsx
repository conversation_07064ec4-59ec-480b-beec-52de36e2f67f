import { useForm } from 'react-hook-form';
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod";
import Logo from '@/assets/logo.svg';
import { Link } from 'react-router-dom';
import { Loader, Mail, Play } from 'lucide-react';
import { usePostForgotPasswordMutation } from '@/redux/slices/auth';
import { toast } from '@/components/custom/Toast/MyToast';

const ForgetPassword = () => {
  // form
  const formSchema = z.object({
    email: z.string().min(1, { message: "Email is required.", }).email({ message: "Invalid email format." }),
  })

  const { register, handleSubmit, watch, formState: { errors } } = useForm<z.infer<typeof formSchema>>({ resolver: zodResolver(formSchema), });
  const [postForgotPassword, { isLoading }] = usePostForgotPasswordMutation();
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      const response = await postForgotPassword(values).unwrap();
      toast.success(response?.message || 'Check your email for a reset link');
      // console.log('Login successful:', response);
    } catch (err: any) {
      const { data } = err
      const error = data.non_field_errors[0]
      toast.error(error || 'Something went wrong! Please try again.')
      // console.error('Login failed:', data);
    }
  }

  return <div className="w-full lg:w-1/2 flex items-center justify-center p-8 bg-gray-900">
    <div className="w-full max-w-md space-y-8">
      {/* Mobile Logo */}
      <div className="lg:hidden flex justify-center">
        <div className="absolute top-6 left-6 z-10   px-3 py-1.5 rounded">
          <img src={Logo} alt="Optiven Logo" className="w-32 h-auto" />
        </div>
      </div>

      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold text-white">
          Timua Kivumbi Na 5 Billion<br />
          By Optiven
        </h1>
      </div>

      {/* Login Link */}
      <div className="text-center">
        <span className="text-gray-400 text-sm">Go back to </span>
        <Link to='/auth/login' className="text-green-400 hover:text-green-300 text-sm font-medium">
          Log In
        </Link>
      </div>
      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="space-y-6">
          {/* Email Field */}
          <div>
            <div className="relative">
              <Mail className="absolute left-5 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                {...register("email")}
                placeholder="Your Email"
                className="w-full pl-14 pr-4 py-3 !bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              />
            </div>
            {errors.email && (
              <p className="text-red-500 text-xs pt-2 pl-3">{errors.email.message}</p>
            )}
          </div>


          {/* Submit Button */}
          <button className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg transition-colors  group" type="submit" disabled={isLoading}>
            {
              isLoading ? (
                <span className='flex items-center justify-center space-x-2 '>
                  <Loader className="fas fa-spinner animate-spin" size={22} />
                  <span>Requesting...</span>
                </span>
              ) : <span className='flex items-center justify-center space-x-2 '>
                <span>Request Email OTP</span>
                <Play className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
              </span>
            }

          </button>
        </div>
      </form>

      {/* Terms */}
      <div className="text-center">
        <p className="text-xs text-gray-500">
          By creating your Gen account, you agree to our{' '}
          <button className="text-green-400 hover:text-green-300 underline">
            Terms of Service
          </button>{' '}
          and{' '}
          <button className="text-green-400 hover:text-green-300 underline">
            Privacy Policy
          </button>
        </p>
      </div>
    </div>
  </div>
}

export default ForgetPassword

// import { Link } from "react-router-dom";
// import {
//   Form,
//   FormControl,
//   FormField,
//   FormItem,
//   FormLabel,
//   FormMessage,
// } from "@/components/ui/form";
// import { Input } from "@/components/ui/input";
// import { useForm } from "react-hook-form";
// import { PrimaryButton } from "@/components/custom/buttons/buttons";
// import { ArrowLeft, Loader } from "lucide-react";
// import { z } from "zod"
// import { zodResolver } from "@hookform/resolvers/zod";
// import { usePostForgotPasswordMutation } from "@/redux/slices/auth";
// import { toast } from "@/components/custom/Toast/MyToast";

// type Props = {};

// const ForgetPassword = ({ }: Props) => {

//   const formSchema = z.object({
//     email: z.string().min(2).max(50),
//   })

//   const form = useForm<z.infer<typeof formSchema>>({
//     resolver: zodResolver(formSchema),
//     defaultValues: {
//       email: "",
//     },
//   })

//   const [postForgotPassword, { isLoading }] = usePostForgotPasswordMutation();

//   async function onSubmit(values: z.infer<typeof formSchema>) {
//     try {
//       const response = await postForgotPassword(values).unwrap();
//       toast.success(response?.message || 'Check your email for a reset link');
//       // console.log('Login successful:', response);
//     } catch (err: any) {
//       const { data } = err
//       const error = data.non_field_errors[0]
//       toast.error(error || 'Something went wrong! Please try again.')
//       // console.error('Login failed:', data);
//     }
//   }

//   return (
//     <>
//       <h1 className="text-center text-2xl font-semibold text-gray-800 dark:text-gray-100 ">
//         Forgot Password
//       </h1>
//       <p className="text-center text-gray-600 dark:text-gray-300 w-[80%] mx-auto text-sm">
//         Enter your email below
//       </p>
//       <div className="flex flex-col gap-4 mt-4 w-[80%] mx-auto">
//         <Form {...form}>
//           <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-3">
//             <FormField
//               control={form.control}
//               name="email"
//               render={({ field }) => (
//                 <FormItem>
//                   <FormLabel className="ml-2">Email</FormLabel>
//                   <FormControl>
//                     <Input type="email" placeholder="Email Address" {...field} />
//                   </FormControl>
//                   <FormMessage />
//                 </FormItem>
//               )}
//             />
//             <PrimaryButton variant="primary" type="submit" className="w-full" disabled={isLoading}>
//               {isLoading ? (
//                 <span className='flex gap-2 items-center justify-center'>
//                   <Loader className="fas fa-spinner animate-spin" size={20} />
//                   Loading...
//                 </span>
//               ) : ('Submit')}
//             </PrimaryButton>
//           </form>
//         </Form>

//         <div className="flex items-center justify-between flex-wrap mt-1">
//           <Link
//             to="/auth/login"
//             className="text-xs text-primary dark:text-gray-50 flex items-center hover:scale-105 transition-all duration-300 ease-in-out"
//           >
//             <ArrowLeft size={12} /> Back to Login
//           </Link>
//         </div>
//       </div>
//     </>
//   );
// };

// export default ForgetPassword;
