import BaseModal from "@/components/custom/modals/BaseModal";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useLazyGetPlotPaymentOptionsQuery } from "@/pages/OfferLetter/api/offerLetterApi";
import { useState } from "react";
import { toast } from "sonner";

type Props = {
  openModal: boolean;
  setOpenModal: (e: boolean) => void;
};

interface PaymentOption {
  no_of_ins: number;
  cash_price: number;
  deposit: number;
  monthly_install: number;
}

interface ApiError {
  data: {
    error: string;
  };
}

interface PlotPaymentData {
  data: {
    project_name: string;
    plot_no: string;
    plot_type: string;
    plot_size_definition: string;
    cash_price: number;
    Options: string; // JSON string of PaymentOption[]
  };
}

const PlotPaymentOptionModal = ({ openModal, setOpenModal }: Props) => {
  const [plotNumber, setPlotNumber] = useState("");
  const [fetchPlotPayment, { data, isLoading: loading, isError, error }] =
    useLazyGetPlotPaymentOptionsQuery();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!plotNumber.trim()) {
      toast.error("Please enter a plot number");
      return;
    }
    try {
      const res = await fetchPlotPayment({ PLOT_NO: plotNumber }).unwrap();
      if (res) {
        console.log("Plot number submitted:", plotNumber, res);
      }
    } catch (error) {
      console.error("Error:", error);
      return;
    }
  };

  const parseOptions = () => {
    try {
      let optionsString = (data as any)?.Options || "[]";

      // Fix malformed JSON: replace `][` with `,` and wrap in array brackets if needed
      if (optionsString.includes("]["))
        optionsString = optionsString.replace(/\]\[/g, ",");

      // Ensure it's a valid JSON array
      const options = JSON.parse(optionsString);
      const parsedOptions = Array.isArray(options) ? options : [];

      return parsedOptions.map((option: PaymentOption, index: number) => (
        <div
          key={index}
          className="border rounded-md p-4 hover:bg-gray-50 transition-colors duration-200"
        >
          <h4 className="font-semibold text-primary mb-3">
            {option.no_of_ins === 1
              ? "30 Days Payment Plan"
              : `${option.no_of_ins} Months Payment Plan`}
          </h4>
          <div className="space-y-2 text-sm text-gray-700">
            <p className="flex justify-between">
              <span>Total Price:</span>
              <span className="font-medium">
                KES {option.cash_price.toLocaleString()}
              </span>
            </p>
            <p className="flex justify-between">
              <span>Initial Deposit:</span>
              <span className="font-medium">
                KES {option.deposit.toLocaleString()}
              </span>
            </p>
            {option.no_of_ins > 1 && (
              <p className="flex justify-between text-primary">
                <span>Monthly Installment:</span>
                <span className="font-bold">
                  KES {Math.round(option.monthly_install).toLocaleString()}
                </span>
              </p>
            )}
          </div>
        </div>
      ));
    } catch (error) {
      console.error("Failed to parse payment options:", error);
      toast.error("Invalid payment options data received");
      return [];
    }
  };

  return (
    <BaseModal
      isOpen={openModal}
      onOpenChange={setOpenModal}
      size="xl"
      title="Plot Payment Option"
      description="Enter plot number to view payment options"
    >
      <form
        onSubmit={handleSubmit}
        className=" py-4 grid grid-cols-1 md:grid-cols-4 gap-4 items-center"
      >
        <div className="md:col-span-3 col-span-1 my-1">
          <Input
            id="plotNumber"
            value={plotNumber}
            onChange={(e) => setPlotNumber(e.target.value)}
            placeholder="Enter plot number"
            required
          />
        </div>
        <Button type="submit" className="my-1">
          View Payment Options
        </Button>
      </form>
      <div className="my-4">
        {loading ? (
          <div className="flex justify-center items-center w-full h-full">
            <SpinnerTemp type="spinner-double" size="md" />
          </div>
        ) : isError ? (
          <p className="text-red-500 border border-red-500 p-2 rounded-lg text-center">
            {(error as ApiError)?.data?.error ||
              "Failed to fetch payment options"}
          </p>
        ) : data ? (
          <div className="space-y-4">
            <div className="border rounded-md p-4">
              <h3 className="text-lg font-semibold mb-2">
                {(data as any)?.data?.project_name || "Plot Details"}
              </h3>
              <div className="space-y-2 text-sm">
                <p>Plot Number: {(data as any)?.plot_no || plotNumber}</p>
                <p>Plot Type: {(data as any)?.plot_type || "N/A"}</p>
                <p>Plot Size: {(data as any)?.plot_size_definition || "N/A"}</p>
                <p>
                  Cash Price: KES{" "}
                  {(data as any)?.cash_price?.toLocaleString() ?? "N/A"}
                </p>
              </div>
            </div>
            <div className="space-y-3">{parseOptions()}</div>
          </div>
        ) : null}
      </div>
    </BaseModal>
  );
};

export default PlotPaymentOptionModal;
