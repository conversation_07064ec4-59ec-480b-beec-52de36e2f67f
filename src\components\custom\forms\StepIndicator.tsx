import React from 'react';
import { Check } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useFormContext } from './FormContext';

interface StepIndicatorProps {
  steps: number;
  currentStep: number;
  labels?: string[];
  onStepClick?: (step: number) => void;
}

const StepIndicator: React.FC<StepIndicatorProps> = ({
  steps,
  currentStep,
  labels,
  onStepClick
}) => {
  const { canNavigateToStep } = useFormContext();

  return (
    <div className="w-full">
      <div className="flex items-center justify-between">
        {Array.from({ length: steps }, (_, index) => {
          const stepNumber = index + 1;
          const isActive = stepNumber === currentStep;
          const isCompleted = stepNumber < currentStep;
          const canNavigate = canNavigateToStep(stepNumber);
          const isClickable = !!onStepClick && canNavigate;
          
          // Container for step circle and label
          return (
            <div key={stepNumber} className="flex flex-col items-center relative">
              {/* Step circle */}
              <button
                type="button"
                onClick={() => onStepClick && canNavigate && onStepClick(stepNumber)}
                disabled={!isClickable}
                className={cn(
                  "w-10 h-10 rounded-full flex items-center justify-center transition-all duration-200",
                  isActive
                    ? "bg-primary text-primary-foreground"
                    : isCompleted
                      ? "bg-primary/80 text-primary-foreground"
                      : canNavigate
                        ? "bg-muted text-muted-foreground"
                        : "bg-gray-200 text-gray-400",
                  isClickable && !isActive && canNavigate && "hover:bg-primary/70 hover:text-primary-foreground cursor-pointer",
                  !canNavigate && "cursor-not-allowed opacity-50"
                )}
              >
                {isCompleted ? (
                  <Check className="h-5 w-5" />
                ) : (
                  <span>{stepNumber}</span>
                )}
              </button>
              
              {/* Connector line */}
              {stepNumber < steps && (
                <div className="absolute w-full left-1/2 right-0 h-0.5 top-5 z-0">
                  <div
                    className={cn(
                      "h-full transition-all duration-300",
                      stepNumber < currentStep ? "bg-primary/80" : "bg-muted"
                    )}
                    style={{ width: '100%' }}
                  />
                </div>
              )}
              
              {/* Step label */}
              {labels && labels[index] && (
                <span 
                  className={cn(
                    "mt-2 text-xs font-medium text-center",
                    isActive ? "text-primary" : "text-muted-foreground"
                  )}
                >
                  {labels[index]}
                </span>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default StepIndicator;