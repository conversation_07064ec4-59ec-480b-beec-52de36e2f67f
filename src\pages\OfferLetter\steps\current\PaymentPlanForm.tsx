import React from 'react';

interface PaymentPlanFormProps {
  formData: any;
  setFormData: (data: any) => void;
  onNext: () => void;
  onBack: () => void;
}

const PaymentPlanForm: React.FC<PaymentPlanFormProps> = ({
  formData,
  setFormData,
  onNext,
  onBack,
}) => {
  const handlePaymentChange = (plotNumber: string, paymentOption: string) => {
    setFormData({
      ...formData,
      selectedPayments: {
        ...formData.selectedPayments,
        [plotNumber]: paymentOption,
      },
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onNext();
  };

  const paymentOptions = [
    { value: '30days', label: '30 Days Cash Price @ Ksh 545,000' },
    { value: '3months', label: '3 Months @ Ksh 558,350 ( Deposit of Ksh 100,000 + 3 installments of Ksh 152,783 each )' },
    { value: '6months', label: '6 Months @ Ksh 571,700 ( Deposit of Ksh 100,000 + 6 installments of Ksh 78,617 each )' },
    { value: '12months', label: '12 Months @ Ksh 598,400 ( Deposit of Ksh 100,000 + 12 installments of Ksh 41,533 each )' },
  ];

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold text-gray-800 mb-2">Select your payment plan</h2>
        <p className="text-gray-600 mb-6">Here are the details of your plot(s)</p>
      </div>

      <div className="space-y-8">
        {/* Plot 1 */}
        <div>
          <h3 className="text-lg font-medium text-gray-700 mb-2">1. Plot Number : FO196</h3>
          <p className="text-gray-600 mb-2">Furaha Gardens 1/8th Acre - Residential</p>
          <p className="text-gray-600 mb-4">Expected Deposit - Ksh 100,000</p>
          <p className="text-gray-700 font-medium mb-4">Here are the Payments Options;</p>
          
          <div className="space-y-3">
            {paymentOptions.map((option) => (
              <label key={option.value} className="flex items-start space-x-3 cursor-pointer">
                <input
                  type="radio"
                  name="fo196-payment"
                  value={option.value}
                  checked={formData.selectedPayments?.fo196 === option.value}
                  onChange={(e) => handlePaymentChange('fo196', e.target.value)}
                  className="mt-1 text-green-500 focus:ring-green-500"
                />
                <span className="text-gray-700">{option.label}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Plot 2 */}
        <div>
          <h3 className="text-lg font-medium text-gray-700 mb-2">2. Plot Number : FG226</h3>
          <p className="text-gray-600 mb-2">Furaha Gardens 1/8th Acre - Residential</p>
          <p className="text-gray-600 mb-4">Expected Deposit - Ksh 100,000</p>
          <p className="text-gray-700 font-medium mb-4">Here are the Payments Options;</p>
          
          <div className="space-y-3">
            {paymentOptions.map((option) => (
              <label key={option.value} className="flex items-start space-x-3 cursor-pointer">
                <input
                  type="radio"
                  name="fg226-payment"
                  value={option.value}
                  checked={formData.selectedPayments?.fg226 === option.value}
                  onChange={(e) => handlePaymentChange('fg226', e.target.value)}
                  className="mt-1 text-green-500 focus:ring-green-500"
                />
                <span className="text-gray-700">{option.label}</span>
              </label>
            ))}
          </div>
        </div>
      </div>

      <div className="flex justify-between pt-6">
        <button
          type="button"
          onClick={onBack}
          className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
        >
          ← Go Back
        </button>
        <button
          type="submit"
          className="px-6 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors"
        >
          Next Page →
        </button>
      </div>
    </form>
  );
};

export default PaymentPlanForm;