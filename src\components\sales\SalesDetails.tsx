import { Separator } from '../ui/separator'
import { useSalesStore } from '@/zustand/useSaleStore'
import { formatNumberWithCommas, formatShortDate } from '@/utils/salesDataFormatter'
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { NavLink } from 'react-router-dom';
import { PrimaryButton } from '../custom/buttons/buttons';
import { ScrollArea } from '../ui/scroll-area';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger, } from "@/components/ui/accordion"


const SalesDetails = () => {
    const salesData = useSalesStore((state) => state.salesData);
    const {
        lead_file_no,
        lead_file_status_dropped,
        plot_no,
        purchase_price,
        selling_price,
        balance_lcy,
        customer_name,
        purchase_type,
        commission_threshold,
        deposit_threshold,
        discount,
        completion_date,
        no_of_installments,
        installment_amount,
        sale_agreement_sent,
        sale_agreement_signed,
        total_paid,
        transfer_cost_charged,
        transfer_cost_paid,
        overpayments,
        refunds,
        refundable_amount,
        penalties_accrued,
        booking_id,
        booking_date,
        additional_deposit_date,
        title_status,
        credit_officer_id,
        plot,
        project,
        marketer,
        customer_id,
        customer_lead_source,
        cat_lead_source,
    } = salesData;

    // Helper function to handle N/A display
    const displayValue = (value:any) => {
        return value === null || value === undefined || value === '' || value === ' ' ? 'N/A' : value;
    };

    // Helper function for formatted numbers with N/A fallback
    const displayFormattedNumber = (value:any) => {
        return value === null || value === undefined || value === '' || value === ' ' ? 'N/A' : formatNumberWithCommas(value);
    };

    // Helper function for formatted dates with N/A fallback
    const displayFormattedDate = (value:any) => {
        return value === null || value === undefined || value === '' || value === ' ' ? 'N/A' : formatShortDate(value);
    };

    return (
        <ScrollArea className="h-full overflow-auto">
            <div className='pl-0.5 pr-2.5 pb-6 overflow-x-hidden'>
                <div className='flex items-center gap-4 py-3'>
                    <Avatar className='h-12 w-12 '>
                        <AvatarImage src="" />
                        <AvatarFallback className='bg-primary text-primary-foreground text-2xl'>
                            {customer_name ? customer_name.charAt(0).toUpperCase() : 'N'}
                        </AvatarFallback>
                    </Avatar>
                    <div>
                        <p className='font-bold text-sm truncate whitespace-nowrap overflow-hidden'>
                            {displayValue(customer_name)}
                        </p>
                        <NavLink to={`/customer/${customer_id}`}>
                            <PrimaryButton size='sm' className='text-xs px-3'>View Customer</PrimaryButton>
                        </NavLink>
                    </div>
                </div>

                <Separator />

                <div className='grid grid-cols-2 gap-2 py-5'>
                    <div>
                        <p className='text-xs  font-bold truncate whitespace-nowrap overflow-hidden'>Plot No</p>
                        <p className='text-sm font-bold truncate whitespace-nowrap overflow-hidden'>
                            {displayValue(plot_no)}
                        </p>
                    </div>
                    <div className='text-right'>
                        <p className='text-xs  font-bold truncate whitespace-nowrap overflow-hidden'>Plot</p>
                        <p className='text-sm font-bold truncate whitespace-nowrap overflow-hidden'>
                            {displayValue(plot)}
                        </p>
                    </div>
                </div>

                <Separator />

                <Accordion type="multiple" defaultValue={['item-1', 'item-3']}>
                    <AccordionItem value="item-1" >
                        <AccordionTrigger> <p className='font-bold text-sm'>Lead Information</p></AccordionTrigger>
                        <AccordionContent>
                            <div className='grid grid-cols-2 gap-2'>
                                <div>
                                    <p className='text-[11px]  font-bold truncate whitespace-nowrap overflow-hidden'>Lead Source</p>
                                    <p className='text-xs  truncate whitespace-nowrap overflow-hidden'>
                                        {displayValue(cat_lead_source)}
                                    </p>
                                </div>
                                <div className='text-right'>
                                    <p className='text-[11px]  font-bold truncate whitespace-nowrap overflow-hidden'>Customer Lead Source</p>
                                    <p className='text-xs  truncate whitespace-nowrap overflow-hidden'>
                                        {displayValue(customer_lead_source)}
                                    </p>
                                </div>
                                <div>
                                    <p className='text-[11px]  truncate whitespace-nowrap overflow-hidden font-bold'>Lead No</p>
                                    <p className='text-xs  truncate whitespace-nowrap overflow-hidden'>
                                        {displayValue(lead_file_no)}
                                    </p>
                                </div>
                                <div className='text-right'>
                                    <p className='text-[11px]  font-bold truncate whitespace-nowrap overflow-hidden'>Lead Status</p>
                                    <p className='text-xs  truncate whitespace-nowrap overflow-hidden'>
                                        {lead_file_status_dropped === null || lead_file_status_dropped === undefined ? 'N/A' : (lead_file_status_dropped ? 'Dropped' : 'Active')}
                                    </p>
                                </div>
                            </div>
                        </AccordionContent>
                    </AccordionItem>

                    <AccordionItem value="item-2">
                        <AccordionTrigger><p className='font-bold text-sm'>Payment Information</p></AccordionTrigger>
                        <AccordionContent>
                            <div className='grid grid-cols-2 gap-2'>
                                <div>
                                    <p className='text-[11px]  font-bold truncate whitespace-nowrap overflow-hidden'>Purchase Type</p>
                                    <p className='text-xs  truncate whitespace-nowrap overflow-hidden'>
                                        {displayValue(purchase_type)}
                                    </p>
                                </div>
                                <div className='text-right'>
                                    <p className='text-[11px]  font-bold truncate whitespace-nowrap overflow-hidden'>Completion Date</p>
                                    <p className='text-xs  truncate whitespace-nowrap overflow-hidden'>
                                        {displayFormattedDate(completion_date)}
                                    </p>
                                </div>

                                <div>
                                    <p className='text-[11px]  font-bold truncate whitespace-nowrap overflow-hidden'>Installment Amount</p>
                                    <p className='text-xs  truncate whitespace-nowrap overflow-hidden'>
                                        {displayFormattedNumber(installment_amount)}
                                    </p>
                                </div>
                                <div className='text-right'>
                                    <p className='text-[11px]  font-bold truncate whitespace-nowrap overflow-hidden'>No of Installments</p>
                                    <p className='text-xs  truncate whitespace-nowrap overflow-hidden'>
                                        {displayValue(no_of_installments)}
                                    </p>
                                </div>

                                <div>
                                    <p className='text-[11px]  font-bold truncate whitespace-nowrap overflow-hidden'>Sell agre.. sent</p>
                                    <p className='text-xs  truncate whitespace-nowrap overflow-hidden'>
                                        {displayValue(sale_agreement_sent)}
                                    </p>
                                </div>
                                <div className='text-right'>
                                    <p className='text-[11px]  font-bold truncate whitespace-nowrap overflow-hidden'>Sell agre.. signed</p>
                                    <p className='text-xs  truncate whitespace-nowrap overflow-hidden'>
                                        {displayValue(sale_agreement_signed)}
                                    </p>
                                </div>

                                <div>
                                    <p className='text-[11px]  font-bold truncate whitespace-nowrap overflow-hidden'>Booking ID</p>
                                    <p className='text-xs  truncate whitespace-nowrap overflow-hidden'>
                                        {displayValue(booking_id)}
                                    </p>
                                </div>
                                <div className='text-right'>
                                    <p className='text-[11px]  font-bold truncate whitespace-nowrap overflow-hidden'>Booking Date</p>
                                    <p className='text-xs  truncate whitespace-nowrap overflow-hidden'>
                                        {displayFormattedDate(booking_date)}
                                    </p>
                                </div>

                                <div>
                                    <p className='text-[11px]  font-bold truncate whitespace-nowrap overflow-hidden'>Transfer Cost</p>
                                    <p className='text-xs  truncate whitespace-nowrap overflow-hidden'>
                                        {displayFormattedNumber(transfer_cost_charged)}
                                    </p>
                                </div>
                                <div className='text-right'>
                                    <p className='text-[11px]  font-bold truncate whitespace-nowrap overflow-hidden'>Transfer Cost Paid</p>
                                    <p className='text-xs  truncate whitespace-nowrap overflow-hidden'>
                                        {displayFormattedNumber(transfer_cost_paid)}
                                    </p>
                                </div>

                                <div>
                                    <p className='text-[11px]  font-bold truncate whitespace-nowrap overflow-hidden'>Purchase Price</p>
                                    <p className='text-xs  truncate whitespace-nowrap overflow-hidden'>
                                        {displayFormattedNumber(purchase_price)}
                                    </p>
                                </div>
                                <div className='text-right'>
                                    <p className='text-[11px]  font-bold truncate whitespace-nowrap overflow-hidden'>Selling Price</p>
                                    <p className='text-xs  truncate whitespace-nowrap overflow-hidden'>
                                        {displayFormattedNumber(selling_price)}
                                    </p>
                                </div>
                                <div>
                                    <p className='text-[11px]  font-bold truncate whitespace-nowrap overflow-hidden'>Discount</p>
                                    <p className='text-xs  truncate whitespace-nowrap overflow-hidden'>
                                        {displayFormattedNumber(discount)}
                                    </p>
                                </div>

                            </div>
                        </AccordionContent>
                    </AccordionItem>

                    <AccordionItem value="item-3">
                        <AccordionTrigger><p className='font-bold text-sm'>Additional Information</p></AccordionTrigger>
                        <AccordionContent>
                            <div className='grid grid-cols-2 gap-2'>
                                <div>
                                    <p className='text-[11px]  font-bold truncate whitespace-nowrap overflow-hidden'>Project</p>
                                    <p className='text-xs  truncate whitespace-nowrap overflow-hidden'>
                                        {displayValue(project)}
                                    </p>
                                </div>
                                <div className='text-right'>
                                    <p className='text-[11px]  font-bold truncate whitespace-nowrap overflow-hidden'>Marketer</p>
                                    <p className='text-xs  truncate whitespace-nowrap overflow-hidden'>
                                        {displayValue(marketer)}
                                    </p>
                                </div>
                                <div>
                                    <p className='text-[11px]  font-bold truncate whitespace-nowrap overflow-hidden'>Credit Officer</p>
                                    <p className='text-xs  truncate whitespace-nowrap overflow-hidden'>
                                        {displayValue(credit_officer_id)}
                                    </p>
                                </div>
                                <div className='text-right'>
                                    <p className='text-[11px]  font-bold truncate whitespace-nowrap overflow-hidden'>Title Status</p>
                                    <p className='text-xs  truncate whitespace-nowrap overflow-hidden'>
                                        {displayValue(title_status)}
                                    </p>
                                </div>
                                <div>
                                    <p className='text-[11px]  font-bold truncate whitespace-nowrap overflow-hidden'>Plot</p>
                                    <p className='text-xs  truncate whitespace-nowrap overflow-hidden'>
                                        {displayValue(plot)}
                                    </p>
                                </div>

                            </div>
                        </AccordionContent>
                    </AccordionItem>

                </Accordion>
            </div>
        </ScrollArea>
    )
}

export default SalesDetails