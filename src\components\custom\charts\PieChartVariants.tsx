import {
  Pie<PERSON>hart as Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Pie, Cell, Tooltip, Legend, ResponsiveContainer,
  Sector, Label, RadialBar
} from 'recharts';
import { <PERSON><PERSON><PERSON> } from 'lucide-react';
import React from 'react';

// Sample data
const data = [
  { name: 'Group A', value: 400 },
  { name: 'Group B', value: 300 },
  { name: 'Group C', value: 300 },
  { name: 'Group D', value: 200 },
  { name: 'Group E', value: 100 },
];

const twoLevelData = [
  { name: 'Group A', value: 400 },
  { name: 'Group B', value: 300 },
  { name: 'Group C', value: 300 },
  { name: 'Group D', value: 200 },
];

const innerData = [
  { name: 'Inner A', value: 700 },
  { name: 'Inner B', value: 500 },
];

const COLORS = [
  'hsl(var(--primary))',
  'hsl(var(--accent))',
  'hsl(var(--destructive))',
  'hsl(var(--success))',
  'hsl(var(--warning))',
];

// Helper for styling
const chartStyles = {
  containerStyle: { width: '100%', height: 300 },
  margin: { top: 5, right: 30, left: 20, bottom: 5 },
  tooltipStyle: { 
    backgroundColor: 'hsl(var(--card))', 
    border: '1px solid hsl(var(--border))',
    borderRadius: '0.5rem',
    color: 'hsl(var(--card-foreground))'
  }
};

// Simple Pie Chart
export const SimplePieChart = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <RechartsPieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={true}
            outerRadius={80}
            fill="hsl(var(--primary))"
            dataKey="value"
            nameKey="name"
            label={(entry) => entry.name}
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
        </RechartsPieChart>
      </ResponsiveContainer>
    </div>
  );
};

// Two Level Pie Chart
export const TwoLevelPieChart = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <RechartsPieChart>
          <Pie
            data={twoLevelData}
            cx="50%"
            cy="50%"
            outerRadius={80}
            fill="hsl(var(--primary))"
            dataKey="value"
            nameKey="name"
            label={(entry) => entry.name}
          >
            {twoLevelData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <Pie
            data={innerData}
            cx="50%"
            cy="50%"
            innerRadius={40}
            outerRadius={60}
            fill="hsl(var(--accent))"
            dataKey="value"
            nameKey="name"
          >
            {innerData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[(index + 2) % COLORS.length]} />
            ))}
          </Pie>
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
        </RechartsPieChart>
      </ResponsiveContainer>
    </div>
  );
};

// Straight Angle Pie Chart
export const StraightAnglePieChart = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <RechartsPieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            startAngle={180}
            endAngle={0}
            innerRadius={60}
            outerRadius={80}
            fill="hsl(var(--primary))"
            dataKey="value"
            nameKey="name"
            label={(entry) => entry.name}
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
        </RechartsPieChart>
      </ResponsiveContainer>
    </div>
  );
};

// Two Simple Pie Chart
export const TwoSimplePieChart = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <RechartsPieChart>
          <Pie
            data={twoLevelData}
            cx="30%"
            cy="50%"
            outerRadius={60}
            fill="hsl(var(--primary))"
            dataKey="value"
            nameKey="name"
          >
            {twoLevelData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <Pie
            data={innerData}
            cx="70%"
            cy="50%"
            outerRadius={60}
            fill="hsl(var(--accent))"
            dataKey="value"
            nameKey="name"
          >
            {innerData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[(index + 2) % COLORS.length]} />
            ))}
          </Pie>
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
        </RechartsPieChart>
      </ResponsiveContainer>
    </div>
  );
};

// Custom Active Shape Pie Chart
interface CustomActiveShapePieChartProps {
  data?: Array<{ name: string; value: number }>;
}

export const CustomActiveShapePieChart = ({ data: propData }: CustomActiveShapePieChartProps) => {
  // Use provided data or fallback to sample data
  const chartData = propData && propData.length > 0 ? propData : data;
  const [activeIndex, setActiveIndex] = React.useState(0);

  const onPieEnter = (_, index) => {
    setActiveIndex(index);
  };

  const renderActiveShape = (props) => {
    const { 
      cx, cy, innerRadius, outerRadius, startAngle, endAngle,
      fill, payload, percent, value 
    } = props;
  
    return (
      <g>
        <text key="name-text" x={cx} y={cy} dy={-20} textAnchor="middle" fill="hsl(var(--foreground))">
          {payload.name}
        </text>
        <text key="value-text" x={cx} y={cy} dy={0} textAnchor="middle" fill="hsl(var(--foreground))">
          {`Value: ${value}`}
        </text>
        <text key="percent-text" x={cx} y={cy} dy={20} textAnchor="middle" fill="hsl(var(--foreground))">
          {`(${(percent * 100).toFixed(2)}%)`}
        </text>
        <Sector
          key="inner-sector"
          cx={cx}
          cy={cy}
          innerRadius={innerRadius}
          outerRadius={outerRadius}
          startAngle={startAngle}
          endAngle={endAngle}
          fill={fill}
        />
        <Sector
          key="outer-sector"
          cx={cx}
          cy={cy}
          startAngle={startAngle}
          endAngle={endAngle}
          innerRadius={outerRadius + 6}
          outerRadius={outerRadius + 10}
          fill={fill}
        />
      </g>
    );
  };

  // Show message when no data is available
  if (!chartData || chartData.length === 0) {
    return (
      <div style={chartStyles.containerStyle}>
        <div className="flex items-center justify-center h-full">
          <div className="text-center text-gray-500 dark:text-gray-400">
            <PieChart className="w-12 h-12 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No valid site data available</p>
            <p className="text-xs">Site visits with valid project names will appear here</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <RechartsPieChart>
          <Pie
            activeIndex={activeIndex}
            activeShape={renderActiveShape}
            data={chartData}
            cx="50%"
            cy="50%"
            innerRadius={60}
            outerRadius={80}
            fill="hsl(var(--primary))"
            dataKey="value"
            nameKey="name"
            onMouseEnter={onPieEnter}
          >
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
        </RechartsPieChart>
      </ResponsiveContainer>
    </div>
  );
};

// Pie Chart With Customized Label
export const PieChartWithCustomizedLabel = () => {
  const renderCustomizedLabel = ({
    cx, cy, midAngle, innerRadius, outerRadius, percent, index,
  }) => {
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * Math.PI / 180);
    const y = cy + radius * Math.sin(-midAngle * Math.PI / 180);
  
    return (
      <text 
        key={`label-${index}`}
        x={x} 
        y={y} 
        fill="hsl(var(--background))" 
        textAnchor="middle" 
        dominantBaseline="central"
        fontSize={12}
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <RechartsPieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={renderCustomizedLabel}
            outerRadius={80}
            fill="hsl(var(--primary))"
            dataKey="value"
            nameKey="name"
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
        </RechartsPieChart>
      </ResponsiveContainer>
    </div>
  );
};

// Pie Chart With Padding Angle
export const PieChartWithPaddingAngle = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <RechartsPieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            innerRadius={60}
            outerRadius={80}
            fill="hsl(var(--primary))"
            paddingAngle={5}
            dataKey="value"
            nameKey="name"
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
        </RechartsPieChart>
      </ResponsiveContainer>
    </div>
  );
};

// Pie Chart With Needle
export const PieChartWithNeedle = () => {
  // Target value
  const targetValue = 65; // Percentage
  const total = 100;
  
  // Calculate angle for needle
  const needleAngle = 180 - (targetValue / total) * 180;

  // Modified data for the gauge
  const gaugeData = [
    { name: 'Low', value: 33, color: 'hsl(var(--destructive))' },
    { name: 'Moderate', value: 33, color: 'hsl(var(--warning))' },
    { name: 'High', value: 34, color: 'hsl(var(--success))' },
  ];

  const needleRadius = 80;
  const cx = '50%';
  const cy = '50%';

  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <RechartsPieChart>
          <Pie
            data={gaugeData}
            cx={cx}
            cy={cy}
            startAngle={180}
            endAngle={0}
            innerRadius={40}
            outerRadius={80}
            dataKey="value"
            nameKey="name"
          >
            {gaugeData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
            <Label
              value={`${targetValue}%`}
              position="center"
              fill="hsl(var(--foreground))"
              style={{ fontSize: '24px', fontWeight: 'bold' }}
            />
          </Pie>

          {/* Needle */}
          <svg>
            <defs>
              <marker
                id="arrow"
                viewBox="0 0 10 10"
                refX="5"
                refY="5"
                markerWidth="6"
                markerHeight="6"
                orient="auto-start-reverse"
              >
                <path key="arrow-path" d="M 0 0 L 10 5 L 0 10 z" fill="hsl(var(--foreground))" />
              </marker>
            </defs>
            <line
              key="needle-line"
              x1="50%"
              y1="50%"
              x2={`${50 + 45 * Math.cos((needleAngle * Math.PI) / 180)}%`}
              y2={`${50 - 45 * Math.sin((needleAngle * Math.PI) / 180)}%`}
              stroke="hsl(var(--foreground))"
              strokeWidth={2}
              markerEnd="url(#arrow)"
            />
            <circle key="needle-center" cx="50%" cy="50%" r="5" fill="hsl(var(--foreground))" />
          </svg>

          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
        </RechartsPieChart>
      </ResponsiveContainer>
    </div>
  );
}; 