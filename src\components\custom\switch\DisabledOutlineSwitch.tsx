import * as React from "react"
import { X } from "lucide-react"

export const DisabledOutlineSwitch = () => (
  <label className="relative inline-flex items-center cursor-not-allowed">
    <input type="checkbox" className="sr-only" disabled />
    <div className="w-11 h-6 border border-gray-400 bg-gray-100 rounded-full flex items-center justify-center">
      <X className="text-gray-500 w-4 h-4" />
    </div>
  </label>
)