Offer Letters


POST
/inventory/projects
inventory_projects_create


PATCH
/inventory/projects/{projectId}
inventory_projects_partial_update


DELETE
/inventory/projects/{projectId}
inventory_projects_delete


GET
/offer-letter-company-directors/
offer-letter-company-directors_list

Parameters
Try it out
Name	Description
page
integer
(query)
A page number within the paginated result set.

page
page_size
integer
(query)
Number of results to return per page.

page_size
Responses
Response content type

application/json
Code	Description
200	
Example Value
Model
{
count*	integer
next	string($uri)
x-nullable: true
previous	string($uri)
x-nullable: true
results*	[OfferLetterCompanyDirector{
id	ID[...]
director_id*	Director id[...]
first_name*	First name[...]
last_name*	Last name[...]
country_codes	Country codes[...]
phone*	Phone[...]
email*	Email[...]
national_id*	National id[...]
created_at*	Created at[...]
company*	Company[...]
 
}]
 
}

POST
/offer-letter-company-directors/
offer-letter-company-directors_create

Parameters
Try it out
Name	Description
data *
object
(body)
Example Value
Model
OfferLetterCompanyDirector{
director_id*	integer
title: Director id
maximum: 2147483647
minimum: -2147483648
first_name*	string
title: First name
maxLength: 150
minLength: 1
last_name*	string
title: Last name
maxLength: 150
minLength: 1
country_codes	string
title: Country codes
maxLength: 7
x-nullable: true
phone*	string
title: Phone
maxLength: 30
minLength: 1
email*	string
title: Email
maxLength: 150
minLength: 1
national_id*	string
title: National id
maxLength: 30
minLength: 1
created_at*	string($date)
title: Created at
company*	integer
title: Company
 
}
Responses
Response content type

application/json
Code	Description
201	
Example Value
Model
OfferLetterCompanyDirector{
id	integer
title: ID
readOnly: true
director_id*	integer
title: Director id
maximum: 2147483647
minimum: -2147483648
first_name*	string
title: First name
maxLength: 150
minLength: 1
last_name*	string
title: Last name
maxLength: 150
minLength: 1
country_codes	string
title: Country codes
maxLength: 7
x-nullable: true
phone*	string
title: Phone
maxLength: 30
minLength: 1
email*	string
title: Email
maxLength: 150
minLength: 1
national_id*	string
title: National id
maxLength: 30
minLength: 1
created_at*	string($date)
title: Created at
company*	integer
title: Company
 
}

GET
/offer-letter-company-directors/{id}/
offer-letter-company-directors_read


PATCH
/offer-letter-company-directors/{id}/
offer-letter-company-directors_partial_update


DELETE
/offer-letter-company-directors/{id}/
offer-letter-company-directors_delete


GET
/offer-letter-company/
offer-letter-company_list


POST
/offer-letter-company/
offer-letter-company_create

Parameters
Try it out
Name	Description
data *
object
(body)
Example Value
Model
OfferLetterCompany{
company_name	string
title: Company name
maxLength: 150
x-nullable: true
company_registration_number	string
title: Company registration number
maxLength: 100
x-nullable: true
company_country_code	string
title: Company country code
maxLength: 7
x-nullable: true
phone	string
title: Phone
maxLength: 20
x-nullable: true
email	string
title: Email
maxLength: 150
x-nullable: true
address	string
title: Address
maxLength: 200
x-nullable: true
country	string
title: Country
maxLength: 50
x-nullable: true
city	string
title: City
maxLength: 50
x-nullable: true
company_kra	string
title: Company kra
maxLength: 30
x-nullable: true
preferred_contact	Preferred contact{
 
x-nullable	true
}
offer_letter*	integer
title: Offer letter
 
}
Responses
Response content type

application/json
Code	Description
201	
Example Value
Model
OfferLetterCompany{
id	integer
title: ID
readOnly: true
company_name	string
title: Company name
maxLength: 150
x-nullable: true
company_registration_number	string
title: Company registration number
maxLength: 100
x-nullable: true
company_country_code	string
title: Company country code
maxLength: 7
x-nullable: true
phone	string
title: Phone
maxLength: 20
x-nullable: true
email	string
title: Email
maxLength: 150
x-nullable: true
address	string
title: Address
maxLength: 200
x-nullable: true
country	string
title: Country
maxLength: 50
x-nullable: true
city	string
title: City
maxLength: 50
x-nullable: true
company_kra	string
title: Company kra
maxLength: 30
x-nullable: true
preferred_contact	Preferred contact{
 
x-nullable	true
}
created_at	string($date-time)
title: Created at
readOnly: true
offer_letter*	integer
title: Offer letter
 
}

GET
/offer-letter-company/{id}/
offer-letter-company_read


PATCH
/offer-letter-company/{id}/
offer-letter-company_partial_update


DELETE
/offer-letter-company/{id}/
offer-letter-company_delete


GET
/offer-letter-group-members/
offer-letter-group-members_list


POST
/offer-letter-group-members/
offer-letter-group-members_create

Parameters
Try it out
Name	Description
data *
object
(body)
Example Value
Model
OfferLetterGroupMember{
member_id*	integer
title: Member id
maximum: 2147483647
minimum: -2147483648
first_name*	string
title: First name
maxLength: 255
minLength: 1
last_name*	string
title: Last name
maxLength: 255
minLength: 1
country_codes	string
title: Country codes
maxLength: 7
x-nullable: true
phone*	string
title: Phone
maxLength: 30
minLength: 1
email*	string
title: Email
maxLength: 150
minLength: 1
national_id*	string
title: National id
maxLength: 20
minLength: 1
created_at*	string($date)
title: Created at
group*	integer
title: Group
 
}
Responses
Response content type

application/json
Code	Description
201	
Example Value
Model
OfferLetterGroupMember{
id	integer
title: ID
readOnly: true
member_id*	integer
title: Member id
maximum: 2147483647
minimum: -2147483648
first_name*	string
title: First name
maxLength: 255
minLength: 1
last_name*	string
title: Last name
maxLength: 255
minLength: 1
country_codes	string
title: Country codes
maxLength: 7
x-nullable: true
phone*	string
title: Phone
maxLength: 30
minLength: 1
email*	string
title: Email
maxLength: 150
minLength: 1
national_id*	string
title: National id
maxLength: 20
minLength: 1
created_at*	string($date)
title: Created at
group*	integer
title: Group
 
}

GET
/offer-letter-group-members/{id}/
offer-letter-group-members_read


PATCH
/offer-letter-group-members/{id}/
offer-letter-group-members_partial_update


DELETE
/offer-letter-group-members/{id}/
offer-letter-group-members_delete


GET
/offer-letter-groups/
offer-letter-groups_list


POST
/offer-letter-groups/
offer-letter-groups_create

Parameters
Try it out
Name	Description
data *
object
(body)
Example Value
Model
OfferLetterGroup{
group_name	string
title: Group name
maxLength: 100
x-nullable: true
group_code	string
title: Group code
maxLength: 7
x-nullable: true
group_phone	string
title: Group phone
maxLength: 20
x-nullable: true
group_email	string
title: Group email
maxLength: 150
x-nullable: true
Group_KRA_PIN	string
title: Group KRA PIN
maxLength: 30
x-nullable: true
Group_country	string
title: Group country
maxLength: 200
x-nullable: true
Group_city	string
title: Group city
maxLength: 30
x-nullable: true
offer_letter*	integer
title: Offer letter
 
}
Responses
Response content type

application/json
Code	Description
201	
Example Value
Model
OfferLetterGroup{
id	integer
title: ID
readOnly: true
group_name	string
title: Group name
maxLength: 100
x-nullable: true
group_code	string
title: Group code
maxLength: 7
x-nullable: true
group_phone	string
title: Group phone
maxLength: 20
x-nullable: true
group_email	string
title: Group email
maxLength: 150
x-nullable: true
Group_KRA_PIN	string
title: Group KRA PIN
maxLength: 30
x-nullable: true
Group_country	string
title: Group country
maxLength: 200
x-nullable: true
Group_city	string
title: Group city
maxLength: 30
x-nullable: true
created_at	string($date-time)
title: Created at
readOnly: true
offer_letter*	integer
title: Offer letter
 
}

GET
/offer-letter-groups/{id}/
offer-letter-groups_read


PATCH
/offer-letter-groups/{id}/
offer-letter-groups_partial_update


DELETE
/offer-letter-groups/{id}/
offer-letter-groups_delete


GET
/offer-letter-individuals/
offer-letter-individuals_list


POST
/offer-letter-individuals/
offer-letter-individuals_create

Parameters
Try it out
Name	Description
data *
object
(body)
Example Value
Model
OfferLetterIndividual{
first_name	string
title: First name
maxLength: 100
x-nullable: true
last_name	string
title: Last name
maxLength: 100
x-nullable: true
country_code	string
title: Country code
maxLength: 7
x-nullable: true
phone	string
title: Phone
maxLength: 20
x-nullable: true
email	string
title: Email
maxLength: 150
x-nullable: true
national_id	string
title: National id
maxLength: 50
x-nullable: true
country	string
title: Country
maxLength: 50
x-nullable: true
city	string
title: City
maxLength: 50
x-nullable: true
KRA_Pin	string
title: KRA Pin
maxLength: 30
x-nullable: true
DOB	string($date)
title: DOB
x-nullable: true
preferred_contact	Preferred contact{
 
x-nullable	true
}
offer_letter*	integer
title: Offer letter
 
}
Responses
Response content type

application/json
Code	Description
201	
Example Value
Model
OfferLetterIndividual{
id	integer
title: ID
readOnly: true
first_name	string
title: First name
maxLength: 100
x-nullable: true
last_name	string
title: Last name
maxLength: 100
x-nullable: true
country_code	string
title: Country code
maxLength: 7
x-nullable: true
phone	string
title: Phone
maxLength: 20
x-nullable: true
email	string
title: Email
maxLength: 150
x-nullable: true
national_id	string
title: National id
maxLength: 50
x-nullable: true
country	string
title: Country
maxLength: 50
x-nullable: true
city	string
title: City
maxLength: 50
x-nullable: true
KRA_Pin	string
title: KRA Pin
maxLength: 30
x-nullable: true
DOB	string($date)
title: DOB
x-nullable: true
preferred_contact	Preferred contact{
 
x-nullable	true
}
created_at	string($date-time)
title: Created at
readOnly: true
offer_letter*	integer
title: Offer letter
 
}

GET
/offer-letter-individuals/{id}/
offer-letter-individuals_read


PATCH
/offer-letter-individuals/{id}/
offer-letter-individuals_partial_update


DELETE
/offer-letter-individuals/{id}/
offer-letter-individuals_delete


GET
/offer-letter-next-of-kin/
offer-letter-next-of-kin_list


POST
/offer-letter-next-of-kin/
offer-letter-next-of-kin_create

Parameters
Try it out
Name	Description
data *
object
(body)
Example Value
Model
OfferLetterNextOfKin{
full_name*	string
title: Full name
maxLength: 150
minLength: 1
relationship*	string
title: Relationship
maxLength: 50
minLength: 1
country_code	string
title: Country code
maxLength: 7
x-nullable: true
phone*	string
title: Phone
maxLength: 20
minLength: 1
email	string
title: Email
maxLength: 150
x-nullable: true
offer_letter*	integer
title: Offer letter
individual	integer
title: Individual
x-nullable: true
partner	integer
title: Partner
x-nullable: true
 
}
Responses
Response content type

application/json
Code	Description
201	
Example Value
Model
OfferLetterNextOfKin{
id	integer
title: ID
readOnly: true
full_name*	string
title: Full name
maxLength: 150
minLength: 1
relationship*	string
title: Relationship
maxLength: 50
minLength: 1
country_code	string
title: Country code
maxLength: 7
x-nullable: true
phone*	string
title: Phone
maxLength: 20
minLength: 1
email	string
title: Email
maxLength: 150
x-nullable: true
created_at	string($date-time)
title: Created at
readOnly: true
offer_letter*	integer
title: Offer letter
individual	integer
title: Individual
x-nullable: true
partner	integer
title: Partner
x-nullable: true
 
}

GET
/offer-letter-next-of-kin/{id}/
offer-letter-next-of-kin_read


PATCH
/offer-letter-next-of-kin/{id}/
offer-letter-next-of-kin_partial_update


DELETE
/offer-letter-next-of-kin/{id}/
offer-letter-next-of-kin_delete


GET
/offer-letter-partners/
offer-letter-partners_list


POST
/offer-letter-partners/
offer-letter-partners_create

Parameters
Try it out
Name	Description
data *
object
(body)
Example Value
Model
OfferLetterPartner{
first_name	string
title: First name
maxLength: 100
x-nullable: true
last_name	string
title: Last name
maxLength: 100
x-nullable: true
country_code	string
title: Country code
maxLength: 7
x-nullable: true
phone	string
title: Phone
maxLength: 20
x-nullable: true
email	string
title: Email
maxLength: 150
x-nullable: true
national_id	string
title: National id
maxLength: 50
x-nullable: true
country	string
title: Country
maxLength: 50
x-nullable: true
city	string
title: City
maxLength: 50
x-nullable: true
preferred_contact	Preferred contact{
 
x-nullable	true
}
offer_letter*	integer
title: Offer letter
 
}
Responses
Response content type

application/json
Code	Description
201	
Example Value
Model
OfferLetterPartner{
id	integer
title: ID
readOnly: true
first_name	string
title: First name
maxLength: 100
x-nullable: true
last_name	string
title: Last name
maxLength: 100
x-nullable: true
country_code	string
title: Country code
maxLength: 7
x-nullable: true
phone	string
title: Phone
maxLength: 20
x-nullable: true
email	string
title: Email
maxLength: 150
x-nullable: true
national_id	string
title: National id
maxLength: 50
x-nullable: true
country	string
title: Country
maxLength: 50
x-nullable: true
city	string
title: City
maxLength: 50
x-nullable: true
preferred_contact	Preferred contact{
 
x-nullable	true
}
created_at	string($date-time)
title: Created at
readOnly: true
offer_letter*	integer
title: Offer letter
 
}

GET
/offer-letter-partners/{id}/
offer-letter-partners_read


PATCH
/offer-letter-partners/{id}/
offer-letter-partners_partial_update


DELETE
/offer-letter-partners/{id}/
offer-letter-partners_delete


GET
/offer-letter-payments/
offer-letter-payments_list


POST
/offer-letter-payments/
offer-letter-payments_create

Parameters
Try it out
Name	Description
data *
object
(body)
Example Value
Model
OfferLetterPaymentsPlan{
plot_no*	string
title: Plot no
maxLength: 10
minLength: 1
no_of_instalments*	integer
title: No of instalments
maximum: 2147483647
minimum: -2147483648
total_cash_price*	string($decimal)
title: Total cash price
monthly_installments*	string($decimal)
title: Monthly installments
deposit*	string($decimal)
title: Deposit
created_at	string($date)
title: Created at
x-nullable: true
offer_letter*	integer
title: Offer letter
 
}
Responses
Response content type

application/json
Code	Description
201	
Example Value
Model
OfferLetterPaymentsPlan{
id	integer
title: ID
readOnly: true
plot_no*	string
title: Plot no
maxLength: 10
minLength: 1
no_of_instalments*	integer
title: No of instalments
maximum: 2147483647
minimum: -2147483648
total_cash_price*	string($decimal)
title: Total cash price
monthly_installments*	string($decimal)
title: Monthly installments
deposit*	string($decimal)
title: Deposit
created_at	string($date)
title: Created at
x-nullable: true
offer_letter*	integer
title: Offer letter
 
}

GET
/offer-letter-payments/{id}/
offer-letter-payments_read


PATCH
/offer-letter-payments/{id}/
offer-letter-payments_partial_update


DELETE
/offer-letter-payments/{id}/
offer-letter-payments_delete


GET
/offer-letter-pricing/
offer-letter-pricing_list

Parameters
Try it out
Name	Description
page
integer
(query)
A page number within the paginated result set.

page
page_size
integer
(query)
Number of results to return per page.

page_size
Responses
Response content type

application/json
Code	Description
200	
Example Value
Model
{
count*	integer
next	string($uri)
x-nullable: true
previous	string($uri)
x-nullable: true
results*	[OfferLetterPricing{
id	ID[...]
Payment_Model	Payment Model[...]
Plot_Type	Plot Type[...]
view	View[...]
Deposit	Deposit[...]
Monthly_Interest	Monthly Interest[...]
_2M_IN	2M IN[...]
_3M_IN	3M IN[...]
_4M_IN	4M IN[...]
_5M_IN	5M IN[...]
_6M_IN	6M IN[...]
_7M_IN	7M IN[...]
_8M_IN	8M IN[...]
_9M_IN	9M IN[...]
_10M_IN	10M IN[...]
_11M_IN	11M IN[...]
_12M_IN	12M IN[...]
_15M_IN	15M IN[...]
_24M_IN	24M IN[...]
Project_No	Project No[...]
Size_Category	Size Category[...]
 
}]
 
}

POST
/offer-letter-pricing/
offer-letter-pricing_create


GET
/offer-letter-pricing/{id}/
offer-letter-pricing_read


PATCH
/offer-letter-pricing/{id}/
offer-letter-pricing_partial_update


DELETE
/offer-letter-pricing/{id}/
offer-letter-pricing_delete


GET
/offer-letter-reviews/
offer-letter-reviews_list


POST
/offer-letter-reviews/
offer-letter-reviews_create

Parameters
Try it out
Name	Description
data *
object
(body)
Example Value
Model
OfferLetterReview{
status	string
title: Status
Enum:
Array [ 3 ]
offer_letter*	integer
title: Offer letter
individual	integer
title: Individual
x-nullable: true
partner	integer
title: Partner
x-nullable: true
group	integer
title: Group
x-nullable: true
company	integer
title: Company
x-nullable: true
 
}
Responses
Response content type

application/json
Code	Description
201	
Example Value
Model
OfferLetterReview{
id	integer
title: ID
readOnly: true
status	string
title: Status
Enum:
[ Pending, Approved, Rejected ]
created_at	string($date-time)
title: Created at
readOnly: true
offer_letter*	integer
title: Offer letter
individual	integer
title: Individual
x-nullable: true
partner	integer
title: Partner
x-nullable: true
group	integer
title: Group
x-nullable: true
company	integer
title: Company
x-nullable: true
 
}

GET
/offer-letter-reviews/{id}/
offer-letter-reviews_read


PATCH
/offer-letter-reviews/{id}/
offer-letter-reviews_partial_update


DELETE
/offer-letter-reviews/{id}/
offer-letter-reviews_delete


GET
/offer-letter-terms/
offer-letter-terms_list

Parameters
Try it out
Name	Description
page
integer
(query)
A page number within the paginated result set.

page
page_size
integer
(query)
Number of results to return per page.

page_size
Responses
Response content type

application/json
Code	Description
200	
Example Value
Model
{
count*	integer
next	string($uri)
x-nullable: true
previous	string($uri)
x-nullable: true
results*	[OfferLetterTermsConditions{
id	integer
title: ID
readOnly: true
content*	string
title: Content
minLength: 1
acceptance_date	string($date-time)
title: Acceptance date
readOnly: true
offer_letter*	integer
title: Offer letter
 
}]
 
}

POST
/offer-letter-terms/
offer-letter-terms_create

Parameters
Try it out
Name	Description
data *
object
(body)
Example Value
Model
OfferLetterTermsConditions{
content*	string
title: Content
minLength: 1
offer_letter*	integer
title: Offer letter
 
}
Responses
Response content type

application/json
Code	Description
201	
Example Value
Model
OfferLetterTermsConditions{
id	integer
title: ID
readOnly: true
content*	string
title: Content
minLength: 1
acceptance_date	string($date-time)
title: Acceptance date
readOnly: true
offer_letter*	integer
title: Offer letter
 
}

GET
/offer-letter-terms/{id}/
offer-letter-terms_read


PATCH
/offer-letter-terms/{id}/
offer-letter-terms_partial_update


DELETE
/offer-letter-terms/{id}/
offer-letter-terms_delete


GET
/offer-letters/
offer-letters_list

Parameters
Try it out
Name	Description
search
string
(query)
A search term.

search
page
integer
(query)
A page number within the paginated result set.

page
page_size
integer
(query)
Number of results to return per page.

page_size
Responses
Response content type

application/json
Code	Description
200	
Example Value
Model
{
count*	integer
next	string($uri)
x-nullable: true
previous	string($uri)
x-nullable: true
results*	[OfferLetterMain{
id	ID[...]
step*	Step[...]
is_completed	Is completed[...]
customer_type	Customer type[...]
date	Date[...]
acc_payment_conf	Acc payment conf[...]
lead_file	Lead file[...]
plot_number	Plot number[...]
booking_id	Booking id[...]
 
}]
 
}

POST
/offer-letters/
offer-letters_create

Parameters
Try it out
Name	Description
data *
object
(body)
Example Value
Model
OfferLetterMain{
step*	string
title: Step
maxLength: 5
minLength: 1
is_completed	boolean
title: Is completed
customer_type	string
title: Customer type
maxLength: 20
x-nullable: true
acc_payment_conf	string
title: Acc payment conf
maxLength: 5
x-nullable: true
lead_file	string
title: Lead file
maxLength: 255
x-nullable: true
plot_number	string
title: Plot number
x-nullable: true
booking_id	string
title: Booking id
x-nullable: true
 
}
Responses
Response content type

application/json
Code	Description
201	
Example Value
Model
OfferLetterMain{
id	integer
title: ID
readOnly: true
step*	string
title: Step
maxLength: 5
minLength: 1
is_completed	boolean
title: Is completed
customer_type	string
title: Customer type
maxLength: 20
x-nullable: true
date	string($date-time)
title: Date
readOnly: true
acc_payment_conf	string
title: Acc payment conf
maxLength: 5
x-nullable: true
lead_file	string
title: Lead file
maxLength: 255
x-nullable: true
plot_number	string
title: Plot number
x-nullable: true
booking_id	string
title: Booking id
x-nullable: true
 
}

GET
/offer-letters/{id}/
offer-letters_read


PATCH
/offer-letters/{id}/
offer-letters_partial_update


DELETE
/offer-letters/{id}/
offer-letters_delete


GET
/plots-payment-options/
plots-payment-options_list

Returns plot payment options.

Parameters
Try it out
Name	Description
PLOT_NO *
string
(query)
Filter by plot number.

Default value : ALL

ALL
Responses
Response content type

application/json
Code	Description
200	
