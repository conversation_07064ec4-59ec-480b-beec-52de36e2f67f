import React, { useState } from "react";
import { Screen } from "@/app-components/layout/screen";
import { motion } from "framer-motion";
import { FileText, Download, BookAIcon } from "lucide-react";
import Tab1 from "@/components/custom/tabs/Tab1";
import PlotReport from "./Report info";
import MarketerReport from "./diasporareport";
import { Card6 } from "@/components/custom/cards/Card6";



const tabs = [
  {
    value: "PlotReport",
    title: "Plot Report",
    
    icon: <FileText className="w-5 h-5" />,
    content: <PlotReport />,
  },
  {
    value: "MarketerReport",
    title: "Marketer Report",
   
    icon: <FileText className="w-5 h-5" />,
    content: <MarketerReport />,
  },
  
];

const SellingReport = () => {
  const [activeTab, setActiveTab] = useState(tabs[0].value); // Track the active tab

  

  return (
    <Screen>
      <div>
      <h1 className="text-2xl sm:text-3xl font-bold text-gray-800 dark:text-white flex items-center gap-2">
              <FileText className="w-6 h-6 text-gray-600 dark:text-gray-300" aria-hidden="true" />
              REPORT</h1>
      </div>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
         <Card6 
         title={"Plot Reports"} 
         value={"1"} 
         icon={BookAIcon} change={""} 
         changeLabel={""}
         
         cardBg="bg-green-100">

         </Card6>
         <Card6 
         title={"marketer Reports"} 
          value={"3"}
         icon={BookAIcon} change={""} 
         changeLabel={""}
         
         cardBg="bg-purple-100">

         </Card6>
      </div>
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="space-y-2 sm:space-y-4">
        <Tab1
            tabs={tabs}
            defaultValue={activeTab}/>
        </div>
      </motion.div>
    </Screen>
  );
};

export default SellingReport;