import LazyModal, { TableColumn } from '@/components/reports/ReportsModal';
import { formatNumberWithCommas } from '@/utils/salesDataFormatter';
import { Badge } from '@/components/ui/badge';
import { useState } from 'react';

interface CustomerRating {
    customer_no: string;
    customer_name: string;
    customer_type: string;
    phone: string;
    primary_email: string;
    total_leadfiles: number;
    total_investment: number;
    customer_rating: string;
}

interface CustomerRatingReportProps {
    isModalOpen: boolean;
    setIsModalOpen: (state: boolean) => void;
}

const CustomerRatingReport = ({ isModalOpen, setIsModalOpen }: CustomerRatingReportProps) => {
    const [selectedCustomerNo, setSelectedCustomerNo] = useState<string>('ALL');

    // Define table columns with proper typing
    const columns: TableColumn<CustomerRating>[] = [
        {
            key: 'customer_no',
            title: 'Customer No',
            render: (value: string) => (
                <span className="font-medium text-blue-600">{value}</span>
            )
        },
        {
            key: 'customer_name',
            title: 'Customer Name',
            render: (value: string) => (
                <span className="font-medium">{value}</span>
            )
        },
        {
            key: 'customer_type',
            title: 'Type',
            render: (value: string) => (
                <Badge variant="outline" className="text-xs">
                    {value}
                </Badge>
            )
        },
        {
            key: 'customer_rating',
            title: 'Rating',
            render: (value: string) => {
                const getRatingColor = (rating: string) => {
                    switch (rating.toLowerCase()) {
                        case 'silver':
                            return 'bg-gray-100 text-gray-800 border-gray-300';
                        case 'bronze':
                            return 'bg-amber-100 text-amber-800 border-amber-300';
                        case 'sapphire':
                            return 'bg-blue-100 text-blue-800 border-blue-300';
                        case 'gold':
                            return 'bg-yellow-100 text-yellow-800 border-yellow-300';
                        case 'platinum':
                            return 'bg-purple-100 text-purple-800 border-purple-300';
                        case 'diamond':
                            return 'bg-indigo-100 text-indigo-800 border-indigo-300';
                        default:
                            return 'bg-gray-100 text-gray-800 border-gray-300';
                    }
                };

                return (
                    <Badge 
                        variant="outline" 
                        className={`font-semibold ${getRatingColor(value)}`}
                    >
                        {value}
                    </Badge>
                );
            }
        },
        {
            key: 'total_leadfiles',
            title: 'Lead Files',
            render: (value: number) => (
                <div className="flex items-center gap-2">
                    <span className="font-medium text-purple-600">{value}</span>
                    <Badge 
                        variant={value >= 2 ? "default" : "secondary"}
                        className={value >= 2 ? "bg-purple-100 text-purple-800 text-xs" : "text-xs"}
                    >
                        {value >= 2 ? 'Multiple' : 'Single'}
                    </Badge>
                </div>
            )
        },
        {
            key: 'total_investment',
            title: 'Total Investment',
            render: (value: number) => (
                <span className="font-bold text-green-600">
                    KES {formatNumberWithCommas(value.toString())}
                </span>
            )
        },
        {
            key: 'phone',
            title: 'Phone',
            render: (value: string) => (
                <span className="text-sm">{value || 'N/A'}</span>
            )
        },
        {
            key: 'primary_email',
            title: 'Email',
            render: (value: string) => (
                <span className="text-sm text-gray-600">{value}</span>
            )
        },
    ];

    const handleCloseModal = () => {
        setIsModalOpen(false);
    };

    return (
        <LazyModal<CustomerRating>
            isOpen={isModalOpen}
            onClose={handleCloseModal}
            title={
                <div className="flex items-center gap-3">
                    <span>Customer Rating Report</span>
                    <div className="flex items-center gap-2">
                        <label className="text-sm font-medium text-gray-600">Customer:</label>
                        <select
                            value={selectedCustomerNo}
                            onChange={(e) => setSelectedCustomerNo(e.target.value)}
                            className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                            <option value="ALL">All Customers</option>
                            {/* You can add specific customer numbers here if needed */}
                        </select>
                    </div>
                </div>
            }
            url="/customer-rating/"
            params={{ customer_no: selectedCustomerNo }}
            columns={columns}
            size="xl"
        />
    );
};

export default CustomerRatingReport;
