import { useState } from "react";
import { <PERSON><PERSON>Area } from "@/components/ui/scroll-area";
import { Button } from "@/components/ui/button";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import {
  ChevronDown,
  ChevronUp,
  Pencil,
  MapPin,
  Calendar,
  Briefcase,
  AtSign,
  Phone,
  Mail,
  MessageSquare,
  FileText,
  User,
  Calendar as CalendarIcon,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import CustomerAvatar from "./CustomerAvatar";

// Define the complete Customer interface
export interface Customer {
  id: string;
  name: string;
  avatar?: string;
  email: string;
  phone: string;
  position?: string;
  company?: string;
  customer_no: string;

  // Additional customer properties
  nationalId?: string;
  passportNo?: string;
  kraPin?: string;
  dob?: string;
  gender?: string;
  maritalStatus?: string;
  alternativePhone?: string;
  alternativeEmail?: string;
  address?: string;
  customerType?: "Individual" | "Group";
  countryOfResidence?: string;
  dateOfRegistration?: string;
  otp?: string;
  otpGeneratedAt?: string;
  leadSource?: number;
  marketer?: string;
  plotNumbers?: string;
}

interface CustomerInfoHeaderProps {
  customer: Customer;
  onEdit: () => void;
}

const CustomerInfoHeader = ({ customer, onEdit }: CustomerInfoHeaderProps) => {
  const [copied, setCopied] = useState<string | null>(null);
  const [expandedMobile, setExpandedMobile] = useState(false);

  const handleCopy = (text: string, type: string) => {
    if (!text) return;
    navigator.clipboard.writeText(text);
    setCopied(type);
    setTimeout(() => setCopied(null), 2000);
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A";
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
      });
    } catch (e) {
      return dateString;
    }
  };

  return (
    <ScrollArea className="h-full">
      <div className="p-4 space-y-4">
        {/* Mobile Header */}
        <div className="md:hidden">
          {/* Avatar and Basic Info */}
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-3">
              <div className="relative">
                <CustomerAvatar
                  name={customer.name}
                  image={customer.avatar}
                  size="lg"
                />
                <Button
                  variant="outline"
                  size="icon"
                  onClick={onEdit}
                  className="absolute -bottom-1 -right-1 h-6 w-6 rounded-full bg-white shadow-md border-2"
                >
                  <Pencil className="h-3 w-3" />
                  <span className="sr-only">Edit</span>
                </Button>
              </div>
              <div>
                <h2 className="text-base font-semibold">
                  {customer.name || "N/A"}
                </h2>
                <p className="text-xs text-muted-foreground">
                  Customer No: {customer.customer_no || "N/A"}
                </p>
                <p className="text-xs text-muted-foreground truncate max-w-[180px]">
                  {customer.email || "N/A"}
                </p>
              </div>
            </div>
          </div>

          {/* Quick Action Buttons - Now Direct Links */}

          {/* Quick Action Buttons - Real World Icons & Links */}

          <div className="flex justify-center gap-3 mb-3">
            {customer.email && (
              <a href={`mailto:${customer.email}`} className="block">
                <Button

                  variant="ghost"
                  size="icon"
                  className="h-10 w-10 hover:bg-red-50 dark:hover:bg-red-950 relative group"

                  title="Email"
                >
                  {/* Gmail-style Email Icon */}
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    className="h-4 w-4"
                  >
                    <path
                      d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
                      fill="#EA4335"
                      stroke="#EA4335"
                      strokeWidth="1"
                    />
                    <path
                      d="M22 6l-10 7L2 6"
                      stroke="white"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                  <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                    Email
                  </div>
                </Button>
              </a>
            )}
            {!customer.email && (
              <Button
                disabled

                variant="ghost"
                size="icon"
                className="h-10 w-10 relative group opacity-50"
                title="Email"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  className="h-4 w-4"
                >
                  <path
                    d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
                    fill="#9CA3AF"
                    stroke="#9CA3AF"
                    strokeWidth="1"
                  />
                  <path
                    d="M22 6l-10 7L2 6"
                    stroke="white"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>

                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                  Email
                </div>
              </Button>
            )}


            {/* WhatsApp with proper link and real icon */}
            {customer.phone && (
              <a
                href={`https://wa.me/${customer.phone.replace(/[^0-9]/g, '')}`}
                target="_blank"
                rel="noopener noreferrer"
                className="block"
              >
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-10 w-10 hover:bg-green-50 dark:hover:bg-green-950 relative group"
                  title="WhatsApp"
                >
                  {/* Real WhatsApp Icon */}
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="#25D366"
                    className="h-4 w-4"
                  >
                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 ************* 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.465 3.488" />
                  </svg>
                  <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                    WhatsApp
                  </div>
                </Button>
              </a>
            )}
            {!customer.phone && (
              <Button
                disabled
                variant="ghost"
                size="icon"
                className="h-10 w-10 relative group opacity-50"
                title="WhatsApp"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="#9CA3AF"
                  className="h-4 w-4"
                >
                  <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 ************* 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.465 3.488" />

                </svg>
                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                  WhatsApp
                </div>
              </Button>

            )}


            {customer.phone && (
              <a href={`tel:${customer.phone}`} className="block">
                <Button

                  variant="ghost"
                  size="icon"
                  className="h-10 w-10 hover:bg-blue-50 dark:hover:bg-blue-950 relative group"

                  title="Call"
                >
                  {/* Real Phone/Call Icon */}
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    className="h-4 w-4"
                  >
                    <path
                      d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                      fill="#1D4ED8"
                      stroke="#1D4ED8"
                      strokeWidth="1"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                  <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                    Call
                  </div>
                </Button>
              </a>
            )}
            {!customer.phone && (
              <Button
                disabled

                variant="ghost"
                size="icon"
                className="h-10 w-10 relative group opacity-50"
                title="Call"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  className="h-4 w-4"
                >
                  <path
                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                    fill="#9CA3AF"
                    stroke="#9CA3AF"
                    strokeWidth="1"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>

                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                  Call
                </div>
              </Button>
            )}


            {/* Generic Messaging - You can customize this based on your preferred messaging platform */}
            <a
              href="sms:"
              className="block"
            >
              <Button
                variant="ghost"
                size="icon"
                className="h-10 w-10 hover:bg-purple-50 dark:hover:bg-purple-950 relative group"
                title="Message"
              >
                {/* Real Message/SMS Icon */}
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  className="h-4 w-4"
                >
                  <path
                    d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"
                    fill="#8B5CF6"
                    stroke="#8B5CF6"
                    strokeWidth="1"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <circle cx="9" cy="10" r="1" fill="white" />
                  <circle cx="15" cy="10" r="1" fill="white" />
                  <circle cx="12" cy="10" r="1" fill="white" />
                </svg>

                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                  Message
                </div>
              </Button>
            </a>
          </div>

          {/* Contact Information */}
          <div className="border-t pt-3">
            <h3 className="font-bold text-sm mb-2">Contact Information</h3>
            <div className="space-y-2 text-sm">
              {customer.phone && (
                <div className="flex items-center">
                  <Phone className="h-4 w-4 mr-2 text-primary dark:text-primary-light" />
                  <span>{customer.phone}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="ml-1 h-5 w-5 p-0"
                    onClick={() => handleCopy(customer.phone || "", "phone")}
                  >
                    {copied === "phone" ? (
                      <span className="text-xs text-green-600">✓</span>
                    ) : (
                      <svg className="h-3 w-3" viewBox="0 0 24 24">
                        <rect
                          x="9"
                          y="9"
                          width="13"
                          height="13"
                          rx="2"
                          ry="2"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                        />
                        <path
                          d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                        />
                      </svg>
                    )}
                  </Button>
                </div>
              )}
              {customer.alternativePhone && (
                <div className="flex items-center">
                  <Phone className="h-4 w-4 mr-2 text-primary dark:text-primary-light" />
                  <span>{customer.alternativePhone}</span>
                  <Badge
                    variant="outline"
                    className="ml-2 text-xs text-primary dark:text-primary-light border-primary"
                  >
                    Alt
                  </Badge>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="ml-1 h-5 w-5 p-0"
                    onClick={() =>
                      handleCopy(customer.alternativePhone || "", "altPhone")
                    }
                  >
                    {copied === "altPhone" ? (
                      <span className="text-xs text-green-600">✓</span>
                    ) : (
                      <svg className="h-3 w-3" viewBox="0 0 24 24">
                        <rect
                          x="9"
                          y="9"
                          width="13"
                          height="13"
                          rx="2"
                          ry="2"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                        />
                        <path
                          d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                        />
                      </svg>
                    )}
                  </Button>
                </div>
              )}
              {customer.email && (
                <div className="flex items-center">
                  <AtSign className="h-4 w-4 mr-2 text-primary dark:text-primary-light" />
                  <span className="truncate">{customer.email}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="ml-1 h-5 w-5 p-0"
                    onClick={() => handleCopy(customer.email || "", "email")}
                  >
                    {copied === "email" ? (
                      <span className="text-xs text-green-600">✓</span>
                    ) : (
                      <svg className="h-3 w-3" viewBox="0 0 24 24">
                        <rect
                          x="9"
                          y="9"
                          width="13"
                          height="13"
                          rx="2"
                          ry="2"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                        />
                        <path
                          d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                        />
                      </svg>
                    )}
                  </Button>
                </div>
              )}
              {customer.alternativeEmail && (
                <div className="flex items-center">
                  <AtSign className="h-4 w-4 mr-2 text-primary dark:text-primary-light" />
                  <span className="truncate">{customer.alternativeEmail}</span>
                  <Badge
                    variant="outline"
                    className="ml-2 text-xs text-primary dark:text-primary-light border-primary"
                  >
                    Alt
                  </Badge>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="ml-1 h-5 w-5 p-0"
                    onClick={() =>
                      handleCopy(customer.alternativeEmail || "", "altEmail")
                    }
                  >
                    {copied === "altEmail" ? (
                      <span className="text-xs text-green-600">✓</span>
                    ) : (
                      <svg className="h-3 w-3" viewBox="0 0 24 24">
                        <rect
                          x="9"
                          y="9"
                          width="13"
                          height="13"
                          rx="2"
                          ry="2"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                        />
                        <path
                          d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                        />
                      </svg>
                    )}
                  </Button>
                </div>
              )}
              {customer.address && (
                <div className="flex items-center">
                  <MapPin className="h-4 w-4 mr-2 text-primary dark:text-primary-light" />
                  <span className="truncate">{customer.address}</span>
                </div>
              )}
            </div>
          </div>

          {/* Expandable Section Button */}
          <Collapsible defaultOpen={false} className="mt-3">
            <CollapsibleTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="text-xs w-full flex items-center justify-center mt-2 hover:bg-transparent hover:text-primary dark:hover:bg-transparent dark:hover:text-primary-light group data-[state=open]:bg-transparent data-[state=open]:text-primary dark:data-[state=open]:text-primary-light"
              >
                Additional Details
                <ChevronDown className="ml-1 h-3 w-3 transition-transform duration-200 group-data-[state=open]:rotate-180" />
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-4 text-sm border-t pt-3">
              <div className="space-y-2">
                <div className="grid grid-cols-2 gap-y-2">
                  <div>
                    <p className="text-xs text-muted-foreground">National ID</p>
                    <p>{customer.nationalId || "N/A"}</p>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground">Passport No</p>
                    <p>{customer.passportNo || "N/A"}</p>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground">KRA PIN</p>
                    <p>{customer.kraPin || "N/A"}</p>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground">Date of Birth</p>
                    <p>{formatDate(customer.dob)}</p>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground">Gender</p>
                    <p>{customer.gender || "N/A"}</p>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground">Marital Status</p>
                    <p>{customer.maritalStatus || "N/A"}</p>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground">Country</p>
                    <p>{customer.countryOfResidence || "N/A"}</p>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground">Registered</p>
                    <p>{formatDate(customer.dateOfRegistration)}</p>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground">Customer Type</p>
                    <p>{customer.customerType || "N/A"}</p>
                  </div>
                  <div>
                    <p className="text-xs text-muted-foreground">Marketer</p>
                    <p>{customer.marketer || "N/A"}</p>
                  </div>
                </div>
              </div>

              {(customer.position || customer.company) && (
                <div className="space-y-2 border-t pt-3">
                  <h3 className="font-bold text-sm">Company Information</h3>
                  <div className="space-y-2">
                    {customer.position && (
                      <div className="flex items-center">
                        <User className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span>{customer.position}</span>
                      </div>
                    )}
                    {customer.company && (
                      <div className="flex items-center">
                        <Briefcase className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span>{customer.company}</span>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </CollapsibleContent>
          </Collapsible>
        </div>

        {/* Desktop Header */}
        <div className="hidden md:block">
          {/* Avatar and Basic Info - Horizontal Layout */}
          <div className="flex items-start mb-6">
            <div className="relative mr-4">
              <CustomerAvatar
                name={customer.name}
                image={customer.avatar}
                size="lg"
              />
              <Button
                variant="outline"
                size="icon"
                onClick={onEdit}
                className="absolute -bottom-1 -right-1 h-6 w-6 rounded-full bg-white shadow-md border-2"
              >
                <Pencil className="h-3 w-3" />
                <span className="sr-only">Edit</span>
              </Button>
            </div>
            <div>
              <h2 className="text-lg font-semibold">
                {customer.name || "N/A"}
              </h2>
              <p className="text-sm text-muted-foreground">
                Customer No: {customer.customer_no || "N/A"}
              </p>
              <p className="text-sm text-muted-foreground">
                {customer.email || "N/A"}
              </p>
            </div>
          </div>

          {/* Quick Action Buttons - Now Direct Links Desktop View */}
          {/* Quick Action Buttons - Real World Icons & Links */}

          <div className="flex justify-center gap-3 mb-3">
            {customer.email && (
              <a href={`mailto:${customer.email}`} className="block">
                <Button

                  variant="ghost"
                  size="icon"
                  className="h-10 w-10 hover:bg-red-50 dark:hover:bg-red-950 relative group"

                  title="Email"
                >
                  {/* Gmail-style Email Icon */}
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    className="h-4 w-4"
                  >
                    <path
                      d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
                      fill="#EA4335"
                      stroke="#EA4335"
                      strokeWidth="1"
                    />
                    <path
                      d="M22 6l-10 7L2 6"
                      stroke="white"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                  <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                    Email
                  </div>
                </Button>
              </a>
            )}
            {!customer.email && (
              <Button
                disabled

                variant="ghost"
                size="icon"
                className="h-10 w-10 relative group opacity-50"
                title="Email"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  className="h-4 w-4"
                >
                  <path
                    d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
                    fill="#9CA3AF"
                    stroke="#9CA3AF"
                    strokeWidth="1"
                  />
                  <path
                    d="M22 6l-10 7L2 6"
                    stroke="white"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>

                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                  Email
                </div>
              </Button>
            )}


            {/* WhatsApp with proper link and real icon */}
            {customer.phone && (
              <a
                href={`https://wa.me/${customer.phone.replace(/[^0-9]/g, '')}`}
                target="_blank"
                rel="noopener noreferrer"
                className="block"
              >
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-10 w-10 hover:bg-green-50 dark:hover:bg-green-950 relative group"
                  title="WhatsApp"
                >
                  {/* Real WhatsApp Icon */}
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="#25D366"
                    className="h-4 w-4"
                  >
                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 ************* 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.465 3.488" />
                  </svg>
                  <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                    WhatsApp
                  </div>
                </Button>
              </a>
            )}
            {!customer.phone && (
              <Button
                disabled
                variant="ghost"
                size="icon"
                className="h-10 w-10 relative group opacity-50"
                title="WhatsApp"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="#9CA3AF"
                  className="h-4 w-4"
                >
                  <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 ************* 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.465 3.488" />

                </svg>
                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                  WhatsApp
                </div>
              </Button>

            )}


            {customer.phone && (
              <a href={`tel:${customer.phone}`} className="block">
                <Button

                  variant="ghost"
                  size="icon"
                  className="h-10 w-10 hover:bg-blue-50 dark:hover:bg-blue-950 relative group"

                  title="Call"
                >
                  {/* Real Phone/Call Icon */}
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    className="h-4 w-4"
                  >
                    <path
                      d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                      fill="#1D4ED8"
                      stroke="#1D4ED8"
                      strokeWidth="1"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                  <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                    Call
                  </div>
                </Button>
              </a>
            )}
            {!customer.phone && (
              <Button
                disabled

                variant="ghost"
                size="icon"
                className="h-10 w-10 relative group opacity-50"
                title="Call"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  className="h-4 w-4"
                >
                  <path
                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                    fill="#9CA3AF"
                    stroke="#9CA3AF"
                    strokeWidth="1"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>

                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                  Call
                </div>
              </Button>
            )}


            {/* Generic Messaging - You can customize this based on your preferred messaging platform */}
            <a
              href="sms:"
              className="block"
            >
              <Button
                variant="ghost"
                size="icon"
                className="h-10 w-10 hover:bg-purple-50 dark:hover:bg-purple-950 relative group"
                title="Message"
              >
                {/* Real Message/SMS Icon */}
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  className="h-4 w-4"
                >
                  <path
                    d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"
                    fill="#8B5CF6"
                    stroke="#8B5CF6"
                    strokeWidth="1"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <circle cx="9" cy="10" r="1" fill="white" />
                  <circle cx="15" cy="10" r="1" fill="white" />
                  <circle cx="12" cy="10" r="1" fill="white" />
                </svg>

                <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                  Message
                </div>
              </Button>
            </a>
          </div>

          <div className="space-y-6">
            {/* Contact Information */}
            <div className="space-y-3">
              <h3 className="font-bold text-sm">Contact Information</h3>
              <div className="grid grid-cols-1 gap-2">
                {customer.phone && (
                  <div className="flex items-center group">
                    <Phone className="h-4 w-4 mr-2 text-primary dark:text-primary-light" />
                    <span>{customer.phone}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="ml-1 h-5 w-5 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={() =>
                        handleCopy(customer.phone || "", "desktopPhone")
                      }
                    >
                      {copied === "desktopPhone" ? (
                        <span className="text-xs text-green-600">✓</span>
                      ) : (
                        <svg className="h-3 w-3" viewBox="0 0 24 24">
                          <rect
                            x="9"
                            y="9"
                            width="13"
                            height="13"
                            rx="2"
                            ry="2"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                          />
                          <path
                            d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                          />
                        </svg>
                      )}
                    </Button>
                  </div>
                )}
                {customer.alternativePhone && (
                  <div className="flex items-center group">
                    <Phone className="h-4 w-4 mr-2 text-primary dark:text-primary-light" />
                    <span>{customer.alternativePhone}</span>
                    <Badge
                      variant="outline"
                      className="ml-2 text-xs text-primary dark:text-primary-light border-primary"
                    >
                      Alt
                    </Badge>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="ml-1 h-5 w-5 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={() =>
                        handleCopy(
                          customer.alternativePhone || "",
                          "desktopAltPhone"
                        )
                      }
                    >
                      {copied === "desktopAltPhone" ? (
                        <span className="text-xs text-green-600">✓</span>
                      ) : (
                        <svg className="h-3 w-3" viewBox="0 0 24 24">
                          <rect
                            x="9"
                            y="9"
                            width="13"
                            height="13"
                            rx="2"
                            ry="2"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                          />
                          <path
                            d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                          />
                        </svg>
                      )}
                    </Button>
                  </div>
                )}
                {customer.email && (
                  <div className="flex items-center group">
                    <AtSign className="h-4 w-4 mr-2 text-primary dark:text-primary-light" />
                    <span>{customer.email}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="ml-1 h-5 w-5 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={() =>
                        handleCopy(customer.email || "", "desktopEmail")
                      }
                    >
                      {copied === "desktopEmail" ? (
                        <span className="text-xs text-green-600">✓</span>
                      ) : (
                        <svg className="h-3 w-3" viewBox="0 0 24 24">
                          <rect
                            x="9"
                            y="9"
                            width="13"
                            height="13"
                            rx="2"
                            ry="2"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                          />
                          <path
                            d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                          />
                        </svg>
                      )}
                    </Button>
                  </div>
                )}
                {customer.alternativeEmail && (
                  <div className="flex items-center group">
                    <AtSign className="h-4 w-4 mr-2 text-primary dark:text-primary-light" />
                    <span>{customer.alternativeEmail}</span>
                    <Badge
                      variant="outline"
                      className="ml-2 text-xs text-primary dark:text-primary-light border-primary"
                    >
                      Alt
                    </Badge>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="ml-1 h-5 w-5 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={() =>
                        handleCopy(
                          customer.alternativeEmail || "",
                          "desktopAltEmail"
                        )
                      }
                    >
                      {copied === "desktopAltEmail" ? (
                        <span className="text-xs text-green-600">✓</span>
                      ) : (
                        <svg className="h-3 w-3" viewBox="0 0 24 24">
                          <rect
                            x="9"
                            y="9"
                            width="13"
                            height="13"
                            rx="2"
                            ry="2"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                          />
                          <path
                            d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                          />
                        </svg>
                      )}
                    </Button>
                  </div>
                )}
                {customer.address && (
                  <div className="flex items-center">
                    <MapPin className="h-4 w-4 mr-2 text-primary dark:text-primary-light" />
                    <span>{customer.address}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Additional Details */}
            <div className="space-y-3">
              <h3 className="font-bold text-sm">Additional Details</h3>
              <div className="grid grid-cols-2 gap-x-4 gap-y-3 text-sm">
                <div>

                  <p className="text-xs text-muted-foreground font-bold">National ID</p>
                  <p>{customer.nationalId || "N/A"}</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground font-bold">Passport No</p>
                  <p>{customer.passportNo || "N/A"}</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground font-bold">KRA PIN</p>
                  <p>{customer.kraPin || "N/A"}</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground font-bold">Date of Birth</p>
                  <p>{formatDate(customer.dob)}</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground font-bold">Gender</p>
                  <p>{customer.gender || "N/A"}</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground font-bold">Marital Status</p>
                  <p>{customer.maritalStatus || "N/A"}</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground font-bold">Country of Residence</p>
                  <p>{customer.countryOfResidence || "N/A"}</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground font-bold">Date of Registration</p>
                  <p>{formatDate(customer.dateOfRegistration)}</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground font-bold">Customer Type</p>
                  <p>{customer.customerType || "N/A"}</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground font-bold">Marketer</p>

                  <p>{customer.marketer || "N/A"}</p>
                </div>
              </div>
            </div>

            {/* Company Information */}
            {(customer.position || customer.company) && (
              <div className="space-y-3">
                <h3 className="font-bold text-sm">Company Information</h3>
                <div className="space-y-2">
                  {customer.position && (
                    <div className="flex items-center">
                      <User className="h-4 w-4 mr-2 text-muted-foreground" />
                      <span>{customer.position}</span>
                    </div>
                  )}
                  {customer.company && (
                    <div className="flex items-center">
                      <Briefcase className="h-4 w-4 mr-2 text-muted-foreground" />
                      <span>{customer.company}</span>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </ScrollArea>
  );
};

export default CustomerInfoHeader;
