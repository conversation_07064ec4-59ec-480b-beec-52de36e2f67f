import { Screen } from "@/app-components/layout/screen"
import InputWithTags from "@/components/custom/forms/InputWithTags"
import { Button } from "@/components/ui/button";
import { Search } from "lucide-react";
import { useState } from "react";

const PaymentPlanChecker = () => {
    const [tags, setTags] = useState<string[]>([]);

    const submitHandler = () => { }

    const data = [
        {
            plotNumber: 'FG196',
            project: '   Furaha Gardens',
            size: '1/8th Acre',
            plotType: 'Residential',
            deposit: '100,000',
            status: 'sold',
            pricings: [
                {
                    title: '30 days cash price @ ksh 545,000',
                    description: ''
                },
                {
                    title: '3 months @ ksh 558,350',
                    description: 'Deposit of ksh 100,000 + 3 installment of ksh 152,783 each'
                },
                {
                    title: '6 months @ ksh 571,700',
                    description: 'Deposit of ksh 100,000 + 6 installment of ksh 78,617 each'
                },
                {
                    title: '12 months @ ksh 598,400',
                    description: 'Deposit of ksh 100,000 + 12 installment of ksh 41,533 each'
                },
            ]
        },
        {
            plotNumber: 'FG196',
            project: '   Furaha Gardens',
            size: '1/8th Acre',
            plotType: 'Residential',
            deposit: '100,000',
            status: 'available',
            pricings: [
                {
                    title: '30 days cash price @ ksh 545,000',
                    description: ''
                },
                {
                    title: '3 months @ ksh 558,350',
                    description: 'Deposit of ksh 100,000 + 3 installment of ksh 152,783 each'
                },
                {
                    title: '6 months @ ksh 571,700',
                    description: 'Deposit of ksh 100,000 + 6 installment of ksh 78,617 each'
                },
                {
                    title: '12 months @ ksh 598,400',
                    description: 'Deposit of ksh 100,000 + 12 installment of ksh 41,533 each'
                },
            ]
        }
    ]

    return (
        <Screen>
            <div className=" !m-0 min-h-screen border rounded">
                <div className=' border-b px-3 py-4 '>
                    <h2 className=' font-bold text-lg'>Payment plan checker</h2>
                </div>
                <div className='p-2 w-full'>
                    <form onSubmit={submitHandler}>
                        <p className='text-[15px] mb-1 mt-1 '>Enter plot number</p>
                        <div className='relative flex items-center'>
                            <Search className='absolute ml-3' />
                            <InputWithTags tags={tags} setTags={setTags} inputClassName='text-sm' containerClassName='w-full focus-within:ring-0 pl-11' />
                        </div>
                    </form>
                    <div className="grid grid-cols-2 gap-3 py-4">
                        {
                            data.map((item, index) => {
                                return  <Item data={item} key={index}/>
                            })
                        }
                    </div>
                </div>
            </div>
        </Screen>
    )
}

export default PaymentPlanChecker

interface Item {
    data: any
}

function Item({ data }: Item) {
    return data.status == 'sold'
        ? <div className='px-3 py-10 border rounded'>
            <p className='text-sm text-center text-red-600'><strong>Plot Number {data?.plotNumber} is sold</strong></p>
        </div>
        : <div className='px-3 py-10 border rounded'>
            <p className='text-md font-bold text-center'>Plot Number: {data.plotNumber}</p>
            <div className='h-1'></div>
            <p className="text-sm text-center">{data.project}</p>
            <p className="text-sm text-center">{data.size} - {data.plotType} - ksh {data.deposit}</p>
            <div className='h-6'></div>
            <div className='flex flex-col gap-2'>
                {data?.pricings?.map((price: any,index:any) => {
                    return <p className='text-sm' key={index}>{price.title} {price.description && '(' + price.description + ')'}</p>
                })}
            </div>
        </div>
}

