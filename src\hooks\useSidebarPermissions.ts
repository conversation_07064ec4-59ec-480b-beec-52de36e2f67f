import { useAuthHook } from "@/utils/useAuthHook";
import { useGetDepartmentsQuery } from "@/redux/slices/user";
import { useGetTeams2TeamsPermissionsQuery, useGetUser2UserPermissionsQuery } from "@/redux/slices/permissions";
import { useMemo } from "react";

// Sidebar permission codes mapping
export const SIDEBAR_PERMISSIONS = {
  MAIN: 111,
  PERFORMANCE: 112,
  TEAMS: 113,
  REPORTS: 114,
  ANALYTICS: 115,
  SERVICES: 116,
  ADMIN: 117,
} as const;

// Logistics module permission codes
export const LOGISTICS_PERMISSIONS = {
  BOOK_VISIT: 201,
  COMPLETE_TRIPS: 202,
  CREATE_VEHICLE_REQUEST: 203,
  CREATE_SPECIAL_ASSIGNMENT: 204,
  ACCESS_LOGISTICS_DASHBOARD: 205,
  ACCESS_LOGISTICS_STATISTICS: 206,
  ACCESS_CLIENTS: 207,
  ACCESS_DRIVERS: 208,
  ACCESS_VEHICLES: 209,
  ACCESS_LOGISTICS_REPORTS: 210,
  ACCESS_SITEVISIT_REPORTS: 211,
} as const;

// Sales module permission codes
export const SALES_PERMISSIONS = {
  VIEW_SALES_HQ: 1001,
  VIEW_SALES_KAREN: 1002,
  VIEW_SALES_ALL_OFFICES: 1003,
  VIEW_SALES_OWN_MARKETER: 1004,
  VIEW_SALES_ALL_MARKETERS: 1005,
  VIEW_SALES_DIASPORA_TEAM: 1006,
  VIEW_SALES_DIGITAL_TEAM: 1007,
  VIEW_SALES_TELEMARKETING_TEAM: 1008,
  VIEW_SALES_OTHER_TEAM: 1009,
  VIEW_SALES_ALL_TEAMS: 1010,
  VIEW_SALES_DIASPORA_REGION: 1011,
  VIEW_SALES_ALL_DIASPORA_REGIONS: 1012,
} as const;

// Customer module permission codes
export const CUSTOMER_PERMISSIONS = {
  VIEW_CUSTOMER_HQ: 2001,
  VIEW_CUSTOMER_KAREN: 2002,
  VIEW_CUSTOMER_ALL_OFFICES: 2003,
  VIEW_CUSTOMER_OWN_MARKETER: 2004,
  VIEW_CUSTOMER_ALL_MARKETERS: 2005,
  VIEW_CUSTOMER_DIASPORA_TEAM: 2006,
  VIEW_CUSTOMER_DIGITAL_TEAM: 2007,
  VIEW_CUSTOMER_TELEMARKETING_TEAM: 2008,
  VIEW_CUSTOMER_OTHER_TEAM: 2009,
  VIEW_CUSTOMER_ALL_TEAMS: 2010,
  VIEW_CUSTOMER_DIASPORA_REGION: 2011,
  VIEW_CUSTOMER_ALL_DIASPORA_REGIONS: 2012,
} as const;

// Prospect module permission codes
export const PROSPECT_PERMISSIONS = {
  VIEW_PROSPECT_HQ: 3001,
  VIEW_PROSPECT_KAREN: 3002,
  VIEW_PROSPECT_ALL_OFFICES: 3003,
  VIEW_PROSPECT_OWN_MARKETER: 3004,
  VIEW_PROSPECT_ALL_MARKETERS: 3005,
  VIEW_PROSPECT_DIASPORA_TEAM: 3006,
  VIEW_PROSPECT_DIGITAL_TEAM: 3007,
  VIEW_PROSPECT_TELEMARKETING_TEAM: 3008,
  VIEW_PROSPECT_OTHER_TEAM: 3009,
  VIEW_PROSPECT_ALL_TEAMS: 3010,
  VIEW_PROSPECT_DIASPORA_REGION: 3011,
  VIEW_PROSPECT_ALL_DIASPORA_REGIONS: 3012,
} as const;

// Inventory module permission codes
export const INVENTORY_PERMISSIONS = {
  VIEW_INVENTORY_FULL_ACCESS: 7112,
  VIEW_INVENTORY_MARKETER: 7113,
  VIEW_INVENTORY_GM_HQ: 7114,
  VIEW_INVENTORY_GM_KAREN: 7115,
  VIEW_INVENTORY_ACCOUNTS: 7116,
  VIEW_INVENTORY_DIASPORA: 7117,
  VIEW_INVENTORY_REPORTS: 7118,
  VIEW_INVENTORY_PRICING: 7119,
  VIEW_INVENTORY_MPESA_TRANSACTIONS: 7120,
  VIEW_INVENTORY_LOGS: 7121,
  VIEW_INVENTORY_MAPS: 7122,
} as const;

export type SidebarPermissionKey = keyof typeof SIDEBAR_PERMISSIONS;
export type LogisticsPermissionKey = keyof typeof LOGISTICS_PERMISSIONS;
export type SalesPermissionKey = keyof typeof SALES_PERMISSIONS;
export type CustomerPermissionKey = keyof typeof CUSTOMER_PERMISSIONS;
export type ProspectPermissionKey = keyof typeof PROSPECT_PERMISSIONS;

interface DepartmentPermission {
  id: number;
  permission: number | string;
  permission_name: string;
  team: number; // departments are treated as teams in the permission system
}

interface TeamPermission {
  id: number;
  permission: number | string;
  permission_name: string;
  team: number;
}

export const useSidebarPermissions = () => {
  const { user_details } = useAuthHook();

  // Get all departments to find the user's department ID
  const { data: departments = [], isLoading: loadingDepartments } = useGetDepartmentsQuery({
    page: 1,
    page_size: 1000,
  });

  // Find user's department ID based on department name
  const userDepartmentId = useMemo(() => {
    if (!user_details?.department || !departments.length) {
      return null;
    }

    const userDept = departments.find(
      (dept: any) => dept.dp_name === user_details.department
    );
    return userDept?.dp_id || null;
  }, [user_details?.department, departments]);

  // Find user's team ID based on team name
  const userTeamId = useMemo(() => {
    if (!user_details?.team || !departments.length) {
      return null;
    }

    // For now, we'll use the team field directly if it's a number
    // or try to find it in departments if it's a string
    if (typeof user_details.team === 'number') {
      return user_details.team;
    }

    // If team is a string, try to find matching department
    const userTeam = departments.find(
      (dept: any) => dept.dp_name === user_details.team
    );
    return userTeam?.dp_id || null;
  }, [user_details?.team, departments]);

  // Fetch department permissions for the user's department
  // Note: Based on the existing DepartmentPermissionsModal, departments use the teams endpoint
  const {
    data: departmentPermissions = [],
    isLoading: loadingDepartmentPermissions
  } = useGetTeams2TeamsPermissionsQuery(
    {
      page: 1,
      page_size: 1000,
      team: userDepartmentId, // departments are treated as teams in the permission system
    },
    {
      skip: !userDepartmentId, // Skip query if no department ID
    }
  );

  // Fetch team permissions for the user's team (if different from department)
  const {
    data: teamPermissions = [],
    isLoading: loadingTeamPermissions
  } = useGetTeams2TeamsPermissionsQuery(
    {
      page: 1,
      page_size: 1000,
      team: userTeamId,
    },
    {
      skip: !userTeamId || userTeamId === userDepartmentId, // Skip if no team ID or same as department
    }
  );

  // Fetch user-specific permissions from the API
  const {
    data: userSpecificPermissions = [],
    isLoading: loadingUserPermissions
  } = useGetUser2UserPermissionsQuery(
    {
      page: 1,
      page_size: 1000,
      user: user_details?.employee_no || '',
    },
    {
      skip: !user_details?.employee_no, // Skip if no employee number
    }
  );



  // Create a set of permission codes for quick lookup
  // Team permissions take precedence over user permissions
  const userPermissionCodes = useMemo(() => {
    const codes = new Set<number>();

    // // console.log('=== PERMISSION DEBUG ===');
    // // console.log('User details:', user_details);
    // // console.log('Team permissions:', teamPermissions);
    // // console.log('Department permissions:', departmentPermissions);
    // // console.log('User specific permissions:', userSpecificPermissions);

    // First, add permissions from team permissions (highest priority)
    teamPermissions.forEach((perm: TeamPermission) => {
      const permCode = Number(perm.permission);
      // // console.log('Adding team permission:', permCode);
      codes.add(permCode);
    });

    // Then, add permissions from department permissions
    departmentPermissions.forEach((perm: DepartmentPermission) => {
      const permCode = Number(perm.permission);
      // // console.log('Adding department permission:', permCode);
      codes.add(permCode);
    });

    // Add permissions from user-specific API permissions
    userSpecificPermissions.forEach((perm: any) => {
      const permCode = Number(perm.permission);
      // // console.log('Adding user-specific permission:', permCode);
      codes.add(permCode);
    });

    // Finally, add permissions from user_permissions array in user_details (lowest priority)
    // But only if they're not already blocked by team/department permissions
    if (user_details?.user_permissions && Array.isArray(user_details.user_permissions)) {
      // // console.log('Processing user_permissions from login:', user_details.user_permissions);
      user_details.user_permissions.forEach((perm: any) => {
        let permCode: number | null = null;

        if (typeof perm === 'number') {
          permCode = perm;
        } else if (typeof perm === 'string' && !isNaN(Number(perm))) {
          permCode = Number(perm);
        } else if (perm?.permission_id) {
          permCode = Number(perm.permission_id);
        } else if (perm?.id) {
          permCode = Number(perm.id);
        } else if (perm?.permission) {
          permCode = Number(perm.permission);
        }

        if (permCode !== null && !isNaN(permCode)) {
          // // console.log('Adding login permission:', permCode);
          codes.add(permCode);
        }
      });
    }

    // If no permissions found, only add Main permission as fallback
    if (codes.size === 0) {
      // // console.log('No permissions found, adding fallback Main permission (111)');
      codes.add(111); // Only Main section as fallback
    }

    // // console.log('Final permission codes:', Array.from(codes));
    // // console.log('=== END PERMISSION DEBUG ===');

    return codes;
  }, [user_details?.user_permissions, departmentPermissions, teamPermissions, userSpecificPermissions]);

  // Function to check if user has a specific permission
  const hasPermission = (permissionCode: number): boolean => {
    return userPermissionCodes.has(permissionCode);
  };

  // Function to check sidebar section access
  const hasSidebarAccess = (section: SidebarPermissionKey): boolean => {
    const permissionCode = SIDEBAR_PERMISSIONS[section];
    return hasPermission(permissionCode);
  };

  // Function to check logistics permission access
  const hasLogisticsPermission = (permission: LogisticsPermissionKey): boolean => {
    const permissionCode = LOGISTICS_PERMISSIONS[permission];
    return hasPermission(permissionCode);
  };

  // Function to check sales permission access
  const hasSalesPermission = (permission: SalesPermissionKey): boolean => {
    const permissionCode = SALES_PERMISSIONS[permission];
    return hasPermission(permissionCode);
  };

  // Function to check customer permission access
  const hasCustomerPermission = (permission: CustomerPermissionKey): boolean => {
    const permissionCode = CUSTOMER_PERMISSIONS[permission];
    return hasPermission(permissionCode);
  };

  // Function to check prospect permission access
  const hasProspectPermission = (permission: ProspectPermissionKey): boolean => {
    const permissionCode = PROSPECT_PERMISSIONS[permission];
    return hasPermission(permissionCode);
  };

  // Function to check inventory permission access
  const hasInventoryPermission = (permission: keyof typeof INVENTORY_PERMISSIONS): boolean => {
    const permissionCode = INVENTORY_PERMISSIONS[permission];
    return hasPermission(permissionCode);
  };

  // Get all accessible sidebar sections
  const accessibleSections = useMemo(() => {
    const sections: SidebarPermissionKey[] = [];

    Object.entries(SIDEBAR_PERMISSIONS).forEach(([key, code]) => {
      if (hasPermission(code)) {
        sections.push(key as SidebarPermissionKey);
      }
    });

    return sections;
  }, [userPermissionCodes]);

  return {
    hasPermission,
    hasSidebarAccess,
    hasLogisticsPermission,
    hasSalesPermission,
    hasCustomerPermission,
    hasProspectPermission,
    hasInventoryPermission,
    accessibleSections,
    userPermissionCodes: Array.from(userPermissionCodes),
    isLoading: loadingDepartmentPermissions || loadingTeamPermissions || loadingDepartments || loadingUserPermissions,
    userDepartmentId,
    userTeamId,
  };
};
