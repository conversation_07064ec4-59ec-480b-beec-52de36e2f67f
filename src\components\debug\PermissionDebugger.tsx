import React from "react";
import { useSidebarPermissions } from "@/hooks/useSidebarPermissions";
import { useAuthHook } from "@/utils/useAuthHook";
import { PERMISSION_DESCRIPTIONS } from "@/utils/permissionChecker";

/**
 * Debug component to display current user's permissions and sidebar access
 * This component can be temporarily added to any page to debug permission issues
 */
export const PermissionDebugger: React.FC = () => {
  const { user_details } = useAuthHook();
  const {
    hasPermission,
    hasSidebarAccess,
    accessibleSections,
    userPermissionCodes,
    isLoading,
    userDepartmentId,
  } = useSidebarPermissions();

  if (isLoading) {
    return (
      <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg max-w-md">
        <h3 className="font-bold text-sm mb-2">Permission Debugger</h3>
        <p className="text-sm text-gray-600">Loading permissions...</p>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg max-w-md max-h-96 overflow-y-auto">
      <h3 className="font-bold text-sm mb-2">Permission Debugger</h3>
      
      <div className="space-y-2 text-xs">
        <div>
          <strong>User:</strong> {user_details?.fullnames || "Unknown"}
        </div>
        
        <div>
          <strong>Department:</strong> {user_details?.department || "Unknown"}
        </div>
        
        <div>
          <strong>Department ID:</strong> {userDepartmentId || "Not found"}
        </div>
        
        <div>
          <strong>Permission Codes:</strong>
          <div className="ml-2">
            {userPermissionCodes.length > 0 ? (
              userPermissionCodes.map(code => (
                <div key={code} className="text-green-600">
                  {code} - {PERMISSION_DESCRIPTIONS[code as keyof typeof PERMISSION_DESCRIPTIONS] || "Unknown"}
                </div>
              ))
            ) : (
              <div className="text-red-600">No permissions found</div>
            )}
          </div>
        </div>
        
        <div>
          <strong>Sidebar Access:</strong>
          <div className="ml-2">
            <div className={`${hasSidebarAccess("MAIN") ? "text-green-600" : "text-red-600"}`}>
              Main (111): {hasSidebarAccess("MAIN") ? "✓" : "✗"}
            </div>
            <div className={`${hasSidebarAccess("PERFORMANCE") ? "text-green-600" : "text-red-600"}`}>
              Performance (112): {hasSidebarAccess("PERFORMANCE") ? "✓" : "✗"}
            </div>
            <div className={`${hasSidebarAccess("TEAMS") ? "text-green-600" : "text-red-600"}`}>
              Teams (113): {hasSidebarAccess("TEAMS") ? "✓" : "✗"}
            </div>
            <div className={`${hasSidebarAccess("REPORTS") ? "text-green-600" : "text-red-600"}`}>
              Reports (114): {hasSidebarAccess("REPORTS") ? "✓" : "✗"}
            </div>
            <div className={`${hasSidebarAccess("ANALYTICS") ? "text-green-600" : "text-red-600"}`}>
              Analytics (115): {hasSidebarAccess("ANALYTICS") ? "✓" : "✗"}
            </div>
            <div className={`${hasSidebarAccess("SERVICES") ? "text-green-600" : "text-red-600"}`}>
              Services (116): {hasSidebarAccess("SERVICES") ? "✓" : "✗"}
            </div>
            <div className={`${hasSidebarAccess("ADMIN") ? "text-green-600" : "text-red-600"}`}>
              Admin (117): {hasSidebarAccess("ADMIN") ? "✓" : "✗"}
            </div>
          </div>
        </div>
        
        <div>
          <strong>Accessible Sections:</strong>
          <div className="ml-2">
            {accessibleSections.length > 0 ? (
              accessibleSections.map(section => (
                <div key={section} className="text-green-600">
                  {section}
                </div>
              ))
            ) : (
              <div className="text-red-600">No accessible sections</div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PermissionDebugger;
