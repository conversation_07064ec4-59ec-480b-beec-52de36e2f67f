import React, { useState, useRef, useEffect } from "react";

export interface DropdownButtonProps {
  label: string;
  items: {
    label: string;
    onClick: () => void;
    icon?: React.ReactNode;
    disabled?: boolean;
  }[];
  variant?:
    | "default"
    | "primary"
    | "secondary"
    | "destructive"
    | "outline"
    | "ghost";
  size?: "sm" | "md" | "lg";
  className?: string;
  dropdownClassName?: string;
  icon?: React.ReactNode;
  disabled?: boolean;
  align?: "left" | "right";
  fullWidth?: boolean;
}

const DropdownButton = ({
  label,
  items,
  variant = "default",
  size = "md",
  className = "",
  dropdownClassName = "",
  icon,
  disabled = false,
  align = "left",
  fullWidth = false,
}: DropdownButtonProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const toggleDropdown = () => {
    if (!disabled) setIsOpen(!isOpen);
  };

  const closeDropdown = () => {
    setIsOpen(false);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        closeDropdown();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Styles based on index.css variables
  const baseStyles =
    "inline-flex items-center justify-center font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-[hsl(var(--ring))] focus:ring-offset-2";

  const sizeStyles = {
    sm: "text-xs px-2.5 py-1.5 rounded",
    md: "text-sm px-4 py-2 rounded-md",
    lg: "text-base px-5 py-2.5 rounded-md",
  };

  const variantStyles = {
    default:
      "bg-[hsl(var(--background))] text-[hsl(var(--foreground))] border border-[hsl(var(--border))] hover:bg-[hsl(var(--muted))]",
    primary:
      "bg-[hsl(var(--primary))] text-[hsl(var(--primary-foreground))] hover:bg-[hsl(var(--primary))] hover:opacity-90",
    secondary:
      "bg-[hsl(var(--secondary))] text-[hsl(var(--secondary-foreground))] hover:bg-[hsl(var(--secondary))] hover:opacity-90",
    destructive:
      "bg-[hsl(var(--destructive))] text-[hsl(var(--destructive-foreground))] hover:bg-[hsl(var(--destructive))] hover:opacity-90",
    outline:
      "border border-[hsl(var(--border))] text-[hsl(var(--foreground))] bg-transparent hover:bg-[hsl(var(--muted))]",
    ghost:
      "bg-transparent text-[hsl(var(--foreground))] hover:bg-[hsl(var(--muted))]",
  };

  const buttonStyles = `
    ${baseStyles}
    ${sizeStyles[size]}
    ${variantStyles[variant]}
    ${disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}
    ${fullWidth ? "w-full" : ""}
    ${className}
  `;

  const dropdownStyles = `
    absolute z-10 min-w-[12rem] py-1 mt-1 bg-[hsl(var(--background))]  rounded-md shadow-lg border border-[hsl(var(--border))]
    ${align === "right" ? "right-0" : "left-0"}
    ${dropdownClassName}
  `;

  return (
    <div
      className={`relative inline-block ${fullWidth ? "w-full" : ""}`}
      ref={dropdownRef}
    >
      <button
        className={buttonStyles}
        onClick={toggleDropdown}
        disabled={disabled}
        type="button"
      >
        {icon && <span className="mr-2">{icon}</span>}
        <span>{label}</span>
        <svg
          className={`ml-2 h-4 w-4 transition-transform ${
            isOpen ? "rotate-180" : ""
          }`}
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
            clipRule="evenodd"
          />
        </svg>
      </button>

      {isOpen && (
        <div className={dropdownStyles}>
          <ul className="py-1">
            {items.map((item, index) => (
              <li key={index}>
                <button
                  className={`w-full text-left block px-4 py-2 text-sm ${
                    item.disabled
                      ? "text-[hsl(var(--muted-foreground))] cursor-not-allowed"
                      : "text-[hsl(var(--foreground))] hover:bg-[hsl(var(--muted))]"
                  }`}
                  onClick={() => {
                    if (!item.disabled) {
                      item.onClick();
                      closeDropdown();
                    }
                  }}
                  disabled={item.disabled}
                >
                  <div className="flex items-center">
                    {item.icon && <span className="mr-2">{item.icon}</span>}
                    {item.label}
                  </div>
                </button>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default DropdownButton;
