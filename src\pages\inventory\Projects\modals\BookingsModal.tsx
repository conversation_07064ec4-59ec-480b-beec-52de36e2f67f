import BaseModal from "@/components/custom/modals/BaseModal";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useEffect, useState } from "react";
import { SingleDatePicker } from "@/components/custom/datepicker/DatePicker";
import { IconSwitch } from "@/components/custom/switch/IconSwitch";
import InputWithTags from "@/components/custom/forms/InputWithTags";
import { Label } from "@/components/ui/label";
import {
  useBookPlotMutation,
  useGetCustomerQuery,
  useLazyGetCustomerQuery,
} from "@/redux/slices/projects";
import { toast } from "sonner";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { FileText, Image } from "lucide-react";
import CustomSelectField from "@/components/CustomSelectField";
import { useLazyGetPropectsQuery } from "@/redux/slices/propects";

type Props = {
  setSbModal: (e: boolean) => void;
  sbModal: boolean;
  bookingType: string;
  title: string;
  b_plot?: string;
};

const formSchema = z.object({
  transportation_transaction_id: z.string().min(0).optional(),
  transaction_id: z.string().min(0).optional(),
  description: z.string().min(1).optional(),
});

const BookingsModal = ({
  setSbModal,
  sbModal,
  bookingType,
  title,
  b_plot,
}: Props) => {
  const [bookSite, { isLoading: booking }] = useBookPlotMutation();
  const [fetchCustomers, { data: customers, isLoading: cus_loading }] =
    useLazyGetCustomerQuery();
  const [fetchProspects, { data: prospects, isLoading: pr_loading }] =
    useLazyGetPropectsQuery();

  const [file, setFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isPdf, setIsPdf] = useState(false);
  const [amount, setAmount] = useState("");
  const [clientType, setClientType] = useState("");
  const [customer, setCustomer] = useState("");
  const [lead, setLead] = useState("");

  const handleFileChange = (e: any) => {
    const selected = e.target.files[0];
    if (selected) {
      setFile(selected);
      setIsPdf(selected.type === "application/pdf");

      if (selected.type.startsWith("image/")) {
        setPreviewUrl(URL.createObjectURL(selected));
      } else {
        setPreviewUrl(null);
      }
    } else {
      setFile(null);
      setPreviewUrl(null);
      setIsPdf(false);
    }
  };

  const [tags, setTags] = useState<string[]>([]);
  const [expected_payment_date, setExpectedDate] = useState<Date | undefined>(
    undefined
  );

  const [checked48Hr, setChecked48Hr] = useState(false);
  const toggleChecked48Hr = () => setChecked48Hr(!checked48Hr);

  const [clientTransport, setClientTransport] = useState(false);
  const toggleClientTransport = () => setClientTransport(!clientTransport);

  useEffect(() => {
    if (b_plot) {
      setTags((prevTags) => {
        const updatedTags = prevTags.filter((tag) => tag !== b_plot);
        return [...updatedTags, b_plot];
      });
    }
  }, [b_plot]);

  // Define your form.
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      description: "",
      transportation_transaction_id: "",
      transaction_id: "",
    },
  });

  // submit handler.
  async function onSubmit(values: z.infer<typeof formSchema>): Promise<void> {
    let client;
    if (clientType === "Lead") {
      client = lead;
    } else if (clientType === "Customer") {
      client = customer;
    } else {
      toast.error("Customer or Lead is required!!");
      return;
    }
    console.log("cliet", client);

    if ((bookingType == "SPECIAL" || bookingType == "OTHER") && amount == "") {
      toast.error("Amount is required");
      return;
    }

    console.log("amount", amount);

    const formData = {
      ...values,
      amount,
      proof_of_payment: file,
      expected_payment_date,
      plots: tags,
      booking_type: bookingType,
      customer: client,
    };

    const newFormData = new FormData();
    Object.entries(formData).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          // value.forEach((item) => newFormData.append(key, item));
          newFormData.append(key, JSON.stringify(value));
        } else if (value instanceof Date) {
          newFormData.append(key, value.toISOString());
        } else {
          newFormData.append(key, value as string | Blob);
        }
      }
    });

    try {
      const res = await bookSite(newFormData).unwrap();
      if (res) {
        toast.success("Booking created successfully");
        setSbModal(false);
      }
    } catch (error: any) {
      console.log("Error: ", error);
      if (error?.data) {
        toast.error(`${error?.data?.error}`);
      } else {
        toast.error(`Error booking plot(s)`);
      }
      return;
    }
  }

  return (
    <BaseModal
      isOpen={sbModal}
      onOpenChange={setSbModal}
      size="lg"
      title={title}
      description="Fields with (*) are required"
      // footer={<Button onClick={() => setSbModal(false)}>Close</Button>}
    >
      <div className="py-4">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            <div className="space-y-2">
              <div className="">
                <Label className="mr-2 mb-2">Client Type:</Label>
                <div className="flex gap-2 p-2">
                  <div className="flex items-center gap-1">
                    <input
                      type="radio"
                      value="Customer"
                      checked={clientType === "Customer"}
                      onChange={() => {
                        setClientType("Customer");
                      }}
                      className="accent-primary"
                    />
                    <Label htmlFor="customer" className="cursor-pointer">
                      Customer
                    </Label>
                  </div>
                  <div className="flex items-center gap-1">
                    <input
                      type="radio"
                      value="Lead"
                      checked={clientType === "Lead"}
                      onChange={() => {
                        setClientType("Lead");
                      }}
                      className="accent-primary"
                    />
                    <Label htmlFor="lead" className="cursor-pointer">
                      Lead
                    </Label>
                  </div>
                </div>
              </div>
            </div>
            {clientType && (
              <div className="space-y-2">
                <div className="flex gap-2 items-center flex-wrap">
                  <FormLabel>Select {clientType} *</FormLabel>
                </div>
                {clientType === "Customer" && (
                  <CustomSelectField
                    valueField="customer_no"
                    labelField="customer_name"
                    data={customers?.data?.results}
                    queryFunc={fetchCustomers}
                    setValue={setCustomer}
                    useSearchField={true}
                    loader={cus_loading}
                  />
                )}
                {clientType === "Lead" && (
                  <CustomSelectField
                    valueField="id"
                    labelField="name"
                    data={prospects?.data?.results}
                    queryFunc={fetchProspects}
                    setValue={setLead}
                    useSearchField={true}
                    loader={pr_loading}
                  />
                )}
              </div>
            )}

            <div className="space-y-2">
              <div className="flex gap-2 items-center flex-wrap">
                <FormLabel>Plot Number(s) *</FormLabel>
                <FormDescription>
                  <small className="text-destructive">
                    ( Use <b>comma</b> to separate your plot numbers or press{" "}
                    <b>Enter</b> )
                  </small>
                </FormDescription>
              </div>
              <InputWithTags tags={tags} setTags={setTags} />
            </div>

            {bookingType == "MPESA" && (
              <div>
                <div className="flex items-center gap-2 mb-6">
                  <IconSwitch
                    checked={clientTransport}
                    onChange={toggleClientTransport}
                  />{" "}
                  <p>
                    Include Client's Site Visit Transportation Cost for Booking
                    the Plot.
                  </p>
                </div>
                {clientTransport && (
                  <FormField
                    control={form.control}
                    name="transportation_transaction_id"
                    render={({ field }) => (
                      <FormItem className="mb-4">
                        <FormLabel>Transport Cost Mpesa ID.*</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter Mpesa Transaction ID"
                            className="border border-accent  dark:border-white/40"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                <FormField
                  control={form.control}
                  name="transaction_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Booking Fee Mpesa ID*</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter Mpesa Transaction ID"
                          className="border border-accent  dark:border-white/40"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}
            {(bookingType == "SPECIAL" || bookingType == "OTHER") && (
              <div className="space-y-2">
                <FormLabel>Enter Required Amount *</FormLabel>

                <input
                  value={amount}
                  name="amount"
                  type="number"
                  onChange={(e) => setAmount(e.target.value)}
                  className="px-4 py-2 w-full border bg-inherit rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>
            )}

            {bookingType == "OTHER" && (
              <div className="mx-auto ">
                <FormLabel>Attach Proof of Payment document*</FormLabel>
                <label
                  htmlFor="file-upload"
                  className="flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-2xl cursor-pointer p-6 text-center hover:border-blue-500 transition duration-300"
                >
                  <input
                    id="file-upload"
                    type="file"
                    accept="image/*,application/pdf"
                    onChange={handleFileChange}
                    className="hidden"
                  />

                  {previewUrl && !isPdf ? (
                    <img
                      src={previewUrl}
                      alt="Preview"
                      className="w-full h-64 object-contain rounded-xl shadow-md"
                    />
                  ) : file && isPdf ? (
                    <div className="flex flex-col items-center">
                      <FileText className="h-12 w-12 text-gray-400 mb-2" />
                      <p className="text-gray-700 font-medium">PDF Selected</p>
                    </div>
                  ) : (
                    <>
                      <Image className="h-12 w-12 text-gray-400 mb-2" />
                      <p className="text-gray-500">
                        Click to upload image or PDF
                      </p>
                    </>
                  )}
                </label>

                {file && (
                  <p className="mt-2 text-center text-sm text-gray-600">
                    Selected file:{" "}
                    <span className="font-medium">{file.name}</span>
                  </p>
                )}
              </div>
            )}

            <SingleDatePicker
              label="Expected Payment Date *"
              value={expected_payment_date}
              onChange={(date) => {
                setExpectedDate(date);
              }}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description *</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter description"
                      className="resize-none border border-accent   dark:border-white/40"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex items-center gap-2">
              <IconSwitch checked={checked48Hr} onChange={toggleChecked48Hr} />{" "}
              <p>I am confident that my client will pay in 48hrs.</p>
            </div>

            <div className="w-full flex justify-end">
              {!checked48Hr ? (
                <Button
                  type="button"
                  variant="ghost"
                  className="justify-end"
                  disabled
                >
                  Please confirm 48hr payment
                </Button>
              ) : booking ? (
                <SpinnerTemp type="spinner-double" size="sm" />
              ) : (
                <Button type="submit" className="justify-end">
                  Submit
                </Button>
              )}
            </div>
          </form>
        </Form>
      </div>
    </BaseModal>
  );
};

export default BookingsModal;
