import { Calendar } from "@/components/ui/calendar";

interface CalendarViewProps {
  date: Date;
  setDate: (date: Date) => void;
  hasEvents: (day: Date) => boolean;
  showEvents: boolean;
  setShowEvents: (show: boolean) => void;
}

export default function CalendarView({ 
  date, 
  setDate, 
  hasEvents, 
  showEvents, 
  setShowEvents 
}: CalendarViewProps) {
  const renderDay = (day: Date) => {
    const hasEventOnDay = hasEvents(day);
    const isToday = new Date().toDateString() === day.toDateString();
    const isSelected = date.toDateString() === day.toDateString();

    return (
      <div className="flex items-center justify-center w-full h-full relative p-2">
        <div className={`
          flex items-center justify-center w-full h-full min-h-16 aspect-square
          rounded-xl border-2 shadow-sm transition-all duration-200 cursor-pointer
          ${isSelected
            ? 'bg-gradient-to-br from-green-100 to-emerald-100 border-green-300 shadow-md'
            : isToday
              ? 'bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200'
              : hasEventOnDay
                ? 'bg-gradient-to-br from-amber-50 to-yellow-50 border-amber-200 hover:border-amber-300'
                : 'bg-gradient-to-br from-gray-50 to-slate-50 border-gray-200 hover:border-gray-300'
          }
          hover:shadow-md hover:scale-105
        `}>
          <span className={`
            text-lg font-semibold
            ${isSelected
              ? 'text-green-800'
              : isToday
                ? 'text-blue-700'
                : hasEventOnDay
                  ? 'text-amber-700'
                  : 'text-gray-700'
            }
          `}>
            {day.getDate()}
          </span>

          {hasEventOnDay && (
            <div className="absolute bottom-2 right-2">
              <div className={`
                h-3 w-3 rounded-full shadow-sm
                ${isSelected
                  ? 'bg-green-500'
                  : 'bg-gradient-to-r from-blue-500 to-indigo-500'
                }
              `} />
            </div>
          )}

          {isToday && (
            <div className="absolute top-2 left-2">
              <div className="h-2 w-2 rounded-full bg-blue-500 animate-pulse" />
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className={`h-full w-full transition-all duration-300 ${showEvents ? 'w-2/3' : 'w-full'}`}>
      <Calendar
        mode="single"
        selected={date}
        onSelect={(newDate) => {
          if (newDate) {
            setDate(newDate);
            setShowEvents(true);
          }
        }}
        className="w-full h-full rounded-xl border-2 border-green-200 bg-gradient-to-br from-white to-green-50 p-6 shadow-lg"
        numberOfMonths={1}
        showOutsideDays={false}
        classNames={{
          month: "space-y-6",
          caption: "flex justify-center pt-1 relative items-center mb-6",
          caption_label: "text-2xl font-bold text-green-800 bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent",
          nav_button: "h-8 w-8 bg-green-100 hover:bg-green-200 text-green-700 rounded-lg border border-green-200",
          nav_button_previous: "absolute left-1",
          nav_button_next: "absolute right-1",
          table: "w-full border-collapse",
          head_row: "flex mb-4",
          head_cell: "text-green-600 font-semibold text-lg w-full text-center py-2",
          row: "flex w-full mt-2",
          cell: "w-full h-20 text-center relative p-1",
          day: "h-full w-full p-0",
          day_selected: "",
          day_today: "",
          day_outside: "opacity-0",
          day_disabled: "opacity-40",
          day_hidden: "invisible",
        }}
        components={{
          Day: ({ date }) => renderDay(date),
        }}
      />
    </div>
  );
}