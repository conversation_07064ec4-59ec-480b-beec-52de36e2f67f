import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import {
  useAddReminderMutation,
  useEditReminderMutation,
} from "@/redux/slices/services";
import { useEffect, useState } from "react";
import { useGetUsersQuery } from "@/redux/slices/user";
import Multiselect from "@/components/custom/forms/Multiselect";

type Props = {
  prospect_id?: string;
  onOpenChange?: (e: boolean) => void;
  updateData?: any;
};

const ReminderForm = ({ prospect_id, onOpenChange, updateData }: Props) => {
  const [addReminder, { isLoading: submitting }] = useAddReminderMutation();
  const [updateReminder, { isLoading: updating }] = useEditReminderMutation();

  const { data: recipientsData, isLoading: rLoading } = useGetUsersQuery({});

  const [recipientOptions, setRecipientsOptions] = useState<
    { value: any; label: string }[]
  >([]);
  const [selectedRecipient, setSelectedRecipient] = useState<{
    label: string;
    value: string;
  } | null>(null);

  useEffect(() => {
    if (recipientsData?.data?.results.length ?? 0 > 0) {
      let options: any = [];
      recipientsData?.data?.results.map((ls: any) => {
        let option = {
          label: `${ls?.fullnames} (${ls?.employee_no})`,
          value: ls?.employee_no,
        };
        options.push(option);
      });
      setRecipientsOptions(options);
    }
  }, [rLoading]);
  // form schema
  const formSchema = z.object({
    reminder_type: z.string().min(1, { message: "reminder Type is required." }),
    title: z.string().min(1, { message: "title is required." }),
    priority: z.string().min(1, { message: "priority is required." }),
    description: z.string().optional(),
    status: z.string().optional(),
    remind_at: z.string().min(1, { message: "remind at time is required." }),
  });

  // instantiate react hook form
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      reminder_type: updateData?.reminder_type || "Info",
      title: updateData?.title || "",
      description: updateData?.description || "",
      status: updateData?.status || "",
      priority: updateData?.priority || "Normal",
      remind_at: updateData?.remind_at || "",
    },
  });

  const handleReminderSubmit = async (data: any) => {
    let formData = updateData
      ? { ...data, reminder_id: updateData?.reminder_id }
      : {
          ...data,
          recipient: selectedRecipient?.value,
          prospect_id: prospect_id,
          client_status: "prospect",
        };
    try {
      let res;
      if (updateData) {
        res = await updateReminder(formData).unwrap();
      } else {
        res = await addReminder(formData).unwrap();
      }
      if (res?.reminder_id) {
        toast.success(
          `Reminder ${updateData ? "updated" : "added"} successfully`
        );
        onOpenChange && onOpenChange(false);
      } else {
        toast.error("Failed to add");
      }
    } catch (error: any) {
      console.log("error", error);
      toast.error(error?.data ? error?.data?.error : "Something went wrong");
    }
  };

  return (
    <div className="">
      <form onSubmit={handleSubmit(handleReminderSubmit)}>
        <div className="space-y-2">
          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Reminder Type*
            </label>
            <div className="relative pt-1">
              <select
                {...register("reminder_type")}
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              >
                <option value="General">General</option>
                <option value="Task">Task</option>
                <option value="Follow-up">Follow-up</option>
                <option value="Meeting">Meeting</option>
                <option value="Deadline">Deadline</option>
                <option value="Payment">Payment</option>
                <option value="Review">Review</option>
                <option value="Call">Call</option>
                <option value="Email">Email</option>
              </select>
            </div>
            {errors.reminder_type && (
              <p className="text-red-500 text-xs pl-3">
                {errors.reminder_type.message}
              </p>
            )}
          </div>
          <div className="space-y-2">
            <label className="ml-1 text-sm">Assign to</label>
            <Multiselect
              value={
                updateData
                  ? {
                      label: `${updateData?.recipient_name} (${updateData?.recipient_display})`,
                      value: updateData?.recipient_display,
                    }
                  : selectedRecipient
              }
              data={recipientOptions}
              setValue={setSelectedRecipient}
              loading={rLoading}
              isClearable={false}
              isDisabled={false}
              isMultiple={false}
              isSearchable={true}
            />
          </div>

          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Title*
            </label>
            <div className="relative pt-1">
              <input
                {...register("title")}
                placeholder="title"
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              />
            </div>
            {errors.title && (
              <p className="text-red-500 text-xs pl-3">
                {errors.title.message}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Description*
            </label>
            <div className="relative pt-1">
              <textarea
                {...register("description")}
                rows={3}
                placeholder="description..."
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              ></textarea>
            </div>
            {errors.description && (
              <p className="text-red-500 text-xs pl-3">
                {errors.description.message}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Remind At*
            </label>
            <div className="relative pt-1">
              <input
                {...register("remind_at")}
                type="datetime-local"
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              />
            </div>
            {errors.remind_at && (
              <p className="text-red-500 text-xs pl-3">
                {errors.remind_at.message}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Priority
            </label>
            <div className="relative pt-1">
              <select
                {...register("priority")}
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              >
                <option value="Low">Low</option>
                <option value="Normal">Normal</option>
                <option value="High">High</option>
                <option value="Urgent">Urgent</option>
              </select>
            </div>
            {errors.priority && (
              <p className="text-red-500 text-xs pl-3">
                {errors.priority.message}
              </p>
            )}
          </div>
          <div>
            <label htmlFor="" className="ml-1 text-sm">
              status
            </label>
            <div className="relative pt-1">
              <select
                {...register("status")}
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              >
                <option value="Active">Active</option>
                <option value="Snoozed">Snoozed</option>
                <option value="Completed">Completed</option>
                <option value="Cancelled">Cancelled</option>
              </select>
            </div>
            {errors.status && (
              <p className="text-red-500 text-xs pl-3">
                {errors.status.message}
              </p>
            )}
          </div>

          <div className="w-full">
            {submitting ? (
              <SpinnerTemp type="spinner-double" size="sm" />
            ) : (
              <Button type="submit" className="w-full">
                Submit
              </Button>
            )}
          </div>
        </div>
      </form>
    </div>
  );
};

export default ReminderForm;
