import { Screen } from '@/app-components/layout/screen'
import NavTab1 from '@/components/custom/tabs/NavTab1'
import React from 'react'
import Customers from './Customer'
import ActiveCustomers from './ActiveCustomers'
import CompletedCustomers from './CompletedCustomers'
import DroppedCustomers from './DroppedCustomers'
import RefreshPermissionsButton from '@/components/permissions/RefreshPermissionsButton'

const CustomersOverview = () => {
  const tabs = [
    {
      label: "All Customers",
      content: <Customers />,
      value: "all-customers",
      title: "All Customers",
    },
    // {
    //   label: "Active Customers",
    //   content: <ActiveCustomers />,
    //   value: "active-customers",
    //   title: "Active Customers",
    // },
    // {
    //   label: "Completed Customers",
    //   content: <CompletedCustomers />,
    //   value: "completed-customers",
    //   title: "Completed Customers",
    // },
    // {
    //   label: "Dropped Customers",
    //   content: <DroppedCustomers />,
    //   value: "dropped-customers",
    //   title: "Dropped Customers",
    // },
  ]

  return (
    <Screen>
      <div className="!m-0 min-h-screen w-full border rounded">
        <div className='px-3 py-5 border-b flex justify-between items-center'>
          <h2 className='font-bold text-lg'>All Customers</h2>
          {/* <div className="flex items-center gap-2">
            <RefreshPermissionsButton />
          </div> */}
        </div>
        <div className=''>
          <NavTab1
            tabs={tabs}
            TabsListClassName='!bg-transparent rounded-none !border-b'
            TabsTriggerClassName='md:!px-16 !rounded-none data-[state=active]:!bg-primary data-[state=active]:!text-primary-foreground'
          />
        </div>
      </div>
    </Screen>
  )
}

export default CustomersOverview