import { useUpdateProspectMutation } from "@/redux/slices/propects";
import {
  CheckCircle,
  ChevronLeft,
  ChevronRightIcon,
  CircleAlert,
} from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

type StepStatus = "complete" | "current" | "upcoming";

interface Step {
  label: string;
  status: StepStatus;
}
// Replace this with a prop or state from your actual API response

const stepLabels = [
  "New",
  "Qualified Leads",
  "Nurturing",
  "Site Visits",
  "Booking",
  "Offer Letter",
  "Sale Agreement",
  "Converted",
  "Lost",
  "Completed",
];

const getStepsWithStatus = (currentIndex: number): Step[] => {
  return stepLabels.map((label, index) => {
    let status: StepStatus = "upcoming";
    if (index < currentIndex) status = "complete";
    else if (index === currentIndex) status = "current";
    return { label, status };
  });
};

const getBgColor = (status: StepStatus) => {
  switch (status) {
    case "complete":
      return "bg-green-500 text-white";
    case "current":
      return "bg-blue-600 text-white";
    case "upcoming":
      return "bg-gray-200 text-gray-600 dark:!bg-gray-300/10";
  }
};

const getChevronColor = (status: StepStatus) => {
  switch (status) {
    case "complete":
      return "border-l-green-500";
    case "current":
      return "border-l-blue-600";
    case "upcoming":
      return "border-l-gray-200 dark:!border-l-gray-700/25";
  }
};

type PropTypes = {
  pipeline_level: string;
  prospect_id: string;
};

const Pipeline = ({ pipeline_level, prospect_id }: PropTypes) => {
  const [updateProspect, { isLoading }] = useUpdateProspectMutation();

  const handleUpdateProspect = async (newLevel: string) => {
    const formData = {
      id: prospect_id,
      pipeline_level: newLevel,
    };
    console.log("formdata", formData);

    try {
      const res = await updateProspect(formData).unwrap();
      if (res?.id) {
        toast.success("Level Updated successfully");
      }
    } catch (error) {
      console.error("Error Updating prospect:", error);
      toast.error("Failed to add prospect");
      return;
    }
  };

  const [currentIndex, setCurrentIndex] = useState<number>(
    stepLabels.indexOf(pipeline_level) // You can initialize this dynamically
  );

  const steps = getStepsWithStatus(currentIndex);

  const handlePrev = async () => {
    if (currentIndex > 0) {
      const prevIndex = currentIndex - 1;
      const prevValue = stepLabels[prevIndex];

      await handleUpdateProspect(prevValue);
      setCurrentIndex(prevIndex);
    }
  };

  const handleNext = async () => {
    if (currentIndex < stepLabels.length - 1) {
      const nextIndex = currentIndex + 1;
      const nextValue = stepLabels[nextIndex];

      await handleUpdateProspect(nextValue);
      setCurrentIndex(nextIndex);
    }
  };

  return (
    <div className="p-4 space-y-2 shadow-sm border rounded-md">
      <p className="font-bold ">Lead Pipeline Level</p>
      <small className="md:hidden sm:flex text-blue-400 flex gap-1">
        <CircleAlert size={16} /> Slide to view all levels. Click on the arrows
        to switch level
      </small>
      <div className="flex w-full max-w-full  rounded-md overflow-auto -space-x-[12px] scrollbar-hide">
        {steps.map((step, index) => {
          const isLast = index === steps.length - 1;
          const zIndex = steps.length - index + 10;

          const isCurrent = step.status === "current";

          return (
            <div key={index} className="relative flex " style={{ zIndex }}>
              {/* Step content */}
              <div
                className={`flex items-center rounded-l-sm justify-center gap-1 px-8 py-2 text-sm font-medium ${getBgColor(
                  step.status
                )}`}
              >
                {isCurrent && currentIndex > 0 && (
                  <button
                    title="return to previous level"
                    onClick={handlePrev}
                    className="ml-3 text-xs py-1 px-2  text-center rounded cursor-pointer hover:bg-gray-200 hover:text-gray-500"
                  >
                    {isLoading ? "-" : <ChevronLeft size={20} />}
                  </button>
                )}
                {step.status == "complete" && <CheckCircle size={18} />}{" "}
                {step.label}
                {isCurrent && currentIndex < stepLabels.length - 1 && (
                  <button
                    title="advance to next level"
                    onClick={handleNext}
                    className="text-xs py-1 px-2  text-center rounded cursor-pointer hover:bg-gray-200 hover:text-gray-500"
                  >
                    {isLoading ? "-" : <ChevronRightIcon size={20} />}
                  </button>
                  // <p className="ml-3 text-xs px-2 border text-center rounded cursor-pointer">
                  //   <ArrowRight size={16} />
                  // </p>
                )}
              </div>

              {/* Chevron Arrow */}
              {!isLast && (
                <div
                  className={`w-0 h-0 border-y-transparent  border-y-[28px] border-l-[12px] ${getChevronColor(
                    step.status
                  )}`}
                />
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default Pipeline;
