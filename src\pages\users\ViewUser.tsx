import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/custom/badges/badges";
import { User, Mail, Phone, Building, Briefcase, Users, MapPin, Calendar, Tag } from "lucide-react";

interface ViewUserModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  user: any;
}

export default function ViewUserModal({ isOpen, onOpenChange, user }: ViewUserModalProps) {
  if (!user) return null;

  const getAvatarUrl = (name: string) => {
    const initials = name
      ?.split(" ")
      .map((n) => n[0])
      .join("");
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(initials)}&background=random`;
  };

  const getStatusBadge = (status: string) => {
    const statusLower = status?.toLowerCase();
    if (statusLower === "active") {
      return (
        <Badge
          variant="primary"
          className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
        >
          Active
        </Badge>
      );
    } else if (statusLower === "inactive") {
      return (
        <Badge
          variant="destructive"
          className="bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"
        >
          Inactive
        </Badge>
      );
    } else {
      return (
        <Badge
          variant="outline"
          className="bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"
        >
          {status}
        </Badge>
      );
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <User className="h-5 w-5" />
            User Details
          </DialogTitle>
          <DialogDescription>
            View comprehensive information about this user
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Profile Section */}
          <div className="flex items-center gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <img
              src={getAvatarUrl(user.fullnames)}
              alt={user.fullnames}
              className="w-16 h-16 rounded-full border-2 border-white shadow-md"
            />
            <div className="flex-1">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                {user.fullnames}
              </h3>
              <p className="text-gray-600 dark:text-gray-400">{user.email}</p>
              <div className="mt-2">{getStatusBadge(user.status)}</div>
            </div>
          </div>

          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h4 className="text-lg font-medium text-gray-900 dark:text-white border-b pb-2">
                Basic Information
              </h4>

              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <User className="h-4 w-4 text-gray-500" />
                  <div>
                    <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Employee No
                    </Label>
                    <p className="text-gray-900 dark:text-white">{user.employee_no || "N/A"}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <div>
                    <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Email
                    </Label>
                    <p className="text-gray-900 dark:text-white">{user.email || "N/A"}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Phone className="h-4 w-4 text-gray-500" />
                  <div>
                    <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Phone
                    </Label>
                    <p className="text-gray-900 dark:text-white">{user.phone || "N/A"}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <User className="h-4 w-4 text-gray-500" />
                  <div>
                    <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Gender
                    </Label>
                    <p className="text-gray-900 dark:text-white">{user.gender || "N/A"}</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="text-lg font-medium text-gray-900 dark:text-white border-b pb-2">
                Work Information
              </h4>

              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Building className="h-4 w-4 text-gray-500" />
                  <div>
                    <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Department
                    </Label>
                    <p className="text-gray-900 dark:text-white">{user.department || "N/A"}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Briefcase className="h-4 w-4 text-gray-500" />
                  <div>
                    <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Designation
                    </Label>
                    <p className="text-gray-900 dark:text-white">{user.designation || "N/A"}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Users className="h-4 w-4 text-gray-500" />
                  <div>
                    <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Team
                    </Label>
                    <p className="text-gray-900 dark:text-white">{user.team || "N/A"}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <MapPin className="h-4 w-4 text-gray-500" />
                  <div>
                    <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Region
                    </Label>
                    <p className="text-gray-900 dark:text-white">{user.region || "N/A"}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Additional Information */}
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-900 dark:text-white border-b pb-2">
              Additional Information
            </h4>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-3">
                <User className="h-4 w-4 text-gray-500" />
                <div>
                  <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Manager
                  </Label>
                  <p className="text-gray-900 dark:text-white">{user.manager || "N/A"}</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Tag className="h-4 w-4 text-gray-500" />
                <div>
                  <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Category
                  </Label>
                  <p className="text-gray-900 dark:text-white">{user.category || "N/A"}</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Calendar className="h-4 w-4 text-gray-500" />
                <div>
                  <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Created Date
                  </Label>
                  <p className="text-gray-900 dark:text-white">
                    {user.created_date ? new Date(user.created_date).toLocaleDateString() : "N/A"}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}