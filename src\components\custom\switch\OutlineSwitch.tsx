import * as React from "react"

export const OutlineSwitch = ({ checked, onChange }: { checked: boolean; onChange: () => void }) => (
  <label className="relative inline-flex items-center cursor-pointer">
    <input type="checkbox" className="sr-only peer" checked={checked} onChange={onChange} />
    <div className="w-11 h-6 border border-gray-400 rounded-full peer-checked:border-green-500 transition-all"></div>
    <div className="absolute left-0.5 top-0.5 w-5 h-5 bg-gray-100 rounded-full shadow-inner transform peer-checked:translate-x-5 peer-checked:bg-green-500 transition-transform"></div>
  </label>
)