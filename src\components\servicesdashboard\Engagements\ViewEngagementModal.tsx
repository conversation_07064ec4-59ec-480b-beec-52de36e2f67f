
import { useState, useEffect } from 'react';
import MultiStepModal from '@/components/custom/modals/MultiStepModal';
import { Label } from '@/components/ui/label';
import {
  AlertCircle,
  Users,
  Calendar,
  User,
  Building2,
  FileText,
  Paperclip,
  Star,
  Eye,
  Edit3,
  Save,
  X,
  CheckCircle,
  Clock,
  AlertTriangle,
  Sparkles,
  MessageSquare,
  Target,
  Heart
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Badge } from '@/components/ui/badge';

interface Participant {
  id: string;
  avatar: string;
  name: string;
  role: string;
}

interface Comment {
  id: string;
  text: string;
}

interface EngagementData {
  id: string;
  unread: boolean;
  status: string;
  title: string;
  customer: string;
  marketingTeam: string;
  createdDate: string;
  starred: boolean;
  participants: Participant[];
  comments: Comment[];
  attachment: File | null;
  description: string;
}

interface ViewEngagementModalProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  engagementData: EngagementData | null;
  onSubmit: (data: Omit<EngagementData, 'id' | 'createdDate' | 'participants' | 'comments'>) => void;
}

export default function ViewEngagementModal({
  isOpen,
  onOpenChange,
  engagementData,
  onSubmit,
}: ViewEngagementModalProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [isEditMode, setIsEditMode] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    status: '' as 'Critical' | 'Active' | 'Pending',
    customer: '',
    marketingTeam: '',
    description: '',
    attachment: null as File | null,
    unread: false,
    starred: false,
  });

  useEffect(() => {
    if (engagementData) {
      setFormData({
        title: engagementData.title,
        status: engagementData.status as 'Critical' | 'Active' | 'Pending',
        customer: engagementData.customer,
        marketingTeam: engagementData.marketingTeam,
        description: engagementData.description,
        attachment: engagementData.attachment,
        unread: engagementData.unread,
        starred: engagementData.starred,
      });
    }
  }, [engagementData]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setFormData((prev) => ({ ...prev, [id]: value }));
  };

  const handleStatusChange = (value: 'Critical' | 'Active' | 'Pending') => {
    setFormData((prev) => ({ ...prev, status: value }));
  };

  const handleSaveChanges = () => {
    if (engagementData) {
      onSubmit(formData);
    }
    onOpenChange(false);
    setCurrentStep(0);
    setIsEditMode(false);
  };

  return (
    <MultiStepModal
      isOpen={isOpen}
      onOpenChange={(open) => {
        onOpenChange(open);
        if (!open) {
          setCurrentStep(0);
          setIsEditMode(false);
        }
      }}
      title={isEditMode ? 'Edit Engagement Details' : 'View Engagement Details'}
      description={isEditMode ? 'Update the engagement details' : 'Review the details of the selected engagement'}
      currentStep={currentStep}
      onStepChange={setCurrentStep}
      onComplete={() => {
        onOpenChange(false);
        setCurrentStep(0);
        setIsEditMode(false);
      }}
      steps={[
        {
          title: 'Basic Information',
          content: (
            <div className="space-y-6 py-4">
              {/* Header Section */}
              <div className="text-center mb-6">
                <div className="mx-auto w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mb-4 shadow-lg">
                  <Eye className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-gray-800">
                  {isEditMode ? 'Edit Basic Details' : 'Engagement Overview'}
                </h3>
                <p className="text-gray-600 text-sm">
                  {isEditMode ? 'Update the core engagement information' : 'Review the essential engagement details'}
                </p>
              </div>

              {/* Content Card */}
              <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl p-6 border border-green-200">
                <div className="space-y-6">
                  {/* Title Section */}
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-green-600" />
                      <Label className="text-sm font-medium text-gray-700">
                        Engagement Title {isEditMode && <span className="text-red-500">*</span>}
                      </Label>
                    </div>
                    {isEditMode ? (
                      <Input
                        id="title"
                        value={formData.title}
                        onChange={handleInputChange}
                        placeholder="Enter a descriptive engagement title..."
                        className="border-2 border-green-200 focus:border-green-500 focus:ring-green-500 rounded-xl bg-white"
                      />
                    ) : (
                      <div className="bg-white rounded-xl p-4 border border-green-200 shadow-sm">
                        <p className="text-gray-800 font-medium">{engagementData?.title || 'No title specified'}</p>
                      </div>
                    )}
                  </div>

                  {/* Status Section */}
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <Target className="h-4 w-4 text-green-600" />
                      <Label className="text-sm font-medium text-gray-700">
                        Engagement Status {isEditMode && <span className="text-red-500">*</span>}
                      </Label>
                    </div>
                    {isEditMode ? (
                      <RadioGroup
                        value={formData.status}
                        onValueChange={handleStatusChange}
                        className="grid grid-cols-3 gap-3"
                      >
                        <div className="flex items-center space-x-3">
                          <RadioGroupItem value="Critical" id="critical" />
                          <Label
                            htmlFor="critical"
                            className={`flex items-center gap-2 px-3 py-2 rounded-lg border cursor-pointer transition-all duration-200 hover:shadow-sm ${
                              formData.status === 'Critical'
                                ? 'bg-red-50 border-red-200 text-red-700'
                                : 'bg-white border-gray-200 text-gray-600'
                            }`}
                          >
                            <AlertTriangle className="h-4 w-4" />
                            <span className="text-sm font-medium">Critical</span>
                          </Label>
                        </div>
                        <div className="flex items-center space-x-3">
                          <RadioGroupItem value="Active" id="active" />
                          <Label
                            htmlFor="active"
                            className={`flex items-center gap-2 px-3 py-2 rounded-lg border cursor-pointer transition-all duration-200 hover:shadow-sm ${
                              formData.status === 'Active'
                                ? 'bg-green-50 border-green-200 text-green-700'
                                : 'bg-white border-gray-200 text-gray-600'
                            }`}
                          >
                            <CheckCircle className="h-4 w-4" />
                            <span className="text-sm font-medium">Active</span>
                          </Label>
                        </div>
                        <div className="flex items-center space-x-3">
                          <RadioGroupItem value="Pending" id="pending" />
                          <Label
                            htmlFor="pending"
                            className={`flex items-center gap-2 px-3 py-2 rounded-lg border cursor-pointer transition-all duration-200 hover:shadow-sm ${
                              formData.status === 'Pending'
                                ? 'bg-yellow-50 border-yellow-200 text-yellow-700'
                                : 'bg-white border-gray-200 text-gray-600'
                            }`}
                          >
                            <Clock className="h-4 w-4" />
                            <span className="text-sm font-medium">Pending</span>
                          </Label>
                        </div>
                      </RadioGroup>
                    ) : (
                      <div className="bg-white rounded-xl p-4 border border-green-200 shadow-sm">
                        <Badge
                          className={`${
                            engagementData?.status === 'Critical'
                              ? 'bg-red-100 text-red-700 border-red-200'
                              : engagementData?.status === 'Active'
                              ? 'bg-green-100 text-green-700 border-green-200'
                              : 'bg-yellow-100 text-yellow-700 border-yellow-200'
                          } border font-medium`}
                        >
                          {engagementData?.status === 'Critical' && <AlertTriangle className="h-3 w-3 mr-1" />}
                          {engagementData?.status === 'Active' && <CheckCircle className="h-3 w-3 mr-1" />}
                          {engagementData?.status === 'Pending' && <Clock className="h-3 w-3 mr-1" />}
                          {engagementData?.status || 'No status'}
                        </Badge>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ),
        },
        {
          title: 'Detailed Information',
          content: (
            <div className="space-y-6 py-4">
              {/* Header Section */}
              <div className="text-center mb-6">
                <div className="mx-auto w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mb-4 shadow-lg">
                  <MessageSquare className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-lg font-semibold text-gray-800">
                  {isEditMode ? 'Edit Engagement Details' : 'Engagement Details'}
                </h3>
                <p className="text-gray-600 text-sm">
                  {isEditMode ? 'Update the detailed engagement information' : 'Review comprehensive engagement information'}
                </p>
              </div>

              {/* Content Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Customer Section */}
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-blue-600" />
                    <Label className="text-sm font-medium text-gray-700">
                      Customer {isEditMode && <span className="text-red-500">*</span>}
                    </Label>
                  </div>
                  {isEditMode ? (
                    <Input
                      id="customer"
                      value={formData.customer}
                      onChange={handleInputChange}
                      placeholder="Enter customer name..."
                      className="border-2 border-blue-200 focus:border-blue-500 focus:ring-blue-500 rounded-xl bg-white"
                    />
                  ) : (
                    <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200 shadow-sm">
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-blue-600" />
                        <p className="text-gray-800 font-medium">{engagementData?.customer || 'No customer specified'}</p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Marketing Team Section */}
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Building2 className="h-4 w-4 text-blue-600" />
                    <Label className="text-sm font-medium text-gray-700">
                      Marketing Team {isEditMode && <span className="text-red-500">*</span>}
                    </Label>
                  </div>
                  {isEditMode ? (
                    <Input
                      id="marketingTeam"
                      value={formData.marketingTeam}
                      onChange={handleInputChange}
                      placeholder="Enter marketing team..."
                      className="border-2 border-blue-200 focus:border-blue-500 focus:ring-blue-500 rounded-xl bg-white"
                    />
                  ) : (
                    <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200 shadow-sm">
                      <div className="flex items-center gap-2">
                        <Building2 className="h-4 w-4 text-blue-600" />
                        <p className="text-gray-800 font-medium">{engagementData?.marketingTeam || 'No team specified'}</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Description Section */}
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4 text-blue-600" />
                  <Label className="text-sm font-medium text-gray-700">
                    Description {isEditMode && <span className="text-red-500">*</span>}
                  </Label>
                </div>
                {isEditMode ? (
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Describe the engagement in detail..."
                    rows={4}
                    className="border-2 border-blue-200 focus:border-blue-500 focus:ring-blue-500 rounded-xl bg-white resize-none"
                  />
                ) : (
                  <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200 shadow-sm">
                    <p className="text-gray-800 leading-relaxed">{engagementData?.description || 'No description provided'}</p>
                  </div>
                )}
              </div>

              {/* Additional Info Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Attachment Section */}
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Paperclip className="h-4 w-4 text-blue-600" />
                    <Label className="text-sm font-medium text-gray-700">Attachment</Label>
                  </div>
                  <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200 shadow-sm">
                    <div className="flex items-center gap-2">
                      <Paperclip className="h-4 w-4 text-blue-600" />
                      <p className="text-gray-800 font-medium">
                        {engagementData?.attachment ? engagementData.attachment.name : 'No attachment'}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Created Date Section */}
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-blue-600" />
                    <Label className="text-sm font-medium text-gray-700">Created Date</Label>
                  </div>
                  <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200 shadow-sm">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-blue-600" />
                      <p className="text-gray-800 font-medium">
                        {engagementData?.createdDate
                          ? new Date(engagementData.createdDate).toLocaleDateString('en-US', {
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric',
                            })
                          : 'No date specified'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Participants Section (if available) */}
              {engagementData?.participants && engagementData.participants.length > 0 && (
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-blue-600" />
                    <Label className="text-sm font-medium text-gray-700">Participants</Label>
                  </div>
                  <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200 shadow-sm">
                    <div className="flex flex-wrap gap-2">
                      {engagementData.participants.map((participant) => (
                        <div key={participant.id} className="flex items-center gap-2 bg-white rounded-lg px-3 py-2 border border-blue-200">
                          <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                            <span className="text-white text-xs font-medium">
                              {participant.name.charAt(0).toUpperCase()}
                            </span>
                          </div>
                          <span className="text-sm font-medium text-gray-700">{participant.name}</span>
                          <span className="text-xs text-gray-500">({participant.role})</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          ),
        },
        {
          title: 'Review & Actions',
          content: (
            <div className="py-6 space-y-6">
              {/* Header Section */}
              <div className="text-center">
                <div className="mx-auto w-16 h-16 bg-gradient-to-br from-emerald-500 to-green-600 rounded-full flex items-center justify-center mb-4 shadow-lg">
                  {isEditMode ? <Save className="h-8 w-8 text-white" /> : <CheckCircle className="h-8 w-8 text-white" />}
                </div>
                <h3 className="text-xl font-semibold text-gray-800">
                  {isEditMode ? 'Save Changes' : 'Review Complete'}
                </h3>
                <p className="text-gray-600 text-sm mt-2">
                  {isEditMode
                    ? 'Please review your changes before saving to the system.'
                    : 'You have successfully reviewed all engagement details.'}
                </p>
              </div>

              {/* Summary Card (for edit mode) */}
              {isEditMode && (
                <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl p-6 border border-green-200">
                  <div className="flex items-center gap-2 mb-4">
                    <Sparkles className="h-5 w-5 text-green-600" />
                    <h4 className="text-lg font-semibold text-gray-800">Changes Summary</h4>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm text-gray-700">
                        <strong>Title:</strong> {formData.title || 'Not specified'}
                      </span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm text-gray-700">
                        <strong>Status:</strong> {formData.status}
                      </span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm text-gray-700">
                        <strong>Customer:</strong> {formData.customer || 'Not specified'}
                      </span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm text-gray-700">
                        <strong>Marketing Team:</strong> {formData.marketingTeam || 'Not specified'}
                      </span>
                    </div>
                  </div>
                </div>
              )}

              {/* Success Message (for view mode) */}
              {!isEditMode && (
                <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl p-6 border border-green-200 text-center">
                  <div className="flex items-center justify-center gap-2 text-green-600 mb-2">
                    <Heart className="h-5 w-5" />
                    <span className="font-semibold">Engagement Review Complete</span>
                  </div>
                  <p className="text-gray-600 text-sm">
                    All engagement details have been successfully reviewed. You can now edit the engagement or close this dialog.
                  </p>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex justify-center gap-4">
                {!isEditMode ? (
                  <>
                    <Button
                      variant="outline"
                      onClick={() => setIsEditMode(true)}
                      className="flex items-center gap-2 px-6 py-3 border-2 border-green-200 text-green-700 hover:bg-green-50 hover:border-green-300 rounded-xl font-semibold transition-all duration-200"
                    >
                      <Edit3 className="h-4 w-4" />
                      Edit Engagement
                    </Button>
                    <Button
                      variant="default"
                      onClick={() => onOpenChange(false)}
                      className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white hover:from-green-600 hover:to-emerald-700 rounded-xl font-semibold transition-all duration-200 shadow-lg hover:shadow-xl"
                    >
                      <CheckCircle className="h-4 w-4" />
                      Close
                    </Button>
                  </>
                ) : (
                  <>
                    <Button
                      variant="outline"
                      onClick={() => setIsEditMode(false)}
                      className="flex items-center gap-2 px-6 py-3 border-2 border-gray-200 text-gray-700 hover:bg-gray-50 hover:border-gray-300 rounded-xl font-semibold transition-all duration-200"
                    >
                      <X className="h-4 w-4" />
                      Cancel
                    </Button>
                    <Button
                      variant="default"
                      onClick={handleSaveChanges}
                      className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white hover:from-green-600 hover:to-emerald-700 rounded-xl font-semibold transition-all duration-200 shadow-lg hover:shadow-xl"
                    >
                      <Save className="h-4 w-4" />
                      Save Changes
                      <Sparkles className="h-4 w-4 opacity-70" />
                    </Button>
                  </>
                )}
              </div>

              {/* Footer Note */}
              <div className="text-center">
                <div className="flex items-center justify-center gap-2 text-gray-500 text-xs">
                  <Star className="h-3 w-3" />
                  <span>Engagement management made simple</span>
                  <Star className="h-3 w-3" />
                </div>
              </div>
            </div>
          ),
        },
      ]}
    />
  );
}
