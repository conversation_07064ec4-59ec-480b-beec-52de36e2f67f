import ConfirmModal from "@/components/custom/modals/ConfirmationModal";

interface DeleteUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onDelete: () => void;
  user: {
    id: number;
    fullnames: string;
    email: string;
    employee_no: string;
  };
}

export default function DeleteUserModal({
  isOpen,
  onClose,
  onDelete,
  user,
}: DeleteUserModalProps) {
  return (
    <ConfirmModal
      isOpen={isOpen}
      onOpenChange={onClose}
      title="Confirm User Deletion"
      variant="danger"
      message={
        <div className="space-y-2">
          <p>
            Are you sure you want to delete the user <strong>{user.fullnames}</strong>?
          </p>
          <div className="text-sm text-gray-600 dark:text-gray-400">
            <p>Employee No: {user.employee_no}</p>
            <p>Email: {user.email}</p>
          </div>
          <p className="text-sm font-medium text-red-600 dark:text-red-400">
            This action cannot be undone.
          </p>
        </div>
      }
      confirmText="Delete User"
      confirmVariant="destructive"
      cancelText="Cancel"
      onConfirm={onDelete}
    />
  );
}
