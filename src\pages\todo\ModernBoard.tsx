import React, { useState } from "react";
import { motion } from "framer-motion";
import { Screen } from "@/app-components/layout/screen";
import { CardDetailModal } from "./CardDetailModal";
import { AddCardModal } from "./AddCardModal";
import {
  Plus,
  Settings,
  Users,
  Filter,
  Search,
  MoreHorizontal,
  Star,
  Share2,
  Calendar,
  Clock,
  MessageSquare,
  Paperclip,
  CheckSquare,
  Eye,
  Edit3,
  AlertCircle,
  Flag,
  Trash2,
  Zap,
  Target,
  TrendingUp,
  Activity,
  Bell,
  User,
  ChevronDown,
  Sparkles,
  BarChart3,
  Layout,
  Layers,
} from "lucide-react";
import Todo from "./todo";
import { useGetTodoQuery, useCreateTodoMutation, useUpdateTodoMutation, useDeleteTodoMutation } from "@/redux/slices/todoApiSlice";

// Define valid statuses for type safety
type ValidStatus = "PENDING" | "IN_PROGRESS" | "COMPLETED" | "CANCELLED";

// Modern card interface
interface ModernCard {
  id: string;
  title: string;
  description?: string;
  column: ValidStatus; // Updated to use ValidStatus
  assignee?: string;
  labels?: { id: string; name: string; color: string }[];
  dueDate?: Date;
  priority?: "low" | "medium" | "high" | "urgent";
  attachments?: number;
  comments?: number;
  checklist?: { completed: number; total: number };
}

// Sample data updated to use valid statuses
const SAMPLE_CARDS: ModernCard[] = [
  {
    id: "1",
    title: "Design new landing page",
    description: "Create a modern, responsive landing page for the new product launch",
    column: "PENDING", // Changed from "todo"
    assignee: "John Doe",
    labels: [
      { id: "1", name: "Design", color: "#10B981" },
      { id: "2", name: "High Priority", color: "#EF4444" },
    ],
    dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
    priority: "high",
    attachments: 3,
    comments: 2,
    checklist: { completed: 2, total: 5 },
  },
  {
    id: "2",
    title: "API Integration",
    description: "Integrate payment gateway API",
    column: "IN_PROGRESS", // Changed from "doing"
    assignee: "Jane Smith",
    labels: [{ id: "3", name: "Backend", color: "#3B82F6" }],
    priority: "urgent",
    comments: 1,
    checklist: { completed: 3, total: 4 },
  },
  {
    id: "3",
    title: "User Testing",
    description: "Conduct user testing sessions",
    column: "COMPLETED", // Changed from "done"
    assignee: "Mike Johnson",
    labels: [{ id: "4", name: "Research", color: "#8B5CF6" }],
    priority: "medium",
    attachments: 1,
    checklist: { completed: 5, total: 5 },
  },
];

// Updated COLUMNS to use valid statuses
const COLUMNS = [
  { id: "PENDING", title: "Pending", color: "from-gray-400 to-gray-500" },
  { id: "IN_PROGRESS", title: "In Progress", color: "from-blue-400 to-indigo-500" },
  { id: "COMPLETED", title: "Completed", color: "from-yellow-400 to-orange-500" },
  { id: "CANCELLED", title: "Cancelled", color: "from-green-400 to-emerald-500" },
];

const ModernKanbanCard: React.FC<{
  card: ModernCard;
  onCardClick: (card: ModernCard) => void;
  onDragStart: (e: React.DragEvent, card: ModernCard) => void;
  onDeleteCard?: (cardId: string) => void;
  onToggleSelection?: (cardId: string) => void;
  isSelected?: boolean;
  isUpdatingStatus?: boolean;
}> = ({ card, onCardClick, onDragStart, onDeleteCard, onToggleSelection, isSelected = false, isUpdatingStatus = false }) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isDragging, setIsDragging] = useState(false);

  const getPriorityColor = (priority?: string) => {
    switch (priority) {
      case "urgent":
        return "from-red-500 to-pink-500";
      case "high":
        return "from-orange-500 to-red-500";
      case "medium":
        return "from-yellow-500 to-orange-500";
      case "low":
        return "from-green-500 to-emerald-500";
      default:
        return "from-gray-400 to-gray-500";
    }
  };

  const formatDueDate = (date: Date) => {
    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) return { text: "Overdue", color: "bg-red-500 text-white" };
    if (diffDays === 0) return { text: "Due today", color: "bg-orange-500 text-white" };
    if (diffDays === 1) return { text: "Due tomorrow", color: "bg-yellow-500 text-white" };
    if (diffDays <= 7) return { text: `${diffDays} days`, color: "bg-blue-500 text-white" };
    return { text: date.toLocaleDateString(), color: "bg-gray-500 text-white" };
  };

  const handleDragStart = (e: React.DragEvent) => {
    setIsDragging(true);
    onDragStart(e, card);
  };

  const handleDragEnd = () => {
    setIsDragging(false);
  };

  const handleClick = (e: React.MouseEvent) => {
    if (!isDragging) {
      onCardClick(card);
    }
  };

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: isDragging ? 0 : -2 }}
      transition={{ duration: 0.2 }}
      draggable={!isUpdatingStatus}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={handleClick}
      className={`group relative bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-lg transition-all duration-200 overflow-hidden mb-3 ${
        isDragging ? "opacity-50 rotate-3 scale-105 shadow-2xl" : ""
      } ${isUpdatingStatus ? "cursor-not-allowed opacity-60" : "cursor-move"} ${
        isSelected ? "ring-2 ring-blue-500 border-blue-500" : ""
      }`}
    >
      {/* Selection checkbox */}
      {onToggleSelection && (
        <div className="absolute top-2 left-2 z-10">
          <input
            type="checkbox"
            checked={isSelected}
            onChange={(e) => {
              e.stopPropagation();
              onToggleSelection(card.id);
            }}
            className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
          />
        </div>
      )}
      
      {card.priority && (
        <div className={`absolute top-0 left-0 right-0 h-1 bg-gradient-to-r ${getPriorityColor(card.priority)}`}></div>
      )}
      <div className="p-4">
        {card.labels && card.labels.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-3">
            {card.labels.slice(0, 2).map((label) => (
              <span
                key={label.id}
                className="px-2 py-1 text-xs font-medium rounded-full text-white shadow-sm"
                style={{ backgroundColor: label.color }}
              >
                {label.name}
              </span>
            ))}
            {card.labels.length > 2 && (
              <span className="px-2 py-1 text-xs font-medium rounded-full bg-gray-200 text-gray-600">
                +{card.labels.length - 2}
              </span>
            )}
          </div>
        )}
        <h3 className="text-sm font-semibold text-gray-900 mb-2 line-clamp-2 leading-tight">{card.title}</h3>
        {card.description && (
          <p className="text-xs text-gray-600 mb-3 line-clamp-2">{card.description}</p>
        )}
        {card.dueDate && (
          <div className="mb-3">
            {(() => {
              const { text, color } = formatDueDate(card.dueDate);
              return (
                <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-md text-xs font-medium ${color}`}>
                  <Calendar className="h-3 w-3" />
                  {text}
                </div>
              );
            })()}
          </div>
        )}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3 text-xs text-gray-500">
            {card.checklist && (
              <div className="flex items-center gap-1">
                <CheckSquare className="h-3 w-3" />
                <span className={card.checklist.completed === card.checklist.total ? "text-green-600 font-medium" : ""}>
                  {card.checklist.completed}/{card.checklist.total}
                </span>
              </div>
            )}
            {card.comments && card.comments > 0 && (
              <div className="flex items-center gap-1">
                <MessageSquare className="h-3 w-3" />
                <span>{card.comments}</span>
              </div>
            )}
            {card.attachments && card.attachments > 0 && (
              <div className="flex items-center gap-1">
                <Paperclip className="h-3 w-3" />
                <span>{card.attachments}</span>
              </div>
            )}
          </div>
          {card.assignee && card.assignee !== 'Unassigned' && (
            <div className="w-6 h-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white text-xs font-semibold">
              {card.assignee.includes('/') 
                ? card.assignee.split('/').pop()?.substring(0, 2).toUpperCase() || "U"
                : card.assignee.split(" ").map((n) => n[0]).join("").substring(0, 2).toUpperCase()}
            </div>
          )}
        </div>
        {isHovered && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="absolute top-2 right-2 flex gap-1"
          >
            <button 
              onClick={(e) => {
                e.stopPropagation();
                onCardClick(card);
              }}
              className="p-1 bg-white rounded-md shadow-md hover:bg-gray-50 transition-colors"
            >
              <Eye className="h-3 w-3 text-gray-600" />
            </button>
            <button 
              onClick={(e) => {
                e.stopPropagation();
                onCardClick(card);
              }}
              className="p-1 bg-white rounded-md shadow-md hover:bg-gray-50 transition-colors"
            >
              <Edit3 className="h-3 w-3 text-gray-600" />
            </button>
            {onDeleteCard && (
              <button 
                onClick={(e) => {
                  e.stopPropagation();
                  onDeleteCard(card.id);
                }}
                className="p-1 bg-white rounded-md shadow-md hover:bg-red-50 transition-colors group"
              >
                <Trash2 className="h-3 w-3 text-gray-600 group-hover:text-red-600" />
              </button>
            )}
          </motion.div>
        )}
      </div>
    </motion.div>
  );
};

const ModernColumn: React.FC<{
  column: any;
  cards: ModernCard[];
  onCardClick: (card: ModernCard) => void;
  onAddCard: (columnId: string, columnTitle: string) => void;
  onDragStart: (e: React.DragEvent, card: ModernCard) => void;
  onDrop: (e: React.DragEvent, columnId: string) => void;
  onDragOver: (e: React.DragEvent) => void;
  onDragLeave: (e: React.DragEvent) => void;
  onDeleteCard?: (cardId: string) => void;
  onToggleSelection?: (cardId: string) => void;
  selectedCards?: string[];
  isUpdatingStatus?: boolean;
}> = ({ column, cards, onCardClick, onAddCard, onDragStart, onDrop, onDragOver, onDragLeave, onDeleteCard, onToggleSelection, selectedCards = [], isUpdatingStatus = false }) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const columnCards = cards.filter((card) => card.column === column.id);

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    onDrop(e, column.id);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
    onDragOver(e);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setIsDragOver(false);
      onDragLeave(e);
    }
  };

  return (
    <div className="w-80 shrink-0">
      <div
        className={`bg-white rounded-xl shadow-sm border-2 overflow-hidden transition-all duration-200 ${
          isDragOver ? "border-blue-400 bg-blue-50 shadow-lg transform scale-105" : "border-gray-200"
        }`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        <div className="p-4 border-b border-gray-100">
          <div className="flex items-center justify-between mb-2">
            <h2 className="text-lg font-bold text-gray-900">{column.title}</h2>
            <div className="flex items-center gap-2">
              <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                {columnCards.length}
              </span>
              <button className="p-1 text-gray-400 hover:text-gray-600 transition-colors">
                <MoreHorizontal className="h-4 w-4" />
              </button>
            </div>
          </div>
          <div className={`h-1 w-full bg-gradient-to-r ${column.color} rounded-full`}></div>
          {isDragOver && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-2 p-2 bg-blue-100 border border-blue-300 rounded-lg text-center"
            >
              <span className="text-sm text-blue-700 font-medium">Drop card here</span>
            </motion.div>
          )}
        </div>
        <div className="p-4 max-h-96 overflow-y-auto">
          {columnCards.map((card) => (
            <ModernKanbanCard
              key={card.id}
              card={card}
              onCardClick={onCardClick}
              onDragStart={onDragStart}
              onDeleteCard={onDeleteCard}
              onToggleSelection={onToggleSelection}
              isSelected={selectedCards.includes(card.id)}
              isUpdatingStatus={isUpdatingStatus}
            />
          ))}
          <button
            onClick={() => onAddCard(column.id, column.title)}
            className="w-full p-3 border-2 border-dashed border-gray-300 rounded-xl hover:border-gray-400 hover:bg-gray-50 transition-all duration-200 flex items-center justify-center gap-2 text-gray-500 hover:text-gray-600"
          >
            <Plus className="h-4 w-4" />
            <span className="text-sm font-medium">Add a card</span>
          </button>
        </div>
        <div className="p-4 border-t border-gray-100 bg-gray-50">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>{columnCards.length} cards</span>
            <button className="text-blue-600 hover:text-blue-700 font-medium">View all</button>
          </div>
        </div>
      </div>
    </div>
  );
};

export const ModernBoard: React.FC = () => {
  const [isAddCardModalOpen, setIsAddCardModalOpen] = useState(false);
  const [selectedColumn, setSelectedColumn] = useState<{ id: string; title: string } | null>(null);
  const [selectedCard, setSelectedCard] = useState<ModernCard | null>(null);
  const [draggedCard, setDraggedCard] = useState<ModernCard | null>(null);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const [selectedCards, setSelectedCards] = useState<string[]>([]);

  const { data: todos = [], isLoading, error } = useGetTodoQuery({});
  const [createTodo] = useCreateTodoMutation();
  const [updateTodo] = useUpdateTodoMutation();
  const [deleteTodo] = useDeleteTodoMutation();

  // Console log useGetTodoQuery results
  React.useEffect(() => {
    console.log("useGetTodoQuery results:", {
      isLoading,
      error,
      todos,
      todosLength: todos?.data?.results?.length,
      todosType: typeof todos,
      todosIsArray: Array.isArray(todos),
      actualTodos: todos?.data?.results
    });
  }, [todos, isLoading, error]);

  // Helper function to get priority color
  const getPriorityColor = (priority: string): string => {
    switch (priority?.toLowerCase()) {
      case 'urgent':
        return '#EF4444';
      case 'high':
        return '#F97316';
      case 'medium':
        return '#EAB308';
      case 'low':
        return '#10B981';
      default:
        return '#6B7280';
    }
  };

  // Transform API data to ModernCard format
  const transformTodoToCard = (todo: any): ModernCard => {
    try {
      console.log("Transforming todo:", todo);
      
      const card: ModernCard = {
        id: todo.id?.toString() || '',
        title: todo.title || 'Untitled Task',
        description: todo.description || '',
        column: todo.status as ValidStatus,
        assignee: todo.assigned_to 
          ? (typeof todo.assigned_to === 'string' 
              ? todo.assigned_to // Show employee_no as is
              : (todo.assigned_to.fullnames || (todo.assigned_to.first_name && todo.assigned_to.last_name 
                  ? `${todo.assigned_to.first_name} ${todo.assigned_to.last_name}` 
                  : 'Assigned User')))
          : 'Unassigned',
        labels: todo.priority ? [{ 
          id: todo.priority, 
          name: todo.priority.charAt(0).toUpperCase() + todo.priority.slice(1).toLowerCase(), 
          color: getPriorityColor(todo.priority) 
        }] : [],
        dueDate: todo.due_date ? new Date(todo.due_date) : undefined,
        priority: todo.priority?.toLowerCase() as "low" | "medium" | "high" | "urgent",
        attachments: 0, // API doesn't provide this, set to 0
        comments: 0, // API doesn't provide this, set to 0
        checklist: undefined, // API doesn't provide this
      };
      
      console.log("Transformed card:", card);
      return card;
    } catch (error) {
      console.error("Error transforming todo to card:", error, todo);
      // Return a fallback card to prevent crashes
      return {
        id: todo.id?.toString() || Math.random().toString(),
        title: todo.title || 'Error loading task',
        description: 'There was an error loading this task',
        column: 'PENDING',
        attachments: 0,
        comments: 0,
      };
    }
  };

  // Transform todos to cards
  const cards: ModernCard[] = React.useMemo(() => {
    // Extract actual todos from paginated response
    const actualTodos = todos?.data?.results || [];
    
    console.log("Transforming todos to cards...", {
      todos,
      actualTodos,
      todosIsArray: Array.isArray(actualTodos),
      todosLength: actualTodos?.length
    });
    
    if (!Array.isArray(actualTodos)) {
      console.log("Actual todos is not an array, returning empty array");
      return [];
    }
    
    const transformedCards = actualTodos.map(transformTodoToCard);
    console.log("Transformed cards:", transformedCards);
    
    // If no cards, show sample cards for testing
    if (transformedCards.length === 0) {
      console.log("No cards from API, showing sample cards for testing");
      return SAMPLE_CARDS;
    }
    
    return transformedCards;
  }, [todos]);

  const handleAddCard = async (newCardData: any) => {
    try {
      const result = await createTodo({
        ...newCardData,
        status: selectedColumn?.id, // Ensure this is a valid status
      }).unwrap();
      
      // The data will be automatically updated through the useGetTodoQuery hook
      // No need to manually update state since we're using real API data
      console.log("Todo created successfully:", result);
      
      setIsAddCardModalOpen(false);
      setSelectedColumn(null);
    } catch (error) {
      console.error("Failed to create todo:", error);
    }
  };

  const handleCardClick = (card: ModernCard) => {
    setSelectedCard(card);
  };

  const openAddCardModal = (columnId: string, columnTitle: string) => {
    setSelectedColumn({ id: columnId, title: columnTitle });
    setIsAddCardModalOpen(true);
  };

  const handleDeleteTodo = async (todoId: string) => {
    const confirmed = ('Are you sure you want to delete this task? This action cannot be undone.');
    
    if (!confirmed) {
      return;
    }

    console.log("🗑️ Starting delete operation for todo ID:", todoId);
    
    try {
      await deleteTodo(todoId).unwrap();
      console.log("✅ Todo deleted successfully");
      
      // Close any open modals
      setSelectedCard(null);
      
    } catch (error) {
      console.error("❌ Failed to delete todo:", error);
      
      // Log detailed error information
      if (error?.data) {
        console.error("API Error Response:", error.data);
      }
      if (error?.status) {
        console.error("HTTP Status Code:", error.status);
      }
      
      // Show error feedback to user
      alert(`Failed to delete task: ${error?.data?.message || error?.message || 'Unknown error'}`);
    }
  };

  const handleBulkDelete = async () => {
    if (selectedCards.length === 0) return;
    
    const confirmed = window.confirm(
      `Are you sure you want to delete ${selectedCards.length} task${selectedCards.length > 1 ? 's' : ''}? This action cannot be undone.`
    );
    
    if (!confirmed) return;

    console.log("🗑️ Starting bulk delete operation for IDs:", selectedCards);
    
    try {
      // Delete all selected cards
      await Promise.all(
        selectedCards.map(cardId => deleteTodo(cardId).unwrap())
      );
      
      console.log("✅ Bulk delete completed successfully");
      setSelectedCards([]);
      
    } catch (error) {
      console.error("❌ Failed to bulk delete todos:", error);
      alert(`Failed to delete some tasks: ${error?.data?.message || error?.message || 'Unknown error'}`);
    }
  };

  const toggleCardSelection = (cardId: string) => {
    setSelectedCards(prev => 
      prev.includes(cardId) 
        ? prev.filter(id => id !== cardId)
        : [...prev, cardId]
    );
  };

  const selectAllCards = () => {
    setSelectedCards(cards.map(card => card.id));
  };

  const deselectAllCards = () => {
    setSelectedCards([]);
  };

  const handleDragStart = (e: React.DragEvent, card: ModernCard) => {
    // Prevent dragging while an update is in progress
    if (isUpdatingStatus) {
      e.preventDefault();
      return;
    }
    
    setDraggedCard(card);
    e.dataTransfer.effectAllowed = "move";
    e.dataTransfer.setData("text/plain", card.id);
    setTimeout(() => {
      const dragImage = e.target as HTMLElement;
      dragImage.style.opacity = "0.5";
    }, 0);
  };

  const handleDrop = async (e: React.DragEvent, targetColumnId: string) => {
    e.preventDefault();
    if (!draggedCard || draggedCard.column === targetColumnId) {
      setDraggedCard(null);
      return;
    }
    
    console.log("🔄 Starting drag & drop operation:", {
      cardId: draggedCard.id,
      fromStatus: draggedCard.column,
      toStatus: targetColumnId
    });
    
    setIsUpdatingStatus(true);
    
    try {
      // Find the original todo data to get all required fields
      const actualTodos = todos?.data?.results || [];
      const originalTodo = actualTodos.find(todo => todo.id?.toString() === draggedCard.id);
      
      if (!originalTodo) {
        console.error("❌ Original todo not found for drag operation");
        setDraggedCard(null);
        return;
      }

      console.log("📝 Original todo data:", originalTodo);
      
      // Prepare the update payload with correct format
      const updatePayload = { 
        ...originalTodo, 
        status: targetColumnId // Already in correct format (IN_PROGRESS, etc.)
      };
      
      console.log("📤 Sending PATCH request with payload:", updatePayload);

      // Update the todo with the new status
      const result = await updateTodo(updatePayload).unwrap();
      
      console.log("✅ Todo status updated successfully:", result);
      
      // Show success feedback
      console.log(`📋 Task "${draggedCard.title}" moved from ${draggedCard.column} to ${targetColumnId}`);
      
    } catch (error) {
      console.error("❌ Failed to update todo:", error);
      
      // Log detailed error information
      if (error?.data) {
        console.error("API Error Response:", error.data);
      }
      if (error?.status) {
        console.error("HTTP Status Code:", error.status);
      }
      
      // Show error feedback to user
      alert(`Failed to move task: ${error?.data?.message || error?.message || 'Unknown error'}`);
    } finally {
      setIsUpdatingStatus(false);
    }
    
    setDraggedCard(null);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "move";
  };

  const handleDragLeave = (e: React.DragEvent) => {};

  // Debug: Log component state
  console.log("ModernBoard render state:", {
    isLoading,
    error,
    cardsCount: cards.length,
    cards: cards.slice(0, 2), // Log first 2 cards to avoid too much output
    todosCount: todos?.data?.results?.length || 0,
    totalDataFromAPI: todos?.data?.total_data
  });

  // Show loading state while fetching todos
  if (isLoading) {
    return (
      <Screen>
        <div className="flex items-center justify-center h-64">
          <div className="flex items-center gap-3">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="text-lg font-medium text-gray-600">Loading todos...</span>
          </div>
        </div>
      </Screen>
    );
  }

  // Show error state if there's an error
  if (error) {
    return (
      <Screen>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="text-red-500 text-lg font-medium mb-2">Error loading todos</div>
            <p className="text-gray-600">Please try refreshing the page</p>
          </div>
        </div>
      </Screen>
    );
  }

  return (
    <Screen>
      <div className="sticky top-0 z-20 bg-gradient-to-r from-green-500 via-emerald-500 to-teal-600 shadow-2xl">
        <div className="absolute top-4 right-8 w-20 h-20 bg-white/10 rounded-full blur-xl pointer-events-none" />
        <div className="absolute bottom-6 left-12 w-16 h-16 bg-white/10 rounded-full blur-xl pointer-events-none" />
        <div className="absolute top-1/2 right-1/4 w-8 h-8 bg-white/20 rounded-full pointer-events-none" />
        <div className="relative z-10">
          <div className="flex flex-wrap lg:flex-nowrap items-center justify-between px-4 md:px-8 py-6 gap-4">
            <div className="flex items-center gap-4 min-w-0">
              <div className="p-4 bg-white/20 rounded-2xl backdrop-blur-sm shadow-lg shrink-0">
                <Layout className="h-8 w-8 text-white" />
              </div>
              <div className="min-w-0">
                <div className="flex items-center gap-3 mb-2">
                  <h1 className="text-2xl md:text-3xl font-bold text-white truncate max-w-xs md:max-w-md">
                    Project Board
                  </h1>
                  <button className="p-2 text-white/70 hover:text-yellow-300 transition-colors transform hover:scale-110">
                    <Star className="h-6 w-6" />
                  </button>
                </div>
                <p className="text-green-100 text-base md:text-lg truncate max-w-xs md:max-w-md">
                  Manage your tasks and workflow efficiently • {cards.length} tasks loaded
                </p>
                <div className="flex flex-wrap items-center gap-4 mt-3 text-green-100 text-sm">
                  <div className="flex items-center gap-1">
                    <Target className="h-4 w-4" />
                    <span>{cards.length} Active Tasks</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <TrendingUp className="h-4 w-4" />
                    <span>Real-time Updates</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Activity className="h-4 w-4" />
                    <span>Live Collaboration</span>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex flex-wrap items-center gap-3 justify-end min-w-0">
              <div className="relative flex-1 min-w-[180px] max-w-xs">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-white/70" />
                <input
                  type="text"
                  placeholder="Search tasks..."
                  className="pl-12 pr-4 py-3 w-full bg-white/20 border-2 border-white/30 rounded-xl text-white placeholder-white/70 backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-white/50 transition-all duration-200"
                />
              </div>
              <div className="flex flex-wrap items-center gap-2">
                <button className="flex items-center gap-2 px-4 py-3 bg-white/20 hover:bg-white/30 text-white rounded-xl font-semibold transition-all duration-200 backdrop-blur-sm border border-white/30 hover:border-white/50">
                  <Filter className="h-5 w-5" />
                  <span>Filter</span>
                  <ChevronDown className="h-4 w-4" />
                </button>
                <button className="flex items-center gap-2 px-4 py-3 bg-white/20 hover:bg-white/30 text-white rounded-xl font-semibold transition-all duration-200 backdrop-blur-sm border border-white/30 hover:border-white/50">
                  <Users className="h-5 w-5" />
                  <span>Team</span>
                </button>
                <button className="flex items-center gap-2 px-4 py-3 bg-white/20 hover:bg-white/30 text-white rounded-xl font-semibold transition-all duration-200 backdrop-blur-sm border border-white/30 hover:border-white/50">
                  <Share2 className="h-5 w-5" />
                  <span>Share</span>
                </button>
                <button 
                  onClick={selectedCards.length === cards.length ? deselectAllCards : selectAllCards}
                  className="flex items-center gap-2 px-4 py-3 bg-white/20 hover:bg-white/30 text-white rounded-xl font-semibold transition-all duration-200 backdrop-blur-sm border border-white/30 hover:border-white/50"
                >
                  <CheckSquare className="h-5 w-5" />
                  <span>{selectedCards.length === cards.length ? 'Deselect All' : 'Select All'}</span>
                </button>
                {selectedCards.length > 0 && (
                  <div className="flex items-center gap-2 px-4 py-3 bg-red-500/20 border border-red-300/30 text-white rounded-xl backdrop-blur-sm">
                    <span className="text-sm font-medium">{selectedCards.length} selected</span>
                    <button
                      onClick={deselectAllCards}
                      className="text-xs text-red-200 hover:text-white transition-colors"
                    >
                      Clear
                    </button>
                    <button
                      onClick={handleBulkDelete}
                      className="flex items-center gap-1 px-3 py-1 bg-red-500 hover:bg-red-600 text-white rounded-lg font-medium transition-colors"
                    >
                      <Trash2 className="h-4 w-4" />
                      Delete
                    </button>
                  </div>
                )}
                <button
                  onClick={() => openAddCardModal("PENDING", "Pending")}
                  className="flex items-center gap-2 px-6 py-3 bg-white text-green-600 rounded-xl font-semibold hover:bg-green-50 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
                >
                  <Plus className="h-5 w-5" />
                  <span>Add Task</span>
                  <Sparkles className="h-4 w-4 opacity-70" />
                </button>
                <div className="flex items-center gap-2 ml-2">
                  <button className="relative p-3 bg-white/20 hover:bg-white/30 text-white rounded-xl transition-all duration-200 backdrop-blur-sm">
                    <Bell className="h-5 w-5" />
                    <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full animate-pulse"></div>
                  </button>
                  <button className="flex items-center gap-2 p-2 bg-white/20 hover:bg-white/30 rounded-xl transition-all duration-200 backdrop-blur-sm">
                    <div className="w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full flex items-center justify-center">
                      <User className="h-4 w-4 text-white" />
                    </div>
                  </button>
                  <button className="p-3 bg-white/20 hover:bg-white/30 text-white rounded-xl transition-all duration-200 backdrop-blur-sm">
                    <MoreHorizontal className="h-5 w-5" />
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div className="px-4 md:px-8 pb-6 overflow-x-auto">
            <div className="flex flex-wrap gap-4 items-center justify-between">
              {COLUMNS.map((column, index) => {
                const columnCards = cards.filter((card) => card.column === column.id);
                return (
                  <motion.div
                    key={column.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="flex items-center gap-3 px-4 py-2 bg-white/20 rounded-xl backdrop-blur-sm border border-white/30 min-w-[120px]"
                  >
                    <div className={`w-3 h-3 rounded-full bg-gradient-to-r ${column.color}`}></div>
                    <span className="text-white font-medium truncate">{column.title}</span>
                    <span className="text-white/80 text-sm bg-white/20 px-2 py-1 rounded-full">
                      {columnCards.length}
                    </span>
                  </motion.div>
                );
              })}
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2 text-white/80">
                  <BarChart3 className="h-4 w-4" />
                  <span className="text-sm">Analytics</span>
                </div>
                <div className="flex items-center gap-2 text-white/80">
                  <Zap className="h-4 w-4" />
                  <span className="text-sm">Productivity: High</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        className={`flex gap-6 overflow-x-auto p-6 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 min-h-screen transition-all duration-200 ${
          draggedCard ? "bg-gradient-to-br from-blue-100 via-indigo-100 to-purple-100" : ""
        }`}
      >
        {COLUMNS.map((column) => {
          const columnCards = cards.filter((card) => card.column === column.id);
          console.log(`Column ${column.id} has ${columnCards.length} cards:`, columnCards);
          
          return (
            <ModernColumn
              key={column.id}
              column={column}
              cards={cards}
              onCardClick={handleCardClick}
              onAddCard={openAddCardModal}
              onDragStart={handleDragStart}
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDeleteCard={handleDeleteTodo}
              onToggleSelection={toggleCardSelection}
              selectedCards={selectedCards}
              isUpdatingStatus={isUpdatingStatus}
            />
          );
        })}
        <div className="w-80 shrink-0">
          <button className="w-full h-32 border-2 border-dashed border-gray-300 rounded-xl hover:border-gray-400 hover:bg-gray-50 transition-all duration-200 flex flex-col items-center justify-center gap-2 text-gray-500 hover:text-gray-600">
            <Plus className="h-8 w-8" />
            <span className="text-sm font-medium">Add another list</span>
          </button>
        </div>
      </div>
      {draggedCard && (
        <div className="fixed inset-0 pointer-events-none z-50">
          <div className="absolute top-4 left-1/2 transform -translate-x-1/2">
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white border-2 border-blue-400 rounded-xl shadow-2xl p-4 max-w-xs"
            >
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                  >
                    <Flag className="h-4 w-4 text-white" />
                  </motion.div>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 text-sm">{draggedCard.title}</h3>
                  <p className="text-xs text-blue-600">
                    {isUpdatingStatus ? "Updating status..." : "Moving card..."}
                  </p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      )}
      {selectedCard && (
        <CardDetailModal
          isOpen={!!selectedCard}
          onClose={() => setSelectedCard(null)}
          card={selectedCard}
          onDelete={handleDeleteTodo}
        />
      )}
      <AddCardModal
        isOpen={isAddCardModalOpen && !!selectedColumn}
        onClose={() => {
          setIsAddCardModalOpen(false);
          setSelectedColumn(null);
        }}
        columnId={selectedColumn?.id || ""}
        columnTitle={selectedColumn?.title || ""}
        onAddCard={handleAddCard}
      />
    </Screen>
  );
};