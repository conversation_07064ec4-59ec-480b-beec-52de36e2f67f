import { SetStateAction, useMemo } from "react";
import BaseModal from "@/components/custom/modals/BaseModal";
import { ColumnDef } from "@tanstack/react-table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Settings, Activity } from "lucide-react";
import { DataTable } from "@/components/custom/tables/Table1";
import { ProspectTypes } from "@/types/prospects";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { useGetDigitalCustomersQuery } from "@/redux/slices/digitalApiSlice";

interface ProspectsTableModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function DigitalCustomersTableModal({ open, onOpenChange }: ProspectsTableModalProps) {
  // Fetch prospects data
  const {
    data: customersData,
    isLoading,
    error,
  } = useGetDigitalCustomersQuery({
    page: 1,
    page_size: 10,
  });

  // Use prospects data directly
  const prospects = useMemo(() => customersData?.data?.results || [], [customersData]);

  const columns: ColumnDef<ProspectTypes>[] = [
    {
      accessorKey: "customer_id",
      header: "Customer ID",
    },
    {
      accessorKey: "customer_name",
      header: "Name",
      cell: (info) => (
        <span className="font-medium capitalize">{info.getValue() as string}</span>
      ),
    },
    {
      accessorKey: "phone_number",
      header: "Phone Number",
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: "primary_email",
      header: "Email",
      cell: (info) => info.getValue() || "N/A",
    },
    {
      accessorKey: "plot_numbers",
      header: "Plot Numbers",
      cell: (info) => info.getValue() || "N/A",
    },
    {
      accessorKey: "marketer_employee_no",
      header: "Marketer No",
      cell: (info) => info.getValue() || "Unallocated",
    },
    {
      accessorKey: "marketer_name",
      header: "Marketer Name",
      cell: (info) => info.getValue() || "Unallocated",
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger>
            <Settings size={20} className="text-gray-500 hover:text-blue-600 transition-colors" />
          </DropdownMenuTrigger>
          <DropdownMenuContent className="bg-white dark:bg-gray-800 shadow-lg rounded-md border border-gray-200 dark:border-gray-700">
            <DropdownMenuLabel className="text-gray-700 dark:text-gray-200">Actions</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                console.log(`Edit prospect ${row.original.name}`);
              }}
              className="hover:bg-blue-50 dark:hover:bg-gray-700"
            >
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                console.log(`View prospect ${row.original.name}`);
              }}
              className="hover:bg-blue-50 dark:hover:bg-gray-700"
            >
              View Details
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
    },
  ];

  return (
    <BaseModal
      open={open}
      onOpenChange={onOpenChange}
      title="Leads Management"
      description="View and manage your leads with ease"
      className="max-w-5xl"
      size="full"
    >
      <div className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-900 dark:to-gray-800 rounded-xl shadow-2xl transition-all duration-300">
        {/* Header Section */}
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-gray-800 dark:text-white bg-clip-text">
            Digital Customers Overview
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Comprehensive view of all customers
          </p>
        </div>

        {/* Table Section */}
        {isLoading ? (
          <div className="flex items-center justify-center py-16">
            <SpinnerTemp type="spinner-double" size="lg" />
          </div>
        ) : error ? (
          <div className="text-center py-16 bg-white dark:bg-gray-800 rounded-lg shadow-inner">
            <Activity className="w-16 h-16 text-red-500 mx-auto mb-4 animate-pulse" />
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              Failed to Load Customers
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Unable to fetch customers data. Please try again later.
            </p>
          </div>
        ) : prospects.length === 0 ? (
          <div className="text-center py-16 bg-white dark:bg-gray-800 rounded-lg shadow-inner">
            <p className="text-gray-500 dark:text-gray-400 text-lg">No customers found.</p>
          </div>
        ) : (
          <div className="w-full overflow-x-auto">
            <DataTable<ProspectTypes>
              data={prospects}
              columns={columns}
              title="Leads Overview"
              enableExportToExcel={true}
              enablePrintPdf={true}
              enableColumnFilters={true}
              enablePagination={true}
              enableSorting={true}
              enableToolbar={true}
              containerClassName="min-w-[700px] bg-white dark:bg-gray-800 rounded-lg shadow-lg"
              tableClassName="w-full border-collapse text-sm text-left text-gray-700 dark:text-gray-300"
              tHeadClassName="bg-gradient-to-r from-blue-100 to-indigo-100 dark:from-gray-700 dark:to-gray-600 text-gray-800 dark:text-gray-200 font-semibold"
              tHeadCellsClassName="px-6 py-3 border-b border-gray-200 dark:border-gray-700"
              tBodyClassName="divide-y divide-gray-200 dark:divide-gray-700"
              tBodyTrClassName="hover:bg-blue-50 dark:hover:bg-gray-700 transition-colors duration-200"
              tBodyCellsClassName="px-6 py-4"
            />
          </div>
        )}
      </div>
    </BaseModal>
  );
}

