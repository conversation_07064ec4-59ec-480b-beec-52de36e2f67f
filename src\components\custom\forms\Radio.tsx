import { getInputColorClassNames } from "@/helpers";

type Props = {
  label: string;
  value: string;
  checked?: boolean;
  color?: string;
  disabled?: boolean;
  id?: string;
};

const Radio = ({ label, checked, disabled, id, value, color }: Props) => {
  return (
    <label className="gap-2 flex items-center">
      <input
        type="radio"
        checked={checked}
        id={id}
        value={value}
        disabled={disabled}
        className={`w-4 h-4 bg-gray-100 border-gray-300 rounded focus:ring-2 ${getInputColorClassNames(
          color
        )}`}
      />
      <label htmlFor={id}>{label}</label>
    </label>
  );
};

export default Radio;
