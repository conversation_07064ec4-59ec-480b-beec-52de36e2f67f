import React from 'react';
import { User, Users, Building, Briefcase } from 'lucide-react';

interface OnboardingFormProps {
  formData: any;
  setFormData: (data: any) => void;
  onNext: () => void;
}

const OnboardingForm: React.FC<OnboardingFormProps> = ({
  formData,
  setFormData,
  onNext,
}) => {
  const handleInvestmentTypeSelect = (type: string) => {
    setFormData({ ...formData, investmentType: type });
  };

  const handleContinue = () => {
    if (formData.investmentType) {
      onNext();
    }
  };

  const investmentOptions = [
    {
      id: 'individual',
      title: 'Individual',
      icon: User,
      description: 'Invest as an individual'
    },
    {
      id: 'partner',
      title: 'Partner',
      icon: Users,
      description: 'Invest with a partner'
    },
    {
      id: 'group',
      title: 'Group',
      icon: Briefcase,
      description: 'Invest as a group'
    },
    {
      id: 'company',
      title: 'Company',
      icon: Building,
      description: 'Invest as a company'
    }
  ];

  return (
    <div className="max-w-4xl mx-auto text-center space-y-12">
      {/* Header Section */}
      <div className="space-y-6">
        <h1 className="text-3xl md:text-4xl font-bold text-green-600">
          Optiven Offer Letter
        </h1>
        
        <h2 className="text-xl md:text-2xl font-semibold text-gray-800">
          Congratulations on your decision to invest with Optiven!
        </h2>
        
        <div className="max-w-3xl mx-auto space-y-4 text-gray-600 leading-relaxed">
          <p>
            We are excited to officially present you with this Offer Letter, that is specifically created to mark the beginning of your investment journey. 
            The document contains comprehensive details on your property acquisition to provide clarity, confidence and a clear road map to your 
            new asset.
          </p>
          
          <p>
            As you review the offer, our team is ready to offer assistance with any clarification and questions that may arise.
          </p>
        </div>
      </div>

      {/* Welcome Section */}
      <div className="bg-green-50 rounded-lg p-8 space-y-4">
        <h3 className="text-2xl font-semibold text-green-700">
          Welcome to the Optiven family.
        </h3>
        
        <div className="max-w-2xl mx-auto text-gray-700 leading-relaxed">
          <p>
            At Optiven, we prioritize your privacy and protect your personal data through secure practices, limited sharing, and compliance 
            with applicable data protection laws, ensuring transparency and giving you control over your information.
          </p>
          
          <p className="mt-4">
            Read our Privacy Policy{' '}
            <a href="#" className="text-green-600 underline hover:text-green-700 transition-colors">
              Here
            </a>
          </p>
        </div>
      </div>

      {/* Investment Type Selection */}
      <div className="space-y-8">
        <h3 className="text-2xl md:text-3xl font-semibold text-green-600">
          Welcome! How are you coming on board with Optiven?
        </h3>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-3xl mx-auto">
          {investmentOptions.map((option) => {
            const IconComponent = option.icon;
            const isSelected = formData.investmentType === option.id;
            
            return (
              <button
                key={option.id}
                onClick={() => handleInvestmentTypeSelect(option.id)}
                className={`p-6 rounded-lg border-2 transition-all duration-200 hover:shadow-md ${
                  isSelected
                    ? 'border-green-500 bg-green-50 shadow-md'
                    : 'border-gray-200 bg-white hover:border-green-300'
                }`}
              >
                <div className="flex flex-col items-center space-y-3">
                  <div className={`p-3 rounded-full ${
                    isSelected ? 'bg-green-500 text-white' : 'bg-green-100 text-green-600'
                  }`}>
                    <IconComponent size={24} />
                  </div>
                  
                  <div className="text-center">
                    <h4 className={`font-semibold ${
                      isSelected ? 'text-green-700' : 'text-gray-800'
                    }`}>
                      {option.title}
                    </h4>
                  </div>
                </div>
              </button>
            );
          })}
        </div>
      </div>

      {/* Continue Button */}
      <div className="pt-8">
        <button
          onClick={handleContinue}
          disabled={!formData.investmentType}
          className="px-8 py-3 bg-green-500 text-white rounded-lg font-semibold hover:bg-green-600 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed"
        >
          Continue to Personal Details →
        </button>
      </div>
    </div>
  );
};

export default OnboardingForm;