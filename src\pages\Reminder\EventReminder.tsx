import { useState, useEffect, useMemo } from "react";
import { useToast } from "@/hooks/use-toast";
import { Calendar, Plus, RefreshCw, ChevronRight } from "lucide-react";
import { Screen } from "@/app-components/layout/screen";
import AddEventModal from "./AddReview";
import AllEventsSheet from "./AllSheet";
import EditEventModal from "./EditReview";
import CalendarView from "./CalenderView";
import EventSidebar from "./EventSideBar";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  useGetRemindersQuery,
  useAddReminderMutation,
  useUpdateReminderMutation,
  useDeleteReminderMutation,
  useSnoozeReminderMutation,
} from "@/redux/slices/reminderApiSlice";
import { Skeleton } from "@/components/ui/skeleton";
import { Toggle } from "@/components/ui/toggle";
import { format } from "date-fns";

interface Reminder {
  reminder_id?: string;
  title: string;
  description?: string;
  reminder_type?: 'General' | 'Task' | 'Appointment' | 'Meeting' | 'Birthday' | 'Anniversary' | 'Payment' | 'Health' | 'Other';
  priority?: 'Low' | 'Normal' | 'High' | 'Critical';
  status?: 'Active' | 'Completed' | 'Snoozed' | 'Cancelled';
  remind_at: string;
  advance_notice_minutes?: number;
  repeat_pattern?: 'None' | 'Daily' | 'Weekly' | 'Monthly' | 'Yearly';
  repeat_until?: string;
  assigned_to?: string;
  completion_notes?: string;
  tags?: string | null;
  action_url?: string;
  is_active?: boolean;
  created_at?: string;
  entity_name?: string;
  is_due?: boolean;
  is_overdue?: boolean;
  related_entity?: any;
}

const convertReminderToEvent = (reminder: Reminder) => {
  return {
    id: reminder.reminder_id || '',
    date: new Date(reminder.remind_at),
    title: reminder.title,
    description: reminder.description || 'No description',
    time: new Date(reminder.remind_at).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    }),
    status: reminder.status || 'Active',
    priority: reminder.priority || 'Normal',
    reminder_type: reminder.reminder_type || 'General',
    advance_notice_minutes: reminder.advance_notice_minutes || 30,
    repeat_pattern: reminder.repeat_pattern || 'None',
    tags: reminder.tags,
    snoozed: reminder.status === 'Snoozed',
    is_active: reminder.is_active ?? true,
    is_due: reminder.is_due || false,
    is_overdue: reminder.is_overdue || false,
    entity_name: reminder.entity_name || '',
    created_at: reminder.created_at || '',
  };
};

const convertEventToReminder = (event: any, selectedDate: Date): Partial<Reminder> => {
  const [hours, minutes] = event.time.split(':');
  const isPM = event.time.includes('PM');
  let hour24 = parseInt(hours);
  if (isPM && hour24 !== 12) hour24 += 12;
  if (!isPM && hour24 === 12) hour24 = 0;

  const reminderDate = new Date(selectedDate);
  reminderDate.setHours(hour24, parseInt(minutes), 0, 0);

  return {
    title: event.title,
    description: event.description || null,
    reminder_type: event.reminder_type || 'General',
    priority: event.priority || 'Normal',
    status: event.status || 'Active',
    remind_at: reminderDate.toISOString(),
    advance_notice_minutes: event.advance_notice_minutes || 30,
    repeat_pattern: event.repeat_pattern || 'None',
    tags: event.tags || null,
    is_active: event.is_active ?? true,
  };
};

const PriorityBadge = ({ priority }: { priority: string }) => {
  const priorityMap = {
    Low: 'bg-blue-100 text-blue-800',
    Normal: 'bg-green-100 text-green-800',
    High: 'bg-yellow-100 text-yellow-800',
    Critical: 'bg-red-100 text-red-800'
  };

  return (
    <Badge className={`${priorityMap[priority as keyof typeof priorityMap] || 'bg-gray-100 text-gray-800'} text-xs font-medium`}>
      {priority}
    </Badge>
  );
};

export default function EventsReminder() {
  const [date, setDate] = useState<Date>(new Date());
  const [selectedDayEvents, setSelectedDayEvents] = useState<any[]>([]);
  const [newEvent, setNewEvent] = useState({
    title: "",
    description: "",
    time: "",
    date: new Date(),
    reminder_type: "General",
    priority: "Normal",
    status: "Active",
    advance_notice_minutes: 30,
    repeat_pattern: "None",
    tags: "",
    snoozed: false,
    snoozeUntil: undefined,
    is_active: true,
  });
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [eventToEdit, setEventToEdit] = useState<any>(null);
  const [showEvents, setShowEvents] = useState(false);
  const [showAllEvents, setShowAllEvents] = useState(false);
  const { toast } = useToast();

  const {
    data: remindersResponse,
    error: remindersError,
    isLoading: remindersLoading,
    refetch: refetchReminders,
  } = useGetRemindersQuery({});

  const remindersData = useMemo(() => {
    if (!remindersResponse) return [];
    
    if (remindersResponse.data && Array.isArray(remindersResponse.data.results)) {
      return remindersResponse.data.results;
    }
    
    if (Array.isArray(remindersResponse)) {
      return remindersResponse;
    }
    
    return [];
  }, [remindersResponse]);

  const [addReminder] = useAddReminderMutation();
  const [updateReminder] = useUpdateReminderMutation();
  const [deleteReminder] = useDeleteReminderMutation();
  const [snoozeReminder] = useSnoozeReminderMutation();

  const events = useMemo(() => {
    if (!Array.isArray(remindersData)) return [];
    return remindersData.map(convertReminderToEvent);
  }, [remindersData]);

  const hasEvents = (day: Date) => {
    return events.some(
      (event) =>
        event.date.getDate() === day.getDate() &&
        event.date.getMonth() === day.getMonth() &&
        event.date.getFullYear() === day.getFullYear()
    );
  };

  useEffect(() => {
    const dayEvents = showAllEvents
      ? events
      : events.filter(
          (event) =>
            event.date.getDate() === date.getDate() &&
            event.date.getMonth() === date.getMonth() &&
            event.date.getFullYear() === date.getFullYear()
        );

    setSelectedDayEvents(dayEvents);
    setShowEvents(dayEvents.length > 0);
  }, [date, events, showAllEvents]);

  const handleAddEvent = async () => {
    if (!newEvent.title || !newEvent.time) {
      toast({
        title: "Missing information",
        description: "Please provide at least a title and time for the event.",
        variant: "destructive",
      });
      return;
    }

    try {
      const reminderData = convertEventToReminder(newEvent, date);
      await addReminder(reminderData).unwrap();

      setNewEvent({
        title: "",
        description: "",
        time: "",
        date: new Date(),
        reminder_type: "General",
        priority: "Normal",
        status: "Active",
        advance_notice_minutes: 30,
        repeat_pattern: "None",
        tags: "",
        snoozed: false,
        snoozeUntil: undefined,
        is_active: true,
      });
      setIsModalOpen(false);

      toast({
        title: "Reminder added",
        description: "Your reminder has been successfully added.",
      });

      refetchReminders();
    } catch (error) {
      toast({
        title: "Error adding reminder",
        description: "There was an error adding your reminder. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleRemoveEvent = async (eventId: string) => {
    try {
      await deleteReminder(eventId).unwrap();
      setSelectedDayEvents(selectedDayEvents.filter((event) => event.id !== eventId));

      toast({
        title: "Reminder removed",
        description: "Your reminder has been successfully removed.",
      });

      refetchReminders();
    } catch (error) {
      toast({
        title: "Error removing reminder",
        description: "There was an error removing your reminder. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleEditEvent = (event: any) => {
    setEventToEdit(event);
    setIsEditModalOpen(true);
  };

  const handleSaveEvent = async () => {
    if (!eventToEdit) return;

    try {
      const reminderData = convertEventToReminder(eventToEdit, eventToEdit.date);
      await updateReminder({
        id: eventToEdit.id,
        ...reminderData,
      }).unwrap();

      setIsEditModalOpen(false);
      setEventToEdit(null);

      toast({
        title: "Reminder updated",
        description: "Your reminder has been successfully updated.",
      });

      refetchReminders();
    } catch (error) {
      toast({
        title: "Error updating reminder",
        description: "There was an error updating your reminder. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleSnoozeEvent = async (eventId: string, snoozeUntil: Date) => {
    try {
      await snoozeReminder({
        id: eventId,
        remind_at: snoozeUntil.toISOString(),
      }).unwrap();

      toast({
        title: "Reminder snoozed",
        description: "Your reminder has been snoozed successfully.",
      });

      refetchReminders();
    } catch (error) {
      toast({
        title: "Error snoozing reminder",
        description: "There was an error snoozing your reminder. Please try again.",
        variant: "destructive",
      });
    }
  };

  if (remindersLoading) {
    return (
      <Screen>
        <div className="flex flex-col h-full">
          <div className="px-6 py-4 border-b">
            <div className="flex items-center gap-3">
              <Skeleton className="h-8 w-8 rounded-full" />
              <div className="space-y-2">
                <Skeleton className="h-6 w-40" />
                <Skeleton className="h-4 w-60" />
              </div>
            </div>
            <div className="flex gap-2 mt-4">
              <Skeleton className="h-10 w-32" />
              <Skeleton className="h-10 w-32" />
              <Skeleton className="h-10 w-32" />
            </div>
          </div>
          <div className="flex flex-1 overflow-hidden">
            <div className="flex-1 p-4">
              <Skeleton className="h-full w-full rounded-xl" />
            </div>
            <div className="w-80 border-l p-4">
              <Skeleton className="h-full w-full rounded-lg" />
            </div>
          </div>
        </div>
      </Screen>
    );
  }

  if (remindersError) {
    return (
      <Screen>
        <div className="flex items-center justify-center h-full">
          <div className="text-center max-w-md p-6 bg-white dark:bg-slate-900 rounded-xl shadow-lg">
            <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-red-100 mb-4">
              <svg
                className="h-6 w-6 text-red-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-slate-900 dark:text-slate-100 mb-2">
              Failed to load reminders
            </h3>
            <p className="text-slate-600 dark:text-slate-400 mb-4">
              {remindersError.toString()}
            </p>
            <Button onClick={() => refetchReminders()}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Retry
            </Button>
          </div>
        </div>
      </Screen>
    );
  }

  return (
    <Screen className="bg-slate-50 dark:bg-slate-950">
      <header className="flex-none px-6 py-4 border-b bg-white dark:bg-slate-900 sticky top-0 z-10">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-indigo-100 dark:bg-indigo-900/50 text-indigo-600 dark:text-indigo-300">
              <Calendar className="h-5 w-5" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-slate-800 dark:text-slate-100">Reminders</h1>
              <p className="text-sm text-slate-500 dark:text-slate-400">
                {format(date, 'MMMM yyyy')} • {events.length} total reminders
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Toggle
              pressed={showAllEvents}
              onPressedChange={setShowAllEvents}
              className="data-[state=on]:bg-purple-100 data-[state=on]:text-purple-700 dark:data-[state=on]:bg-purple-900/50 dark:data-[state=on]:text-purple-300"
            >
              {showAllEvents ? "Show Today" : "Show All"}
            </Toggle>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => refetchReminders()}
              className="gap-1.5"
            >
              <RefreshCw className="h-4 w-4" />
              <span>Refresh</span>
            </Button>
            
            <Button
              onClick={() => setIsModalOpen(true)}
              size="sm"
              className="gap-1.5"
            >
              <Plus className="h-4 w-4" />
              <span>Add Reminder</span>
            </Button>
          </div>
        </div>
      </header>

      <div className="flex flex-1 min-h-0 overflow-hidden">
        <div className="flex-1 p-4 min-h-0 overflow-auto">
          <div className="bg-white dark:bg-slate-900 rounded-xl shadow-sm border border-slate-200 dark:border-slate-800 h-full">
            <CalendarView
              date={date}
              setDate={setDate}
              hasEvents={hasEvents}
              showEvents={showEvents}
              setShowEvents={setShowEvents}
            />
          </div>
        </div>
        
        <div className="w-96 border-l bg-white dark:bg-slate-900 flex flex-col">
          <div className="p-4 border-b">
            <div className="flex items-center justify-between">
              <h2 className="font-semibold text-lg">
                {showAllEvents ? "All Reminders" : "Today's Reminders"}
              </h2>
              <Badge variant="outline" className="px-2 py-1 text-xs">
                {selectedDayEvents.length} items
              </Badge>
            </div>
            <p className="text-sm text-slate-500 dark:text-slate-400 mt-1">
              {format(date, 'EEEE, MMMM d')}
            </p>
          </div>
          
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {selectedDayEvents.length > 0 ? (
              selectedDayEvents.map((event) => (
                <div 
                  key={event.id}
                  className="p-3 rounded-lg border border-slate-200 dark:border-slate-700 hover:bg-slate-50 dark:hover:bg-slate-800/50 transition-colors cursor-pointer"
                  onClick={() => handleEditEvent(event)}
                >
                  <div className="flex items-start justify-between">
                    <div>
                      <h3 className="font-medium">{event.title}</h3>
                      <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                        {event.time} • {event.description}
                      </p>
                    </div>
                    <ChevronRight className="h-5 w-5 text-slate-400" />
                  </div>
                  <div className="flex gap-2 mt-3">
                    <PriorityBadge priority={event.priority} />
                    {event.is_overdue && (
                      <Badge variant="destructive" className="text-xs">
                        Overdue
                      </Badge>
                    )}
                    {event.reminder_type !== 'General' && (
                      <Badge variant="outline" className="text-xs">
                        {event.reminder_type}
                      </Badge>
                    )}
                  </div>
                </div>
              ))
            ) : (
              <div className="flex flex-col items-center justify-center h-full text-center p-8">
                <Calendar className="h-10 w-10 text-slate-300 dark:text-slate-600 mb-4" />
                <h3 className="font-medium text-slate-500 dark:text-slate-400 mb-1">
                  No reminders found
                </h3>
                <p className="text-sm text-slate-400 dark:text-slate-500 mb-4">
                  {showAllEvents 
                    ? "You don't have any reminders yet." 
                    : "You don't have any reminders for today."}
                </p>
                <Button 
                  size="sm" 
                  onClick={() => setIsModalOpen(true)}
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Add Reminder
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>

      <AddEventModal
        isOpen={isModalOpen}
        onOpenChange={setIsModalOpen}
        date={date}
        newEvent={newEvent}
        setNewEvent={setNewEvent}
        handleAddEvent={handleAddEvent}
      />
      
      <EditEventModal
        isOpen={isEditModalOpen}
        onOpenChange={setIsEditModalOpen}
        eventToEdit={eventToEdit}
        setEventToEdit={setEventToEdit}
        handleSaveEvent={handleSaveEvent}
        handleRemoveEvent={handleRemoveEvent}
        handleSnoozeEvent={handleSnoozeEvent}
      />
    </Screen>
  );
}