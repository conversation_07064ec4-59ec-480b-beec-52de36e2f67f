import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { ticketTypes } from "..";
import { formatDateTime } from "@/utils/formatDate";
import { Ticket } from "lucide-react";

interface TicketDetailsSideBarProps {
  ticket: ticketTypes;
}

const TicketDetailsSideBar = ({ ticket }: TicketDetailsSideBarProps) => {
  return (
    <ScrollArea className="">
      <div className="p-4 space-y-4">
        {/* Mobile Header */}
        <div className="">
          <div className="flex items-center flex-wrap gap-3">
            <div className="p-5 rounded-full bg-amber-500">
              <Ticket />
            </div>
            <div className="">
              <p className="text-xs">Ticket Id:</p>
              <h2 className="text-base font-semibold"> #{ticket?.ticket_id}</h2>
              <Badge
                variant="outline"
                // className="ml-2 text-xs text-primary dark:text-primary-light border-primary"
                className={`${
                  ticket?.priority === "low"
                    ? "bg-primary text-white px-3"
                    : ticket?.priority === "medium"
                    ? "bg-yellow-400 text-black px-3"
                    : ticket?.priority === "high"
                    ? "bg-orange-400 text-black px-3"
                    : "bg-destructive text-white"
                } text-xs `}
              >
                Priority: {ticket?.priority}
              </Badge>
            </div>
          </div>

          {/* Expanded Additional Details */}
          <div className="mt-3 space-y-4 text-sm border-t pt-3">
            <div className="space-y-2">
              <h3 className="font-bold text-sm">Ticket Details</h3>
              <div className="grid grid-cols-2  gap-y-2">
                <div className="col-span-2 ">
                  <p className="text-xs text-muted-foreground">Title</p>
                  <p>{ticket?.title || "N/A"}</p>
                </div>
                <div className="col-span-2 ">
                  <p className="text-xs text-muted-foreground">Customer</p>
                  <p>{ticket?.customer_name || "N/A"}</p>
                </div>{" "}
                <div className="col-span-2 ">
                  <p className="text-xs text-muted-foreground">Raised By</p>
                  <p>{ticket?.user_name || "N/A"}</p>
                </div>
                <div className="">
                  <p className="text-xs text-muted-foreground">Category</p>
                  <p>{ticket?.category_name || "N/A"}</p>
                </div>
                <div className="">
                  <p className="text-xs text-muted-foreground">Source</p>
                  <p>{ticket?.source_name || "N/A"}</p>
                </div>
                <div className="">
                  <p className="text-xs text-muted-foreground">Sales</p>
                  <p>{ticket?.sales_number || "N/A"}</p>
                </div>
                <div className="">
                  <p className="text-xs text-muted-foreground">Propect</p>
                  <p>{ticket?.prospect_name || "N/A"}</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Created_on</p>
                  <p>{formatDateTime(ticket?.created_at)}</p>
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Status</p>
                  <Badge
                    className={`${
                      ticket?.status === "open"
                        ? "bg-primary text-white px-3"
                        : ticket?.status === "in_progress"
                        ? "bg-yellow-400 text-black px-3"
                        : ticket?.status === "resolved"
                        ? "bg-gray-500 text-black px-3"
                        : "bg-destructive text-white"
                    } text-xs py-0 rounded-full`}
                  >
                    {ticket?.status}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ScrollArea>
  );
};

export default TicketDetailsSideBar;
