import React, { useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Screen } from '@/app-components/layout/screen';
import MultiStepForm from '@/components/custom/forms/MultiStepForm';
import FormStep from '@/components/custom/forms/FormStep';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { FileText, User, Users, CreditCard, FileCheck, Eye, CheckCircle } from 'lucide-react';
import {
  useCreateOfferLetterMutation,
  useUpdateOfferLetterMutation,
  useCreateOfferLetterIndividualMutation,
  useCreateOfferLetterCompanyMutation,
  useCreateOfferLetterGroupMutation,
  useCreateOfferLetterPartnerMutation,
  useCreateOfferLetterNextOfKinMutation,
  useCreateOfferLetterPaymentsMutation,
  useCreateOfferLetterTermsMutation,
  useCreateOfferLetterReviewMutation,
  useGetBookingDetailsQuery,
  useGetPlotDetailsQuery,
  useGetProjectDetailsQuery,
  useGetOfferLetterPricingQuery
} from './api/offerLetterApi';

// Import step components
import OnboardingStep from './steps/OnboardingStep';
import PersonalDetailsStep from './steps/PersonalDetailsStep';
import NextOfKinStep from './steps/NextOfKinStep';
import PaymentDetailsStep from './steps/PaymentDetailsStep';
import TermsConditionsStep from './steps/TermsConditionsStep';
import ReviewStep from './steps/ReviewStep';
import CompletionStep from './steps/CompletionStep';

// Types for land sales offer letter data
export type CustomerType = 'individual' | 'company' | 'group' | 'partners';

export interface OfferLetterData {
  // Main offer letter info
  id?: number;
  step?: string;
  is_completed?: boolean;
  customer_type?: CustomerType;
  lead_file?: string;
  plot_number?: string;
  booking_id?: string;

  // Plot and Project Details
  project_name?: string;
  project_id?: string;
  plot_size?: string;
  plot_type?: string;
  plot_location?: string;

  // API Entity IDs for linking
  offer_letter_id?: number;
  individual_id?: number;
  company_id?: number;
  group_id?: number;
  partner_id?: number;
  next_of_kin_id?: number;
  payment_plan_id?: number;
  terms_id?: number;
  review_id?: number;

  // Individual Customer Details
  individual?: {
    first_name?: string;
    last_name?: string;
    country_code?: string;
    phone?: string;
    email?: string;
    national_id?: string;
    country?: string;
    city?: string;
    KRA_Pin?: string;
    DOB?: string;
    preferred_contact?: string;
  };

  // Company Details
  company?: {
    company_name?: string;
    company_registration_number?: string;
    company_country_code?: string;
    phone?: string;
    email?: string;
    address?: string;
    country?: string;
    city?: string;
    company_kra?: string;
    preferred_contact?: string;
  };

  // Company Directors
  directors?: Array<{
    director_id?: number;
    first_name?: string;
    last_name?: string;
    country_codes?: string;
    phone?: string;
    email?: string;
    national_id?: string;
  }>;

  // Group Details
  group?: {
    group_name?: string;
    group_code?: string;
    group_phone?: string;
    group_email?: string;
    Group_KRA_PIN?: string;
    Group_country?: string;
    Group_city?: string;
  };

  // Group Members
  group_members?: Array<{
    member_id?: number;
    first_name?: string;
    last_name?: string;
    country_codes?: string;
    phone?: string;
    email?: string;
    national_id?: string;
  }>;

  // Partners Details
  partners?: Array<{
    first_name?: string;
    last_name?: string;
    country_code?: string;
    phone?: string;
    email?: string;
    national_id?: string;
    country?: string;
    city?: string;
    preferred_contact?: string;
  }>;

  // Next of Kin
  next_of_kin?: {
    full_name?: string;
    relationship?: string;
    country_code?: string;
    phone?: string;
    email?: string;
  };

  // Payment Plan
  payment_plan?: {
    plot_no?: string;
    no_of_instalments?: number;
    total_cash_price?: string;
    monthly_installments?: string;
    deposit?: string;
  };

  // Pricing Details
  pricing?: {
    Payment_Model?: string;
    Plot_Type?: string;
    view?: string;
    Deposit?: string;
    Monthly_Interest?: string;
    Project_No?: string;
    Size_Category?: number;
  };

  // Terms & Conditions
  terms_accepted?: boolean;
  privacyAccepted?: boolean;
  terms_content?: string;

  // Review Status
  review_status?: 'Pending' | 'Approved' | 'Rejected';
}

const OfferLetter: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [offerData, setOfferData] = useState<OfferLetterData>({});
  const [isCompleted, setIsCompleted] = useState(false);

  // Clear any potential navigation blockers
  React.useEffect(() => {
    return () => {
      // Cleanup any potential navigation blockers when component unmounts
      window.onbeforeunload = null;
    };
  }, []);

  const [createOfferLetter, { isLoading: isCreating }] = useCreateOfferLetterMutation();
  const [updateOfferLetter, { isLoading: isUpdating }] = useUpdateOfferLetterMutation();
  const [createIndividual] = useCreateOfferLetterIndividualMutation();
  const [createCompany] = useCreateOfferLetterCompanyMutation();
  const [createGroup] = useCreateOfferLetterGroupMutation();
  const [createPartner] = useCreateOfferLetterPartnerMutation();
  const [createNextOfKin] = useCreateOfferLetterNextOfKinMutation();
  const [createPayments] = useCreateOfferLetterPaymentsMutation();
  const [createTerms] = useCreateOfferLetterTermsMutation();
  const [createReview] = useCreateOfferLetterReviewMutation();

  // Get URL parameters
  const plotNumber = searchParams.get('plot_number');
  const bookingId = searchParams.get('booking_id');
  const leadFile = searchParams.get('lead_file');

  // Fetch booking details if booking ID is provided
  const { data: bookingDetails, isLoading: bookingLoading } = useGetBookingDetailsQuery(
    bookingId!,
    { skip: !bookingId }
  );

  // Fetch plot details if plot number is provided
  const { data: plotDetails, isLoading: plotLoading } = useGetPlotDetailsQuery(
    plotNumber!,
    { skip: !plotNumber }
  );

  // Fetch project details if plot details contain project info
  const { data: projectDetails, isLoading: projectLoading } = useGetProjectDetailsQuery(
    plotDetails?.project_id || plotDetails?.project?.toString(),
    { skip: !plotDetails?.project_id && !plotDetails?.project }
  );

  // Fetch pricing data for the plot
  const { data: pricingData, isLoading: pricingLoading } = useGetOfferLetterPricingQuery(
    { page: 1, page_size: 100 },
    { skip: !plotNumber && !plotDetails }
  );

  // Get initial data from URL params and API data
  React.useEffect(() => {
    const initialData: Partial<OfferLetterData> = {};

    // Set basic parameters
    if (plotNumber) initialData.plot_number = plotNumber;
    if (bookingId) initialData.booking_id = bookingId;
    if (leadFile) initialData.lead_file = leadFile;

    // Enhance with booking details
    if (bookingDetails) {
      initialData.booking_id = bookingDetails.booking_id;
      if (bookingDetails.plots) initialData.plot_number = bookingDetails.plots;
      if (bookingDetails.customer_name) {
        // Try to parse customer info if available
        const names = bookingDetails.customer_name.split(' ');
        if (names.length >= 2) {
          initialData.individual = {
            first_name: names[0],
            last_name: names.slice(1).join(' '),
          };
        }
      }
    }

    // Enhance with plot details
    if (plotDetails) {
      initialData.plot_number = plotDetails.plot_no || plotDetails.plot_number;
      initialData.plot_size = plotDetails.size || plotDetails.plot_size;
      initialData.plot_type = plotDetails.plot_type;
      initialData.plot_location = plotDetails.location;
      initialData.project_id = plotDetails.project_id || plotDetails.project?.toString();
    }

    // Enhance with project details
    if (projectDetails) {
      initialData.project_name = projectDetails.project_name || projectDetails.name;
      initialData.project_id = projectDetails.id?.toString() || projectDetails.project_id;
    }

    // Enhance with pricing data
    if (pricingData?.results && plotDetails) {
      // Find matching pricing based on plot characteristics
      const matchingPricing = pricingData.results.find(pricing =>
        pricing.Project_No === (plotDetails.project_id || plotDetails.project?.toString()) ||
        pricing.Plot_Type === plotDetails.plot_type ||
        pricing.Size_Category === plotDetails.size_category
      );

      if (matchingPricing) {
        initialData.pricing = matchingPricing;

        // Set default payment plan based on pricing
        initialData.payment_plan = {
          plot_no: plotDetails.plot_no || plotDetails.plot_number,
          total_cash_price: matchingPricing.Deposit || '0',
          deposit: matchingPricing.Deposit || '0',
        };
      }
    }

    // Only update if we have new data
    if (Object.keys(initialData).length > 0) {
      setOfferData(prev => ({ ...prev, ...initialData }));
    }
  }, [plotNumber, bookingId, leadFile, bookingDetails, plotDetails, projectDetails, pricingData]);

  const handleFormComplete = async (data: Record<string, any>) => {
    try {
      const offerLetterData = data as OfferLetterData;

      // Create the main offer letter first
      const mainOfferLetter = await createOfferLetter({
        step: 'comp', // Shortened to meet 5 character limit
        is_completed: true,
        customer_type: offerLetterData.customer_type,
        lead_file: offerLetterData.lead_file,
        // Only include plot_number if it exists and is valid
        ...(offerLetterData.plot_number && { plot_number: offerLetterData.plot_number }),
        // Only include booking_id if it exists
        ...(offerLetterData.booking_id && { booking_id: offerLetterData.booking_id }),
      }).unwrap();

      console.log('Offer Letter Created:', mainOfferLetter);

      const offerLetterId = mainOfferLetter.id!;

      // Create customer-specific data based on customer type
      let customerEntityId: number | undefined;

      if (offerLetterData.customer_type === 'individual' && offerLetterData.individual) {
        const individual = await createIndividual({
          ...offerLetterData.individual,
          offer_letter: offerLetterId
        }).unwrap();
        customerEntityId = individual.id;
      } else if (offerLetterData.customer_type === 'company' && offerLetterData.company) {
        const company = await createCompany({
          ...offerLetterData.company,
          offer_letter: offerLetterId
        }).unwrap();
        customerEntityId = company.id;
      } else if (offerLetterData.customer_type === 'group' && offerLetterData.group) {
        const group = await createGroup({
          ...offerLetterData.group,
          offer_letter: offerLetterId
        }).unwrap();
        customerEntityId = group.id;
      }

      // Create next of kin
      if (offerLetterData.next_of_kin) {
        await createNextOfKin({
          ...offerLetterData.next_of_kin,
          offer_letter: offerLetterId,
          individual: offerLetterData.customer_type === 'individual' ? customerEntityId : undefined,
        }).unwrap();
      }

      // Create payment plan
      if (offerLetterData.payment_plan) {
        await createPayments({
          ...offerLetterData.payment_plan,
          offer_letter: offerLetterId
        }).unwrap();
      }

      // Create terms and conditions
      if (offerLetterData.terms_content) {
        await createTerms({
          content: offerLetterData.terms_content,
          offer_letter: offerLetterId
        }).unwrap();
      }

      // Create review record
      await createReview({
        status: 'Pending',
        offer_letter: offerLetterId,
        individual: offerLetterData.customer_type === 'individual' ? customerEntityId : undefined,
        company: offerLetterData.customer_type === 'company' ? customerEntityId : undefined,
        group: offerLetterData.customer_type === 'group' ? customerEntityId : undefined,
      }).unwrap();

      // Update the offer data with the created offer letter ID
      const updatedData: OfferLetterData = {
        ...offerLetterData,
        offer_letter_id: offerLetterId,
        id: offerLetterId,
        is_completed: true,
        step: 'comp' // Shortened to meet 5 character limit
      };

      setOfferData(updatedData);
      setIsCompleted(true);
    } catch (error) {
      console.error('Failed to create offer letter:', error);
      // Handle error (show toast, etc.)
    }
  };

  const updateOfferData = (stepData: Partial<OfferLetterData>) => {
    const updatedData = { ...offerData, ...stepData };
    setOfferData(updatedData);
  };

  if (isCompleted) {
    return (
      <Screen>
        <div className="container mx-auto px-4 py-8">
          <CompletionStep data={offerData} />
        </div>
      </Screen>
    );
  }

  return (
    <Screen>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-primary mb-2">Create Your Offer Letter</h1>
            <p className="text-muted-foreground">Follow our simple step-by-step process to create your personalized land purchase offer</p>
            <Badge variant="outline" className="mt-2">
              <FileText className="w-4 h-4 mr-1" />
              Secure & Professional Process
            </Badge>
          </div>

          {/* Multi-step form */}
          <MultiStepForm
            onComplete={handleFormComplete}
            variant="horizontal"
            showStepTitles={true}
            submitLabel={isCreating ? "Submitting..." : "Submit Land Purchase Offer"}
            className="space-y-6"
            allowSkipSteps={false}
            initialData={offerData}
          >
            <FormStep
              title="Onboarding"
              description="Welcome and customer type selection"
            >
              <OnboardingStep
                data={offerData}
                onUpdate={updateOfferData}
              />
            </FormStep>

            <FormStep
              title="Customer Details"
              description="Buyer information based on customer type"
            >
              <PersonalDetailsStep
                data={offerData}
                onUpdate={updateOfferData}
              />
            </FormStep>

            <FormStep
              title="Next of Kin"
              description="Emergency contact information"
            >
              <NextOfKinStep
                data={offerData}
                onUpdate={updateOfferData}
              />
            </FormStep>

            <FormStep
              title="Payment Plan"
              description="Choose your preferred payment option and configure details"
            >
              <PaymentDetailsStep
                data={offerData}
                onUpdate={updateOfferData}
              />
            </FormStep>

            <FormStep
              title="Terms & Conditions"
              description="Review and accept purchase terms"
            >
              <TermsConditionsStep
                data={offerData}
                onUpdate={updateOfferData}
              />
            </FormStep>

            <FormStep
              title="Review"
              description="Review all information before submission"
            >
              <ReviewStep
                data={offerData}
                onUpdate={updateOfferData}
              />
            </FormStep>
          </MultiStepForm>
        </div>
      </div>
    </Screen>
  );
};

export default OfferLetter;
