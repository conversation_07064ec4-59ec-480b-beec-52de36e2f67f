import BaseModal from "@/components/custom/modals/BaseModal";
import { Button } from "@/components/ui/button";

import { useEffect, useState } from "react";
import { toast } from "sonner";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import {
  useGetProjectsQuery,
  useUpdateProjectMutation,
} from "@/redux/slices/projects";
import { projectTypes } from "@/types/project";
import { DataTable } from "@/components/custom/tables/Table1";
import { ColumnDef } from "@tanstack/react-table";

type Props = {
  openModal: boolean;
  setOpenModal: (e: boolean) => void;
  setSearchValue: (e: string) => void;
  searchValue: string;
  totalItems?: number;
};

const ProjectManagementModal = ({
  openModal,
  setOpenModal,
  searchValue,
  setSearchValue,
}: Props) => {
  const [updateProject, { isLoading: updating }] = useUpdateProjectMutation();
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const { data, isLoading: projectsLoading } = useGetProjectsQuery({
    search: searchValue,
    page: currentPage,
    page_size: itemsPerPage,
    visibiliy: "SHOW", // Only show visible projects
  });

  // submit handler.

  const handleUpdateStatus = async (product: projectTypes, status: string) => {
    const formData = {
      projectId: product?.projectId,
      visibiliy: status,
    };

    try {
      const res = await updateProject(formData).unwrap();
      if (res) {
        toast.success("Updated successfully");
        // setOpenModal(false);
      }
    } catch (error: any) {
      console.log("Error: ", error);
      if (error?.data) {
        toast.error(`${error?.data?.error}`);
      } else {
        toast.error(`${error?.error}`);
      }
      return;
    }
  };

  const columns: ColumnDef<projectTypes>[] = [
    {
      accessorKey: "name",
      header: "Project.",
      cell: (info) => info.getValue(),
      enableColumnFilter: false,
    },
    {
      accessorKey: "priority",
      header: "Priority",
      cell: (info) => info.getValue(),
      enableColumnFilter: false,
    },
    {
      accessorKey: "visibiliy",
      header: "Status",
      cell: (info) => (
        <span
          className={`${
            info?.getValue() === "SHOW"
              ? "bg-primary text-white"
              : "bg-destructive text-white"
          } text-center px-2 pt-1 pb-1.5 rounded-full`}
        >
          {info?.getValue() as string}
        </span>
      ),
      enableColumnFilter: false,
    },
    {
      accessorKey: "action",
      header: "Action",
      cell: ({ row }) => {
        const a = row.original;
        return a?.visibiliy === "SHOW" ? (
          <>
            {updating ? (
              <SpinnerTemp type="spinner-double" size="sm" />
            ) : (
              <Button
                variant="destructive"
                onClick={() => handleUpdateStatus(a, "HIDE")}
              >
                Hide
              </Button>
            )}
          </>
        ) : (
          <>
            {updating ? (
              <SpinnerTemp type="spinner-double" size="sm" />
            ) : (
              <Button
                variant="default"
                onClick={() => handleUpdateStatus(a, "SHOW")}
              >
                Show
              </Button>
            )}
          </>
        );
      },
      enableColumnFilter: false,
      enableSorting: false,
    },
  ];

  return (
    <BaseModal
      isOpen={openModal}
      onOpenChange={setOpenModal}
      size="xl"
      title="Project Visibility Management"
      description="Manage projec visibility."
      // footer={<Button onClick={() => setSbModal(false)}>Close</Button>}
    >
      <DataTable<projectTypes>
        data={data?.data?.results || []}
        columns={columns}
        title="Projects"
        enableToolbar={true}
        enableExportToExcel={true}
        enablePagination={true}
        enablePrintPdf={false}
        enableFullScreenToggle={false}
        enableColumnControl={false}
        enableColumnFilters={true}
        enableSorting={true}
        enableSelectColumn={true}
        enableSelectToolbar={false}
        enableSelectToolbarButtonExportToExcel={true}
        enableSelectToolbarButtonPrintToPdf={true}
        containerClassName=""
        tableClassName=""
        tHeadClassName="bg-secondary"
        tHeadCellsClassName="text-primary"
        tBodyClassName=""
        tBodyTrClassName=""
        tBodyCellsClassName=""
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        itemsPerPage={itemsPerPage}
        setItemsPerPage={setItemsPerPage}
        totalItems={data?.data?.total_data || 0}
        searchInput={
          <input
            value={searchValue}
            name="searchValue"
            type="search"
            onChange={(e) => setSearchValue(e.target.value)}
            className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
            placeholder="Search prospects..."
          />
        }
      />
    </BaseModal>
  );
};

export default ProjectManagementModal;
