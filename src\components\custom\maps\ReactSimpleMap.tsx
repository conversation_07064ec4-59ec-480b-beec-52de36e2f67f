import { memo } from "react";
import {
  ComposableMap,
  Geographies,
  Geography,
  ZoomableGroup,
} from "react-simple-maps";

// Define Props Interface
interface TheMapProps {
  setTooltipContent: (content: string) => void;
  data: { [key: string]: string };
  map: any;
}

const TheMap = ({ setTooltipContent, data, map }: TheMapProps) => {
  // Function to handle tooltip content
  const handleSetTooltipContent = (name: string) => {
    setTooltipContent(name);
  };

  return (
    <div data-tip="">
      <ComposableMap>
        <ZoomableGroup>
          <Geographies geography={map}>
            {({ geographies }) =>
              geographies.map((geo) => {
                const countryCode = geo.id;
                const fillColor = data[countryCode] || "#D6D6DA"; // Default color
                return (
                  <Geography
                    key={geo.rsmKey}
                    geography={geo}
                    data-tooltip-id="map1-id"
                    data-tooltip-content={`${geo.properties.name} (${countryCode})`}
                    onMouseEnter={() =>
                      handleSetTooltipContent(geo.properties.name)
                    }
                    onMouseLeave={() => setTooltipContent("")}
                    style={{
                      default: { fill: fillColor, outline: "none" },
                      hover: { fill: "#F53009", outline: "none" },
                      pressed: { fill: "#E42009", outline: "none" },
                    }}
                  />
                );
              })
            }
          </Geographies>
        </ZoomableGroup>
      </ComposableMap>
    </div>
  );
};

export default memo(TheMap);
