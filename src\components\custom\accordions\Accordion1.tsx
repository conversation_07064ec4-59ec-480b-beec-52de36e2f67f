import React from 'react';
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent 
} from '@/components/ui/accordion';

export interface AccordionItemProps {
  value: string;
  trigger: React.ReactNode;
  content: React.ReactNode;
}

// Base props shared by both accordion types
interface BaseAccordionProps {
  items: AccordionItemProps[];
  className?: string;
  triggerClassName?: string;
  contentClassName?: string;
}

// Props specific to single type accordion
interface SingleAccordionProps extends BaseAccordionProps {
  type: 'single';
  collapsible?: boolean;
  defaultValue?: string;
  onValueChange?: (value: string) => void;
}

// Props specific to multiple type accordion
interface MultipleAccordionProps extends BaseAccordionProps {
  type: 'multiple';
  defaultValue?: string[];
  onValueChange?: (value: string[]) => void;
}

// Combined type with either SingleAccordionProps or MultipleAccordionProps
export type DynamicAccordionProps = SingleAccordionProps | MultipleAccordionProps;

const Accordion1: React.FC<DynamicAccordionProps> = (props) => {
  const {
    items,
    className = '',
    triggerClassName = 'cursor-pointer hover:no-underline',
    contentClassName = '',
  } = props;

  // We need to conditionally render the Accordion based on the type
  if (props.type === 'single') {
    return (
      <Accordion 
        type="single"
        collapsible={props.collapsible}
        defaultValue={props.defaultValue}
        className={className}
        onValueChange={props.onValueChange}
      >
        {items.map((item) => (
          <AccordionItem key={item.value} value={item.value}>
            <AccordionTrigger className={triggerClassName}>
              {item.trigger}
            </AccordionTrigger>
            <AccordionContent className={contentClassName}>
              {item.content}
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    );
  } else {
    return (
      <Accordion 
        type="multiple"
        defaultValue={props.defaultValue}
        className={className}
        onValueChange={props.onValueChange}
      >
        {items.map((item) => (
          <AccordionItem key={item.value} value={item.value}>
            <AccordionTrigger className={triggerClassName}>
              {item.trigger}
            </AccordionTrigger>
            <AccordionContent className={contentClassName}>
              {item.content}
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    );
  }
};

export default Accordion1;