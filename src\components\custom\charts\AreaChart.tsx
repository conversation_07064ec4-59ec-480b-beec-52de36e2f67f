import {
  AreaChart as Recharts<PERSON>rea<PERSON>hart,
  Area,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer
} from 'recharts';

type Props = {
  data: {
    name: string;
    [key: string]: any;
  }[];
  dataKey: string;
  color?: string;
  showGrid?: boolean;
  showTooltip?: boolean;
  showLegend?: boolean;
};

const AreaChart = ({
  data,
  dataKey,
  color = "primary",
  showGrid = true,
  showTooltip = true,
  showLegend = true
}: Props) => {
  return (
    <div className="w-full h-64">
      <ResponsiveContainer width="100%" height="100%">
        <RechartsAreaChart
          data={data}
          margin={{
            top: 10,
            right: 30,
            left: 0,
            bottom: 0,
          }}
        >
          {showGrid && <CartesianGrid strokeDasharray="3 3" className="opacity-20" />}
          <XAxis 
            dataKey="name" 
            axisLine={{ stroke: 'hsl(var(--muted-foreground))', strokeWidth: 1 }}
            tick={{ fill: 'hsl(var(--foreground))', fontSize: 12 }}
          />
          <YAxis 
            axisLine={{ stroke: 'hsl(var(--muted-foreground))', strokeWidth: 1 }}
            tick={{ fill: 'hsl(var(--foreground))', fontSize: 12 }}
          />
          {showTooltip && (
            <Tooltip 
              contentStyle={{ 
                backgroundColor: 'hsl(var(--card))', 
                border: '1px solid hsl(var(--border))',
                borderRadius: '0.5rem',
                color: 'hsl(var(--card-foreground))'
              }} 
            />
          )}
          {showLegend && <Legend />}
          <Area
            type="monotone"
            dataKey={dataKey}
            stroke={`hsl(var(--${color}))`}
            strokeWidth={2}
            fill={`hsl(var(--${color}))`}
            fillOpacity={0.2}
            activeDot={{ r: 6, strokeWidth: 2 }}
            animationDuration={1500}
          />
        </RechartsAreaChart>
      </ResponsiveContainer>
    </div>
  );
};

export default AreaChart; 