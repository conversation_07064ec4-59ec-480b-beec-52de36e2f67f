import React, { useState, useEffect } from "react";
import { Home } from "lucide-react";
import { PrimaryButton } from "../buttons/buttons";


const Lock: React.FC = () => {
  const [isLoaded, setIsLoaded] = useState<boolean>(false);
  const [isError, setIsError] = useState<boolean>(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoaded(true);
    }, 800);

    return () => clearTimeout(timer);
  }, []);

  const notFoundSvgPath = "500.svg";

  const renderFallbackSVG = (): JSX.Element => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 500 500"
      className="w-full h-64"
    >
      <rect width="500" height="500" fill="#f3f4f6" opacity="0.3" />
      <text
        x="250"
        y="270"
        fontSize="100"
        fontWeight="bold"
        textAnchor="middle"
        fill="#4b5563"
      >
        404
      </text>
    </svg>
  );

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50 px-4 py-12">
      <div className="max-w-md w-full space-y-8 text-center">
        <div className="w-full h-64 relative">
          {!isLoaded && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-12 h-12 border-4 border-indigo-500 border-t-transparent rounded-full animate-spin"></div>
            </div>
          )}

          {isLoaded && !isError ? (
            <img
              src={notFoundSvgPath}
              alt="404 Page Not Found Illustration"
              className="w-full h-full object-contain"
              onError={() => setIsError(true)}
            />
          ) : isLoaded && isError ? (
            renderFallbackSVG()
          ) : null}
        </div>

        <h1 className="text-4xl font-extrabold text-gray-900 tracking-tight sm:text-5xl">
          Page not found
        </h1>

        <p className="mt-2 text-lg text-gray-600">
          We couldn't find the page you're looking for. Please check the URL or go back home.
        </p>

        <div >
          <a
            href="/"
            
          >
            <PrimaryButton variant="primary">
              Back
            </PrimaryButton>
          </a>
        </div>
      </div>
    </div>
  );
};

export default Lock;
