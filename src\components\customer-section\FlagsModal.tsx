import React from "react";
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetDes<PERSON>,
  She<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>rigger,
} from "@/components/ui/sheet";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Trash2, 
  Edit, 
  Eye, 
  CheckCircle, 
  AlertTriangle,
  Flag,
  User,
  Calendar,
  FileText
} from "lucide-react";
import Multiselect from "@/components/custom/forms/Multiselect";
import { useGetUsersQuery } from "@/redux/slices/user";

interface FlagsModalsProps {
  activeModal: string | null;
  onCloseModal: () => void;
  onOpenModal: (modalName: string) => void;
  flagForm: any;
  onFlagFormChange: (field: string, value: any) => void;
  formErrors: Record<string, string>;
  successMessage: string;
  editingFlag: any;
  onOpenEditFlag: (flag: any) => void;
  onCreateFlag: () => void;
  onUpdateFlag: () => void;
  onDeleteFlag: (flagId: string, createdBy: string) => void;
  onResolveFlag: (flagId: string, resolutionNotes: string) => void;
  flagItems: any[];
  currentUser?: any;
  isCreating: boolean;
  isDeleting: boolean;
  renderFlagTypeBadge: (type: string) => JSX.Element;
  renderSeverityBadge: (severity: string) => JSX.Element;
  renderStatusBadge: (status: string) => JSX.Element;
  formatDate: (date: string) => string;
}

const FlagsModals: React.FC<FlagsModalsProps> = ({
  activeModal,
  onCloseModal,
  onOpenModal,
  flagForm,
  onFlagFormChange,
  formErrors,
  successMessage,
  editingFlag,
  onOpenEditFlag,
  onCreateFlag,
  onUpdateFlag,
  onDeleteFlag,
  onResolveFlag,
  flagItems,
  currentUser,
  isCreating,
  isDeleting,
  renderFlagTypeBadge,
  renderSeverityBadge,
  renderStatusBadge,
  formatDate,
}) => {
  const [resolutionNotes, setResolutionNotes] = React.useState("");
  const [selectedFlagForResolve, setSelectedFlagForResolve] = React.useState<any>(null);

  // User fetching state
  const { data: usersData, isLoading: usersLoading } = useGetUsersQuery({});
  const [userOptions, setUserOptions] = React.useState<{ value: string; label: string }[]>([]);
  const [selectedUser, setSelectedUser] = React.useState<{ label: string; value: string } | null>(null);

  // Process users data for the multiselect
  React.useEffect(() => {
    if (usersData?.data?.results && usersData.data.results.length > 0) {
      const options = usersData.data.results.map((user: any) => ({
        label: `${user?.fullnames} (${user?.employee_no})`,
        value: user?.employee_no,
      }));
      setUserOptions(options);
    }
  }, [usersData]);

  // Set selected user when editing a flag
  React.useEffect(() => {
    if (editingFlag && activeModal === "editFlag" && userOptions.length > 0) {
      const assignedUser = userOptions.find(option => option.value === flagForm.assigned_to);
      setSelectedUser(assignedUser || null);
    }
  }, [editingFlag, flagForm.assigned_to, userOptions, activeModal]);

  // Handle user selection
  const handleUserSelection = (user: { label: string; value: string } | null) => {
    setSelectedUser(user);
    onFlagFormChange('assigned_to', user?.value || '');
  };

  // Reset selected user when modal closes or changes
  React.useEffect(() => {
    if (!activeModal || activeModal === "flags") {
      setSelectedUser(null);
    }
  }, [activeModal]);

  const handleResolveFlag = () => {
    if (selectedFlagForResolve && resolutionNotes.trim()) {
      onResolveFlag(selectedFlagForResolve.flag_id, resolutionNotes);
      setResolutionNotes("");
      setSelectedFlagForResolve(null);
      onCloseModal();
    }
  };

  const openResolveModal = (flag: any) => {
    setSelectedFlagForResolve(flag);
    setResolutionNotes(flag.resolution_notes || "");
    onOpenModal("resolveFlag");
  };

  const flagTypeOptions = [
    "Customer Issue",
    "Data Quality", 
    "Security",
    "Compliance",
    "Performance",
    "Content"
  ];

  const severityOptions = [
    "Info",
    "Warning", 
    "Error",
    "Critical"
  ];

  const statusOptions = [
    "Active",
    "Resolved",
    "Dismissed"
  ];

  return (
    <>
      {/* Flags List Sheet */}
      <Sheet open={activeModal === "flags"} onOpenChange={onCloseModal}>
        <SheetContent className="w-[800px] sm:max-w-[800px]">
          <SheetHeader>
            <SheetTitle className="flex items-center gap-2">
              <Flag className="h-5 w-5" />
              Flags ({flagItems.length})
            </SheetTitle>
            <SheetDescription>
              Manage flags for this entity
            </SheetDescription>
          </SheetHeader>
          
          <div className="mt-6">
            <div className="flex justify-between items-center mb-4">
              <Button 
                onClick={() => onOpenModal("createFlag")}
                className="flex items-center gap-2"
              >
                <Flag className="h-4 w-4" />
                Create Flag
              </Button>
            </div>

            <ScrollArea className="h-[calc(100vh-200px)]">
              <div className="space-y-4">
                {flagItems.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <Flag className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No flags found</p>
                  </div>
                ) : (
                  flagItems.map((flag) => (
                    <Card key={flag.flag_id} className="hover:shadow-md transition-shadow">
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <div className="space-y-1">
                            <CardTitle className="text-lg">{flag.title}</CardTitle>
                            <div className="flex items-center gap-2 text-sm text-gray-500">
                              <User className="h-3 w-3" />
                              <span>{flag.created_by_name || "Unknown"}</span>
                              <span>•</span>
                              <Calendar className="h-3 w-3" />
                              <span>{formatDate(flag.created_at)}</span>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => onOpenEditFlag(flag)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            {flag.status === "Active" && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => openResolveModal(flag)}
                              >
                                <CheckCircle className="h-4 w-4" />
                              </Button>
                            )}
                            {currentUser?.id === flag.created_by && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => onDeleteFlag(flag.flag_id, flag.created_by)}
                                disabled={isDeleting}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="space-y-3">
                          <div className="flex items-center gap-2 flex-wrap">
                            {renderFlagTypeBadge(flag.flag_type)}
                            {renderSeverityBadge(flag.severity)}
                            {renderStatusBadge(flag.status)}
                          </div>
                          
                          <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2">
                            {flag.description}
                          </p>
                          
                          {flag.assigned_to_name && (
                            <div className="flex items-center gap-2 text-sm text-gray-500">
                              <User className="h-3 w-3" />
                              <span>Assigned to: {flag.assigned_to_name}</span>
                            </div>
                          )}
                          
                          {flag.resolution_notes && (
                            <div className="mt-2 p-2 bg-green-50 dark:bg-green-900/20 rounded text-sm">
                              <div className="flex items-center gap-1 text-green-700 dark:text-green-300 mb-1">
                                <CheckCircle className="h-3 w-3" />
                                <span className="font-medium">Resolution Notes:</span>
                              </div>
                              <p className="text-green-600 dark:text-green-400">{flag.resolution_notes}</p>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </ScrollArea>
          </div>
        </SheetContent>
      </Sheet>

      {/* Create Flag Sheet */}
      <Sheet open={activeModal === "createFlag"} onOpenChange={onCloseModal}>
        <SheetContent>
          <SheetHeader>
            <SheetTitle>Create New Flag</SheetTitle>
            <SheetDescription>
              Create a new flag for this entity
            </SheetDescription>
          </SheetHeader>
          
          <div className="mt-6 space-y-4">
            {successMessage && (
              <Alert className="bg-green-50 border-green-200 text-green-800">
                <AlertDescription>{successMessage}</AlertDescription>
              </Alert>
            )}

            {formErrors.submit && (
              <Alert className="bg-red-50 border-red-200 text-red-800">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>{formErrors.submit}</AlertDescription>
              </Alert>
            )}

            <ScrollArea className="h-[calc(100vh-200px)]">
              <div className="space-y-4 pr-4">
                <div className="space-y-2">
                  <Label htmlFor="flag_type">Flag Type *</Label>
                  <Select
                    value={flagForm.flag_type}
                    onValueChange={(value) => onFlagFormChange("flag_type", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select flag type" />
                    </SelectTrigger>
                    <SelectContent>
                      {flagTypeOptions.map((type) => (
                        <SelectItem key={type} value={type}>
                          {type}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {formErrors.flag_type && (
                    <p className="text-sm text-red-600">{formErrors.flag_type}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="title">Title *</Label>
                  <Input
                    id="title"
                    value={flagForm.title}
                    onChange={(e) => onFlagFormChange("title", e.target.value)}
                    placeholder="Enter flag title"
                  />
                  {formErrors.title && (
                    <p className="text-sm text-red-600">{formErrors.title}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description *</Label>
                  <Textarea
                    id="description"
                    value={flagForm.description}
                    onChange={(e) => onFlagFormChange("description", e.target.value)}
                    placeholder="Describe the flag..."
                    rows={4}
                  />
                  {formErrors.description && (
                    <p className="text-sm text-red-600">{formErrors.description}</p>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="severity">Severity *</Label>
                    <Select
                      value={flagForm.severity}
                      onValueChange={(value) => onFlagFormChange("severity", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select severity" />
                      </SelectTrigger>
                      <SelectContent>
                        {severityOptions.map((severity) => (
                          <SelectItem key={severity} value={severity}>
                            {severity}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {formErrors.severity && (
                      <p className="text-sm text-red-600">{formErrors.severity}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="status">Status</Label>
                    <Select
                      value={flagForm.status}
                      onValueChange={(value) => onFlagFormChange("status", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        {statusOptions.map((status) => (
                          <SelectItem key={status} value={status}>
                            {status}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Assigned To</Label>
                  <Multiselect
                    value={selectedUser}
                    data={userOptions}
                    setValue={handleUserSelection}
                    loading={usersLoading}
                    isClearable={true}
                    isDisabled={false}
                    isMultiple={false}
                    isSearchable={true}
                  />
                  {formErrors.assigned_to && (
                    <p className="text-sm text-red-600">{formErrors.assigned_to}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="resolution_notes">Resolution Notes</Label>
                  <Textarea
                    id="resolution_notes"
                    value={flagForm.resolution_notes}
                    onChange={(e) => onFlagFormChange("resolution_notes", e.target.value)}
                    placeholder="Enter resolution notes (if applicable)..."
                    rows={3}
                  />
                </div>

                <Separator />

                <div className="flex justify-end gap-2 pt-4">
                  <Button variant="outline" onClick={onCloseModal}>
                    Cancel
                  </Button>
                  <Button onClick={onCreateFlag} disabled={isCreating}>
                    {isCreating ? "Creating..." : "Create Flag"}
                  </Button>
                </div>
              </div>
            </ScrollArea>
          </div>
        </SheetContent>
      </Sheet>

      {/* Edit/View Flag Sheet */}
      <Sheet open={activeModal === "editFlag"} onOpenChange={onCloseModal}>
        <SheetContent>
          <SheetHeader>
            <SheetTitle>Edit Flag</SheetTitle>
            <SheetDescription>
              Update flag details
            </SheetDescription>
          </SheetHeader>
          
          <div className="mt-6 space-y-4">
            {successMessage && (
              <Alert className="bg-green-50 border-green-200 text-green-800">
                <AlertDescription>{successMessage}</AlertDescription>
              </Alert>
            )}

            {formErrors.submit && (
              <Alert className="bg-red-50 border-red-200 text-red-800">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>{formErrors.submit}</AlertDescription>
              </Alert>
            )}

            <ScrollArea className="h-[calc(100vh-200px)]">
              <div className="space-y-4 pr-4">
                <div className="space-y-2">
                  <Label htmlFor="flag_type">Flag Type *</Label>
                  <Select
                    value={flagForm.flag_type}
                    onValueChange={(value) => onFlagFormChange("flag_type", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select flag type" />
                    </SelectTrigger>
                    <SelectContent>
                      {flagTypeOptions.map((type) => (
                        <SelectItem key={type} value={type}>
                          {type}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {formErrors.flag_type && (
                    <p className="text-sm text-red-600">{formErrors.flag_type}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="title">Title *</Label>
                  <Input
                    id="title"
                    value={flagForm.title}
                    onChange={(e) => onFlagFormChange("title", e.target.value)}
                    placeholder="Enter flag title"
                  />
                  {formErrors.title && (
                    <p className="text-sm text-red-600">{formErrors.title}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description *</Label>
                  <Textarea
                    id="description"
                    value={flagForm.description}
                    onChange={(e) => onFlagFormChange("description", e.target.value)}
                    placeholder="Describe the flag..."
                    rows={4}
                  />
                  {formErrors.description && (
                    <p className="text-sm text-red-600">{formErrors.description}</p>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="severity">Severity *</Label>
                    <Select
                      value={flagForm.severity}
                      onValueChange={(value) => onFlagFormChange("severity", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select severity" />
                      </SelectTrigger>
                      <SelectContent>
                        {severityOptions.map((severity) => (
                          <SelectItem key={severity} value={severity}>
                            {severity}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {formErrors.severity && (
                      <p className="text-sm text-red-600">{formErrors.severity}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="status">Status</Label>
                    <Select
                      value={flagForm.status}
                      onValueChange={(value) => onFlagFormChange("status", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        {statusOptions.map((status) => (
                          <SelectItem key={status} value={status}>
                            {status}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Assigned To</Label>
                  <Multiselect
                    value={selectedUser}
                    data={userOptions}
                    setValue={handleUserSelection}
                    loading={usersLoading}
                    isClearable={true}
                    isDisabled={false}
                    isMultiple={false}
                    isSearchable={true}
                  />
                  {formErrors.assigned_to && (
                    <p className="text-sm text-red-600">{formErrors.assigned_to}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="resolution_notes">Resolution Notes</Label>
                  <Textarea
                    id="resolution_notes"
                    value={flagForm.resolution_notes}
                    onChange={(e) => onFlagFormChange("resolution_notes", e.target.value)}
                    placeholder="Enter resolution notes (if applicable)..."
                    rows={3}
                  />
                </div>

                <Separator />

                <div className="flex justify-end gap-2 pt-4">
                  <Button variant="outline" onClick={onCloseModal}>
                    Cancel
                  </Button>
                  <Button onClick={onUpdateFlag} disabled={isCreating}>
                    {isCreating ? "Updating..." : "Update Flag"}
                  </Button>
                </div>
              </div>
            </ScrollArea>
          </div>
        </SheetContent>
      </Sheet>

      {/* Resolve Flag Sheet */}
      <Sheet open={activeModal === "resolveFlag"} onOpenChange={onCloseModal}>
        <SheetContent>
          <SheetHeader>
            <SheetTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              Resolve Flag
            </SheetTitle>
            <SheetDescription>
              Add resolution notes and resolve this flag
            </SheetDescription>
          </SheetHeader>
          
          <div className="mt-6 space-y-4">
            {selectedFlagForResolve && (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">{selectedFlagForResolve.title}</CardTitle>
                  <div className="flex items-center gap-2 flex-wrap">
                    {renderFlagTypeBadge(selectedFlagForResolve.flag_type)}
                    {renderSeverityBadge(selectedFlagForResolve.severity)}
                    {renderStatusBadge(selectedFlagForResolve.status)}
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    {selectedFlagForResolve.description}
                  </p>
                </CardContent>
              </Card>
            )}

            <div className="space-y-2">
              <Label htmlFor="resolution_notes">Resolution Notes *</Label>
              <Textarea
                id="resolution_notes"
                value={resolutionNotes}
                onChange={(e) => setResolutionNotes(e.target.value)}
                placeholder="Describe how this flag was resolved..."
                rows={4}
              />
              <p className="text-sm text-gray-500">
                Provide detailed notes about how this flag was resolved.
              </p>
            </div>

            <Separator />

            <div className="flex justify-end gap-2 pt-4">
              <Button variant="outline" onClick={onCloseModal}>
                Cancel
              </Button>
              <Button 
                onClick={handleResolveFlag} 
                disabled={!resolutionNotes.trim()}
                className="bg-green-600 hover:bg-green-700"
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Resolve Flag
              </Button>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </>
  );
};

export default FlagsModals;