import { useState, useEffect, useCallback } from "react";
import { motion } from "framer-motion";
import { Screen } from "@/app-components/layout/screen";
import AddComplaintModal from "./CreateComplient"; // Fixed typo in import
import ViewComplaintModal from "./viewmodal"; // Standardized naming
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import { DataTable } from "@/components/custom/tables/Table1";
import { Eye, Search, PlusCircle, MessageCircle, AlertCircle, RefreshCw } from "lucide-react";
import { Pagination, PaginationContent, PaginationItem, PaginationNext, PaginationPrevious } from "@/components/ui/pagination";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { useLazyGetComplaintsQuery, useAddComplaintMutation } from "@/redux/slices/services";
import React from "react";

interface Complaint {
  complaint_id: string;
  title: string;
  category: string;
  priority: string;
  status: string;
  created_at: string;
  entity_type: string;
  entity_name: string;
  description?: string;
  related_entity?: { id: number; name: string; type: string } | null;
}

interface FormData {
  complaint_id: string;
  title: string;
  description: string;
  category: string;
  priority: string;
}

const PAGE_SIZE = 4;
const STATUS_FILTERS = ["All", "Open", "In Progress", "Resolved", "Closed", "Escalated"] as const;
type StatusFilter = typeof STATUS_FILTERS[number];

const getPriorityVariant = (priority: string): "default" | "success" | "warning" | "destructive" => {
  switch (priority) {
    case "High":
    case "Urgent":
      return "destructive";
    case "Medium":
      return "warning";
    case "Low":
      return "success";
    default:
      return "default";
  }
};

const getStatusVariant = (status: string): "default" | "success" | "warning" | "secondary" => {
  switch (status) {
    case "Resolved":
    case "Closed":
      return "success";
    case "In Progress":
    case "Escalated":
      return "secondary";
    case "Open":
      return "warning";
    default:
      return "default";
  }
};

// Helper function to get user-friendly error messages
const getUserFriendlyErrorMessage = (error: any): string => {
  // Network errors
  if (error?.status === 'FETCH_ERROR' || error?.name === 'NetworkError') {
    return "Unable to connect to the server. Please check your internet connection and try again.";
  }
  
  // Authentication errors
  if (error?.status === 401 || error?.status === 403) {
    return "You don't have permission to access this information. Please log in again.";
  }
  
  // Server errors
  if (error?.status >= 500 && error?.status < 600) {
    return "Our servers are experiencing issues. Please try again in a few moments.";
  }
  
  // Client errors
  if (error?.status >= 400 && error?.status < 500) {
    return "There was an issue with your request. Please try again.";
  }
  
  // Timeout errors
  if (error?.code === 'ETIMEDOUT' || error?.message?.includes('timeout')) {
    return "The request is taking too long. Please try again.";
  }
  
  // Generic fallback
  return "Something went wrong. Please try again or contact support if the problem persists.";
};

const ErrorBoundary: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [hasError, setHasError] = useState(false);

  React.useEffect(() => {
    const handleError = () => setHasError(true);
    window.addEventListener("error", handleError);
    return () => window.removeEventListener("error", handleError);
  }, []);

  if (hasError) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700">
        <AlertCircle className="h-12 w-12 text-red-400 mb-4" />
        <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Something went wrong</h3>
        <p className="text-gray-600 dark:text-gray-400 mb-4">We're having trouble loading this page. Please refresh and try again.</p>
        <PrimaryButton 
          onClick={() => window.location.reload()} 
          className="flex items-center gap-2"
        >
          <RefreshCw className="h-4 w-4" />
          Refresh Page
        </PrimaryButton>
      </div>
    );
  }
  return <>{children}</>;
};

export default function ComplaintsCard() {
  const [trigger, { data: apiResponse, isLoading, isError, error, isFetching }] = useLazyGetComplaintsQuery();
  const [addComplaint] = useAddComplaintMutation();
  const { toast } = useToast();
  const [complaints, setComplaints] = useState<Complaint[]>([]);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [selectedComplaint, setSelectedComplaint] = useState<FormData>({
    complaint_id: "",
    title: "",
    description: "",
    category: "Technical",
    priority: "Medium",
  });
  const [page, setPage] = useState(1);
  const [statusFilter, setStatusFilter] = useState<StatusFilter>("All");
  const [searchQuery, setSearchQuery] = useState("");
  const [hasDataError, setHasDataError] = useState(false);

  const fetchComplaints = useCallback(() => {
    setHasDataError(false);
    
    trigger({ page, status: statusFilter === "All" ? undefined : statusFilter, search: searchQuery || undefined })
      .unwrap()
      .then(response => {
        // Success - data will be handled in useEffect
      })
      .catch((err) => {
        setHasDataError(true);
        const friendlyMessage = getUserFriendlyErrorMessage(err);
        toast({
          title: "Unable to load complaints",
          description: friendlyMessage,
          variant: "destructive",
          duration: 5000,
        });
      });
  }, [trigger, page, statusFilter, searchQuery, toast]);

  useEffect(() => {
    fetchComplaints();
  }, [fetchComplaints]);

  useEffect(() => {
    // Check multiple possible response structures
    let results = null;
    
    if (apiResponse?.data?.results) {
      results = apiResponse.data.results;
    } else if (apiResponse?.results) {
      results = apiResponse.results;
    } else if (Array.isArray(apiResponse)) {
      results = apiResponse;
    }
    
    if (results && Array.isArray(results) && results.length > 0) {
      const transformedComplaints: Complaint[] = results.map((complaint: any) => ({
        complaint_id: complaint.complaint_id,
        title: complaint.title,
        category: complaint.category,
        priority: complaint.priority,
        status: complaint.status,
        created_at: complaint.created_at,
        entity_type: complaint.entity_type,
        entity_name: complaint.entity_name,
        description: complaint.description || "No description provided",
        related_entity: complaint.related_entity,
      }));
      setComplaints(transformedComplaints);
      setHasDataError(false);
    } else {
      if (!isLoading && !isFetching && !hasDataError) {
        setComplaints([]);
      }
    }
  }, [apiResponse, isLoading, isFetching, hasDataError]);

  const handleAddModalSubmit = async (formData: FormData) => {
    // Client-side validation with user-friendly messages
    if (formData.complaint_id.length < 1 || formData.complaint_id.length > 50) {
      toast({
        title: "Invalid Complaint ID",
        description: "Please enter a complaint ID between 1 and 50 characters.",
        variant: "destructive",
        duration: 3000,
      });
      return;
    }
    if (formData.title.length < 1 || formData.title.length > 255) {
      toast({
        title: "Invalid Title",
        description: "Please enter a title between 1 and 255 characters.",
        variant: "destructive",
        duration: 3000,
      });
      return;
    }
    if (!formData.description || formData.description.trim().length === 0) {
      toast({
        title: "Description Required",
        description: "Please provide a description for the complaint.",
        variant: "destructive",
        duration: 3000,
      });
      return;
    }

    try {
      await addComplaint(formData).unwrap();
      setIsAddModalOpen(false);
      setPage(1);
      fetchComplaints();
      toast({
        title: "Complaint Created",
        description: `Your complaint "${formData.title}" has been submitted successfully.`,
        duration: 3000,
      });
    } catch (err: any) {
      const friendlyMessage = getUserFriendlyErrorMessage(err);
      toast({
        title: "Unable to Create Complaint",
        description: friendlyMessage,
        variant: "destructive",
        duration: 5000,
      });
    }
  };

  const handleView = (complaint: Complaint) => {
    setSelectedComplaint({
      complaint_id: complaint.complaint_id,
      title: complaint.title,
      description: complaint.description || "",
      category: complaint.category,
      priority: complaint.priority,
    });
    setIsViewModalOpen(true);
  };

  const handleRetry = () => {
    setHasDataError(false);
    fetchComplaints();
  };

  const filteredComplaints = complaints.filter(
    (c) =>
      (statusFilter === "All" || c.status === statusFilter) &&
      (c.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        c.complaint_id.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (c.description || "").toLowerCase().includes(searchQuery.toLowerCase()) ||
        c.entity_name.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const totalPages = apiResponse?.data?.last_page || Math.ceil(filteredComplaints.length / PAGE_SIZE);
  const paginatedComplaints = filteredComplaints.slice((page - 1) * PAGE_SIZE, page * PAGE_SIZE);

  const columns = [
    {
      header: "Complaint ID",
      accessorKey: "complaint_id",
      cell: ({ row }: any) => (
        <span className="font-semibold text-gray-800 dark:text-gray-200">{row.original.complaint_id}</span>
      ),
    },
    {
      header: "Title",
      accessorKey: "title",
      cell: ({ row }: any) => (
        <span className="font-semibold text-gray-800 dark:text-gray-200">{row.original.title}</span>
      ),
    },
    {
      header: "Description",
      accessorKey: "description",
      cell: ({ row }: any) => (
        <span className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">{row.original.description || "N/A"}</span>
      ),
    },
    {
      header: "Category",
      accessorKey: "category",
      cell: ({ row }: any) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">{row.original.category}</span>
      ),
    },
    {
      header: "Priority",
      accessorKey: "priority",
      cell: ({ row }: any) => (
        <Badge variant={getPriorityVariant(row.original.priority)} className="px-2 py-1 text-xs font-medium rounded-full">
          {row.original.priority}
        </Badge>
      ),
    },
    {
      header: "Status",
      accessorKey: "status",
      cell: ({ row }: any) => (
        <Badge variant={getStatusVariant(row.original.status)} className="px-2 py-1 text-xs font-medium rounded-full">
          {row.original.status}
        </Badge>
      ),
    },
    {
      header: "Entity",
      accessorKey: "entity_name",
      cell: ({ row }: any) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {row.original.entity_name} ({row.original.entity_type})
        </span>
      ),
    },
    {
      header: "Created At",
      accessorKey: "created_at",
      cell: ({ row }: any) => (
        <span className="text-sm text-gray-600 dark:text-gray-400">
          {new Date(row.original.created_at).toLocaleDateString("en-US", {
            month: "short",
            day: "numeric",
            year: "numeric",
          })}
        </span>
      ),
    },
    {
      header: "Actions",
      id: "actions",
      cell: ({ row }: any) => (
        <div className="flex gap-2">
          <PrimaryButton
            size="sm"
            className="p-2 text-blue-600 hover:bg-blue-100/50 rounded-full transition-colors duration-200"
            onClick={() => handleView(row.original)}
            aria-label={`View complaint ${row.original.complaint_id}`}
          >
            <Eye className="w-4 h-4" />
          </PrimaryButton>
        </div>
      ),
      enableSorting: false,
      enableColumnFilter: false,
    },
  ];

  return (
    <ErrorBoundary>
      <Screen className="min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="sticky top-0 z-10 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-gray-100 dark:border-gray-700 rounded-2xl p-4 sm:p-6 mb-6 shadow-sm"
          >
            <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
              <h1 className="text-2xl sm:text-3xl font-semibold text-gray-800 dark:text-white tracking-tight">
                Complaints Dashboard
              </h1>
              <div className="flex items-center gap-3 w-full sm:w-auto">
                <div className="relative flex-1 sm:w-64">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <Input
                    placeholder="Search complaints..."
                    value={searchQuery}
                    onChange={(e) => {
                      setSearchQuery(e.target.value);
                      setPage(1);
                    }}
                    className="pl-10 w-full rounded-lg border-gray-200 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:ring-2 focus:ring-blue-500 transition-shadow"
                  />
                </div>
                <PrimaryButton
                  className="group flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-all duration-200 shadow-sm hover:shadow-md"
                  onClick={() => setIsAddModalOpen(true)}
                >
                  <PlusCircle className="h-5 w-5 group-hover:scale-110 transition-transform" />
                  <span className="font-medium">Create Complaint</span>
                </PrimaryButton>
              </div>
            </div>
            <div className="flex gap-2 mt-4 flex-wrap">
              {STATUS_FILTERS.map((status) => (
                <motion.button
                  key={status}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className={`px-4 py-1.5 rounded-full text-sm font-medium transition-colors duration-200 ${
                    statusFilter === status
                      ? "bg-blue-600 text-white"
                      : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                  }`}
                  onClick={() => {
                    setStatusFilter(status);
                    setPage(1);
                  }}
                >
                  {status}
                </motion.button>
              ))}
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            {isLoading || isFetching ? (
              <div className="flex flex-col items-center justify-center py-12 bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
                <p className="text-gray-600 dark:text-gray-400">Loading your complaints...</p>
              </div>
            ) : hasDataError ? (
              <div className="flex flex-col items-center justify-center py-12 bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700">
                <AlertCircle className="h-12 w-12 text-red-400 mb-4" />
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Unable to Load Complaints</h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4 text-center max-w-md">
                  We're having trouble loading your complaints right now. This might be a temporary issue.
                </p>
                <PrimaryButton onClick={handleRetry} className="flex items-center gap-2">
                  <RefreshCw className="h-4 w-4" />
                  Try Again
                </PrimaryButton>
              </div>
            ) : filteredComplaints.length === 0 ? (
              <div className="text-center py-12 bg-white dark:bg-gray-800 rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700">
                <MessageCircle className="h-12 w-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">No Complaints Found</h3>
                <p className="text-gray-500 dark:text-gray-400">
                  {searchQuery || statusFilter !== "All" 
                    ? "Try adjusting your search or filters to find what you're looking for."
                    : "Get started by creating your first complaint using the button above."
                  }
                </p>
              </div>
            ) : (
              <DataTable
                columns={columns}
                data={paginatedComplaints}
                tHeadCellsClassName="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-200 font-semibold text-xs uppercase tracking-wider"
                tHeadClassName="border-b border-gray-200 dark:border-gray-600"
                tBodyClassName="bg-white dark:bg-gray-800"
                tBodyTrClassName="hover:bg-blue-50/50 dark:hover:bg-blue-900/20 transition-colors duration-200"
                tableClassName="rounded-2xl shadow-sm border border-gray-100 dark:border-gray-700 overflow-hidden"
                enableColumnFilters={true}
                enableSorting={true}
                enableSelectColumn={true}
                enableSelectToolbar={false}
                enableSelectToolbarButtonExportToExcel={true}
                enableSelectToolbarButtonPrintToPdf={true}
              />
            )}
          </motion.div>

          {totalPages > 1 && !hasDataError && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="flex justify-end mt-6"
            >
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => setPage((p) => Math.max(1, p - 1))}
                      aria-disabled={page === 1}
                      className={page === 1 ? "pointer-events-none opacity-50" : "hover:bg-gray-100 dark:hover:bg-gray-700"}
                    />
                  </PaginationItem>
                  <PaginationItem>
                    <span className="px-4 py-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-sm font-medium">
                      Page {page} of {totalPages}
                    </span>
                  </PaginationItem>
                  <PaginationItem>
                    <PaginationNext
                      onClick={() => setPage((p) => Math.min(totalPages, p + 1))}
                      aria-disabled={page === totalPages}
                      className={page === totalPages ? "pointer-events-none opacity-50" : "hover:bg-gray-100 dark:hover:bg-gray-700"}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </motion.div>
          )}

          <AddComplaintModal isOpen={isAddModalOpen} onOpenChange={setIsAddModalOpen} onSubmit={handleAddModalSubmit} />
          <ViewComplaintModal isOpen={isViewModalOpen} onOpenChange={setIsViewModalOpen} complaintData={selectedComplaint} />
        </div>
      </Screen>
    </ErrorBoundary>
  );
}