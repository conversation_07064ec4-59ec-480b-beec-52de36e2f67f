import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import React from "react";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import BaseModal from "@/components/custom/modals/BaseModal";
import { CalendarIcon, Eye, Loader, Pen, Plus } from "lucide-react";
import { VehicleRequest } from "./VehicleRequests";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  useCreateVehicleRequestMutation,
  useGetVehiclesQuery,
  useUpdateVehicleRequestMutation,
} from "@/redux/slices/logistics";
import { toast } from "@/components/custom/Toast/MyToast";
import { useAuthHook } from "@/utils/useAuthHook";

interface VehicleRequestProps {
  mode: "create" | "edit";
  vehicleRequestData?: VehicleRequest;
}

export default function VehicleRequestForm({
  mode = "create",
  vehicleRequestData,
}: VehicleRequestProps) {
  const [isModalOpen, setIsModalOpen] = React.useState(false);

  // get vehicle data only for edit mode
  const { data: VehiclesData, isLoading } = useGetVehiclesQuery({}, {
    skip: mode === "create" // Skip fetching vehicles for create mode
  });
  const { user_details } = useAuthHook();

  const formSchema = z.object({
    pickup_time: z.string().min(1, { message: "Time is required" }).trim(),
    pickup_date: z.date(),
    pickup_location: z
      .string()
      .min(1, { message: "Pickup location is required" })
      .trim(),
    destination_location: z
      .string()
      .min(1, { message: "Destination is required" })
      .trim(),
    number_of_passengers: z
      .string()
      .min(1, { message: "Number of passengers is required" })
      .trim(),
    remarks: z.string().optional(),
    purpose: z.string().min(1, { message: "Purpose is required" }).trim(),
    // Vehicle and driver are only included for edit mode (when updating approved requests)
    ...(mode === "edit" && {
      vehicle: z.string().optional(),
      driver: z.string().optional(),
    }),
  });

  const [createVehicleRequest, { isLoading: createVehicleRequestIsLoading }] =
    useCreateVehicleRequestMutation();
  const [updateVehicleRequest, { isLoading: updateVehicleRequestIsLoading }] =
    useUpdateVehicleRequestMutation();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues:
      mode === "edit" && vehicleRequestData
        ? {
            pickup_time: vehicleRequestData.pickup_time || "",
            pickup_date: vehicleRequestData.pickup_date
              ? new Date(vehicleRequestData.pickup_date)
              : new Date(),
            pickup_location: vehicleRequestData.pickup_location || "",
            destination_location: vehicleRequestData.destination_location || "",
            number_of_passengers:
              vehicleRequestData.number_of_passengers.toString() || "",
            remarks: vehicleRequestData.remarks || "",
            purpose: vehicleRequestData.purpose || "",
            // Vehicle and driver only for edit mode
            vehicle: vehicleRequestData.vehicle?.id?.toString() || "",
            driver: vehicleRequestData.driver?.id?.toString() || "",
          }
        : {
            pickup_time: "",
            pickup_date: new Date(),
            pickup_location: "",
            destination_location: "",
            number_of_passengers: "",
            remarks: "",
            purpose: "",
            // No vehicle field for create mode
          },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    // Check if purpose contains "site visit" (case insensitive)
    if (values.purpose.toLowerCase().includes("site visit")) {
      toast.error("For site visits, please use the Site Visit booking feature instead of vehicle requests.");
      return;
    }

    if (mode === "create") {
      try {
        await createVehicleRequest({
          ...values,
          pickup_date: values.pickup_date.toISOString().split("T")[0],
          requester: user_details?.employee_no,
        }).unwrap();
        toast.success("Vehicle creation successful");
        setIsModalOpen(false);
      } catch (error) {
        console.log(error);
        toast.error("Error creating vehicle. Please try again later.");
      }
    } else {
      try {
        if (!vehicleRequestData?.id) {
          toast.error("Vehicle request ID is required for updating.");
          throw new Error("Vehicle request ID is required for updating.");
        }

        const response = await updateVehicleRequest({
          id: vehicleRequestData.id,
          ...values,
          pickup_date: values.pickup_date.toISOString().split("T")[0],
          requester: user_details?.employee_no,
        }).unwrap();
        toast.success("Vehicle details update successful");
        setIsModalOpen(false);
      } catch (error) {
        console.log(error);
        toast.error("Error creating vehicle. Please try again later.");
      }
    }
  }

  // Handle modal close
  const handleCloseModal = () => {
    setIsModalOpen(false);
    form.reset();
  };

  // Default trigger button if none provided
  const defaultTrigger =
    mode === "create" ? (
      <button
        onClick={() => setIsModalOpen(true)}
        className="flex items-center gap-2 px-3 py-2 bg-primary text-primary-foreground rounded-md shadow-sm text-sm hover:bg-primary/95"
      >
        <Plus size={16} />
        <span>Request Vehicle</span>
      </button>
    ) : (
      <div onClick={() => setIsModalOpen(true)} className="flex justify-center">
        <Pen size={16} />
      </div>
    );

  return (
    <>
      {defaultTrigger}

      <BaseModal
        className="pt-8 pb-12 dark:bg-background"
        size="2xl"
        isOpen={isModalOpen}
        onOpenChange={(open) => {
          if (!open) handleCloseModal();
          else setIsModalOpen(open);
        }}
      >
        <div className="">
          <p className="text-2xl mb-3 font-bold">
            {mode === "create" ? "Request a Vehicle" : "Edit Vehicle Request"}
          </p>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-2">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 gap-y-3 w-full">
                <FormField
                  control={form.control}
                  name="pickup_location"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="!text-foreground">
                        Pickup Location
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Absa Towers"
                          {...field}
                          className="border border-accent !rounded-sm bg-none"
                        />
                      </FormControl>
                      <FormMessage className="text-[10px] px-3 dark:!text-foreground/75" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="destination_location"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="!text-foreground">
                        Destination
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Nakuru"
                          {...field}
                          className="border border-accent !rounded-sm bg-none"
                        />
                      </FormControl>
                      <FormMessage className="text-[10px] px-3 dark:!text-foreground/75" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="purpose"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="!text-foreground">
                        Purpose
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Site Visit"
                          {...field}
                          className="border border-accent !rounded-sm bg-none"
                        />
                      </FormControl>
                      <FormMessage className="text-[10px] px-3 dark:!text-foreground/75" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="number_of_passengers"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="!text-foreground">
                        Number of Passangers
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="4"
                          {...field}
                          className="border border-accent !rounded-sm bg-none"
                        />
                      </FormControl>
                      <FormMessage className="text-[10px] px-3 dark:!text-foreground/75" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="remarks"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel className="!text-foreground">
                        Remarks (Optional)
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Additional notes or special requirements"
                          {...field}
                          className="border border-accent !rounded-sm bg-none"
                        />
                      </FormControl>
                      <FormMessage className="text-[10px] px-3 dark:!text-foreground/75" />
                    </FormItem>
                  )}
                />

                {/* Vehicle and Driver fields only shown in edit mode */}
                {mode === "edit" && (
                  <>
                    <FormField
                      control={form.control}
                      name="vehicle"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="!text-foreground">
                            Vehicle (Assigned during approval)
                          </FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value?.toString()}
                          >
                            <FormControl>
                              <SelectTrigger className="text-black border border-accent !rounded-sm !bg-none focus:!ring-0">
                                <SelectValue placeholder="Vehicle assigned during approval" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {VehiclesData?.data?.results?.length ? (
                                VehiclesData.data.results.map((vehicle: any) => (
                                  vehicle.id ? (
                                    <SelectItem
                                      key={vehicle.id}
                                      value={vehicle.id.toString()}
                                    >
                                      {vehicle.vehicle_registration}
                                    </SelectItem>
                                  ) : null
                                )).filter(Boolean)
                              ) : (
                                <SelectItem value="" disabled>
                                  No vehicles available
                                </SelectItem>
                              )}
                            </SelectContent>
                          </Select>
                          <FormMessage className="text-[10px] px-3 dark:!text-foreground/75" />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="driver"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="!text-foreground">Driver (Assigned during approval)</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value?.toString()}
                          >
                            <FormControl>
                              <SelectTrigger className="border border-accent !rounded-sm bg-none focus:!ring-0">
                                <SelectValue placeholder="Driver assigned during approval" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="">No driver assigned</SelectItem>
                              <SelectItem value="OL/HR/125">Driver 1</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage className="text-[10px] px-3 dark:!text-foreground/75" />
                        </FormItem>
                      )}
                    />
                  </>
                )}

                <FormField
                  control={form.control}
                  name="pickup_date"
                  render={({ field }) => (
                    <FormItem className="flex flex-col md:col-span-2">
                      <FormLabel className="!text-foreground">Date</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={"outline"}
                              className={cn(
                                " pl-3 text-left font-normal border border-accent !rounded-sm bg-none",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => date < new Date()}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage className="text-[10px] px-3 dark:!text-foreground/75" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="pickup_time"
                  render={({ field }) => (
                    <FormItem className="md:col-span-2">
                      <FormLabel className={"!text-foreground"}>Time</FormLabel>
                      <FormControl>
                        <input
                          {...field}
                          className="border border-accent !rounded-sm !bg-none w-full px-3 py-2 focus:outline-none"
                          type="time"
                        />
                      </FormControl>
                      <FormMessage className="text-[10px] px-3 dark:!text-foreground/75" />
                    </FormItem>
                  )}
                />

                <PrimaryButton type="submit" className="w-full md:col-span-2 ">
                  {createVehicleRequestIsLoading ||
                  updateVehicleRequestIsLoading ? (
                    <span className="flex gap-2 items-center justify-center">
                      <Loader
                        className="fas fa-spinner animate-spin"
                        size={20}
                      />
                      Loading...
                    </span>
                  ) : mode === "create" ? (
                    "Request Vehicle"
                  ) : (
                    "Update Request"
                  )}
                </PrimaryButton>
              </div>
            </form>
          </Form>
        </div>
      </BaseModal>
    </>
  );
}

// import React from "react";
// import { z } from "zod"
// import { useForm } from "react-hook-form";
// import { zodResolver } from "@hookform/resolvers/zod"
// import { format } from "date-fns"
// import { cn } from "@/lib/utils"
// import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
// import { Input } from "@/components/ui/input";
// import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
// import { Button } from "@/components/ui/button";
// import { Calendar } from "@/components/ui/calendar";
// import { PrimaryButton } from "@/components/custom/buttons/buttons";
// import BaseModal from "@/components/custom/modals/BaseModal";
// import { CalendarIcon, Plus, Edit, PenTool } from "lucide-react";
// import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
// import { useGetVehiclesQuery } from "@/redux/slices/logistics";
// import { VehicleRequest } from "./VehicleRequests";

// // Form schema definition
// const formSchema = z.object({
//     pickup_location: z.string().min(1, { message: 'Pickup location is required' }).trim(),
//     destination_location: z.string().min(1, { message: 'Destination is required' }).trim(),
//     purpose: z.string().min(1, { message: 'Purpose is required' }).trim(),
//     number_of_passengers: z.string().min(1, { message: 'Number of passengers is required' }).trim(),
//     pickup_date: z.date(),
//     pickup_time: z.string().min(1, { message: 'Pickup time is required' }).trim(),
//     remarks: z.string().min(1, { message: 'Remarks is required' }).trim(),
//     vehicle: z.number(),
//     requester: z.string().min(1, { message: 'Requester is required' }).trim(),
//     driver: z.string().min(1, { message: 'Driver is required' }).trim(),
// })

// // Component props
// interface VehicleFormProps {
//     mode: 'create' | 'edit';
//     vehicleRequestData?: VehicleRequest;// Custom trigger element
// }

// export default function VehicleRequestForm({
//     mode = 'create',
//     vehicleRequestData,
// }: VehicleFormProps) {
//     const [isModalOpen, setIsModalOpen] = React.useState(false);

//     // Initialize form with either initial data or default values
//     const form = useForm<z.infer<typeof formSchema>>({
//         resolver: zodResolver(formSchema),
//         defaultValues: mode === 'edit' && vehicleRequestData ? {
//             pickup_location: vehicleRequestData.pickup_location || "",
//             destination_location: vehicleRequestData.destination_location || "",
//             purpose: vehicleRequestData.purpose || "",
//             number_of_passengers: vehicleRequestData.number_of_passengers.toString() || "",
//             remarks: vehicleRequestData.remarks || '',
//             vehicle: vehicleRequestData.vehicle,
//             driver: vehicleRequestData.driver || '',
//             pickup_date: vehicleRequestData.pickup_date ? new Date(vehicleRequestData.pickup_date) : new Date(),
//             pickup_time: vehicleRequestData.pickup_time || '',
//         } : {
//             pickup_location: "",
//             destination_location: "",
//             purpose: "",
//             number_of_passengers: "",
//             remarks: '',
//             driver: '',
//             pickup_date: new Date(),
//             pickup_time: '',
//         },
//     });

//     // get vehicle
//     const { data: VehiclesData, isLoading } = useGetVehiclesQuery({})

//     // submit
//     function onSubmit(values: z.infer<typeof formSchema>) {

//         // Handle create vehicle request logic here
//         console.log("Creating vehicle request", values);

//         // Close modal
//         handleCloseModal();
//     }

//     // Handle modal close
//     const handleCloseModal = () => {
//         setIsModalOpen(false);
//         // Only reset the form if creating a new request
//         if (mode === 'edit') {
//             form.reset();
//         }
//     };

//     // Default trigger button if none provided
//     const defaultTrigger = mode === 'create' ? (
//         <button onClick={() => setIsModalOpen(true)} className="flex items-center gap-2 px-3 py-2 bg-primary text-primary-foreground rounded-md shadow-sm text-sm hover:bg-primary/95">
//             <Plus size={16} />
//             <span>New Vehicle Request</span>
//         </button>
//     ) : (
//         <div onClick={() => setIsModalOpen(true)} className='flex justify-center'>
//             <PenTool size={20} className='-rotate-90' />
//         </div>
//     );

//     return (
//         <>
//             {defaultTrigger}

//             <BaseModal
//                 className="pt-8 pb-12 dark:bg-background"
//                 size="2xl"
//                 isOpen={isModalOpen}
//                 onOpenChange={(open) => {
//                     if (!open) handleCloseModal();
//                     else setIsModalOpen(open);
//                 }}
//             >
//                 <div>
//                     <p className='text-2xl mb-3 font-bold'>
//                         {mode == 'edit' ? "Edit Vehicle Request" : "Request a Vehicle"}
//                     </p>

//                     <Form {...form}>
//                         <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-2">
//                             <div className='grid grid-cols-1 md:grid-cols-2 gap-4 gap-y-6 w-full'>
//                                 <FormField
//                                     control={form.control}
//                                     name="pickup_location"
//                                     render={({ field }) => (
//                                         <FormItem>
//                                             <FormLabel className='!text-foreground'>Pickup Location</FormLabel>
//                                             <FormControl>
//                                                 <Input
//                                                     placeholder="Absa Towers"
//                                                     {...field}
//                                                     className="border border-accent !rounded-sm bg-none"
//                                                 />
//                                             </FormControl>
//                                             <FormMessage className="text-[10px] px-3 dark:!text-foreground/75" />
//                                         </FormItem>
//                                     )}
//                                 />

//                                 <FormField
//                                     control={form.control}
//                                     name="destination_location"
//                                     render={({ field }) => (
//                                         <FormItem>
//                                             <FormLabel className='!text-foreground'>Destination</FormLabel>
//                                             <FormControl>
//                                                 <Input
//                                                     placeholder="Nakuru"
//                                                     {...field}
//                                                     className="border border-accent !rounded-sm bg-none"
//                                                 />
//                                             </FormControl>
//                                             <FormMessage className="text-[10px] px-3 dark:!text-foreground/75" />
//                                         </FormItem>
//                                     )}
//                                 />

//                                 <FormField
//                                     control={form.control}
//                                     name="purpose"
//                                     render={({ field }) => (
//                                         <FormItem>
//                                             <FormLabel className='!text-foreground'>Purpose</FormLabel>
//                                             <FormControl>
//                                                 <Input
//                                                     placeholder="Site Visit"
//                                                     {...field}
//                                                     className="border border-accent !rounded-sm bg-none"
//                                                 />
//                                             </FormControl>
//                                             <FormMessage className="text-[10px] px-3 dark:!text-foreground/75" />
//                                         </FormItem>
//                                     )}
//                                 />

//                                 <FormField
//                                     control={form.control}
//                                     name="number_of_passengers"
//                                     render={({ field }) => (
//                                         <FormItem>
//                                             <FormLabel className='!text-foreground'>Number of Passengers</FormLabel>
//                                             <FormControl>
//                                                 <Input
//                                                     placeholder="4"
//                                                     {...field}
//                                                     className="border border-accent !rounded-sm bg-none"
//                                                 />
//                                             </FormControl>
//                                             <FormMessage className="text-[10px] px-3 dark:!text-foreground/75" />
//                                         </FormItem>
//                                     )}
//                                 />

//                                 <FormField
//                                     control={form.control}
//                                     name="remarks"
//                                     render={({ field }) => (
//                                         <FormItem className="md:col-span-2">
//                                             <FormLabel className='!text-foreground'>Remarks</FormLabel>
//                                             <FormControl>
//                                                 <Input
//                                                     placeholder="Site visit Ngong Kimuka"
//                                                     {...field}
//                                                     className="border border-accent !rounded-sm bg-none"
//                                                 />
//                                             </FormControl>
//                                             <FormMessage className="text-[10px] px-3 dark:!text-foreground/75" />
//                                         </FormItem>
//                                     )}
//                                 />

//                                 <FormField
//                                     control={form.control}
//                                     name="driver"
//                                     render={({ field }) => (
//                                         <FormItem>
//                                             <FormLabel className='!text-foreground'>Driver</FormLabel>
//                                             <Select onValueChange={field.onChange} defaultValue={field.value}>
//                                                 <FormControl>
//                                                     <SelectTrigger className="border border-accent !rounded-sm bg-none focus:!ring-0">
//                                                         <SelectValue placeholder="Select driver" />
//                                                     </SelectTrigger>
//                                                 </FormControl>
//                                                 <SelectContent>
//                                                     <SelectItem value="OL/HR/125">Driver 1</SelectItem>
//                                                 </SelectContent>
//                                             </Select>
//                                             <FormMessage className="text-[10px] px-3 dark:!text-foreground/75" />
//                                         </FormItem>
//                                     )}
//                                 />

//                                 <FormField
//                                     control={form.control}
//                                     name="vehicle"
//                                     render={({ field }) => (
//                                         <FormItem>
//                                             <FormLabel className='!text-foreground'>Vehicle</FormLabel>
//                                             <Select onValueChange={(value) => field.onChange(Number(value))} defaultValue={field.value?.toString()}>
//                                                 <FormControl>
//                                                     <SelectTrigger className="border border-accent !rounded-sm bg-none focus:!ring-0">
//                                                         <SelectValue placeholder="Select vehicle" />
//                                                     </SelectTrigger>
//                                                 </FormControl>
//                                                 <SelectContent>
//                                                     {
//                                                         VehiclesData?.data?.results?.length ? (
//                                                             VehiclesData.data.results.map((vehicle: any) => (
//                                                                 <SelectItem key={`${vehicle.id}`} value={`${vehicle.id}`}>
//                                                                     {vehicle.vehicle_registration}
//                                                                 </SelectItem>
//                                                             ))
//                                                         ) : (
//                                                             <SelectItem value="" disabled>
//                                                                 No vehicles available
//                                                             </SelectItem>
//                                                         )
//                                                     }
//                                                 </SelectContent>
//                                             </Select>
//                                             <FormMessage className="text-[10px] px-3 dark:!text-foreground/75" />
//                                         </FormItem>
//                                     )}
//                                 />

//                                 <FormField
//                                     control={form.control}
//                                     name="pickup_date"
//                                     render={({ field }) => (
//                                         <FormItem className="flex flex-col md:col-span-2">
//                                             <FormLabel className='!text-foreground'>Date</FormLabel>
//                                             <Popover>
//                                                 <PopoverTrigger asChild>
//                                                     <FormControl>
//                                                         <Button
//                                                             variant={"outline"}
//                                                             className={cn(
//                                                                 "pl-3 text-left font-normal border border-accent !rounded-sm bg-none",
//                                                                 !field.value && "text-muted-foreground"
//                                                             )}
//                                                         >
//                                                             {field.value ? (
//                                                                 format(field.value, "PPP")
//                                                             ) : (
//                                                                 <span>Pick a date</span>
//                                                             )}
//                                                             <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
//                                                         </Button>
//                                                     </FormControl>
//                                                 </PopoverTrigger>
//                                                 <PopoverContent className="w-auto p-0" align="start">
//                                                     <Calendar
//                                                         mode="single"
//                                                         selected={field.value}
//                                                         onSelect={field.onChange}
//                                                         disabled={(date) => date < new Date()}
//                                                         initialFocus
//                                                     />
//                                                 </PopoverContent>
//                                             </Popover>
//                                             <FormMessage className="text-[10px] px-3 dark:!text-foreground/75" />
//                                         </FormItem>
//                                     )}
//                                 />

//                                 <FormField
//                                     control={form.control}
//                                     name="pickup_time"
//                                     render={({ field }) => (
//                                         <FormItem className="md:col-span-2">
//                                             <FormLabel className={'!text-foreground'}>Time</FormLabel>
//                                             <FormControl>
//                                                 <input
//                                                     {...field}
//                                                     className="border border-accent !rounded-sm !bg-none w-full px-3 py-2 focus:outline-none"
//                                                     type="time"
//                                                 />
//                                             </FormControl>
//                                             <FormMessage className="text-[10px] px-3 dark:!text-foreground/75" />
//                                         </FormItem>
//                                     )}
//                                 />

//                                 <PrimaryButton type="submit" className='w-full md:col-span-2'>
//                                     {mode === 'create' ? 'Create Vehicle Request' : 'Update Vehicle Request'}
//                                 </PrimaryButton>
//                             </div>
//                         </form>
//                     </Form>
//                 </div>
//             </BaseModal>
//         </>
//     );
// }
