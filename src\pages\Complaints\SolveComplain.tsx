import { useState } from "react";
import { AlertCircle } from "lucide-react";
import MultiStepModal from "@/components/custom/modals/MultiStepModal";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";


interface FormData {
  subject: string;
  department: string;
  name: string;
  role: string;
  complaint: string;
  description: string;
}

interface AddComplaintModalProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  onSubmit: (formData: FormData) => void;
}

export default function SolveComplaintModal({
  isOpen,
  onOpenChange,
  onSubmit,
}: AddComplaintModalProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState<FormData>({
    subject: "",
    department: "",
    name: "",
    role: "",
    complaint: "",
    description: "",
  });

 
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setFormData((prev) => ({ ...prev, [id]: value }));
  };

 
  const handleComplete = () => {
    onSubmit(formData);
    setCurrentStep(0);
    setFormData({
      subject: "",
      department: "",
      name: "",
      role: "",
      complaint: "",
      description: "",
    });
  };

  return (
    <MultiStepModal
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      title="Add New Complaint"
      description="Complete all steps to submit a new complaint"
      currentStep={currentStep}
      onStepChange={setCurrentStep}
      onComplete={handleComplete}
      steps={[
        {
          title: "Basic Info",
          content: (
            <div className="space-y-4 py-2">
              <div className="space-y-2">
                <Label htmlFor="subject">Subject</Label>
                <Input
                  id="subject"
                  placeholder="Enter the subject matter"
                  value={formData.subject}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="department">Department</Label>
                <Input
                  id="department"
                  placeholder="Enter your Department"
                  value={formData.department}
                  onChange={handleInputChange}
                />
              </div>
            </div>
          ),
        },
        {
          title: "Personal Info",
          content: (
            <div className="space-y-4 py-2">
              <div className="space-y-2">
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  placeholder="Enter your name"
                  value={formData.name}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="role">Role</Label>
                <Input
                  id="role"
                  placeholder="Enter your role"
                  value={formData.role}
                  onChange={handleInputChange}
                />
              </div>
            </div>
          ),
        },
        {
          title: "Complaint Info",
          content: (
            <div className="space-y-4 py-2">
              <div className="space-y-2">
                <Label htmlFor="complaint">Complaint</Label>
                <Input
                  id="complaint"
                  placeholder="Enter the complaint"
                  value={formData.complaint}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Input
                  id="description"
                  placeholder="Describe briefly"
                  value={formData.description}
                  onChange={handleInputChange}
                />
              </div>
            </div>
          ),
        },
        {
          title: "Confirmation",
          content: (
            <div className="py-6 text-center">
              <AlertCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium">Almost Done!</h3>
              <p className="text-muted-foreground mt-2">
                Please review your information before submitting.
              </p>
            </div>
          ),
        },
      ]}
    />
  );
}