import { apiSlice } from "@/redux/apiSlice";
import { OfferLetterData } from "../OfferLetter";

// API types based on documentation
export interface OfferLetterMain {
  id?: number;
  step: string;
  is_completed?: boolean;
  customer_type?: string;
  date?: string;
  acc_payment_conf?: string;
  lead_file?: string;
  plot_number?: string;
  booking_id?: string;
}

export interface OfferLetterIndividual {
  id?: number;
  first_name?: string;
  last_name?: string;
  country_code?: string;
  phone?: string;
  email?: string;
  national_id?: string;
  country?: string;
  city?: string;
  KRA_Pin?: string;
  DOB?: string;
  preferred_contact?: string;
  offer_letter: number;
  created_at?: string;
}

export interface OfferLetterCompany {
  id?: number;
  company_name?: string;
  company_registration_number?: string;
  company_country_code?: string;
  phone?: string;
  email?: string;
  address?: string;
  country?: string;
  city?: string;
  company_kra?: string;
  preferred_contact?: string;
  offer_letter: number;
  created_at?: string;
}

export interface OfferLetterCompanyDirector {
  id?: number;
  director_id: number;
  first_name: string;
  last_name: string;
  country_codes?: string;
  phone: string;
  email: string;
  national_id: string;
  created_at?: string;
  company: number;
}

export interface OfferLetterGroup {
  id?: number;
  group_name?: string;
  group_code?: string;
  group_phone?: string;
  group_email?: string;
  Group_KRA_PIN?: string;
  Group_country?: string;
  Group_city?: string;
  offer_letter: number;
  created_at?: string;
}

export interface OfferLetterGroupMember {
  id?: number;
  member_id: number;
  first_name: string;
  last_name: string;
  country_codes?: string;
  phone: string;
  email: string;
  national_id: string;
  created_at?: string;
  group: number;
}

export interface OfferLetterPartner {
  id?: number;
  first_name?: string;
  last_name?: string;
  country_code?: string;
  phone?: string;
  email?: string;
  national_id?: string;
  country?: string;
  city?: string;
  preferred_contact?: string;
  offer_letter: number;
  created_at?: string;
}

export interface OfferLetterNextOfKin {
  id?: number;
  full_name: string;
  relationship: string;
  country_code?: string;
  phone: string;
  email?: string;
  offer_letter: number;
  individual?: number;
  partner?: number;
  created_at?: string;
}

export interface OfferLetterPaymentsPlan {
  id?: number;
  plot_no: string;
  no_of_instalments: number;
  total_cash_price: string;
  monthly_installments: string;
  deposit: string;
  created_at?: string;
  offer_letter: number;
}

export interface OfferLetterPricing {
  id?: number;
  Payment_Model?: string;
  Plot_Type?: string;
  view?: string;
  Deposit?: string;
  Monthly_Interest?: string;
  _2M_IN?: string;
  _3M_IN?: string;
  _4M_IN?: string;
  _5M_IN?: string;
  _6M_IN?: string;
  _7M_IN?: string;
  _8M_IN?: string;
  _9M_IN?: string;
  _10M_IN?: string;
  _11M_IN?: string;
  _12M_IN?: string;
  _15M_IN?: string;
  _24M_IN?: string;
  Project_No?: string;
  Size_Category?: number;
}

export interface OfferLetterTermsConditions {
  id?: number;
  content: string;
  acceptance_date?: string;
  offer_letter: number;
}

export interface OfferLetterReview {
  id?: number;
  status?: "Pending" | "Approved" | "Rejected";
  created_at?: string;
  offer_letter: number;
  individual?: number;
  partner?: number;
  group?: number;
  company?: number;
}

export interface PlotPaymentOption {
  PLOT_NO?: string;
  // Add other fields as needed based on API response
}

// New interfaces for Legal Dashboard
export interface OfferLetterCategory {
  id: number; // This is the offer_letter_id used for fetching details
  step: string;
  plot_number: string;
  is_completed: boolean;
  customer_type: 'individual' | 'company' | 'partner' | 'group';
  date: string;
  acc_payment_conf: boolean;
  lead_file: string;
  customers: any; // Can be null based on API response
  booking_id: string;
}

export interface OfferLetterCategoriesResponse {
  count: number;
  num_pages: number;
  current_page: number;
  results: OfferLetterCategory[];
}

export interface OfferLetterDetailResponse {
  step: string;
  is_completed: boolean;
  plot_number: string;
  booking_id: string;
  customer_type: 'individual' | 'company' | 'partner' | 'group';
  date: string;
  acc_payment_conf: boolean;
  lead_file: string;
  payment_plan: Array<{
    id: number;
    plot_no: string;
    no_of_instalments: number;
    total_cash_price: string;
    monthly_installments: string;
    deposit: string;
    created_at: string;
    offer_letter: number;
  }>;
  terms_conditions: Array<{
    id: number;
    content: string;
    acceptance_date: string;
    offer_letter: number;
  }>;
  individuals: Array<{
    id: number;
    first_name: string;
    last_name: string;
    country_code: string | null;
    phone: string;
    email: string;
    national_id: string;
    country: string;
    city: string;
    KRA_Pin: string;
    DOB: string;
    preferred_contact: string[] | string | null;
    offer_letter: number;
    created_at: string;
    next_of_kin: Array<any>;
  }>;
  partners: Array<{
    id: number;
    first_name: string;
    last_name: string;
    country_code: string;
    phone: string;
    email: string;
    national_id: string;
    country: string;
    city: string;
    preferred_contact: string | null;
    offer_letter: number;
    created_at: string;
    next_of_kin: Array<any>;
  }>;
  companies: Array<{
    id: number;
    company_name: string;
    company_registration_number: string;
    company_country_code: string;
    phone: string;
    email: string;
    address: string;
    country: string;
    city: string;
    preferred_contact: string;
    company_kra: string;
    offer_letter: number;
    created_at: string;
    directors: Array<{
      id: number;
      director_id: number;
      first_name: string;
      last_name: string;
      country_codes: string;
      phone: string;
      email: string;
      national_id: string;
      created_at: string;
      company: number;
    }>;
  }>;
  groups: Array<{
    id: number;
    group_name: string;
    group_code: string;
    group_phone: string;
    group_email: string;
    Group_KRA_PIN: string;
    Group_country: string;
    Group_city: string;
    offer_letter: number;
    created_at: string;
    members: Array<{
      id: number;
      member_id: number;
      first_name: string;
      last_name: string;
      country_codes: string;
      phone: string;
      email: string;
      national_id: string;
      created_at: string;
      group: number;
    }>;
  }>;
}

// API endpoints for offer letter management
export const offerLetterApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Main Offer Letter endpoints
    createOfferLetter: builder.mutation<
      OfferLetterMain,
      Partial<OfferLetterMain>
    >({
      query: (offerLetterData) => ({
        url: "offer-letters/",
        method: "POST",
        body: offerLetterData,
      }),
      invalidatesTags: ["OfferLetter"],
    }),

    getOfferLetter: builder.query<OfferLetterMain, number>({
      query: (id) => `offer-letters/${id}/`,
      providesTags: (result, error, id) => [{ type: "OfferLetter", id }],
    }),

    updateOfferLetter: builder.mutation<
      OfferLetterMain,
      { id: number; data: Partial<OfferLetterMain> }
    >({
      query: ({ id, data }) => ({
        url: `offer-letters/${id}/`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: "OfferLetter", id }],
    }),

    deleteOfferLetter: builder.mutation<void, number>({
      query: (id) => ({
        url: `offer-letters/${id}/`,
        method: "DELETE",
      }),
      invalidatesTags: ["OfferLetter"],
    }),

    getOfferLetters: builder.query<
      {
        count: number;
        next: string | null;
        previous: string | null;
        results: OfferLetterMain[];
      },
      {
        page?: number;
        customer_type?: string;
        is_completed?: boolean;
        search?: string;
      }
    >({
      query: (params = {}) => ({
        url: "offer-letters/",
        params,
      }),
      providesTags: ["OfferLetter"],
    }),

    // Individual Customer endpoints
    createOfferLetterIndividual: builder.mutation<
      OfferLetterIndividual,
      Partial<OfferLetterIndividual>
    >({
      query: (data) => ({
        url: "offer-letter-individuals/",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["OfferLetterIndividual"],
    }),

    getOfferLetterIndividual: builder.query<OfferLetterIndividual, number>({
      query: (id) => `offer-letter-individuals/${id}/`,
      providesTags: (result, error, id) => [
        { type: "OfferLetterIndividual", id },
      ],
    }),

    updateOfferLetterIndividual: builder.mutation<
      OfferLetterIndividual,
      { id: number; data: Partial<OfferLetterIndividual> }
    >({
      query: ({ id, data }) => ({
        url: `offer-letter-individuals/${id}/`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "OfferLetterIndividual", id },
      ],
    }),

    // Company endpoints
    createOfferLetterCompany: builder.mutation<
      OfferLetterCompany,
      Partial<OfferLetterCompany>
    >({
      query: (data) => ({
        url: "offer-letter-company/",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["OfferLetterCompany"],
    }),

    getOfferLetterCompany: builder.query<OfferLetterCompany, number>({
      query: (id) => `offer-letter-company/${id}/`,
      providesTags: (result, error, id) => [{ type: "OfferLetterCompany", id }],
    }),

    updateOfferLetterCompany: builder.mutation<
      OfferLetterCompany,
      { id: number; data: Partial<OfferLetterCompany> }
    >({
      query: ({ id, data }) => ({
        url: `offer-letter-company/${id}/`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "OfferLetterCompany", id },
      ],
    }),

    // Company Directors endpoints
    createOfferLetterCompanyDirector: builder.mutation<
      OfferLetterCompanyDirector,
      Partial<OfferLetterCompanyDirector>
    >({
      query: (data) => ({
        url: "offer-letter-company-directors/",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["OfferLetterCompany"],
    }),

    getOfferLetterCompanyDirectors: builder.query<
      {
        count: number;
        next: string | null;
        previous: string | null;
        results: OfferLetterCompanyDirector[];
      },
      { page?: number; page_size?: number }
    >({
      query: (params = {}) => ({
        url: "offer-letter-company-directors/",
        params,
      }),
      providesTags: ["OfferLetterCompany"],
    }),

    updateOfferLetterCompanyDirector: builder.mutation<
      OfferLetterCompanyDirector,
      { id: number; data: Partial<OfferLetterCompanyDirector> }
    >({
      query: ({ id, data }) => ({
        url: `offer-letter-company-directors/${id}/`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: ["OfferLetterCompany"],
    }),

    deleteOfferLetterCompanyDirector: builder.mutation<void, number>({
      query: (id) => ({
        url: `offer-letter-company-directors/${id}/`,
        method: "DELETE",
      }),
      invalidatesTags: ["OfferLetterCompany"],
    }),

    // Group endpoints
    createOfferLetterGroup: builder.mutation<
      OfferLetterGroup,
      Partial<OfferLetterGroup>
    >({
      query: (data) => ({
        url: "offer-letter-groups/",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["OfferLetterGroup"],
    }),

    getOfferLetterGroup: builder.query<OfferLetterGroup, number>({
      query: (id) => `offer-letter-groups/${id}/`,
      providesTags: (result, error, id) => [{ type: "OfferLetterGroup", id }],
    }),

    updateOfferLetterGroup: builder.mutation<
      OfferLetterGroup,
      { id: number; data: Partial<OfferLetterGroup> }
    >({
      query: ({ id, data }) => ({
        url: `offer-letter-groups/${id}/`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "OfferLetterGroup", id },
      ],
    }),

    // Group Members endpoints
    createOfferLetterGroupMember: builder.mutation<
      OfferLetterGroupMember,
      Partial<OfferLetterGroupMember>
    >({
      query: (data) => ({
        url: "offer-letter-group-members/",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["OfferLetterGroup"],
    }),

    getOfferLetterGroupMembers: builder.query<
      {
        count: number;
        next: string | null;
        previous: string | null;
        results: OfferLetterGroupMember[];
      },
      { page?: number; page_size?: number }
    >({
      query: (params = {}) => ({
        url: "offer-letter-group-members/",
        params,
      }),
      providesTags: ["OfferLetterGroup"],
    }),

    updateOfferLetterGroupMember: builder.mutation<
      OfferLetterGroupMember,
      { id: number; data: Partial<OfferLetterGroupMember> }
    >({
      query: ({ id, data }) => ({
        url: `offer-letter-group-members/${id}/`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: ["OfferLetterGroup"],
    }),

    deleteOfferLetterGroupMember: builder.mutation<void, number>({
      query: (id) => ({
        url: `offer-letter-group-members/${id}/`,
        method: "DELETE",
      }),
      invalidatesTags: ["OfferLetterGroup"],
    }),

    // Partners endpoints
    createOfferLetterPartner: builder.mutation<
      OfferLetterPartner,
      Partial<OfferLetterPartner>
    >({
      query: (data) => ({
        url: "offer-letter-partners/",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["OfferLetterPartner"],
    }),

    getOfferLetterPartner: builder.query<OfferLetterPartner, number>({
      query: (id) => `offer-letter-partners/${id}/`,
      providesTags: (result, error, id) => [{ type: "OfferLetterPartner", id }],
    }),

    updateOfferLetterPartner: builder.mutation<
      OfferLetterPartner,
      { id: number; data: Partial<OfferLetterPartner> }
    >({
      query: ({ id, data }) => ({
        url: `offer-letter-partners/${id}/`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "OfferLetterPartner", id },
      ],
    }),

    // Next of Kin endpoints
    createOfferLetterNextOfKin: builder.mutation<
      OfferLetterNextOfKin,
      Partial<OfferLetterNextOfKin>
    >({
      query: (data) => ({
        url: "offer-letter-next-of-kin/",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["OfferLetterNextOfKin"],
    }),

    getOfferLetterNextOfKin: builder.query<OfferLetterNextOfKin, number>({
      query: (id) => `offer-letter-next-of-kin/${id}/`,
      providesTags: (result, error, id) => [
        { type: "OfferLetterNextOfKin", id },
      ],
    }),

    updateOfferLetterNextOfKin: builder.mutation<
      OfferLetterNextOfKin,
      { id: number; data: Partial<OfferLetterNextOfKin> }
    >({
      query: ({ id, data }) => ({
        url: `offer-letter-next-of-kin/${id}/`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "OfferLetterNextOfKin", id },
      ],
    }),

    // Payment Plan endpoints
    createOfferLetterPayments: builder.mutation<
      OfferLetterPaymentsPlan,
      Partial<OfferLetterPaymentsPlan>
    >({
      query: (data) => ({
        url: "offer-letter-payments/",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["OfferLetterPayments"],
    }),

    getOfferLetterPayments: builder.query<OfferLetterPaymentsPlan, number>({
      query: (id) => `offer-letter-payments/${id}/`,
      providesTags: (result, error, id) => [
        { type: "OfferLetterPayments", id },
      ],
    }),

    updateOfferLetterPayments: builder.mutation<
      OfferLetterPaymentsPlan,
      { id: number; data: Partial<OfferLetterPaymentsPlan> }
    >({
      query: ({ id, data }) => ({
        url: `offer-letter-payments/${id}/`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "OfferLetterPayments", id },
      ],
    }),
    // Pricing endpoints
    getOfferLetterPricing: builder.query<
      {
        count: number;
        next: string | null;
        previous: string | null;
        results: OfferLetterPricing[];
      },
      { page?: number; page_size?: number }
    >({
      query: (params = {}) => ({
        url: "offer-letter-pricing/",
        params,
      }),
      providesTags: ["OfferLetterPricing"],
    }),

    getOfferLetterPricingById: builder.query<OfferLetterPricing, number>({
      query: (id) => `offer-letter-pricing/${id}/`,
      providesTags: (result, error, id) => [{ type: "OfferLetterPricing", id }],
    }),

    // Terms & Conditions endpoints
    createOfferLetterTerms: builder.mutation<
      OfferLetterTermsConditions,
      Partial<OfferLetterTermsConditions>
    >({
      query: (data) => ({
        url: "offer-letter-terms/",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["OfferLetterTerms"],
    }),

    getOfferLetterTerms: builder.query<
      {
        count: number;
        next: string | null;
        previous: string | null;
        results: OfferLetterTermsConditions[];
      },
      { page?: number; page_size?: number }
    >({
      query: (params = {}) => ({
        url: "offer-letter-terms/",
        params,
      }),
      providesTags: ["OfferLetterTerms"],
    }),

    getOfferLetterTermsById: builder.query<OfferLetterTermsConditions, number>({
      query: (id) => `offer-letter-terms/${id}/`,
      providesTags: (result, error, id) => [{ type: "OfferLetterTerms", id }],
    }),

    updateOfferLetterTerms: builder.mutation<
      OfferLetterTermsConditions,
      { id: number; data: Partial<OfferLetterTermsConditions> }
    >({
      query: ({ id, data }) => ({
        url: `offer-letter-terms/${id}/`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "OfferLetterTerms", id },
      ],
    }),

    // Review endpoints
    createOfferLetterReview: builder.mutation<
      OfferLetterReview,
      Partial<OfferLetterReview>
    >({
      query: (data) => ({
        url: "offer-letter-reviews/",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["OfferLetterReview"],
    }),

    getOfferLetterReviews: builder.query<
      {
        count: number;
        next: string | null;
        previous: string | null;
        results: OfferLetterReview[];
      },
      { page?: number; page_size?: number }
    >({
      query: (params = {}) => ({
        url: "offer-letter-reviews/",
        params,
      }),
      providesTags: ["OfferLetterReview"],
    }),

    getOfferLetterReviewById: builder.query<OfferLetterReview, number>({
      query: (id) => `offer-letter-reviews/${id}/`,
      providesTags: (result, error, id) => [{ type: "OfferLetterReview", id }],
    }),

    updateOfferLetterReview: builder.mutation<
      OfferLetterReview,
      { id: number; data: Partial<OfferLetterReview> }
    >({
      query: ({ id, data }) => ({
        url: `offer-letter-reviews/${id}/`,
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: "OfferLetterReview", id },
      ],
    }),

    // Plot Payment Options endpoint
    getPlotPaymentOptions: builder.query<
      PlotPaymentOption[],
      { PLOT_NO?: string }
    >({
      query: (params = { PLOT_NO: "ALL" }) => ({
        url: "plots-payment-options/",
        params,
      }),
      providesTags: ["PlotPaymentOptions"],
    }),

    // Generate PDF for offer letter
    generateOfferLetterPDF: builder.mutation<{ pdf_url: string }, number>({
      query: (id) => ({
        url: `offer-letters/${id}/generate-pdf/`,
        method: "POST",
      }),
    }),

    // Booking and Plot related endpoints for offer letter integration
    getPlotBookings: builder.query<any, any>({
      query: (params) => ({
        url: "inventory/plot-booking",
        params,
      }),
      providesTags: ["PlotBookings"],
    }),

    getPlotDetails: builder.query<any, string>({
      query: (plotId) => ({
        url: `inventory/plots/${plotId}`,
      }),
      providesTags: ["PlotDetails"],
    }),

    getProjectDetails: builder.query<any, string>({
      query: (projectId) => ({
        url: `inventory/projects/${projectId}`,
      }),
      providesTags: ["ProjectDetails"],
    }),

    getBookingDetails: builder.query<any, string>({
      query: (bookingId) => ({
        url: `inventory/plot-booking/${bookingId}`,
      }),
      providesTags: ["BookingDetails"],
    }),

    // Get deposit paid information
    getDepositPaid: builder.query<
      any,
      { plot_no?: string; customer_no?: string }
    >({
      query: (params) => ({
        url: "deposits-paid",
        params,
      }),
      providesTags: ["DepositPaid"],
    }),

    // Send offer letter via email
    sendOfferLetterEmail: builder.mutation<
      { message: string },
      { id: number; email?: string }
    >({
      query: ({ id, email }) => ({
        url: `offer-letters/${id}/send-email/`,
        method: "POST",
        body: { email },
      }),
    }),

    // Legal Dashboard endpoints
    getOfferLetterCategories: builder.query<
      OfferLetterCategoriesResponse,
      {
        CATEGORY?: 'ALL' | 'ACTIVE' | 'COMPLETE';
        page?: number;
        page_size?: number;
        search?: string;
      }
    >({
      query: (params = {}) => ({
        url: "offer-letter-categories/",
        params: {
          CATEGORY: 'ALL',
          page: 1,
          page_size: 20,
          ...params,
        },
      }),
      providesTags: ["OfferLetterCategories"],
    }),

    getOfferLetterDetails: builder.query<
      OfferLetterDetailResponse,
      { offer_letter_id: number }
    >({
      query: ({ offer_letter_id }) => ({
        url: "offer-letter-details/",
        params: { offer_letter_id },
      }),
      providesTags: (result, error, { offer_letter_id }) => [
        { type: "OfferLetterDetails", id: offer_letter_id },
      ],
    }),
  }),
});

export const {
  // Main Offer Letter
  useCreateOfferLetterMutation,
  useGetOfferLetterQuery,
  useUpdateOfferLetterMutation,
  useDeleteOfferLetterMutation,
  useGetOfferLettersQuery,

  // Individual Customer
  useCreateOfferLetterIndividualMutation,
  useGetOfferLetterIndividualQuery,
  useUpdateOfferLetterIndividualMutation,

  // Company
  useCreateOfferLetterCompanyMutation,
  useGetOfferLetterCompanyQuery,
  useUpdateOfferLetterCompanyMutation,

  // Company Directors
  useCreateOfferLetterCompanyDirectorMutation,
  useGetOfferLetterCompanyDirectorsQuery,
  useUpdateOfferLetterCompanyDirectorMutation,
  useDeleteOfferLetterCompanyDirectorMutation,

  // Group
  useCreateOfferLetterGroupMutation,
  useGetOfferLetterGroupQuery,
  useUpdateOfferLetterGroupMutation,

  // Group Members
  useCreateOfferLetterGroupMemberMutation,
  useGetOfferLetterGroupMembersQuery,
  useUpdateOfferLetterGroupMemberMutation,
  useDeleteOfferLetterGroupMemberMutation,

  // Partners
  useCreateOfferLetterPartnerMutation,
  useGetOfferLetterPartnerQuery,
  useUpdateOfferLetterPartnerMutation,

  // Next of Kin
  useCreateOfferLetterNextOfKinMutation,
  useGetOfferLetterNextOfKinQuery,
  useUpdateOfferLetterNextOfKinMutation,

  // Payment Plan
  useCreateOfferLetterPaymentsMutation,
  useGetOfferLetterPaymentsQuery,
  useUpdateOfferLetterPaymentsMutation,

  // Pricing
  useGetOfferLetterPricingQuery,
  useGetOfferLetterPricingByIdQuery,

  // Terms & Conditions
  useCreateOfferLetterTermsMutation,
  useGetOfferLetterTermsQuery,
  useGetOfferLetterTermsByIdQuery,
  useUpdateOfferLetterTermsMutation,

  // Review
  useCreateOfferLetterReviewMutation,
  useGetOfferLetterReviewsQuery,
  useGetOfferLetterReviewByIdQuery,
  useUpdateOfferLetterReviewMutation,

  // Plot Payment Options
  useGetPlotPaymentOptionsQuery,
  useLazyGetPlotPaymentOptionsQuery,

  // Booking and Plot integration hooks
  useGetPlotBookingsQuery,
  useGetPlotDetailsQuery,
  useGetProjectDetailsQuery,
  useGetBookingDetailsQuery,
  useGetDepositPaidQuery,

  // Utilities
  useGenerateOfferLetterPDFMutation,
  useSendOfferLetterEmailMutation,

  // Legal Dashboard
  useGetOfferLetterCategoriesQuery,
  useGetOfferLetterDetailsQuery,
} = offerLetterApi;
