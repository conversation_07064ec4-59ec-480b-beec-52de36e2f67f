import { useState, useEffect } from "react";
import MultiStepModal from "@/components/custom/modals/MultiStepModal";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { toast } from '@/components/custom/Toast/MyToast'

interface EditGroupProps {
  isOpen: boolean;
  onClose: () => void;
  group: {
    id: string;
    groupName: string;
    groupHead: string;
    description: string;
  };
  onUpdate: (updatedGroup: { id: string; groupName: string; groupHead: string; description: string }) => void;
}

export default function EditGroup({
  isOpen,
  onClose,
  group,
  onUpdate,
}: EditGroupProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [groupData, setGroupData] = useState({
    id: "",
    groupName: "",
    groupHead: "",
    description: "",
  });

  useEffect(() => {
    if (isOpen) {
      setCurrentStep(0); 
      setGroupData(group);
    }
  }, [isOpen, group]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setGroupData((prev) => ({ ...prev, [name]: value }));
  };

  const handleUpdateGroup = () => {
    onUpdate(groupData); 
    onClose(); 
  };

  return (
    <MultiStepModal
      isOpen={isOpen}
      onOpenChange={onClose}
      title="Edit Group"
      description="Complete all steps to update the group"
      currentStep={currentStep}
      onStepChange={setCurrentStep}
      onComplete={handleUpdateGroup}
      steps={[
        {
          title: "Basic Information",
          content: (
            <div className="space-y-4 py-2">
              <div className="space-y-2">
                <Label htmlFor="groupName">Group Name</Label>
                <Input
                  id="groupName"
                  name="groupName"
                  value={groupData.groupName}
                  onChange={handleInputChange}
                  placeholder="Enter group name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="groupHead">Group Head</Label>
                <Input
                  id="groupHead"
                  name="groupHead"
                  value={groupData.groupHead}
                  onChange={handleInputChange}
                  placeholder="Enter group head name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={groupData.description}
                  onChange={handleInputChange}
                  placeholder="Describe the group"
                  rows={3}
                />
              </div>
            </div>
          ),
        },
        {
          title: "Review & Confirm",
          content: (
            <div className="space-y-4 py-2">
              <div className="bg-gray-50 p-4 rounded-md space-y-2">
                <div>
                  <span className="font-medium">Group Name:</span>
                  <span className="ml-2">{groupData.groupName}</span>
                </div>
                <div>
                  <span className="font-medium">Group Head:</span>
                  <span className="ml-2">{groupData.groupHead}</span>
                </div>
                <div>
                  <span className="font-medium">Description:</span>
                  <span className="ml-2">{groupData.description}</span>
                </div>
              </div>
              <p className="text-sm text-gray-600">
                Please review the information above before updating this group.
              </p>
            </div>
          ),
        },
      ]}
    />
  );
}