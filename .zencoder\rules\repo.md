# Optiven Client CRM Information

## Summary

A comprehensive Customer Relationship Management (CRM) system for Optiven, built with React, TypeScript, and Vite. The application manages various aspects of client relationships including customer data, sales, logistics, inventory, and reporting.

## Structure

- **src/**: Main source code directory
  - **app-components/**: Layout and sidebar components
  - **components/**: Reusable UI components organized by feature
  - **pages/**: Feature-specific page components
  - **redux/**: State management using Redux
  - **zustand/**: State management using Zustand
  - **hooks/**: Custom React hooks
  - **utils/**: Utility functions
  - **types/**: TypeScript type definitions
- **public/**: Static assets
- **docs/**: Documentation files

## Language & Runtime

**Language**: TypeScript/JavaScript
**Version**: TypeScript ~5.6.2
**Build System**: Vite 6.0.5
**Package Manager**: npm

## Dependencies

**Main Dependencies**:

- **UI Framework**: React 18.3.1, React DOM 18.3.1
- **Routing**: React Router DOM 7.5.1
- **State Management**: Redux Toolkit 2.3.0, Zustand 5.0.4
- **UI Components**: Radix UI components, Tailwind CSS 3.4.17
- **Forms**: React Hook Form 7.54.2, Zod 3.24.1
- **Data Visualization**: Recharts 2.15.0
- **API Communication**: Axios 1.7.9
- **Date Handling**: Date-fns 3.6.0, Dayjs 1.11.13

**Development Dependencies**:

- **Build Tools**: Vite 6.0.5, TypeScript 5.6.2
- **Linting**: ESLint 9.24.0
- **Styling**: Tailwind CSS 3.4.17, PostCSS 8.5.1

## Build & Installation

```bash
# Install dependencies
npm install

# Development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

## Main Entry Points

- **src/main.tsx**: Application entry point
- **src/App.tsx**: Main component with routing configuration
- **src/redux/store.ts**: Redux store configuration
- **src/pages/Home.tsx**: Dashboard home page

## Features

- **Authentication**: Login, password reset, and session management
- **User Management**: User profiles, roles, and permissions
- **Customer Management**: Customer data, prospects, and engagements
- **Sales**: Sales tracking, commissions, and reporting
- **Logistics**: Site visits, drivers, and vehicles management
- **Inventory**: Projects, bookings, and pricing
- **Reporting**: Various reports for sales, customers, and operations
- **Dashboards**: Role-specific dashboards for different departments

## Permission System

The application implements a comprehensive permission-based access control system:

- **ProtectedRoute**: Guards routes requiring authentication
- **PermissionRouteGuard**: Checks user permissions for route access
- **ProtectedPermissionRoute**: Component-level permission checks

## Testing

The application includes test files for permission system validation:

- **src/pages/test/PermissionTest.tsx**
- **src/pages/test/PermissionSystemTest.tsx**
- **src/pages/test/RoutePermissionTest.tsx**
- **src/pages/test/LogisticsPermissionTest.tsx**
