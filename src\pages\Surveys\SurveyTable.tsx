// src/components/SurveyDataTable.tsx
import React from "react";
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { FileText, Users, CheckCircle } from "lucide-react";

// Define the survey data type
interface Survey {
  id: string;
  title: string;
  value: string;
  icon: React.ComponentType<{ className?: string }>;
  change: string;
  changeLabel: string;
  positive: boolean;
}

interface SurveyDataTableProps {
  data: Survey[];
}

export const SurveyDataTable: React.FC<SurveyDataTableProps> = ({ data }) => {
  // Define table columns
  const columns: ColumnDef<Survey>[] = [
    {
      accessorKey: "id",
      header: "ID",
      cell: ({ row }) => <span>{row.original.id}</span>,
    },
    {
      accessorKey: "title",
      header: "Title",
      cell: ({ row }) => <span>{row.original.title}</span>,
    },
    {
      accessorKey: "value",
      header: "Status",
      cell: ({ row }) => <span>{row.original.value}</span>,
    },
    {
      accessorKey: "change",
      header: "Change",
      cell: ({ row }) => (
        <span className={row.original.positive ? "text-green-500" : "text-red-500"}>
          {row.original.change}
        </span>
      ),
    },
    {
      accessorKey: "changeLabel",
      header: "Change Label",
      cell: ({ row }) => <span>{row.original.changeLabel}</span>,
    },
    {
      accessorKey: "positive",
      header: "Positive",
      cell: ({ row }) => <span>{row.original.positive ? "Yes" : "No"}</span>,
    },
  ];

  // Initialize table
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <div className="mt-6 overflow-x-auto rounded-lg border border-border bg-card">
      <table className="w-full text-left text-sm text-foreground" role="grid">
        <thead className="bg-secondary">
          {table.getHeaderGroups().map((headerGroup) => (
            <tr key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <th
                  key={header.id}
                  className="px-4 py-3 font-semibold"
                  scope="col"
                  aria-label={header.column.columnDef.header as string}
                >
                  {header.isPlaceholder
                    ? null
                    : flexRender(header.column.columnDef.header, header.getContext())}
                </th>
              ))}
            </tr>
          ))}
        </thead>
        <tbody>
          {table.getRowModel().rows.length ? (
            table.getRowModel().rows.map((row) => (
              <tr
                key={row.id}
                className="border-t border-border hover:bg-secondary/50"
                role="row"
              >
                {row.getVisibleCells().map((cell) => (
                  <td key={cell.id} className="px-4 py-3" role="gridcell">
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </td>
                ))}
              </tr>
            ))
          ) : (
            <tr>
              <td colSpan={columns.length} className="px-4 py-3 text-center">
                No surveys found.
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  );
};