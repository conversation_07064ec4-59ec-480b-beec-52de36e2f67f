import React, { ReactNode } from 'react';
import { usePermissionsLoader } from '@/hooks/usePermissionsLoader';
import { Card, CardContent } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';

interface PermissionsLoaderProps {
  children: ReactNode;
  onPermissionsLoaded?: () => void;
  loadingComponent?: ReactNode;
}

/**
 * Component that ensures permissions are loaded before rendering its children
 * 
 * @param children - The content to render once permissions are loaded
 * @param onPermissionsLoaded - Optional callback to run when permissions are loaded
 * @param loadingComponent - Optional custom loading component
 */
export const PermissionsLoader: React.FC<PermissionsLoaderProps> = ({
  children,
  onPermissionsLoaded,
  loadingComponent,
}) => {
  const { isLoading } = usePermissionsLoader(onPermissionsLoaded);

  // Show loading indicator while permissions are being loaded
  if (isLoading) {
    if (loadingComponent) {
      return <>{loadingComponent}</>;
    }

    return (
      <div className="flex items-center justify-center min-h-[50vh]">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 flex flex-col items-center">
            <Loader2 className="h-8 w-8 text-primary animate-spin mb-4" />
            <p className="text-center text-muted-foreground">
              Loading permissions...
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Once permissions are loaded, render the children
  return <>{children}</>;
};

export default PermissionsLoader;