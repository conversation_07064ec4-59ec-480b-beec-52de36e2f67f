export const searchDebouncer = (
  value: string,
  inputSetter: any,
  valueSetter: any
) => {
  inputSetter(value);
  const timeout = 1000;
  console.log("in debouver", value.length);
  if (value.length > 2) {
    const timer = setTimeout(() => {
      valueSetter(value);
      console.log("in debouver setter");
    }, timeout);

    return () => clearInterval(timer);
  } else {
    console.log("in out of if");
    return;
  }
};
