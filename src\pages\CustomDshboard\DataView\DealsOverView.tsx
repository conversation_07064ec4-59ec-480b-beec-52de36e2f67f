import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { ChevronDown, ArrowUp } from "lucide-react";

function DealsStatus() {
  const totalDeals = 4289;
  const comparisonPercentage = 1.02; // 1.02% increase compared to last week
  const dealsData = [
    { status: "Successful Deals", count: 987, color: "bg-purple-600", percentage: (987 / totalDeals) * 100 },
    { status: "Pending Deals", count: 1073, color: "bg-blue-500", percentage: (1073 / totalDeals) * 100 },
    { status: "Rejected Deals", count: 1674, color: "bg-yellow-500", percentage: (1674 / totalDeals) * 100 },
    { status: "Upcoming Deals", count: 921, color: "bg-green-500", percentage: (921 / totalDeals) * 100 },
  ];

  return (
    <Card className="border-0 shadow-xl bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
      <CardHeader className="bg-yellow-400 flex flex-row items-center justify-between border-b border-gray-200 dark:border-gray-700">
        <CardTitle className="text-lg text-gray-900 dark:text-gray-100">
          Deals Status
        </CardTitle>
        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400 cursor-pointer hover:text-gray-900 dark:hover:text-gray-100 transition-colors">
          View ALL
          <ChevronDown className="w-4 h-4 ml-1" />
        </div>
      </CardHeader>
      <CardContent className="p-6">
        {/* Total Deals and Comparison */}
        <div className="flex items-center justify-between mb-4">
          <div className="text-3xl font-bold text-gray-900 dark:text-gray-100">
            {totalDeals.toLocaleString()}
          </div>
          <div className="flex items-center text-sm text-green-600 dark:text-green-400">
            <ArrowUp className="w-4 h-4 mr-1" />
            {comparisonPercentage}% compared to last week
          </div>
        </div>

        {/* Progress Bar */}
        <div className="flex w-full h-2 rounded-full overflow-hidden mb-4">
          {dealsData.map((deal, index) => (
            <div
              key={index}
              className={`${deal.color} h-full`}
              style={{ width: `${deal.percentage}%` }}
            />
          ))}
        </div>

        {/* Deals Breakdown */}
        <div className="space-y-2">
          {dealsData.map((deal, index) => (
            <div key={index} className="flex items-center justify-between text-sm">
              <div className="flex items-center">
                <div className={`w-4 h-4 rounded-full ${deal.color} mr-2`}></div>
                <span className="text-gray-600 dark:text-gray-400">{deal.status}</span>
              </div>
              <span className="font-medium text-gray-900 dark:text-gray-100">
                {deal.count.toLocaleString()} deals
              </span>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

export default DealsStatus;