import BaseModal from "@/components/custom/modals/BaseModal";
import { ticketTypes } from "..";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { Button } from "@/components/ui/button";
import { useEffect, useState } from "react";
import {
  useCreateTicketMutation,
  useLazyFetchTicketsCategoriesQuery,
  useLazyFetchTicketSourcesQuery,
} from "@/redux/slices/tickets";
import { setPriority } from "os";
import { FileText, Image } from "lucide-react";
import { toast } from "sonner";
import { useAuthHook } from "@/utils/useAuthHook";
import CustomSelectField from "@/components/CustomSelectField";
import {
  useGetCustomerQuery,
  useLazyGetCustomerQuery,
} from "@/redux/slices/projects";

interface Props {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  selectedTicket?: ticketTypes | null;
}

const AddTicketModal = ({ isOpen, onOpenChange, selectedTicket }: Props) => {
  const { user_details } = useAuthHook();
  const [title, setTitle] = useState("");
  const [category, setCategory] = useState("");
  const [priority, setPriority] = useState("");
  const [description, setDescription] = useState("");
  const [status, setStatus] = useState("");
  const [source, setSource] = useState("");
  const [customer, setCustomer] = useState("");
  const [file, setFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isPdf, setIsPdf] = useState(false);

  const handleFileChange = (e: any) => {
    const selected = e.target.files[0];
    if (selected) {
      setFile(selected);
      setIsPdf(selected.type === "application/pdf");

      if (selected.type.startsWith("image/")) {
        setPreviewUrl(URL.createObjectURL(selected));
      } else {
        setPreviewUrl(null);
      }
    } else {
      setFile(null);
      setPreviewUrl(null);
      setIsPdf(false);
    }
  };

  const [createTicket, { isLoading: creating }] = useCreateTicketMutation();
  const [fetchCategories, { data: ticketCategories, isLoading: catLoading }] =
    useLazyFetchTicketsCategoriesQuery();
  const [fetchSources, { data: ticketSources, isLoading: srcLoading }] =
    useLazyFetchTicketSourcesQuery();

  useEffect(() => {
    fetchHandler();
  }, [isOpen]);

  const fetchHandler = async () => {
    await fetchCategories({});
    await fetchSources({});
  };

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const user = user_details?.employee_no;

    if (!category) {
      toast.error("Category is required");
      return;
    }
    if (!priority) {
      toast.error("Priority is required");
      return;
    }
    if (!status) {
      toast.error("Status is required");
      return;
    }
    if (!source) {
      toast.error("Source is required");
      return;
    }

    const newFormData = new FormData();
    newFormData.append("user", user ? user : "");
    newFormData.append("source", source);
    newFormData.append("customer", customer);
    newFormData.append("title", title);
    newFormData.append("category", category);
    newFormData.append("priority", priority);
    newFormData.append("description", description);
    newFormData.append("status", status);
    newFormData.append("file", file as string | Blob);

    try {
      const res = await createTicket(newFormData).unwrap();
      if (res) {
        toast.success("Ticket created successfully");
        onOpenChange(false);
      }
    } catch (error: any) {
      console.log("Error: ", error);
      if (error?.data) {
        toast.error(`${error?.data?.error}`);
      } else {
        toast.error(`Error creating ticket`);
      }
      return;
    }
  };

  const [fetchCustomers, { data: customers, isLoading: cus_loading }] =
    useLazyGetCustomerQuery();

  return (
    <BaseModal
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      title={`${
        selectedTicket
          ? "Update " + selectedTicket?.title + " Ticket"
          : "Create Ticket"
      }`}
      size="lg"
      //   showClose={true}
      position="center"
    >
      <form onSubmit={onSubmit} className="space-y-4">
        <div className="grid grid-cols-1 gap-4 items-center text-sm">
          <label className="px-1 text-xs">
            Customers <span className="text-destructive">*</span>
          </label>
          <CustomSelectField
            valueField="customer_no"
            labelField="customer_name"
            data={customers?.data?.results}
            queryFunc={fetchCustomers}
            setValue={setCustomer}
          />
          <div className="space-y-2">
            <label className="px-1 text-xs">
              {" "}
              Ticket Title <span className="text-destructive">*</span>
            </label>
            <input
              className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              type="text"
              name="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter Title"
              required
            />
          </div>
          <div className="space-y-2">
            <label className="px-1 text-xs">
              {" "}
              Ticket Description <span className="text-destructive">*</span>
            </label>
            <textarea
              className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              name="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Enter Description"
              required
            ></textarea>
          </div>
          <div className="grid lg:grid-cols-2 grid-cols-1 gap-4">
            <div className="space-y-2">
              <label className="px-1 text-xs">
                Category <span className="text-destructive">*</span>
              </label>
              <select
                className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                name="category"
                value={category}
                onChange={(e) => setCategory(e.target.value)}
                required
              >
                {catLoading && <option>Loading...</option>}
                <option value="" className="text-gray-400">
                  Select Ticket Category
                </option>
                {!catLoading &&
                  ticketCategories &&
                  ticketCategories?.data?.results?.map(
                    (tc: { id: number; name: string }) => (
                      <option key={tc?.id} value={tc?.id}>
                        {tc.name}
                      </option>
                    )
                  )}
              </select>
            </div>
            <div className="space-y-2">
              <label className="px-1 text-xs">
                Source <span className="text-destructive">*</span>
              </label>
              <select
                className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                name="source"
                value={source}
                onChange={(e) => setSource(e.target.value)}
                required
              >
                {srcLoading && <option>Loading...</option>}
                <option value="" className="text-gray-400">
                  Select Ticket Source
                </option>
                {!srcLoading &&
                  ticketSources &&
                  ticketSources?.data?.results?.map(
                    (tc: { id: number; name: string }) => (
                      <option key={tc?.id} value={tc?.id}>
                        {tc.name}
                      </option>
                    )
                  )}
              </select>
            </div>

            <div className="space-y-2">
              <label className="px-1 text-xs">
                {" "}
                Priority <span className="text-destructive">*</span>
              </label>
              <select
                className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                name="priority"
                value={priority}
                onChange={(e) => setPriority(e.target.value)}
                required
              >
                <option value="" className="text-gray-400">
                  Select Priority
                </option>
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
                <option value="urgent">Urgent</option>
              </select>
            </div>

            <div className="space-y-2">
              <label className="px-1 text-xs">
                {" "}
                Status <span className="text-destructive">*</span>
              </label>
              <select
                className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                name="status"
                value={status}
                onChange={(e) => setStatus(e.target.value)}
                required
              >
                <option value="" className="text-gray-400">
                  Select Status
                </option>
                <option value="open">open</option>
                <option value="in_progress">in_progress</option>
                <option value="resolved">resolved</option>
                <option value="closed">closed</option>
              </select>
            </div>
          </div>
          <div className="space-y-2 ">
            <label className="px-1 text-xs">Attachment </label>
            <label
              htmlFor="file-upload"
              className="flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-2xl cursor-pointer p-6 text-center hover:border-blue-500 transition duration-300"
            >
              <input
                id="file-upload"
                type="file"
                accept="image/*,application/pdf"
                onChange={handleFileChange}
                className="hidden"
              />

              {previewUrl && !isPdf ? (
                <img
                  src={previewUrl}
                  alt="Preview"
                  className="w-full h-64 object-contain rounded-xl shadow-md"
                />
              ) : file && isPdf ? (
                <div className="flex flex-col items-center">
                  <FileText className="h-12 w-12 text-gray-400 mb-2" />
                  <p className="text-gray-700 font-medium">PDF Selected</p>
                </div>
              ) : (
                <>
                  <Image className="h-12 w-12 text-gray-400 mb-2" />
                  <p className="text-gray-500">Click to upload image or PDF</p>
                </>
              )}
            </label>

            {file && (
              <p className="mt-2 text-center text-sm text-gray-600">
                Selected file: <span className="font-medium">{file.name}</span>
              </p>
            )}
          </div>
        </div>
        <div className="w-full flex justify-end">
          {creating ? (
            <SpinnerTemp type="spinner-double" size="sm" />
          ) : (
            <Button type="submit" className="justify-center w-full">
              Submit
            </Button>
          )}
        </div>
      </form>
    </BaseModal>
  );
};

export default AddTicketModal;
