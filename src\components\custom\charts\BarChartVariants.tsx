import {
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axi<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON>ltip, Legend, ResponsiveContainer,
  Brush, ReferenceLine, Cell, LabelList
} from 'recharts';
import { BarChart3 } from 'lucide-react';
import React from 'react';

// Sample data
const data = [
  { name: 'Jan', value: 400 },
  { name: 'Feb', value: 300 },
  { name: 'Mar', value: 600 },
  { name: 'Apr', value: 800 },
  { name: 'May', value: 500 },
  { name: 'Jun', value: 350 },
];

const signData = [
  { name: 'Jan', value: 400 },
  { name: 'Feb', value: -300 },
  { name: 'Mar', value: 600 },
  { name: 'Apr', value: -800 },
  { name: 'May', value: 500 },
  { name: 'Jun', value: -350 },
];

const multiData = [
  { name: 'Jan', uv: 4000, pv: 2400, amt: 2400 },
  { name: 'Feb', uv: 3000, pv: 1398, amt: 2210 },
  { name: '<PERSON>', uv: 2000, pv: 9800, amt: 2290 },
  { name: 'Apr', uv: 2780, pv: 3908, amt: 2000 },
  { name: 'May', uv: 1890, pv: 4800, amt: 2181 },
  { name: 'Jun', uv: 2390, pv: 3800, amt: 2500 },
];

// Helper for styling
const chartStyles = {
  containerStyle: { width: '100%', height: 300 },
  margin: { top: 5, right: 30, left: 20, bottom: 5 },
  strokeDash: "3 3",
  axisLineStyle: { stroke: 'hsl(var(--muted-foreground))', strokeWidth: 1 },
  axisTick: { fill: 'hsl(var(--foreground))', fontSize: 12 },
  tooltipStyle: { 
    backgroundColor: 'hsl(var(--card))', 
    border: '1px solid hsl(var(--border))',
    borderRadius: '0.5rem',
    color: 'hsl(var(--card-foreground))'
  },
  colors: [
    "hsl(var(--primary))",
    "hsl(var(--accent))",
    "hsl(var(--destructive))",
    "hsl(var(--success))",
    "hsl(var(--warning))",
  ]
};

// Simple Bar Chart
export const SimpleBarChart = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <RechartsBarChart
          data={data}
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis
            dataKey="name"
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
          <Bar
            dataKey="value"
            fill="hsl(var(--primary))"
            radius={[4, 4, 0, 0]}
            animationDuration={1500}
          />
        </RechartsBarChart>
      </ResponsiveContainer>
    </div>
  );
};

// Tiny Bar Chart
export const TinyBarChart = () => {
  return (
    <div style={{ ...chartStyles.containerStyle, height: 150 }}>
      <ResponsiveContainer width="100%" height="100%">
        <RechartsBarChart
          data={data}
          margin={{ top: 5, right: 5, left: 5, bottom: 5 }}
        >
          <Bar
            dataKey="value"
            fill="hsl(var(--primary))"
            radius={[2, 2, 0, 0]}
            animationDuration={1500}
          />
        </RechartsBarChart>
      </ResponsiveContainer>
    </div>
  );
};

// Stacked Bar Chart
export const StackedBarChart = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <RechartsBarChart
          data={multiData}
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis
            dataKey="name"
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
          <Bar
            dataKey="uv"
            stackId="a"
            fill="hsl(var(--primary))"
            radius={[0, 0, 0, 0]}
            animationDuration={1500}
          />
          <Bar
            dataKey="pv"
            stackId="a"
            fill="hsl(var(--accent))"
            radius={[0, 0, 0, 0]}
            animationDuration={1500}
          />
          <Bar
            dataKey="amt"
            stackId="a"
            fill="hsl(var(--destructive))"
            radius={[4, 4, 0, 0]}
            animationDuration={1500}
          />
        </RechartsBarChart>
      </ResponsiveContainer>
    </div>
  );
};

// Mix Bar Chart
export const MixBarChart = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={multiData}
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            dataKey="name" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
          <Bar 
            dataKey="uv" 
            fill="hsl(var(--primary))" 
            radius={[4, 4, 0, 0]}
            animationDuration={1500}
          />
          <Bar 
            dataKey="pv" 
            fill="hsl(var(--accent))" 
            radius={[4, 4, 0, 0]}
            animationDuration={1500}
          />
          <Bar 
            dataKey="amt" 
            fill="hsl(var(--destructive))" 
            radius={[4, 4, 0, 0]}
            animationDuration={1500}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

// Custom Shape Bar Chart
interface CustomShapeBarChartProps {
  data?: Array<{ name: string; value: number }>;
}

export const CustomShapeBarChart = ({ data: propData }: CustomShapeBarChartProps) => {
  // Use provided data or fallback to sample data
  const chartData = propData && propData.length > 0 ? propData : data;

  // Show message when no data is available
  if (!chartData || chartData.length === 0) {
    return (
      <div style={chartStyles.containerStyle}>
        <div className="flex items-center justify-center h-full">
          <div className="text-center text-gray-500 dark:text-gray-400">
            <BarChart3 className="w-12 h-12 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No daily visits data available</p>
            <p className="text-xs">Visit data will appear here over time</p>
          </div>
        </div>
      </div>
    );
  }

  // Custom shape for the bars
  const getPath = (x, y, width, height) => {
    return `M${x},${y + height}
            C${x + width / 3},${y + height} ${x + width / 2},${y + height / 3} ${x + width / 2}, ${y}
            C${x + width / 2},${y + height / 3} ${x + 2 * width / 3},${y + height} ${x + width}, ${y + height}
            Z`;
  };

  const TriangleBar = (props) => {
    const { fill, x, y, width, height, index } = props;
    return <path key={`triangle-bar-${index}`} d={getPath(x, y, width, height)} stroke="none" fill={fill} />;
  };

  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <RechartsBarChart
          data={chartData}
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            dataKey="name" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Bar 
            dataKey="value" 
            fill="hsl(var(--primary))" 
            shape={<TriangleBar />}
            animationDuration={1500}
          />
        </RechartsBarChart>
      </ResponsiveContainer>
    </div>
  );
};

// Positive and Negative Bar Chart
export const PositiveAndNegativeBarChart = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={signData}
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            dataKey="name" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
          <ReferenceLine y={0} stroke="hsl(var(--border))" />
          <Bar 
            dataKey="value" 
            fill="hsl(var(--primary))" 
            radius={[4, 4, 0, 0]}
            animationDuration={1500}
          >
            {signData.map((entry, index) => (
              <Cell 
                key={`cell-${index}`} 
                fill={entry.value > 0 ? 'hsl(var(--success))' : 'hsl(var(--destructive))'}
              />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

// Brush Bar Chart
export const BrushBarChart = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={data}
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            dataKey="name" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
          <Bar 
            dataKey="value" 
            fill="hsl(var(--primary))" 
            radius={[4, 4, 0, 0]}
            animationDuration={1500}
          />
          <Brush 
            dataKey="name" 
            height={30} 
            stroke="hsl(var(--muted-foreground))"
            fill="hsl(var(--secondary))"
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

// Bar Chart With Customized Event
export const BarChartWithCustomizedEvent = () => {
  const [activeIndex, setActiveIndex] = React.useState(0);

  const handleClick = (data, index) => {
    setActiveIndex(index);
  };

  return (
    <div style={chartStyles.containerStyle}>
      <p className="mb-4 text-sm">
        <b>Click on a bar to highlight it</b>: {data[activeIndex].name} - {data[activeIndex].value}
      </p>
      <ResponsiveContainer width="100%" height="80%">
        <BarChart
          data={data}
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            dataKey="name" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Bar 
            dataKey="value" 
            onClick={handleClick}
            animationDuration={1500}
          >
            {data.map((entry, index) => (
              <Cell 
                cursor="pointer" 
                fill={index === activeIndex ? 'hsl(var(--destructive))' : 'hsl(var(--primary))'}
                key={`cell-${index}`}
                radius={[4, 4, 0, 0]}
              />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

// Bar Chart With Min Height
export const BarChartWithMinHeight = () => {
  // Min height for bars (5 pixels)
  const getBarHeight = (value) => Math.max(value, 50);

  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={data}
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            dataKey="name" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
            domain={[0, 'dataMax']}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Bar 
            dataKey="value" 
            fill="hsl(var(--primary))" 
            radius={[4, 4, 0, 0]}
            animationDuration={1500}
            minPointSize={5} // Minimum height in pixels
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

// Bar Chart Stacked By Sign
export const BarChartStackedBySign = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={signData}
          margin={chartStyles.margin}
          stackOffset="sign" // Stack by sign (positive/negative)
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            dataKey="name" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
          <ReferenceLine y={0} stroke="hsl(var(--border))" />
          <Bar 
            dataKey="value" 
            fill="hsl(var(--primary))" 
            stackId="stack"
            radius={[4, 4, 0, 0]}
            animationDuration={1500}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

// Biaxial Bar Chart
export const BiaxialBarChart = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={multiData}
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            dataKey="name" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis 
            yAxisId="left"
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis 
            yAxisId="right"
            orientation="right"
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
          <Bar 
            yAxisId="left"
            dataKey="pv" 
            fill="hsl(var(--primary))" 
            radius={[4, 4, 0, 0]}
            animationDuration={1500}
          />
          <Bar 
            yAxisId="right"
            dataKey="uv" 
            fill="hsl(var(--destructive))" 
            radius={[4, 4, 0, 0]}
            animationDuration={1500}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

// Bar Chart Has Background
export const BarChartHasBackground = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={data}
          margin={chartStyles.margin}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            dataKey="name" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
          <Bar 
            dataKey="value" 
            fill="hsl(var(--primary))" 
            radius={[4, 4, 0, 0]}
            animationDuration={1500}
            background={{ fill: 'hsl(var(--muted) / 0.2)', radius: [4, 4, 0, 0] }}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

// Bar Chart With Multi X-Axis
export const BarChartWithMultiXAxis = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={data}
          margin={{ ...chartStyles.margin, bottom: 25 }}
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            dataKey="name" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
            xAxisId={0}
          />
          <XAxis 
            dataKey="name" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
            label={{ value: 'Months', position: 'insideBottomRight', offset: 0 }}
            xAxisId={1}
            dy={10}
          />
          <YAxis 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Legend />
          <Bar 
            dataKey="value" 
            fill="hsl(var(--primary))" 
            radius={[4, 4, 0, 0]}
            animationDuration={1500}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

// Bar Chart No Padding
export const BarChartNoPadding = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={data}
          margin={chartStyles.margin}
          barCategoryGap={0} // No gap between bars of the same category
          barGap={0} // No gap between bars of different categories
        >
          <CartesianGrid strokeDasharray={chartStyles.strokeDash} className="opacity-20" />
          <XAxis 
            dataKey="name" 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <YAxis 
            axisLine={chartStyles.axisLineStyle}
            tick={chartStyles.axisTick}
          />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Bar 
            dataKey="value" 
            fill="hsl(var(--primary))" 
            animationDuration={1500}
          />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
}; 