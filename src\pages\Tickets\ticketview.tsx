import { useState, useEffect } from "react";
import { Card6 } from "@/components/custom/cards/Card6";
import { Screen } from "@/app-components/layout/screen";
import { Eye, Ticket, CheckCircle2, Trash2, Search, Filter, PlusCircle, Calendar, AlertTriangle } from "lucide-react";
import ViewComplaintModal from "./ReviewTicket";
import CreateTicket from "./ticketCreate";
import TicketsStats from "./TicketStats";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import { useLazyGetTicketsQuery } from "@/redux/slices/services";
import React from "react";
import { useToast } from "@/hooks/use-toast";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Pagination, PaginationContent, PaginationItem } from "@/components/ui/pagination";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";

class ErrorBoundary extends React.Component<{ children: React.ReactNode }, { hasError: boolean }> {
  state = { hasError: false };

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Tickets component error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-gray-100">
          <div className="text-center p-8 bg-white rounded-2xl shadow-xl max-w-md">
            <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <AlertTriangle className="h-8 w-8 text-white" />
            </div>
            <h2 className="text-2xl font-bold text-gray-800 mb-2">Oops! Something went wrong</h2>
            <p className="text-gray-600 mb-6">We're having trouble loading the tickets. Please try refreshing the page.</p>
            <button
              onClick={() => window.location.reload()}
              className="bg-gradient-to-r from-blue-500 to-purple-500 text-white px-6 py-2 rounded-lg hover:from-blue-600 hover:to-purple-600 transition-all duration-200"
            >
              Refresh Page
            </button>
          </div>
        </div>
      );
    }
    return this.props.children;
  }
}

interface Ticket {
  id: string;
  title: string;
  description: string;
  value: string;
  category: string;
  priority: string;
  assignee: string;
  entity_type: string;
  entity_id: string;
  created_at: string;
  icon?: React.ComponentType<any>;
  change?: string;
  changeLabel?: string;
}

const statusOptions = ["All", "Open", "Pending", "Resolved", "Closed"];

export default function ViewTickets() {
  const [trigger, { data: apiResponse, isLoading, isError, error }] = useLazyGetTicketsQuery();
  const { toast } = useToast();
  const [hoveredTicket, setHoveredTicket] = useState<string | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [selectedComplaint, setSelectedComplaint] = useState<Ticket | null>(null);
  const [tickets, setTickets] = useState<Ticket[]>([]);
  const [statusFilter, setStatusFilter] = useState("All");
  const [page, setPage] = useState(1);
  const [selectedIds, setSelectedIds] = useState<string[]>([]);

  // Add some sample data for testing if API fails
  const sampleTickets: Ticket[] = [
    {
      id: "sample-1",
      title: "Sample Ticket 1",
      description: "This is a sample ticket for testing",
      value: "Open",
      category: "Support",
      priority: "High",
      assignee: "John Doe",
      entity_type: "ticket",
      entity_id: "sample-1",
      created_at: new Date().toISOString(),
      icon: Ticket,
      change: "+1",
      changeLabel: "New",
    },
    {
      id: "sample-2",
      title: "Sample Ticket 2",
      description: "Another sample ticket",
      value: "Pending",
      category: "Bug",
      priority: "Medium",
      assignee: "Jane Smith",
      entity_type: "ticket",
      entity_id: "sample-2",
      created_at: new Date().toISOString(),
      icon: Ticket,
      change: "+1",
      changeLabel: "New",
    }
  ];

  // Initialize with sample data if no API data
  useEffect(() => {
    if (tickets.length === 0 && !isLoading && !apiResponse) {
      setTickets(sampleTickets);
    }
  }, [tickets.length, isLoading, apiResponse, sampleTickets]);


  useEffect(() => {
    // Add a small delay to prevent rapid API calls
    const timeoutId = setTimeout(() => {
      trigger({ page, status: statusFilter === "All" ? undefined : statusFilter })
        .catch((err) => {
          console.error("Failed to fetch tickets:", err);
          toast({
            title: "Error",
            description: "Failed to fetch tickets. Please try again.",
            variant: "destructive",
            duration: 3000,
          });
        });
    }, 100);

    return () => clearTimeout(timeoutId);
  }, [trigger, page, statusFilter, toast]);

  
  useEffect(() => {
    console.log("API Response:", apiResponse);
    try {
      if (apiResponse?.data?.results && Array.isArray(apiResponse.data.results)) {
        const transformedTickets: Ticket[] = apiResponse.data.results.map((ticket: any) => ({
          id: ticket.entity_id || ticket.id || `ticket-${Math.random().toString(36).substr(2, 9)}`,
          title: ticket.title || "Untitled",
          description: ticket.description || "No description available",
          value: ticket.status || "Open",
          category: ticket.category || "General",
          priority: ticket.priority || "Medium",
          assignee: ticket.assigned_to || "",
          entity_type: ticket.entity_type || "ticket",
          entity_id: ticket.entity_id || ticket.id || "",
          created_at: ticket.created_at || new Date().toISOString(),
          icon: Ticket,
          change: ticket.change || "+1",
          changeLabel: ticket.changeLabel || "New",
        }));
        setTickets(transformedTickets);
      } else if (apiResponse?.data && !apiResponse.data.results) {
        // Handle case where API returns data but no results array
        setTickets([]);
      }
    } catch (error) {
      console.error("Error transforming ticket data:", error);
      toast({
        title: "Data Error",
        description: "Using sample data for demonstration.",
        variant: "default",
        duration: 3000,
      });
      setTickets(sampleTickets);
    }
  }, [apiResponse, toast, sampleTickets]);

  const handleViewTicket = (ticket: Ticket) => {
    setSelectedComplaint(ticket);
    setIsViewModalOpen(true);
  };

  const handleTicketCreated = (newTicket: Ticket) => {
    setTickets([...tickets, { ...newTicket, icon: Ticket }]);
    toast({
      title: "Ticket Created",
      description: `${newTicket.title} has been added.`,
      duration: 3000,
    });
  };

  const handleTicketReassigned = (ticketId: string | number, newAssignee: string) => {
    const idString = ticketId.toString();
    setTickets(tickets.map((ticket) =>
      ticket.id === idString ? { ...ticket, assignee: newAssignee } : ticket
    ));
    if (selectedComplaint?.id === idString) {
      setSelectedComplaint({ ...selectedComplaint, assignee: newAssignee });
    }
    toast({
      title: "Ticket Reassigned",
      description: `Assigned to ${newAssignee}.`,
      duration: 3000,
    });
  };

  // Filtering
  const filteredTickets =
    statusFilter === "All"
      ? tickets
      : tickets.filter((ticket) => ticket.value === statusFilter);

  // Pagination
  const totalPages = apiResponse?.data?.last_page || Math.ceil(filteredTickets.length / 6);
  const paginatedTickets = filteredTickets; // API handles pagination

  // Reset to page 1 when filter changes
  const handleFilterChange = (value: string) => {
    setStatusFilter(value);
    setPage(1);
    setSelectedIds([]);
  };

  // Bulk actions
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedIds(paginatedTickets.map((t) => t.id));
    } else {
      setSelectedIds([]);
    }
  };

  const handleSelectOne = (id: string, checked: boolean) => {
    setSelectedIds(checked ? [...selectedIds, id] : selectedIds.filter((i) => i !== id));
  };

  const handleBulkClose = () => {
    setTickets(tickets.map((ticket) =>
      selectedIds.includes(ticket.id) ? { ...ticket, value: "Resolved" } : ticket
    ));
    setSelectedIds([]);
    toast({
      title: "Tickets Closed",
      description: `${selectedIds.length} tickets marked as resolved.`,
      duration: 3000,
    });
  };

  const handleBulkDelete = () => {
    setTickets(tickets.filter((ticket) => !selectedIds.includes(ticket.id)));
    setSelectedIds([]);
    toast({
      title: "Tickets Deleted",
      description: `${selectedIds.length} tickets deleted.`,
      variant: "destructive",
      duration: 3000,
    });
  };

  return (
    <ErrorBoundary>
      <Screen>
        {/* Hero Section with Gradient Background */}
        <div className="relative overflow-hidden bg-gradient-to-br from-green-600 via-green-600 to-pink-500 text-white">
          <div className="absolute inset-0 bg-black/20"></div>
          <div className="relative container mx-auto px-6 py-12">
            <div className="flex flex-col md:flex-row md:items-center justify-between gap-6">
              <div className="space-y-2">
                <h1 className="text-4xl font-bold bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">
                  Support Tickets
                </h1>
                <p className="text-blue-100 text-lg">Manage and track customer support requests</p>
                {statusFilter !== "All" && (
                  <div className="flex items-center gap-2 mt-2">
                    <div className="px-3 py-1 bg-white/20 rounded-full backdrop-blur-sm border border-white/30">
                      <span className="text-sm font-medium">Filtered by: {statusFilter}</span>
                    </div>
                    <button
                      onClick={() => handleFilterChange("All")}
                      className="px-3 py-1 bg-white/10 hover:bg-white/20 rounded-full backdrop-blur-sm border border-white/30 transition-all duration-200 text-sm"
                    >
                      Clear Filter
                    </button>
                  </div>
                )}
              </div>
              <div className="flex flex-col sm:flex-row gap-3">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search tickets..."
                    className="pl-10 w-full sm:w-64 bg-white/10 border-white/20 text-white placeholder:text-white/70 backdrop-blur-sm"
                  />
                </div>

              </div>
            </div>
          </div>
        </div>

        <div className="container mx-auto px-6 -mt-6 relative z-10">

          {/* Statistics Dashboard */}
          <div className="mb-8">
            <React.Suspense fallback={
              <div className="bg-white rounded-2xl shadow-xl border-0 overflow-hidden animate-pulse">
                <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-500 p-6 text-white">
                  <div className="h-6 bg-white/20 rounded w-48 mb-2"></div>
                  <div className="h-4 bg-white/20 rounded w-64"></div>
                </div>
                <div className="p-6">
                  <div className="flex flex-col lg:flex-row gap-8 items-center">
                    <div className="w-full lg:w-1/3 flex justify-center">
                      <div className="w-48 h-48 bg-gray-200 rounded-full"></div>
                    </div>
                    <div className="flex-1 grid grid-cols-2 lg:grid-cols-4 gap-4 w-full">
                      {[...Array(4)].map((_, i) => (
                        <div key={i} className="bg-gray-50 rounded-xl p-6 border border-gray-200">
                          <div className="h-10 bg-gray-200 rounded mb-3"></div>
                          <div className="h-8 bg-gray-200 rounded w-16 mb-1"></div>
                          <div className="h-4 bg-gray-200 rounded w-20"></div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            }>
              <TicketsStats
                tickets={tickets}
                onStatusFilter={handleFilterChange}
                currentFilter={statusFilter}
              />
            </React.Suspense>
          </div>

          {/* Filter Card with Gradient Border */}
          <Card className="mb-8 bg-gradient-to-r from-white to-gray-50 border-0 shadow-xl">
            <div className="bg-gradient-to-r from-blue-500 to-purple-500 p-[1px] rounded-lg">
              <div className="bg-white rounded-lg">
                <CardHeader className="pb-3 bg-gradient-to-r from-blue-50 to-purple-50 rounded-t-lg">
                  <CardTitle className="text-lg bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    Filter Tickets
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-6">
                  <Tabs
                    defaultValue={statusFilter}
                    onValueChange={handleFilterChange}
                    className="w-full"
                  >
                    <TabsList className="w-full justify-start overflow-x-auto bg-gradient-to-r from-gray-100 to-gray-200 p-1">
                      {statusOptions.map((status) => (
                        <TabsTrigger
                          key={status}
                          value={status}
                          className="px-4 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-500 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-200"
                        >
                          {status}
                          {status !== "All" && (
                            <Badge
                              variant="secondary"
                              className="ml-2 bg-white/20 text-current border-0"
                            >
                              {tickets.filter(t => t.value === status).length}
                            </Badge>
                          )}
                        </TabsTrigger>
                      ))}
                    </TabsList>
                  </Tabs>
                </CardContent>
              </div>
            </div>
          </Card>

          {selectedIds.length > 0 && (
            <div className="flex items-center gap-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl px-6 py-4 my-6 shadow-lg backdrop-blur-sm">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                <span className="font-semibold text-blue-700">{selectedIds.length} tickets selected</span>
              </div>
              <div className="flex gap-3 ml-auto">
                <PrimaryButton
                  className="flex items-center gap-2 px-4 py-2 rounded-lg bg-gradient-to-r from-green-500 to-emerald-500 text-white hover:from-green-600 hover:to-emerald-600 shadow-lg transform hover:scale-105 transition-all duration-200"
                  onClick={handleBulkClose}
                >
                  <CheckCircle2 className="w-4 h-4" /> Resolve
                </PrimaryButton>
                <PrimaryButton
                  className="flex items-center gap-2 px-4 py-2 rounded-lg bg-gradient-to-r from-red-500 to-pink-500 text-white hover:from-red-600 hover:to-pink-600 shadow-lg transform hover:scale-105 transition-all duration-200"
                  onClick={handleBulkDelete}
                >
                  <Trash2 className="w-4 h-4" /> Delete
                </PrimaryButton>
              </div>
            </div>
          )}

          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <Card key={i} className="animate-pulse h-56 bg-gradient-to-br from-gray-50 to-gray-100 border-0 shadow-lg">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="h-5 w-5 bg-gradient-to-r from-blue-300 to-purple-300 rounded"></div>
                      <div className="h-4 bg-gradient-to-r from-gray-300 to-gray-400 rounded w-3/4"></div>
                    </div>
                    <div className="space-y-3">
                      <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-full"></div>
                      <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-5/6"></div>
                      <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-4/6"></div>
                    </div>
                    <div className="flex justify-between items-center mt-8">
                      <div className="h-6 bg-gradient-to-r from-gray-300 to-gray-400 rounded w-1/4"></div>
                      <div className="h-8 w-8 bg-gradient-to-r from-blue-300 to-purple-300 rounded-full"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : paginatedTickets.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {paginatedTickets.map((ticket) => {
                const getStatusColor = (status: string) => {
                  switch (status) {
                    case "Open": return "from-red-500 to-orange-500";
                    case "Pending": return "from-yellow-500 to-amber-500";
                    case "Resolved": return "from-green-500 to-emerald-500";
                    case "Closed": return "from-gray-500 to-slate-500";
                    default: return "from-blue-500 to-indigo-500";
                  }
                };

                const getPriorityColor = (priority: string) => {
                  switch (priority?.toLowerCase()) {
                    case "high": return "from-red-400 to-pink-400";
                    case "medium": return "from-yellow-400 to-orange-400";
                    case "low": return "from-green-400 to-teal-400";
                    default: return "from-blue-400 to-indigo-400";
                  }
                };

                return (
                  <Card
                    key={ticket.id}
                    className="group relative overflow-hidden border-0 bg-white shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1"
                    onMouseEnter={() => setHoveredTicket(ticket.id)}
                    onMouseLeave={() => setHoveredTicket(null)}
                  >
                    {/* Gradient Border */}
                    <div className={`absolute inset-0 bg-gradient-to-r ${getStatusColor(ticket.value)} opacity-0 group-hover:opacity-100 transition-opacity duration-300`}></div>
                    <div className="absolute inset-[1px] bg-white rounded-lg"></div>

                    {/* Status Indicator */}
                    <div className={`absolute top-0 left-0 right-0 h-1 bg-gradient-to-r ${getStatusColor(ticket.value)}`}></div>

                    <CardContent className="relative p-6 z-10">
                      <div className="flex justify-between items-start mb-4">
                        <div className="flex items-center gap-3">
                          <div className={`p-2 rounded-lg bg-gradient-to-r ${getStatusColor(ticket.value)} shadow-lg`}>
                            <Ticket className="h-5 w-5 text-white" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-lg text-gray-800 group-hover:text-gray-900 transition-colors">
                              {ticket.title}
                            </h3>
                            {ticket.priority && (
                              <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gradient-to-r ${getPriorityColor(ticket.priority)} text-white mt-1`}>
                                {ticket.priority} Priority
                              </div>
                            )}
                          </div>
                        </div>
                        <Badge
                          className={`bg-gradient-to-r ${getStatusColor(ticket.value)} text-white border-0 shadow-md`}
                        >
                          {ticket.value}
                        </Badge>
                      </div>

                      <p className="text-gray-600 text-sm line-clamp-2 mb-4 leading-relaxed">
                        {ticket.description}
                      </p>

                      {ticket.assignee && (
                        <div className="flex items-center gap-2 mb-4 p-2 bg-gray-50 rounded-lg">
                          <div className="w-6 h-6 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full flex items-center justify-center">
                            <span className="text-white text-xs font-medium">
                              {ticket.assignee.charAt(0).toUpperCase()}
                            </span>
                          </div>
                          <span className="text-sm text-gray-600">Assigned to {ticket.assignee}</span>
                        </div>
                      )}

                      <div className="flex justify-between items-center mt-6">
                        <div className="text-xs text-gray-500 flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {new Date(ticket.created_at).toLocaleDateString()}
                        </div>
                        <div className="flex gap-2">
                          <input
                            type="checkbox"
                            checked={selectedIds.includes(ticket.id)}
                            onChange={(e) => handleSelectOne(ticket.id, e.target.checked)}
                            className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                          />
                          <button
                            onClick={() => handleViewTicket(ticket)}
                            className="p-2 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 text-white hover:from-blue-600 hover:to-purple-600 shadow-lg transform hover:scale-110 transition-all duration-200"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-16 bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 rounded-2xl border border-gray-200 shadow-inner">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full blur-3xl opacity-20 scale-150"></div>
                <div className="relative bg-gradient-to-r from-blue-500 to-purple-500 p-4 rounded-2xl w-20 h-20 mx-auto mb-6 shadow-lg">
                  <Ticket className="h-12 w-12 text-white" />
                </div>
              </div>
              <h3 className="text-2xl font-bold bg-gradient-to-r from-gray-700 to-gray-900 bg-clip-text text-transparent mb-2">
                No tickets found
              </h3>
              <p className="text-gray-600 mb-8 max-w-md mx-auto">
                No tickets match your current filter criteria. Create a new ticket to get started!
              </p>
             
            </div>
          )}

          {totalPages > 1 && (
            <div className="mt-8 flex justify-center">
              <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-2">
                <Pagination>
                  <PaginationContent className="gap-1">
                    <PaginationItem>
                      <button
                        onClick={() => setPage(Math.max(1, page - 1))}
                        disabled={page === 1}
                        className={`px-4 py-2 rounded-xl font-medium transition-all duration-200 ${
                          page === 1
                            ? "text-gray-400 cursor-not-allowed"
                            : "text-gray-600 hover:bg-gradient-to-r hover:from-blue-500 hover:to-purple-500 hover:text-white hover:shadow-lg transform hover:scale-105"
                        }`}
                      >
                        Previous
                      </button>
                    </PaginationItem>
                    {[...Array(Math.min(totalPages, 5))].map((_, i) => {
                      const pageNum = i + 1;
                      return (
                        <PaginationItem key={i}>
                          <button
                            className={`h-10 w-10 rounded-xl flex items-center justify-center font-medium transition-all duration-200 ${
                              page === pageNum
                                ? "bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg transform scale-105"
                                : "text-gray-600 hover:bg-gradient-to-r hover:from-blue-100 hover:to-purple-100 hover:text-blue-600"
                            }`}
                            onClick={() => setPage(pageNum)}
                          >
                            {pageNum}
                          </button>
                        </PaginationItem>
                      );
                    })}
                    <PaginationItem>
                      <button
                        onClick={() => setPage(Math.min(totalPages, page + 1))}
                        disabled={page === totalPages}
                        className={`px-4 py-2 rounded-xl font-medium transition-all duration-200 ${
                          page === totalPages
                            ? "text-gray-400 cursor-not-allowed"
                            : "text-gray-600 hover:bg-gradient-to-r hover:from-blue-500 hover:to-purple-500 hover:text-white hover:shadow-lg transform hover:scale-105"
                        }`}
                      >
                        Next
                      </button>
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            </div>
          )}

          {isViewModalOpen && selectedComplaint && (
            <ViewComplaintModal
              isOpen={isViewModalOpen}
              onClose={() => setIsViewModalOpen(false)}
              complaint={selectedComplaint}
              onReassign={handleTicketReassigned}
            />
          )}

          {/* Floating Action Button */}
          <div className="fixed bottom-8 right-8 z-50">
            
          </div>
        </div>
      </Screen>
    </ErrorBoundary>
  );
}
