import { apiSlice } from "../apiSlice";

export const projectsApiSlice = apiSlice.injectEndpoints({
    endpoints: (builder) => ({
        getDiasporaDashboard: builder.query({
            query: (params) => ({
                url: "/diaspora-index",
                method: "GET",
                params: params,
            }),
            providesTags: ["Teams"],
        }),
        getAccountsDashboard: builder.query({
            query: (params) => ({
                url: `/accounts-index`,
                method: "GET",
                params: params,
            }),
            providesTags: ["Teams"],
        }),
        getAccountsCreditsDashboard: builder.query({
            query: (params) => ({
                url: `/accounts-credits-team-index`,
                method: "GET",
                params: params,
            }),
            providesTags: ["Teams"],
        }),
        getAccountsCreditTeamDetails: builder.query({
            query: (params) => ({
                url: `/accounts-credits-team-details`,
                method: "GET",
                params: params,
            }),
            providesTags: ["Teams"],
        }),
        getGMKarenDashboard: builder.query({
            query: (params) => ({
                url: `/Global-hos-index`,
                method: "GET",
                params: params,
            }),
            providesTags: ["Teams"],
        }),
        getHqHosDashboard: builder.query({
            query: (params) => ({
                url: `/hq-hos-index`,
                method: "GET",
                params: params,
            }),
            providesTags: ["Teams"],
        }),
        getMarketerDashboard: builder.query({
            query: (params) => ({
                url: `/Marketer-index`,
                method: "GET",
                params: params,
            }),
            providesTags: ["Teams"],
        }),
    })
})

export const {
    useGetAccountsDashboardQuery,
    useGetDiasporaDashboardQuery,
    useGetAccountsCreditsDashboardQuery,
    useGetAccountsCreditTeamDetailsQuery,
    useGetGMKarenDashboardQuery,
    useGetHqHosDashboardQuery,
    useGetMarketerDashboardQuery,
} = projectsApiSlice;