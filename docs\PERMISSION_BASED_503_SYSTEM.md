# Permission-Based 503 Service Unavailable System

This document describes the implementation of a permission-based access control system that shows a 503 Service Unavailable page when users try to access resources they don't have permissions for.

## Overview

The system provides multiple layers of protection:

1. **Route-level protection** using `ProtectedPermissionRoute`
2. **Navigation protection** using `PermissionAwareLink`
3. **Programmatic navigation protection** using `usePermissionNavigation` hook
4. **Custom 503 Service Unavailable page** with contact information

## Components

### 1. ServiceUnavailable Component

A beautiful 503 error page that displays when users don't have access to a resource.

**Location**: `src/components/custom/error/ServiceUnavailable.tsx`

**Features**:
- Customizable title and description
- Contact information for admin and ICT systems team
- Professional design with loading states
- Fallback SVG if image fails to load

**Usage**:
```tsx
<ServiceUnavailable 
  title="Access Denied"
  description="You do not have permission to access this resource."
  showContactInfo={true}
/>
```

### 2. PermissionGuard Component

A wrapper component that checks permissions before rendering children.

**Location**: `src/components/auth/PermissionGuard.tsx`

**Features**:
- Automatic route-to-permission mapping
- Support for custom permission requirements
- Loading state handling
- Fallback to 503 page when access denied

**Usage**:
```tsx
<PermissionGuard requiredPermissions={[117]}>
  <AdminPanel />
</PermissionGuard>
```

### 3. ProtectedPermissionRoute Component

A route wrapper that combines authentication and permission checking.

**Location**: `src/components/auth/ProtectedPermissionRoute.tsx`

**Usage in App.tsx**:
```tsx
<Route 
  path="/admin/services" 
  element={
    <ProtectedPermissionRoute requiredPermissions={[116]}>
      <AdminServices />
    </ProtectedPermissionRoute>
  } 
/>
```

### 4. PermissionAwareLink Component

A Link component that checks permissions before navigation.

**Location**: `src/components/navigation/PermissionAwareLink.tsx`

**Features**:
- Prevents navigation to unauthorized routes
- Automatic permission checking based on target route
- Redirects to `/unauthorized` page when access denied
- Custom fallback handling

**Usage**:
```tsx
<PermissionAwareLink to="/admin/services">
  Admin Services
</PermissionAwareLink>
```

### 5. usePermissionNavigation Hook

A hook for programmatic navigation with permission checking.

**Location**: `src/hooks/usePermissionNavigation.ts`

**Usage**:
```tsx
const { navigateWithPermissionCheck, canAccessRoute } = usePermissionNavigation();

// Navigate with permission check
navigateWithPermissionCheck('/admin/services');

// Check if user can access a route
if (canAccessRoute('/admin/services')) {
  // Show admin menu
}
```

## Permission Mapping

The system uses a centralized permission mapping that maps routes to required permission codes:

```typescript
const ROUTE_PERMISSIONS: Record<string, number[]> = {
  // Main section routes (permission 111)
  '/': [111],
  '/logistics-dash': [111],
  '/logistics': [111],
  
  // Services routes (permission 116)
  '/admin/services': [116],
  '/complaints': [116],
  '/feedback': [116],
  
  // Admin routes (permission 117)
  '/permissions': [117],
  '/admin/teams': [117],
  
  // Profile routes - accessible to all
  '/profile': [],
};
```

## Permission Codes

- **111**: Main section (Dashboard, Logistics, Sales, Customers, Prospects, Inventory)
- **112**: Performance section
- **113**: Teams section
- **114**: Reports section
- **115**: Analytics section
- **116**: Services section
- **117**: Admin section

## Implementation Examples

### 1. Protecting a Route

```tsx
// In App.tsx
<Route 
  path="/admin/services" 
  element={
    <ProtectedPermissionRoute requiredPermissions={[116]}>
      <AdminServices />
    </ProtectedPermissionRoute>
  } 
/>
```

### 2. Protecting Navigation Links

```tsx
// In sidebar or navigation components
<PermissionAwareLink to="/admin/services" className="nav-link">
  Admin Services
</PermissionAwareLink>
```

### 3. Conditional Rendering Based on Permissions

```tsx
import { useSidebarPermissions } from '@/hooks/useSidebarPermissions';

const MyComponent = () => {
  const { hasPermission } = useSidebarPermissions();
  
  return (
    <div>
      {hasPermission(117) && (
        <AdminPanel />
      )}
      {hasPermission(116) && (
        <ServicesPanel />
      )}
    </div>
  );
};
```

### 4. Programmatic Navigation with Permission Check

```tsx
import { usePermissionNavigation } from '@/hooks/usePermissionNavigation';

const MyComponent = () => {
  const { navigateWithPermissionCheck } = usePermissionNavigation();
  
  const handleAdminClick = () => {
    navigateWithPermissionCheck('/admin/services');
  };
  
  return (
    <button onClick={handleAdminClick}>
      Go to Admin Services
    </button>
  );
};
```

## Contact Information

When users see the 503 page, they are provided with contact information:

- **Administrator**: <EMAIL>
- **ICT Systems Team**: <EMAIL>

## Testing

1. **Test with different user permissions**:
   - Log in with users having different permission levels
   - Try accessing routes they don't have permissions for
   - Verify 503 page is shown with proper contact information

2. **Test navigation**:
   - Click on sidebar links without permissions
   - Verify redirection to `/unauthorized` page
   - Test programmatic navigation

3. **Test route protection**:
   - Directly navigate to protected URLs
   - Verify permission checking works correctly

## Security Notes

- This is frontend-only permission checking for UI/UX purposes
- Backend APIs should also validate permissions
- Never rely solely on frontend permission checks for security
- Always implement proper authorization on the server side

## Quick Start

1. **Test the system**: Visit `/test/permission-system` to see the permission system in action
2. **Protect a route**: Wrap your route with `ProtectedPermissionRoute`
3. **Use permission-aware links**: Replace `Link` with `PermissionAwareLink` in navigation
4. **Check permissions programmatically**: Use `useSidebarPermissions` hook

## Files Created/Modified

### New Files:
- `src/components/custom/error/ServiceUnavailable.tsx` - 503 error page
- `src/components/auth/PermissionGuard.tsx` - Permission checking wrapper
- `src/components/auth/ProtectedPermissionRoute.tsx` - Route protection component
- `src/components/navigation/PermissionAwareLink.tsx` - Permission-aware Link component
- `src/hooks/usePermissionNavigation.ts` - Navigation hook with permission checking
- `src/pages/test/PermissionSystemTest.tsx` - Test page for the permission system
- `docs/PERMISSION_BASED_503_SYSTEM.md` - This documentation

### Modified Files:
- `src/App.tsx` - Added unauthorized route and example protected routes
- `src/app-components/sidebar/nav-main.tsx` - Updated to use PermissionAwareLink

## Future Enhancements

1. **Role-based permissions**: Extend to support role-based access control
2. **Dynamic permissions**: Support for permissions that change based on context
3. **Permission caching**: Implement caching for better performance
4. **Audit logging**: Log permission denials for security monitoring
