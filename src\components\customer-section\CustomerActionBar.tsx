
import { FileText, Mail, Phone, FileCheck, Calendar, MoreHorizontal } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface CustomerActionBarProps {
  onNoteClick: () => void;
  onEmailClick: () => void;
  onCallClick: () => void;
  onTaskClick: () => void;
  onMeetingClick: () => void;
}

const CustomerActionBar = ({
  onNoteClick,
  onEmailClick,
  onCallClick,
  onTaskClick,
  onMeetingClick,
}: CustomerActionBarProps) => {
  return (
    <div className="flex flex-wrap gap-2 p-4 border-b overflow-x-auto">
    <Button variant="ghost" size="sm" onClick={onNoteClick} className="shrink-0">
      <FileText className="h-4 w-4 mr-2" />
      <span className="hidden sm:inline">Add Note</span>
      <span className="inline sm:hidden">Note</span>
    </Button>
    
    <Button variant="ghost" size="sm" onClick={onEmailClick} className="shrink-0">
      <Mail className="h-4 w-4 mr-2" />
      <span className="hidden sm:inline">Send Email</span>
      <span className="inline sm:hidden">Email</span>
    </Button>
    
    <Button variant="ghost" size="sm" onClick={onCallClick} className="shrink-0">
      <Phone className="h-4 w-4 mr-2" />
      <span className="hidden sm:inline">Log Call</span>
      <span className="inline sm:hidden">Call</span>
    </Button>
    
    <Button variant="ghost" size="sm" onClick={onTaskClick} className="shrink-0">
      <FileCheck className="h-4 w-4 mr-2" />
      <span className="hidden sm:inline">Add Task</span>
      <span className="inline sm:hidden">Task</span>
    </Button>
    
    <Button variant="ghost" size="sm" onClick={onMeetingClick} className="shrink-0">
      <Calendar className="h-4 w-4 mr-2" />
      <span className="hidden sm:inline">Schedule Meeting</span>
      <span className="inline sm:hidden">Meeting</span>
    </Button>
    
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="shrink-0">
          <MoreHorizontal className="h-4 w-4" />
          <span className="sr-only">More actions</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem>Create deal</DropdownMenuItem>
        <DropdownMenuItem>Create ticket</DropdownMenuItem>
        <DropdownMenuItem>Add to workflow</DropdownMenuItem>
        <DropdownMenuItem>Schedule sequence</DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  </div>
  );
};

export default CustomerActionBar;