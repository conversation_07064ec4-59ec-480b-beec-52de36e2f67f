import Logo from "@/assets/logo.svg";
import { toast } from "@/components/custom/Toast/MyToast";
import { usePostLoginMutation } from "@/redux/slices/auth";
import { useAuthHook } from "@/utils/useAuthHook";
import { <PERSON>, EyeOff, Loader, Lock, Play } from "lucide-react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import useLockScreenStore from "@/zustand/useLockScreenStore";
import { useNavigate } from "react-router-dom";

const LockScreenPage = () => {
  useEffect(() => {
    // Replace history so back button doesn't return to previous page
    window.history.replaceState(
      null,
      "",
      window.location.pathname + window.location.search
    );
    const handlePopState = (e: PopStateEvent) => {
      // Always push user forward if they try to go back
      window.history.go(1);
    };
    window.addEventListener("popstate", handlePopState);
    return () => {
      window.removeEventListener("popstate", handlePopState);
    };
  }, []);

  // zustand
  const navigate = useNavigate();
  const route = useLockScreenStore((state) => state.route);

  // pass
  const [showPassword, setShowPassword] = useState(false);

  // user
  const { user_details } = useAuthHook();
  const email = user_details?.email || "";
  const fullnames = user_details?.fullnames || "";

  // form
  const formSchema = z.object({
    password: z.string().min(1, { message: "Password is required." }),
  });

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
  });
  const [postLogin, { isLoading, error }] = usePostLoginMutation();
  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      await postLogin({ username: email, ...values }).unwrap();
      toast.success("Login successful");
      navigate(route, { replace: true });
      // console.log('Login successful:', response);
    } catch (err: any) {
      console.error("Login failed:", err);
      const { data } = err;
      const { non_field_errors, username } = data;
      toast.error(
        (Array.isArray(non_field_errors) && non_field_errors[0]) ||
          (Array.isArray(username) && username[0]) ||
          "Something went wrong!"
      );
      // console.error('Login failed:', err);
    }
  }

  return (
    <>
      {/* Right Panel - Login Form */}
      <div className="w-full lg:w-1/2 flex items-center justify-center p-8 bg-gray-900">
        <div className="w-full max-w-md space-y-8">
          {/* Mobile Logo */}
          <div className="lg:hidden flex justify-center">
            <div className="absolute top-6 left-6 z-10   px-3 py-1.5 rounded">
              <img src={Logo} alt="Optiven Logo" className="w-32 h-auto" />
            </div>
          </div>

          {/* Header */}
          <div className="text-center space-y-2">
            <h1 className="text-3xl font-bold text-white">
              Timua Kivumbi Na 5 Billion
              <br />
              By Optiven
            </h1>
          </div>

          {/* Login Link */}
          <div className="text-center">
            <span className="text-gray-400 text-sm">
              Welcome back {fullnames}, kindly re-enter your password below to
              continue.
            </span>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="space-y-6">
              {/* Password Field */}
              <div>
                <div className="relative">
                  <Lock className="absolute left-5 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    {...register("password")}
                    type={showPassword ? "text" : "password"}
                    placeholder="Password"
                    autoComplete="off"
                    className="w-full pl-14 py-3 bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all pr-12"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-300 transition-colors"
                  >
                    {showPassword ? (
                      <EyeOff className="w-5 h-5" />
                    ) : (
                      <Eye className="w-5 h-5" />
                    )}
                  </button>
                </div>
                {errors.password && (
                  <p className="text-red-500 text-xs pt-2 pl-3">
                    {errors.password.message}
                  </p>
                )}
              </div>

              {/* Submit Button */}
              <button
                className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg transition-colors  group"
                type="submit"
                disabled={isLoading}
              >
                {isLoading ? (
                  <span className="flex items-center justify-center space-x-2 ">
                    <Loader className="fas fa-spinner animate-spin" size={22} />
                    <span>SIGNING IN...</span>
                  </span>
                ) : (
                  <span className="flex items-center justify-center space-x-2 ">
                    <span>SIGN IN</span>
                    <Play className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                  </span>
                )}
              </button>
            </div>
          </form>

          {/* Terms */}
          <div className="text-center">
            <p className="text-xs text-gray-500">
              By creating your Gen account, you agree to our{" "}
              <button className="text-green-400 hover:text-green-300 underline">
                Terms of Service
              </button>{" "}
              and{" "}
              <button className="text-green-400 hover:text-green-300 underline">
                Privacy Policy
              </button>
            </p>
          </div>
        </div>
      </div>
    </>
  );
};

export default LockScreenPage;

// import { zodResolver } from "@hookform/resolvers/zod"
// import { useForm } from "react-hook-form"
// import { z } from "zod"
// import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
// import { Input } from "@/components/ui/input"
// import { PrimaryButton } from "@/components/custom/buttons/buttons"
// import { usePostLoginMutation } from "@/redux/slices/auth"
// import { toast } from "@/components/custom/Toast/MyToast"
// import { Loader } from "lucide-react"
// import { useAuthHook } from "@/utils/useAuthHook"
// import { useLocation, useNavigate } from "react-router-dom"
// import { useEffect } from "react"

// const formSchema = z.object({
//   password: z.string().min(1, { message: "Password is required" }),
// });

// const LockScreenPage = () => {
//     useEffect(() => {
//     // Replace history so back button doesn't return to previous page
//     window.history.replaceState(null, '', window.location.pathname + window.location.search);
//     const handlePopState = (e: PopStateEvent) => {
//         // Always push user forward if they try to go back
//         window.history.go(1);
//     };
//     window.addEventListener('popstate', handlePopState);
//     return () => {
//         window.removeEventListener('popstate', handlePopState);
//     };
// }, []);

//     const form = useForm<z.infer<typeof formSchema>>({
//         resolver: zodResolver(formSchema),
//         defaultValues: {
//             password: "",
//         },
//     })

//   const navigate = useNavigate();
//   const location = useLocation();

//   // Get the intended destination from location state, or default to dashboard
//   const from = location.state?.from || "/";

//   const { user_details } = useAuthHook();
//   const email = user_details?.email || "";

//   const [postLogin, { isLoading, error }] = usePostLoginMutation();

//   async function onSubmit(values: z.infer<typeof formSchema>) {
//     try {
//       const response = await postLogin({ username: email, ...values }).unwrap();
//       toast.success("Login successful");
//       navigate(from, { replace: true });
//       // console.log('Login successful:', response);
//     } catch (err: any) {
//       console.error("Login failed:", err);
//       const { data } = err;
//       const { non_field_errors, username } = data;
//       toast.error(
//         (Array.isArray(non_field_errors) && non_field_errors[0]) ||
//           (Array.isArray(username) && username[0]) ||
//           "Something went wrong!"
//       );
//       // console.error('Login failed:', err);
//     }
//   }

//   return (
//     <div className="w-full flex justify-center items-center">
//       <div className="w-full px-3 md:w-1/3 md:min-w-[420px] md:px-0">
//         <div className="flex flex-col items-center gap-6 mb-5">
//           {/* <img
//             src="https://www.optiven.co.ke/wp-content/uploads/2023/09/optiven-group-logo.png"
//             width={180}
//             className=""
//           /> */}
//           <p className="text-sm text-center">
//             Hello {email}, kindly re-enter your password below to continue.
//           </p>
//         </div>

//         <Form {...form}>
//           <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
//             <FormField
//               control={form.control}
//               name="password"
//               render={({ field }) => (
//                 <FormItem>
//                   <FormLabel>Password</FormLabel>
//                   <FormControl>
//                     <Input
//                       placeholder=""
//                       {...field}
//                       type="password"
//                       className="border border-border rounded-sm"
//                     />
//                   </FormControl>
//                   <FormMessage />
//                 </FormItem>
//               )}
//             />
//             <PrimaryButton type="submit" className="w-full rounded-sm">
//               {isLoading ? (
//                 <span className="flex gap-2 items-center justify-center">
//                   <Loader className="fas fa-spinner animate-spin" size={20} />
//                   Loading...
//                 </span>
//               ) : (
//                 "Submit"
//               )}
//             </PrimaryButton>
//           </form>
//         </Form>
//       </div>
//     </div>
//   );
// };

// export default LockScreenPage;
