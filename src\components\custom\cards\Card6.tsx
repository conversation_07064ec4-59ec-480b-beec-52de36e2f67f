"use client";

import * as React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>er,
  <PERSON>Title,
  CardDescription,
} from "@/components/ui/card";
import { LucideIcon, ArrowUpRight, ArrowDownRight } from "lucide-react";

export interface Card6Props {
  title: string;
  value: number | string;
  icon: LucideIcon;
  iconBg?: string;
  iconColor?: string;
  change: number | string;
  changeLabel: string;
  positive?: boolean;
  cardBg?: string;
  /** Optional actions (e.g., buttons) to render alongside the change info */
  actions?: React.ReactNode;
}

export function Card6({
  title,
  value,
  icon: Icon,
  iconBg = "bg-blue-100",
  iconColor = "text-blue-500",
  change,
  changeLabel,
  positive = true,
  cardBg = "bg-white",
  actions,
}: Card6Props) {
  const changeColor = positive ? "text-green-500 dark:text-green-400" : "text-red-500 dark:text-red-400";
  const ChangeIcon = positive ? ArrowUpRight : ArrowDownRight;

  return (
    <Card className={`${cardBg} shadow-lg hover:shadow-xl transition-all duration-300`}>
      <CardContent className="p-4">
        <div className="flex justify-between items-start">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-300 truncate">
              {title}
            </CardTitle>
            <CardDescription className="mt-1 text-2xl font-bold text-gray-900 dark:text-gray-100 truncate">
              {value}
            </CardDescription>
          </div>
          <div className={`${iconBg} p-2 rounded-lg shadow-md border border-white/20 dark:border-gray-700/50 flex-shrink-0`}>
            <Icon className={`${iconColor} h-6 w-6`} />
          </div>
        </div>
      </CardContent>

      <CardFooter className="pt-0 px-4 pb-4">
        <div className="flex justify-between items-center w-full">
          <div className="flex items-center space-x-1 min-w-0 flex-1">
            <ChangeIcon className={`${changeColor} h-4 w-4 flex-shrink-0`} />
            <span className={`${changeColor} text-sm font-medium truncate`}>{change}</span>
            <span className="text-xs text-gray-500 dark:text-gray-400 truncate">{changeLabel}</span>
          </div>
          {actions && <div className="flex space-x-2 flex-shrink-0">{actions}</div>}
        </div>
      </CardFooter>
    </Card>
  );
}