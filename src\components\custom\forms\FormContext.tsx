
import React, { createContext, useContext, useState, ReactNode } from 'react';

// Form context types
type FormContextType = {
  currentStep: number;
  totalSteps: number;
  goToStep: (step: number) => void;
  nextStep: () => void;
  prevStep: () => void;
  isFirstStep: boolean;
  isLastStep: boolean;
  setTotalSteps: (steps: number) => void;
  formData: Record<string, any>;
  updateFormData: (data: Record<string, any>) => void;
  resetForm: () => void;
  isStepCompleted: (step: number) => boolean;
  canNavigateToStep: (step: number) => boolean;
  validateStep: (step: number) => boolean;
  setStepValidator: (step: number, validator: () => boolean) => void;
};

// Create the context with default values
const FormContext = createContext<FormContextType>({
  currentStep: 1,
  totalSteps: 1,
  goToStep: () => {},
  nextStep: () => {},
  prevStep: () => {},
  isFirstStep: true,
  isLastStep: true,
  setTotalSteps: () => {},
  formData: {},
  updateFormData: () => {},
  resetForm: () => {},
  isStepCompleted: () => false,
  canNavigateToStep: () => false,
  validateStep: () => true,
  setStepValidator: () => {},
});

// FormProvider props
interface FormProviderProps {
  children: ReactNode;
  initialData?: Record<string, any>;
  initialStep?: number;
  allowSkipSteps?: boolean;
}

// Provider component for form context
export const FormProvider: React.FC<FormProviderProps> = ({
  children,
  initialData = {},
  initialStep = 1,
  allowSkipSteps = true,
}) => {
  const [currentStep, setCurrentStep] = useState(initialStep);
  const [totalSteps, setTotalSteps] = useState(1);
  const [formData, setFormData] = useState<Record<string, any>>(initialData);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [stepValidators, setStepValidators] = useState<Record<number, () => boolean>>({});

  // Check if a step is completed
  const isStepCompleted = (step: number) => {
    return completedSteps.includes(step);
  };

  // Check if we can navigate to a specific step
  const canNavigateToStep = (step: number) => {
    if (allowSkipSteps) return true;
    // Can only navigate to completed steps or the next uncompleted step
    return step <= currentStep || isStepCompleted(step - 1);
  };

  // Navigate to a specific step
  const goToStep = (step: number) => {
    if (step > 0 && step <= totalSteps && (allowSkipSteps || canNavigateToStep(step))) {
      setCurrentStep(step);
    }
  };

  // Validate a specific step
  const validateStep = (step: number) => {
    const validator = stepValidators[step];
    return validator ? validator() : true;
  };

  // Set a validator function for a specific step
  const setStepValidator = (step: number, validator: () => boolean) => {
    setStepValidators(prev => ({ ...prev, [step]: validator }));
  };

  // Move to the next step
  const nextStep = () => {
    if (currentStep < totalSteps) {
      // Validate current step before proceeding
      if (!allowSkipSteps && !validateStep(currentStep)) {
        return; // Don't proceed if validation fails
      }

      // Mark current step as completed when moving forward
      if (!completedSteps.includes(currentStep)) {
        setCompletedSteps(prev => [...prev, currentStep]);
      }
      setCurrentStep(currentStep + 1);
    }
  };

  // Move to the previous step
  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Update form data
  const updateFormData = (data: Record<string, any>) => {
    setFormData((prevData) => ({ ...prevData, ...data }));
  };

  // Reset the form
  const resetForm = () => {
    setCurrentStep(1);
    setFormData(initialData);
    setCompletedSteps([]);
    setStepValidators({});
  };

  // Calculate step states
  const isFirstStep = currentStep === 1;
  const isLastStep = currentStep === totalSteps;

  return (
    <FormContext.Provider
      value={{
        currentStep,
        totalSteps,
        goToStep,
        nextStep,
        prevStep,
        isFirstStep,
        isLastStep,
        setTotalSteps,
        formData,
        updateFormData,
        resetForm,
        isStepCompleted,
        canNavigateToStep,
        validateStep,
        setStepValidator,
      }}
    >
      {children}
    </FormContext.Provider>
  );
};

// Custom hook to use the form context
export const useFormContext = () => useContext(FormContext);