import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Edit, Star } from "lucide-react";
import FeedbackForm from "../forms/FeedbackForm";
import BaseModal from "@/components/custom/modals/BaseModal";
import { useState } from "react";

type Props = {
  data: any;
};

const FeedbacksList = ({ data }: Props) => {
  const [FeedbackModalOpen, setFeedbackModalOpen] = useState<any | null>(null);
  return (
    <div className="border  rounded">
      <Table>
        <TableHeader className="bg-accent">
          <TableRow className="!font-bold">
            <TableHead className="!font-bold">Type</TableHead>
            <TableHead className="!font-bold">Subject</TableHead>
            <TableHead className="!font-bold">Message</TableHead>
            <TableHead className="!font-bold">Rating</TableHead>
            <TableHead className="!font-bold">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data?.map((rowData: any) => (
            <TableRow
              key={rowData?.feedback_id}
              className="hover:!bg-transparent bg-white dark:bg-transparent dark:hover:!bg-gray-300/10"
            >
              <TableCell className="!py-2.5 text-xs">
                {rowData?.feedback_type}
              </TableCell>
              <TableCell className="!py-2.5 text-xs">
                {rowData?.subject}
              </TableCell>
              <TableCell className="!py-2.5 text-xs">
                {rowData?.message}
              </TableCell>
              <TableCell className="!py-2.5 text-xs flex gap-1">
                {rowData?.rating &&
                  Array.from({ length: rowData.rating }, (_, i) => (
                    <Star
                      size={15}
                      key={i}
                      className="text-yellow-500 inline"
                    />
                  ))}
              </TableCell>
              <TableCell className="!py-2.5 text-xs">
                <Edit
                  className="cursor-pointer text-blue-500 hover:text-blue-700"
                  onClick={() => setFeedbackModalOpen(rowData)}
                />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      <BaseModal
        isOpen={!!FeedbackModalOpen}
        onOpenChange={() => setFeedbackModalOpen(null)}
        title="Feedback"
        description="Manage your Feedback here"
      >
        <FeedbackForm updateData={FeedbackModalOpen} />
      </BaseModal>
    </div>
  );
};

export default FeedbacksList;
