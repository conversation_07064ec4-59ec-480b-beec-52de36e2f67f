import { Screen } from "@/app-components/layout/screen";
import PlotsTable from "@/pages/inventory/Projects/components/PlotsTable";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { useGetProjectDetailsQuery } from "@/redux/slices/projects";
import { useLocation } from "react-router-dom";
import { addComma } from "@/utils/helpers";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import DiaporaReservationModal from "./modals/DiaporaReservationModal";
import { useState } from "react";
import { useSidebarPermissions } from "@/hooks/useSidebarPermissions";

type Props = {};

const ProjectDetails = ({}: Props) => {
  const location = useLocation();
  const [isAddModalOpen, setIsAddModalOpen] = useState<boolean>(false);
  const [plotStatus, setStatus] = useState<string>("");
  const projectId = location.pathname.split("/")[2];
  const { data: projectDetails, isLoading } =
    useGetProjectDetailsQuery(projectId);

  // Get inventory permissions
  const { hasInventoryPermission } = useSidebarPermissions();
  const hasFullAccess = hasInventoryPermission("VIEW_INVENTORY_FULL_ACCESS");
  const hasMarketerPermission = hasInventoryPermission("VIEW_INVENTORY_MARKETER");
  const hasDiasporaPermission = hasInventoryPermission("VIEW_INVENTORY_DIASPORA");

  return (
    <Screen>
      <div className="min-h-screen space-y-5">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold ml-4">Optiven Projects</h1>
        </div>

        {isLoading ? (
          <SpinnerTemp type="spinner-double" size="md" />
        ) : (
          <div className=" grid lg:grid-cols-3 md:grid-cols-2 sm:grid-cols-1 px-4 gap-4">
            <div className="rounded border p-4 bg-gradient-to-b from-primary/20 to-white ">
              <div className="flex justify-center">
                <div className="">
                  <p className="font-bold text-center">
                    {projectDetails?.name}
                  </p>
                  <p className="text-sm text-primary text-center">
                    {projectDetails?.open_plots} Plots Available{" "}
                    {(hasFullAccess || (!hasMarketerPermission && !hasDiasporaPermission)) && (
                      <span className="text-destructive ">
                        {addComma(projectDetails?.percentage_sold ?? "0")}% Sold
                      </span>
                    )}
                  </p>
                </div>
              </div>
              <div className="flex items-center justify-center my-2 w-full">
                {(hasMarketerPermission || hasDiasporaPermission) && !hasFullAccess ? (
                  // Show only Open plots for marketer or diaspora users (unless they have full access)
                  <div className="rounded flex text-sm w-full justify-center">
                    <p
                      className="px-2 py-1 bg-primary text-white text-center w-full cursor-pointer"
                      onClick={() => setStatus("Open")}
                    >
                      {projectDetails?.open_plots} Open Plots
                    </p>
                  </div>
                ) : (
                  // Show all plot statuses for full access users or other users
                  <div className="rounded flex text-sm w-full justify-center">
                    <p
                      className="px-2 py-1 bg-primary text-white text-center w-[30%] cursor-pointer"
                      onClick={() => setStatus("Open")}
                    >
                      {projectDetails?.open_plots} Open
                    </p>
                    <p
                      className="px-2 py-1 bg-destructive text-white text-center w-[30%] cursor-pointer"
                      onClick={() => setStatus("Sold")}
                    >
                      {projectDetails?.sold_plots} Sold
                    </p>
                    <p
                      className="px-2 py-1 bg-yellow-400  text-center w-[30%] cursor-pointer"
                      onClick={() => setStatus("Reserved")}
                    >
                      {projectDetails?.reserved_plots} RVD
                    </p>
                  </div>
                )}
              </div>
            </div>
            {(hasFullAccess || hasDiasporaPermission) && (
              <div className="rounded border p-4 bg-gradient-to-b from-primary/20 to-white ">
                <p className="font-bold text-center ">
                  {" "}
                  Diaspora Plots Reservations{" "}
                </p>
                <p className="text-sm text-primary text-center py-1">
                  Reserve Plots for diaspora trip
                </p>
                <div className="flex items-center justify-center">
                  {projectDetails?.open_plots > 0 ? (
                    <Button
                      variant="default"
                      onClick={() => setIsAddModalOpen(true)}
                    >
                      Reserve plot(s)
                    </Button>
                  ) : (
                    <p className="text-sm bg-destructive text-white py-1 px-4 rounded-full">
                      No open plots available!!
                    </p>
                  )}
                </div>
              </div>
            )}
          </div>
        )}

        <div className="rounded border px-4 mx-4 gap-4 pb-4">
          <p className="text-lg font-bold p-2">
            {projectDetails?.name} Project {plotStatus} Plots
          </p>
          {projectDetails?.plots?.length === 0 ? (
            <p className="px-2">No Plots found</p>
          ) : (
            <PlotsTable
              plotStatus={plotStatus}
              projectId={projectDetails?.projectId}
            />
          )}
        </div>

        <div className=" mx-4 gap-4 pb-40  min-h-[300px]">
          <div className="border rounded">
            <p className="text-lg font-bold p-2 pb-4">{`Project ${projectDetails?.name} Map`}</p>
            {projectDetails?.link && (
              <iframe
                loading="eager"
                width="100%"
                title={`Project ${projectDetails?.name} Map`}
                src={`${projectDetails?.link}#toolbar=0`}
                className="w-full h-[60vh]"
              />
            )}
          </div>
        </div>

        {isAddModalOpen && (
          <DiaporaReservationModal
            openModal={isAddModalOpen}
            setOpenModal={setIsAddModalOpen}
            title="Diaspora Trip Plot Reservations"
          />
        )}
      </div>
    </Screen>
  );
};

export default ProjectDetails;
