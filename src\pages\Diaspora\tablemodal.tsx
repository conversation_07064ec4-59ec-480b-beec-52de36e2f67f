import { PrimaryButton } from "@/components/custom/buttons/buttons";
import BaseModal from "@/components/custom/modals/BaseModal";
import { DataTable } from "@/components/custom/tables/Table1";
import { ColumnDef } from "@tanstack/react-table";
import { But<PERSON> } from "@/components/ui/button";
import { Download, Eye, X } from "lucide-react";
import { useState, useEffect } from "react";

interface MarketerDetailsModalProps {
  marketer: string | null;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

interface ProjectDetails {
  projectName: string;
  totalPlots: number;
  openPlots: number;
  reservedPlots: number;
  soldPlots: number;
  bank: string;
}

// Custom hook for detecting small screens
const useMediaQuery = (query: string) => {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    const media = window.matchMedia(query);
    setMatches(media.matches);

    const listener = (e: MediaQueryListEvent) => setMatches(e.matches);
    media.addEventListener('change', listener);
    return () => media.removeEventListener('change', listener);
  }, [query]);

  return matches;
};

// Search Component for DataTable
const SearchComponent = ({
  onSearch,
}: {
  onSearch: (value: string) => void;
}) => {
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    onSearch(searchTerm);
  }, [searchTerm, onSearch]);

  return (
    <input
      value={searchTerm}
      onChange={(e) => setSearchTerm(e.target.value)}
      className="px-4 py-2 w-full max-w-sm border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      placeholder="Search projects..."
    />
  );
};

const MarketerDetailsModal = ({ marketer, isOpen, onOpenChange }: MarketerDetailsModalProps) => {
  if (!marketer) return null;

  // Detect small screen (< 768px)
  const isSmallScreen = useMediaQuery('(max-width: 768px)');

  // Sample project data
  const projectDetails: ProjectDetails[] = [
    {
      projectName: "Sunset Estates",
      totalPlots: 100,
      openPlots: 40,
      reservedPlots: 20,
      soldPlots: 40,
      bank: "KCB Bank",
    },
    {
      projectName: "Coastal Heights",
      totalPlots: 150,
      openPlots: 60,
      reservedPlots: 30,
      soldPlots: 60,
      bank: "Equity Bank",
    },
    {
      projectName: "Lakeview Gardens",
      totalPlots: 80,
      openPlots: 30,
      reservedPlots: 15,
      soldPlots: 35,
      bank: "Co-operative Bank",
    },
  ];

  // Mobile columns (simplified)
  const getMobileColumns = (): ColumnDef<ProjectDetails>[] => [
    {
      accessorKey: "projectName",
      header: "Project Name",
      cell: (info) => <span className="font-medium">{info.getValue() as string}</span>,
      enableColumnFilter: true,
      filterFn: "includesString",
      enableSorting: true,
    },
    {
      accessorKey: "totalPlots",
      header: "Total Plots",
      cell: (info) => info.getValue() as number,
      enableColumnFilter: true,
      filterFn: "includesString",
      enableSorting: true,
    },
    {
      id: "actions",
      header: "",
      cell: ({ row }) => {
        const project = row.original;
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              alert(`Viewing details for ${project.projectName}`);
            }}
            className="h-8 w-8 p-0"
          >
            <Eye className="h-4 w-4" />
            <span className="sr-only">View details</span>
          </Button>
        );
      },
      enableColumnFilter: false,
    },
  ];

  // Desktop columns (full)
  const getDesktopColumns = (): ColumnDef<ProjectDetails>[] => [
    {
      accessorKey: "projectName",
      header: "Project Name",
      cell: (info) => <span className="font-medium">{info.getValue() as string}</span>,
      enableColumnFilter: true,
      filterFn: "includesString",
      enableSorting: true,
    },
    {
      accessorKey: "totalPlots",
      header: "Total Plots",
      cell: (info) => info.getValue() as number,
      enableColumnFilter: true,
      filterFn: "includesString",
      enableSorting: true,
    },
    {
      accessorKey: "openPlots",
      header: "Open Plots",
      cell: (info) => info.getValue() as number,
      enableColumnFilter: true,
      filterFn: "includesString",
      enableSorting: true,
    },
    {
      accessorKey: "reservedPlots",
      header: "Reserved Plots",
      cell: (info) => info.getValue() as number,
      enableColumnFilter: true,
      filterFn: "includesString",
      enableSorting: true,
    },
    {
      accessorKey: "soldPlots",
      header: "Sold Plots",
      cell: (info) => info.getValue() as number,
      enableColumnFilter: true,
      filterFn: "includesString",
      enableSorting: true,
    },
    {
      accessorKey: "bank",
      header: "Bank",
      cell: (info) => info.getValue() as string,
      enableColumnFilter: true,
      filterFn: "includesString",
      enableSorting: true,
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const project = row.original;
        return (
          <div className="flex space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                alert(`Viewing details for ${project.projectName}`);
              }}
              className="h-8 w-8 p-0"
            >
              <Eye className="h-4 w-4" />
              <span className="sr-only">View details</span>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                console.log(`Downloading details for ${project.projectName}`);
              }}
              className="h-8 w-8 p-0"
            >
              <Download className="h-4 w-4" />
              <span className="sr-only">Download</span>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                console.log(`Deleting ${project.projectName}`);
              }}
              className="text-red-500 hover:text-red-700 h-8 w-8 p-0"
            >
              
              <span className="sr-only">Delete</span>
            </Button>
          </div>
        );
      },
      enableColumnFilter: false,
    },
  ];

  const columns = isSmallScreen ? getMobileColumns() : getDesktopColumns();

  // Handle CSV export for PrimaryButton
  

  return (
    <BaseModal
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      title={`Marketer Details: ${marketer}`}
      description="Overview of marketer's project statistics"
      size="full"
      className="max-w-4xl"
      showClose
    >
      <div className="space-y-4">
        <div className="bg-white shadow-md rounded-md p-4">
          {projectDetails.length === 0 ? (
            <div className="text-center py-6">
              <p className="text-sm text-gray-500">No project details available for this marketer.</p>
            </div>
          ) : (
            <DataTable<ProjectDetails>
              data={projectDetails}
              columns={columns}
              enableToolbar={true}
              enableExportToExcel={!isSmallScreen}
              enablePrintPdf={!isSmallScreen}
              enablePagination={true}
              enableColumnFilters={!isSmallScreen}
              enableSorting={true}
              
            />
          )}
        </div>
        <div className="flex justify-end">
          
        </div>
      </div>
    </BaseModal>
  );
};

export default MarketerDetailsModal;