import { useState, useEffect } from 'react';
import MultiStepModal from '@/components/custom/modals/MultiStepModal';
import { Label } from '@/components/ui/label';
import { AlertCircle, Paperclip } from 'lucide-react';

import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

interface EngagementData {
  title: string;
  status: 'Critical' | 'Active' | 'Pending';
  customer: string;
  marketingTeam: string;
  description: string;
  attachment: File | null;
  unread: boolean;
  starred: boolean;
}

interface AddEngagementModalProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  onSubmit: (data: EngagementData) => void;
  engagementData?: EngagementData;
}

export default function AddEngagementModal({
  isOpen,
  onOpenChange,
  onSubmit,
  engagementData,
}: AddEngagementModalProps) {
  const isEditMode = !!engagementData;
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState<EngagementData>({
    title: '',
    status: 'Pending',
    customer: '',
    marketingTeam: '',
    description: '',
    attachment: null,
    unread: true,
    starred: false,
  });

  useEffect(() => {
    if (isEditMode && engagementData) {
      setFormData(engagementData);
    } else {
      setFormData({
        title: '',
        status: 'Pending',
        customer: '',
        marketingTeam: '',
        description: '',
        attachment: null,
        unread: true,
        starred: false,
      });
    }
  }, [isEditMode, engagementData]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setFormData((prev) => ({ ...prev, [id]: value }));
  };

  const handleStatusChange = (value: 'Critical' | 'Active' | 'Pending') => {
    setFormData((prev) => ({ ...prev, status: value }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files[0]) {
      setFormData((prev) => ({ ...prev, attachment: files[0] }));
    }
  };

  const handleComplete = () => {
    onSubmit(formData);
    setCurrentStep(0);
    setFormData({
      title: '',
      status: 'Pending',
      customer: '',
      marketingTeam: '',
      description: '',
      attachment: null,
      unread: true,
      starred: false,
    });
  };

  return (
    <MultiStepModal
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      title={isEditMode ? 'Edit Engagement' : 'Add New Engagement'}
      description={isEditMode ? 'Update engagement details' : 'Complete all steps to add a new engagement'}
      currentStep={currentStep}
      onStepChange={setCurrentStep}
      onComplete={handleComplete}
      steps={[
        {
          title: 'Engagement Info',
          content: (
            <div className="space-y-4 py-2">
              <div className="space-y-2">
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  placeholder="Enter engagement title"
                  value={formData.title}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="customer">Customer</Label>
                <Input
                  id="customer"
                  placeholder="Enter customer name"
                  value={formData.customer}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="marketingTeam">Marketing Team</Label>
                <Input
                  id="marketingTeam"
                  placeholder="Enter marketing team"
                  value={formData.marketingTeam}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label>Status</Label>
                <RadioGroup
                  value={formData.status}
                  onValueChange={handleStatusChange}
                  className="flex space-x-4"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="Critical" id="critical" />
                    <Label htmlFor="critical">Critical</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="Active" id="active" />
                    <Label htmlFor="active">Active</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="Pending" id="pending" />
                    <Label htmlFor="pending">Pending</Label>
                  </div>
                </RadioGroup>
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Input
                  id="description"
                  placeholder="Describe the engagement"
                  value={formData.description}
                  onChange={handleInputChange}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="attachment">Attachment (optional)</Label>
                <div className="flex items-center gap-2">
                  <Label
                    htmlFor="attachment"
                    className="flex items-center gap-2 cursor-pointer py-2 px-4 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                  >
                    <Paperclip className="w-4 h-4" />
                    {formData.attachment ? formData.attachment.name : 'Add file'}
                  </Label>
                  <Input
                    id="attachment"
                    type="file"
                    className="hidden"
                    onChange={handleFileChange}
                  />
                </div>
                {formData.attachment && (
                  <p className="text-sm text-muted-foreground">
                    {formData.attachment.name} ({Math.round(formData.attachment.size / 1024)} KB)
                  </p>
                )}
              </div>
            </div>
          ),
        },
        {
          title: 'Confirmation',
          content: (
            <div className="py-6">
              <AlertCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-center">Almost Done!</h3>
              <p className="text-muted-foreground mt-2 text-center">
                Please review your engagement details before submitting.
              </p>
              <div className="mt-6 space-y-2 text-sm">
                <p><strong>Title:</strong> {formData.title || 'N/A'}</p>
                <p><strong>Customer:</strong> {formData.customer || 'N/A'}</p>
                <p><strong>Marketing Team:</strong> {formData.marketingTeam || 'N/A'}</p>
                <p><strong>Status:</strong> {formData.status || 'N/A'}</p>
                <p><strong>Description:</strong> {formData.description || 'N/A'}</p>
                <p><strong>Attachment:</strong> {formData.attachment ? formData.attachment.name : 'None'}</p>
              </div>
            </div>
          ),
        },
      ]}
    />
  );
}


