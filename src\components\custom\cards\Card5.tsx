"use client"

import * as React from "react"
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardDescription,
  CardContent,
} from "@/components/ui/card"
import { Button } from "@/components/ui/button"

export interface Card5Props {
  title: string
  description?: string
  statValue: string
  statLabel?: string
  /**
   * Optional chart to render inside the card, e.g. <TinyAreaChart />
   * If omitted, we show a fallback message.
   */
  chart?: React.ReactNode
  buttonLabel?: string
  onButtonClick?: () => void
}

/**
 * Card5: A "Statistics" card with an optional chart
 * restricted to the "chart placeholder" area.
 */
export function Card5({
  title,
  description,
  statValue,
  statLabel,
  chart,
  buttonLabel = "View full report",
  onButtonClick,
}: Card5Props) {
  return (
    <Card className="text-center">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex flex-col items-center justify-center">
          <span className="text-3xl font-bold">{statValue}</span>
          {statLabel && (
            <span className="text-xs text-muted-foreground">{statLabel}</span>
          )}
        </div>
        {/* Restrict chart to a 6rem-tall area, horizontally full, with a border */}
        <div className="relative h-24 w-full overflow-hidden rounded-md border flex items-center justify-center">
          {chart ? (
            /* Render the chart if provided */
            <div className="h-full w-full flex items-center justify-center">
              {chart}
            </div>
          ) : (
            /* Fallback text if no chart is passed */
            <span className="text-sm text-muted-foreground">
              [No chart provided]
            </span>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-center">
        <Button onClick={onButtonClick}>{buttonLabel}</Button>
      </CardFooter>
    </Card>
  )
}
