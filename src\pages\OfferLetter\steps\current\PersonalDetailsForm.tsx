import React from 'react';

interface PersonalDetailsFormProps {
  formData: any;
  setFormData: (data: any) => void;
  onNext: () => void;
  onBack: () => void;
}

const PersonalDetailsForm: React.FC<PersonalDetailsFormProps> = ({
  formData,
  setFormData,
  onNext,
  onBack,
}) => {
  const handleInputChange = (field: string, value: string | any[]) => {
    setFormData({ ...formData, [field]: value });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onNext();
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold text-gray-800 mb-2">Enter Your Personal Details</h2>
        <p className="text-gray-600 mb-6">Kindly fill in your personal information as requested below.</p>
      </div>

      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium text-gray-700 mb-4">
            1. Personal Information <span className="text-gray-500 text-sm">- (All Fields are required)</span>
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                First Name (as per your ID)
              </label>
              <input
                type="text"
                placeholder="Jane Doe"
                value={formData.firstName || ''}
                onChange={(e) => handleInputChange('firstName', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Last Name (as per your ID)
              </label>
              <input
                type="text"
                placeholder="Jane Doe"
                value={formData.lastName || ''}
                onChange={(e) => handleInputChange('lastName', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Enter Valid Email Address
              </label>
              <input
                type="email"
                placeholder="<EMAIL>"
                value={formData.email || ''}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Enter National ID / Passport Number
              </label>
              <input
                type="text"
                placeholder="36xxxx16"
                value={formData.nationalId || ''}
                onChange={(e) => handleInputChange('nationalId', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Enter Valid Phone Number
              </label>
              <div className="flex">
                <select className="px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                  <option>🇰🇪</option>
                </select>
                <input
                  type="tel"
                  placeholder="+254 719 116 112"
                  value={formData.phone || ''}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-r-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  
                />
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Enter Your K.R.A Pin
              </label>
              <input
                type="text"
                placeholder="<EMAIL>"
                value={formData.kraPin || ''}
                onChange={(e) => handleInputChange('kraPin', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                
              />
            </div>
          </div>

          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Enter Date of Birth <span className="text-gray-500">(Optional)</span>
            </label>
            <input
              type="date"
              value={formData.dateOfBirth || ''}
              onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
              className="w-full md:w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
          </div>
        </div>

        <div>
          <h3 className="text-lg font-medium text-gray-700 mb-4">
            2. Permanent Residential Details: <span className="text-gray-500 text-sm">- (All Fields are required)</span>
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Country of residence
              </label>
              <select
                value={formData.country || ''}
                onChange={(e) => handleInputChange('country', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                
              >
                <option value="">KENYA</option>
                <option value="kenya">Kenya</option>
                <option value="uganda">Uganda</option>
                <option value="tanzania">Tanzania</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select City of Residence
              </label>
              <select
                value={formData.city || ''}
                onChange={(e) => handleInputChange('city', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                
              >
                <option value="">Nairobi</option>
                <option value="nairobi">Nairobi</option>
                <option value="mombasa">Mombasa</option>
                <option value="kisumu">Kisumu</option>
              </select>
            </div>
          </div>
        </div>

        <div>
          <h3 className="text-lg font-medium text-gray-700 mb-4">
            3. How would you like us to get in touch with you
          </h3>
          
          <div className="flex flex-wrap gap-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.contactMethods?.includes('sms') || false}
                onChange={(e) => {
                  const methods = formData.contactMethods || [];
                  if (e.target.checked) {
                    handleInputChange('contactMethods', [...methods, 'sms']);
                  } else {
                    handleInputChange('contactMethods', methods.filter((m: string) => m !== 'sms'));
                  }
                }}
                className="mr-2 text-green-500"
              />
              SMS
            </label>
            
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.contactMethods?.includes('whatsapp') || false}
                onChange={(e) => {
                  const methods = formData.contactMethods || [];
                  if (e.target.checked) {
                    handleInputChange('contactMethods', [...methods, 'whatsapp']);
                  } else {
                    handleInputChange('contactMethods', methods.filter((m: string) => m !== 'whatsapp'));
                  }
                }}
                className="mr-2 text-green-500"
              />
              Whatsapp
            </label>
            
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.contactMethods?.includes('email') || false}
                onChange={(e) => {
                  const methods = formData.contactMethods || [];
                  if (e.target.checked) {
                    handleInputChange('contactMethods', [...methods, 'email']);
                  } else {
                    handleInputChange('contactMethods', methods.filter((m: string) => m !== 'email'));
                  }
                }}
                className="mr-2 text-green-500"
              />
              Email
            </label>
            
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.contactMethods?.includes('phonecall') || false}
                onChange={(e) => {
                  const methods = formData.contactMethods || [];
                  if (e.target.checked) {
                    handleInputChange('contactMethods', [...methods, 'phonecall']);
                  } else {
                    handleInputChange('contactMethods', methods.filter((m: string) => m !== 'phonecall'));
                  }
                }}
                className="mr-2 text-green-500"
              />
              Phone call
            </label>
          </div>
        </div>
      </div>

      <div className="flex justify-between pt-6">
        <button
          type="button"
          onClick={onBack}
          className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
        >
          ← Go Back
        </button>
        <button
          type="submit"
          className="px-6 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors"
        >
          Next Page →
        </button>
      </div>
    </form>
  );
};

export default PersonalDetailsForm;