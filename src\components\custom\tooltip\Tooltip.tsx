import { useId } from "react";
import { Screen } from "@/app-components/layout/screen";
import TooltipTemp from "./TooltipTemp";
import { Button } from "@/components/ui/button";
import { AuroraTooltipProps } from "./TooltipTemp";
const Tooltip: React.FC = () => {

    const TooltipDemoMaker = ({color, position , content, children}: AuroraTooltipProps , isMother: boolean) => {
        if (isMother) {

            
        }
    const TooltipCodeDemo = `
    return (
    <>
    <TooltipTemp
     content={${content}}
     position=${position}
     color=${color}

    >
      ${children}

      </TooltipTemp>
      ...
    </>
    )
    `;


    const MotherCodeDemo = `
    <div>
            <a id={safeUniqueID}>
                <div>
                    {children}
                </div>
            </a>
            <Tooltip
                className={cn(className)}
                style={{ backgroundColor: colorState }}
                anchorSelect={#{UniqueID}'}
                place={position}
            >
                {content}
            </Tooltip>
        </div>
    `

    if (isMother) {
        return MotherCodeDemo;
            
            
    }

    return TooltipCodeDemo;
    }

       const uniqueID = useId();
       console.log(uniqueID);
    
    return (
        <Screen>
            <div className="demonstrations bg-inherit h-full w-full bg-slate-50 flex flex-col gap-6 p-4 relative">
                <h1 className="absolute top-4 text-lg font-semibold">Tooltip Templates Jibambez</h1>
                <div className="later-1 bg-inherit flex flex-wrap justify-between items-center gap-6 mt-20">
                    <div className="with-gradient bg-secondary rounded-lg px-6 py-4 bg-slate-100 w-full sm:w-[48%] lg:w-[30%]">
                        <p className="text-sm font-medium">Tooltip Example 1</p>
                        <div className="helix flex h-[20vh] justify-center items-center rounded-md">
                            <TooltipTemp
                                content={
                                    
                                    
                                    <p>This is the content</p>
                                }

                                position="top"
                                color="green"

                            >
                                <div>
                                <Button className="">Is this good</Button>

                                </div>

                            </TooltipTemp>
                        </div>
                        <div className="code tutorial h-fit ">
                            <p>Usage and syntax</p>
                            <pre className="text-sm text-white overflow-x-auto w-full bg-black p-2 rounded-md mt-2">
                                <code className="overflow-x-auto w-full">
                                    {TooltipDemoMaker({ color: "green", position: "top", content: "<p>This is the content</p>", children: "<Button className=''>Is this good</Button>" }, false)}
                                </code>
                            </pre> 

                        </div>



                    </div>

                    <div className="with-gradient bg-secondary rounded-lg px-6 py-4 bg-slate-100 w-full sm:w-[48%] lg:w-[30%]">
                        <p className="text-sm font-medium">Tooltip Example 2</p>
                        <div className="helix flex h-[20vh] justify-center items-center rounded-md">
                            <TooltipTemp
                                position="left"
                                content={
                                    <p>Tooltip Appears left</p>
                                }
                                color="red"


                            >
                                <Button className="">Different color</Button>

                            </TooltipTemp>
                        </div>
                        <div className="code tutorial h-fit ">
                            <p>Usage and syntax</p>
                            <pre className="text-sm text-white overflow-x-auto w-full bg-black p-2 rounded-md mt-2">
                                <code className="overflow-x-auto w-full">
                                    {TooltipDemoMaker({ color: "red", position: "left", content: "<p>Tooltip Appears left</p>", children: "<Button>Different color</Button>" }, false)}
                                </code>
                            </pre> 

                        </div>



                    </div>

                    <div className="with-gradient bg-secondary rounded-lg px-6 py-4 bg-slate-100 w-full sm:w-[48%] lg:w-[30%]">
                        <p className="text-sm font-medium">Tooltip Example 3</p>
                        <div className="helix flex h-[20vh] justify-center items-center rounded-md">
                            <TooltipTemp
                                position="bottom"
                                color="blue"
                                content={
                                    <div className="flex ">
                                        {/* <img className="inline-block size-8 rounded-full ring-2 ring-white" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt=""></img> */}
                                        <img className="inline-block size-8 rounded-full ring-2 ring-white" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt=""></img>
                                        <p className="ml-4">Leonard Salest</p>


                                    </div>
                                }

                            >
                                <p className="bg-blue-400 px-2 rounded-md">Leonard Slest</p>


                            </TooltipTemp>
                        </div>
                        <div className="code tutorial h-fit ">
                            <p>Usage and syntax</p>
                            <pre className="text-sm text-white overflow-x-auto w-full bg-black p-2 rounded-md mt-2">
                                <code className="overflow-x-auto w-full">
                                    {TooltipDemoMaker({ color: "blue", position: "bottom",
                                     content: "<p >Leonard Slest</p>", 
                                     children: `
                                     <div className="flex ">
                                        <img className="inline-block size-8
                                         rounded-full ring-2 ring-white" 
                                         src="https://images.unsplash.com/photo-
                                         1472099645785-5658abf4ff4e?ixlib=
                                         rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMD
                                         d9&auto=format&fit=facearea&f
                                         acepad=2&w=256&h=256&q=80" alt=""></img>
                                        <p className="ml-4">Leonard Salest</p>


                                    </div>` }, false)}
                                </code>
                            </pre> 

                        </div>

                        



                    </div>

                    <div className="with-gradient bg-secondary rounded-lg px-6 py-4 bg-slate-100 w-full sm:w-[48%] lg:w-[30%]">
                        <p className="text-sm font-medium">This is the component code Tooltip is from react-tooltip.</p>
                       
                        <div className="code tutorial h-fit ">
                            <pre className="text-sm text-white overflow-x-auto w-full bg-black p-2 rounded-md mt-2">
                                <code className="overflow-x-auto w-full">
                                    {TooltipDemoMaker({ color: "red", position: "left", content: "<p>Tooltip Appears left</p>", children: "<Button>Different color</Button>" }, true)}
                                </code>
                            </pre> 

                        </div>



                    </div>







                </div>

            </div>


        </Screen>
    )

}


export default Tooltip;