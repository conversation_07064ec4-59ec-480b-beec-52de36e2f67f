import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Check, ChevronsUpDown } from 'lucide-react';
import { 
  Car, 
  Calendar, 
  MapPin, 
  Edit, 
  Trash2, 
  Search,
  Filter,
  Clock,
  AlertCircle,
  Building,
  Eye,
  CheckCircle,
  XCircle,
  Play,
  Square
} from 'lucide-react';
import { format } from 'date-fns';
import {
  useGetSpecialBookingsQuery,
  useUpdateSpecialBookingMutation,
  useDeleteSpecialBookingMutation,
  SpecialBooking
} from '@/redux/slices/logistics';
import { useGetDepartmentsQuery } from '@/redux/slices/user';
import { toast } from '@/components/custom/Toast/MyToast';
import { DataTable } from '@/components/custom/tables/Table1';
import { ColumnDef } from '@tanstack/react-table';
import CreateASpecialAssignment from './CreateSpecialAssignment';
import BaseModal from '@/components/custom/modals/BaseModal';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { cn } from '@/lib/utils';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

interface SpecialBookingsTableProps {
  className?: string;
}

const SpecialBookingsTable: React.FC<SpecialBookingsTableProps> = ({ className }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [editingBooking, setEditingBooking] = useState<SpecialBooking | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [assignedToOpen, setAssignedToOpen] = useState(false);
  const [viewingBooking, setViewingBooking] = useState<SpecialBooking | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false);
  const [bookingToReject, setBookingToReject] = useState<number | null>(null);

  // Fetch departments for "Assigned To" selection
  const { data: departmentsResp, isLoading: departmentsLoading } = useGetDepartmentsQuery({
    page: 1,
    page_size: 100,
  });

  const departments = Array.isArray(departmentsResp) ? departmentsResp : [];

  // Form schema for editing
  const editFormSchema = z.object({
    assigned_to: z.string().min(1, 'Assigned to is required'),
    pickup_location: z.string().min(1, 'Pickup location is required'),
    destination: z.string().min(1, 'Destination is required'),
    reason: z.string().optional(),
    remarks: z.string().optional(),
  });

  const editForm = useForm<z.infer<typeof editFormSchema>>({
    resolver: zodResolver(editFormSchema),
    defaultValues: {
      assigned_to: '',
      pickup_location: '',
      destination: '',
      reason: '',
      remarks: '',
    },
  });

  // Fetch special bookings
  const { data: bookingsResp, isLoading, error, refetch } = useGetSpecialBookingsQuery({
    page: 1,
    page_size: 100,
    search: searchTerm || undefined,
    status: statusFilter !== 'all' ? statusFilter : undefined,
  });

  const [updateSpecialBooking] = useUpdateSpecialBookingMutation();
  const [deleteSpecialBooking] = useDeleteSpecialBookingMutation();

  const bookings = bookingsResp?.data?.results || [];

  // Filter bookings based on search and status
  const filteredBookings = useMemo(() => {
    if (!Array.isArray(bookings)) return [];

    return bookings.filter((booking) => {
      if (!booking) return false;

      const searchLower = searchTerm?.toLowerCase() || '';
      
      // Handle assigned_to search for both object and string formats
      let assignedToSearchText = '';
      if (typeof booking.assigned_to === 'object' && booking.assigned_to !== null) {
        assignedToSearchText = String(booking.assigned_to.dp_name || booking.assigned_to.name || '');
      } else {
        const department = Array.isArray(departments) ? departments.find((dept: any) => String(dept?.dp_id) === String(booking.assigned_to)) : null;
        assignedToSearchText = department?.dp_name || String(booking.assigned_to || '');
      }
      
      const matchesSearch = !searchTerm ||
        assignedToSearchText.toLowerCase().includes(searchLower) ||
        String(booking.driver || '').toLowerCase().includes(searchLower) ||
        String(booking.pickup_location || '').toLowerCase().includes(searchLower) ||
        String(booking.destination || '').toLowerCase().includes(searchLower);

      const matchesStatus = statusFilter === 'all' || booking.status === statusFilter;

      return matchesSearch && matchesStatus;
    });
  }, [bookings, searchTerm, statusFilter, departments]);

  // Handle delete
  const handleDelete = async (bookingId: number) => {
    if (window.confirm('Are you sure you want to delete this special booking?')) {
      try {
        await deleteSpecialBooking(bookingId).unwrap();
        toast.success('✅ Booking deleted successfully!');
        refetch();
      } catch (error) {
        console.error('Failed to delete booking:', error);
        toast.error('❌ Failed to delete booking');
      }
    }
  };

  // Handle edit
  const handleEdit = (booking: SpecialBooking) => {
    setEditingBooking(booking);
    
    // Handle assigned_to field - extract ID if it's an object
    let assignedToValue = '';
    if (typeof booking.assigned_to === 'object' && booking.assigned_to !== null) {
      assignedToValue = String((booking.assigned_to as any).dp_id || (booking.assigned_to as any).id || '');
    } else {
      assignedToValue = String(booking.assigned_to || '');
    }
    
    editForm.reset({
      assigned_to: assignedToValue,
      pickup_location: booking.pickup_location,
      destination: booking.destination,
      reason: booking.reason || '',
      remarks: booking.remarks || '',
    });
    setIsEditModalOpen(true);
  };

  // Handle edit form submission
  const handleEditSubmit = async (values: z.infer<typeof editFormSchema>) => {
    if (!editingBooking) return;

    try {
      await updateSpecialBooking({
        id: editingBooking.id!,
        ...values,
      }).unwrap();
      toast.success('✅ Booking updated successfully!');
      setIsEditModalOpen(false);
      setEditingBooking(null);
      editForm.reset();
      refetch();
    } catch (error) {
      console.error('Failed to update booking:', error);
      toast.error('❌ Failed to update booking');
    }
  };

  // Handle view booking
  const handleView = (booking: SpecialBooking) => {
    setViewingBooking(booking);
    setIsViewModalOpen(true);
  };

  // Handle approve booking
  const handleApprove = async (bookingId: number) => {
    try {
      await updateSpecialBooking({ 
        id: bookingId, 
        status: 'Approved' as SpecialBooking['status'] 
      }).unwrap();
      toast.success('✅ Booking approved successfully!');
      setIsViewModalOpen(false);
      setViewingBooking(null);
      refetch();
    } catch (error) {
      console.error('Failed to approve booking:', error);
      toast.error('❌ Failed to approve booking');
    }
  };

  // Handle reject booking - show confirmation dialog
  const handleReject = (bookingId: number) => {
    setBookingToReject(bookingId);
    setIsRejectDialogOpen(true);
  };

  // Confirm reject booking
  const confirmRejectBooking = async () => {
    if (!bookingToReject) return;

    try {
      await updateSpecialBooking({ 
        id: bookingToReject, 
        status: 'Rejected' as SpecialBooking['status'] 
      }).unwrap();
      toast.success('✅ Booking rejected successfully!');
      setIsViewModalOpen(false);
      setViewingBooking(null);
      setIsRejectDialogOpen(false);
      setBookingToReject(null);
      refetch();
    } catch (error) {
      console.error('Failed to reject booking:', error);
      toast.error('❌ Failed to reject booking');
      setIsRejectDialogOpen(false);
      setBookingToReject(null);
    }
  };

  // Handle start trip
  const handleStartTrip = async (bookingId: number) => {
    try {
      await updateSpecialBooking({ 
        id: bookingId, 
        status: 'In Progress' as SpecialBooking['status'] 
      }).unwrap();
      toast.success('✅ Trip started successfully!');
      setIsViewModalOpen(false);
      setViewingBooking(null);
      refetch();
    } catch (error) {
      console.error('Failed to start trip:', error);
      toast.error('❌ Failed to start trip');
    }
  };

  // Handle end trip
  const handleEndTrip = async (bookingId: number) => {
    try {
      await updateSpecialBooking({ 
        id: bookingId, 
        status: 'Completed' as SpecialBooking['status'] 
      }).unwrap();
      toast.success('✅ Trip completed successfully!');
      setIsViewModalOpen(false);
      setViewingBooking(null);
      refetch();
    } catch (error) {
      console.error('Failed to complete trip:', error);
      toast.error('❌ Failed to complete trip');
    }
  };

  // Get status variant for badges
  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'Pending': return 'secondary';
      case 'Approved': return 'default';
      case 'In Progress': return 'outline';
      case 'Completed': return 'destructive';
      case 'Rejected': return 'destructive';
      default: return 'secondary';
    }
  };

  // Define table columns
  const columns: ColumnDef<SpecialBooking>[] = [
    {
      accessorKey: 'assigned_to',
      header: 'Assigned To',
      cell: ({ row }) => {
        // Handle both object and string formats for assigned_to
        const assignedTo = row.original.assigned_to;
        let departmentName = 'Unknown';
        let departmentHead = '';
        
        if (typeof assignedTo === 'object' && assignedTo !== null) {
          // If assigned_to is an object, extract the name directly
          departmentName = String(assignedTo.dp_name || assignedTo.name || 'Unknown');
          departmentHead = String(assignedTo.dep_head_name || assignedTo.head || '');
        } else {
          // If assigned_to is a string/ID, find the department in the departments array
          const department = Array.isArray(departments) ? departments.find((dept: any) => String(dept?.dp_id) === String(assignedTo)) : null;
          departmentName = department?.dp_name || String(assignedTo) || 'Unknown';
          departmentHead = department?.dep_head_name || '';
        }

        return (
          <div className="flex items-center space-x-2">
            <Building className="w-4 h-4 text-gray-500" />
            <div>
              <div className="font-medium">{departmentName}</div>
              {departmentHead && (
                <div className="text-xs text-gray-500">Head: {departmentHead}</div>
              )}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'driver',
      header: 'Driver',
      cell: ({ row }) => {
        const driver = row.original.driver;
        const driverName = typeof driver === 'object' && driver !== null
          ? (driver as any)?.fullnames || (driver as any)?.name || (driver as any)?.employee_no || 'Unknown Driver'
          : driver || 'Not Assigned';

        return (
          <div className="flex items-center space-x-2">
            <Car className="w-4 h-4 text-blue-500" />
            <span>{String(driverName)}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'pickup_location',
      header: 'Pickup',
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <MapPin className="w-4 h-4 text-green-500" />
          <span className="truncate max-w-[150px]" title={row.original.pickup_location}>
            {row.original.pickup_location}
          </span>
        </div>
      ),
    },
    {
      accessorKey: 'destination',
      header: 'Destination',
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <MapPin className="w-4 h-4 text-red-500" />
          <span className="truncate max-w-[150px]" title={row.original.destination}>
            {row.original.destination}
          </span>
        </div>
      ),
    },
    {
      accessorKey: 'reservation_date',
      header: 'Date & Time',
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <Calendar className="w-4 h-4 text-purple-500" />
          <div className="text-sm">
            <div>{format(new Date(row.original.reservation_date), 'MMM dd, yyyy')}</div>
            <div className="text-gray-500 flex items-center">
              <Clock className="w-3 h-3 mr-1" />
              {row.original.reservation_time}
            </div>
          </div>
        </div>
      ),
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <Badge variant={getStatusVariant(row.original.status)}>
            {row.original.status}
          </Badge>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleView(row.original)}
            className="h-8 w-8 p-0"
          >
            <Eye className="w-4 h-4" />
          </Button>
        </div>
      ),
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => (
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEdit(row.original)}
            className="h-8 w-8 p-0"
          >
            <Edit className="w-4 h-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleDelete(row.original.id!)}
            className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>
      ),
    },
  ];

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <AlertCircle className="w-12 h-12 text-red-400 mx-auto mb-2" />
            <p className="text-red-600">Error loading special bookings</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center text-xl">
            <Car className="w-6 h-6 mr-3 text-orange-500" />
            Special Bookings Management
          </CardTitle>
          <CreateASpecialAssignment />
        </div>
      </CardHeader>
      <CardContent>
        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search by department, driver, location..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Filter className="w-4 h-4 text-gray-500" />
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="Pending">Pending</SelectItem>
                <SelectItem value="Approved">Approved</SelectItem>
                <SelectItem value="In Progress">In Progress</SelectItem>
                <SelectItem value="Completed">Completed</SelectItem>
                <SelectItem value="Rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Table */}
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Car className="w-6 h-6 animate-spin text-gray-400 mr-2" />
            <span className="text-gray-500">Loading special bookings...</span>
          </div>
        ) : filteredBookings.length === 0 ? (
          <div className="text-center py-8">
            <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-500">No special bookings found</p>
          </div>
        ) : (
          <DataTable
            data={filteredBookings}
            columns={columns}
            enablePagination={true}
            enableSorting={true}
            enableColumnFilters={false}
            enableToolbar={false}
          />
        )}

        {/* Edit Modal */}
        <BaseModal
          isOpen={isEditModalOpen}
          onOpenChange={setIsEditModalOpen}
          title="Edit Special Booking"
          description="Update the special booking details"
          size="lg"
        >
          <Form {...editForm}>
            <form onSubmit={editForm.handleSubmit(handleEditSubmit)} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={editForm.control}
                  name="assigned_to"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel className="flex items-center">
                        <Building className="w-4 h-4 mr-2 text-orange-500" />
                        Assigned To Department *
                      </FormLabel>
                      <Popover open={assignedToOpen} onOpenChange={setAssignedToOpen}>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              role="combobox"
                              aria-expanded={assignedToOpen}
                              className={cn(
                                "justify-between",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              {field.value
                                ? String(departments.find((dept: any) => String(dept?.dp_id) === field.value)?.dp_name || field.value)
                                : departmentsLoading
                                ? "Loading departments..."
                                : "Select department..."}
                              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-[300px] p-0">
                          <Command>
                            <CommandInput placeholder="Search departments..." />
                            <CommandEmpty>
                              {departmentsLoading ? (
                                <div className="flex items-center justify-center py-4">
                                  <Building className="w-4 h-4 animate-spin mr-2" />
                                  <span className="text-sm text-gray-500">Loading departments...</span>
                                </div>
                              ) : (
                                "No department found."
                              )}
                            </CommandEmpty>
                            <CommandGroup>
                              {!departmentsLoading && departments.map((department: any) => {
                                if (!department || !department.dp_id) return null;
                                return (
                                  <CommandItem
                                    key={department.dp_id}
                                    value={`${String(department.dp_name || '')} ${String(department.dep_head_name || '')}`}
                                    onSelect={() => {
                                      editForm.setValue("assigned_to", String(department.dp_id || ''));
                                      setAssignedToOpen(false);
                                    }}
                                  >
                                    <Check
                                      className={cn(
                                        "mr-2 h-4 w-4",
                                        field.value === String(department.dp_id) ? "opacity-100" : "opacity-0"
                                      )}
                                    />
                                    <div className="flex items-center space-x-2">
                                      <Building className="w-4 h-4 text-orange-600" />
                                      <div>
                                        <div className="font-medium">{String(department.dp_name || 'Unknown Department')}</div>
                                        <div className="text-xs text-gray-500">
                                          Head: {String(department.dep_head_name || 'No head assigned')}
                                        </div>
                                      </div>
                                    </div>
                                  </CommandItem>
                                );
                              })}
                            </CommandGroup>
                          </Command>
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={editForm.control}
                  name="pickup_location"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center">
                        <MapPin className="w-4 h-4 mr-2 text-green-500" />
                        Pickup Location *
                      </FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., ABSA Towers, Westlands" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={editForm.control}
                name="destination"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center">
                      <MapPin className="w-4 h-4 mr-2 text-red-500" />
                      Destination *
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Nakuru, Eldoret" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={editForm.control}
                name="reason"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center">
                      <AlertCircle className="w-4 h-4 mr-2 text-blue-500" />
                      Reason for Assignment
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe the purpose of this special assignment..."
                        className="resize-none min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={editForm.control}
                name="remarks"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center">
                      <AlertCircle className="w-4 h-4 mr-2 text-purple-500" />
                      Additional Remarks
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Any additional notes or special instructions..."
                        className="resize-none min-h-[60px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex items-center justify-end space-x-2 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsEditModalOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit" className="bg-orange-500 hover:bg-orange-600">
                  Update Booking
                </Button>
              </div>
            </form>
          </Form>
        </BaseModal>

        {/* View Modal */}
        <BaseModal
          isOpen={isViewModalOpen}
          onOpenChange={setIsViewModalOpen}
          title="Booking Details"
          description="View and manage booking status"
          size="lg"
        >
          {viewingBooking && (
            <div className="space-y-6">
              {/* Booking Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700 flex items-center">
                    <Building className="w-4 h-4 mr-2 text-orange-500" />
                    Assigned To
                  </label>
                  <div className="p-3 bg-gray-50 rounded-md">
                    {(() => {
                      const assignedTo = viewingBooking.assigned_to;
                      let departmentName = 'Unknown';
                      let departmentHead = '';
                      
                      if (typeof assignedTo === 'object' && assignedTo !== null) {
                        departmentName = String(assignedTo.dp_name || assignedTo.name || 'Unknown');
                        departmentHead = String(assignedTo.dep_head_name || assignedTo.head || '');
                      } else {
                        const department = Array.isArray(departments) ? departments.find((dept: any) => String(dept?.dp_id) === String(assignedTo)) : null;
                        departmentName = department?.dp_name || String(assignedTo) || 'Unknown';
                        departmentHead = department?.dep_head_name || '';
                      }

                      return (
                        <div>
                          <div className="font-medium">{departmentName}</div>
                          {departmentHead && (
                            <div className="text-xs text-gray-500">Head: {departmentHead}</div>
                          )}
                        </div>
                      );
                    })()}
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700 flex items-center">
                    <Car className="w-4 h-4 mr-2 text-blue-500" />
                    Driver
                  </label>
                  <div className="p-3 bg-gray-50 rounded-md">
                    {(() => {
                      const driver = viewingBooking.driver;
                      const driverName = typeof driver === 'object' && driver !== null
                        ? (driver as any)?.fullnames || (driver as any)?.name || (driver as any)?.employee_no || 'Unknown Driver'
                        : driver || 'Not Assigned';
                      return String(driverName);
                    })()}
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700 flex items-center">
                    <MapPin className="w-4 h-4 mr-2 text-green-500" />
                    Pickup Location
                  </label>
                  <div className="p-3 bg-gray-50 rounded-md">
                    {viewingBooking.pickup_location}
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700 flex items-center">
                    <MapPin className="w-4 h-4 mr-2 text-red-500" />
                    Destination
                  </label>
                  <div className="p-3 bg-gray-50 rounded-md">
                    {viewingBooking.destination}
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700 flex items-center">
                    <Calendar className="w-4 h-4 mr-2 text-purple-500" />
                    Date & Time
                  </label>
                  <div className="p-3 bg-gray-50 rounded-md">
                    <div>{format(new Date(viewingBooking.reservation_date), 'MMM dd, yyyy')}</div>
                    <div className="text-sm text-gray-500 flex items-center mt-1">
                      <Clock className="w-3 h-3 mr-1" />
                      {viewingBooking.reservation_time}
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    Current Status
                  </label>
                  <div className="p-3 bg-gray-50 rounded-md">
                    <Badge variant={getStatusVariant(viewingBooking.status)}>
                      {viewingBooking.status}
                    </Badge>
                  </div>
                </div>
              </div>

              {/* Reason and Remarks */}
              {(viewingBooking.reason || viewingBooking.remarks) && (
                <div className="space-y-4">
                  {viewingBooking.reason && (
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-gray-700 flex items-center">
                        <AlertCircle className="w-4 h-4 mr-2 text-blue-500" />
                        Reason
                      </label>
                      <div className="p-3 bg-gray-50 rounded-md">
                        {viewingBooking.reason}
                      </div>
                    </div>
                  )}

                  {viewingBooking.remarks && (
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-gray-700 flex items-center">
                        <AlertCircle className="w-4 h-4 mr-2 text-purple-500" />
                        Remarks
                      </label>
                      <div className="p-3 bg-gray-50 rounded-md">
                        {viewingBooking.remarks}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex items-center justify-end space-x-2 pt-4 border-t">
                <Button
                  variant="outline"
                  onClick={() => setIsViewModalOpen(false)}
                >
                  Close
                </Button>

                {/* Status-based action buttons */}
                {viewingBooking.status === 'Pending' && (
                  <>
                    <Button
                      variant="outline"
                      onClick={() => handleReject(viewingBooking.id!)}
                      className="text-red-600 hover:text-red-700 border-red-200 hover:border-red-300"
                    >
                      <XCircle className="w-4 h-4 mr-2" />
                      Reject
                    </Button>
                    <Button
                      onClick={() => handleApprove(viewingBooking.id!)}
                      className="bg-green-500 hover:bg-green-600"
                    >
                      <CheckCircle className="w-4 h-4 mr-2" />
                      Approve
                    </Button>
                  </>
                )}

                {viewingBooking.status === 'Approved' && (
                  <Button
                    onClick={() => handleStartTrip(viewingBooking.id!)}
                    className="bg-blue-500 hover:bg-blue-600"
                  >
                    <Play className="w-4 h-4 mr-2" />
                    Start Trip
                  </Button>
                )}

                {viewingBooking.status === 'In Progress' && (
                  <Button
                    onClick={() => handleEndTrip(viewingBooking.id!)}
                    className="bg-orange-500 hover:bg-orange-600"
                  >
                    <Square className="w-4 h-4 mr-2" />
                    End Trip
                  </Button>
                )}

                {/* No buttons for Rejected and Completed status */}
              </div>
            </div>
          )}
        </BaseModal>

        {/* Reject Confirmation Dialog */}
        <AlertDialog open={isRejectDialogOpen} onOpenChange={setIsRejectDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle className="flex items-center">
                <XCircle className="w-5 h-5 mr-2 text-red-500" />
                Reject Booking
              </AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to reject this special booking? This action cannot be undone and the booking status will be permanently changed to "Rejected".
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={() => {
                setIsRejectDialogOpen(false);
                setBookingToReject(null);
              }}>
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={confirmRejectBooking}
                className="bg-red-500 hover:bg-red-600 focus:ring-red-500"
              >
                <XCircle className="w-4 h-4 mr-2" />
                Reject Booking
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </CardContent>
    </Card>
  );
};

export default SpecialBookingsTable;
