export interface UserGroupPermission {
    permission_id: number;
    permission_name: string;
    comments?: string | null;
  }
  
  export interface TeamPermission {
    permission_id: number;
    permission_name: string;
    comments?: string | null;
  }
  
  export interface UserGroup2UserGroupPermission {
    id: number;
    user_group: number;
    permission: number;
  }
  
  export interface Teams2TeamsPermission {
    id: number;
    user_group: number;
    permission: number;
  }
  
  export interface User2UserPermission {
    id: number;
    user: string;
    permission: number;
  }
  
  export interface UserPermission {
    permission_id: number;
    permission_name: string;
    comments?: string | null;
  }
  
  export interface User {
    employee_no: string;
    first_name: string;
    last_name: string;
    email: string;
    [key: string]: any;
  }
  