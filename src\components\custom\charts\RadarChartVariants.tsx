import {
  RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar,
  Legend, ResponsiveContainer, Tooltip
} from 'recharts';
import React from 'react';

// Sample data
const data = [
  { subject: 'Math', A: 120, B: 110, fullMark: 150 },
  { subject: 'Chinese', A: 98, B: 130, fullMark: 150 },
  { subject: 'English', A: 86, B: 130, fullMark: 150 },
  { subject: 'Geography', A: 99, B: 100, fullMark: 150 },
  { subject: 'Physics', A: 85, B: 90, fullMark: 150 },
  { subject: 'History', A: 65, B: 85, fullMark: 150 },
];

// Helper for styling
const chartStyles = {
  containerStyle: { width: '100%', height: 300 },
  tooltipStyle: { 
    backgroundColor: 'hsl(var(--card))', 
    border: '1px solid hsl(var(--border))',
    borderRadius: '0.5rem',
    color: 'hsl(var(--card-foreground))'
  }
};

// Simple Radar Chart
export const SimpleRadarChart = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <RadarChart cx="50%" cy="50%" outerRadius="80%" data={data}>
          <PolarGrid />
          <PolarAngleAxis dataKey="subject" />
          <PolarRadiusAxis />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Radar
            name="Student A"
            dataKey="A"
            stroke="hsl(var(--primary))"
            fill="hsl(var(--primary) / 0.5)"
            dot={{ fill: 'hsl(var(--primary))', r: 4 }}
          />
          <Radar
            name="Student B"
            dataKey="B"
            stroke="hsl(var(--destructive))"
            fill="hsl(var(--destructive) / 0.5)"
            dot={{ fill: 'hsl(var(--destructive))', r: 4 }}
          />
          <Legend />
        </RadarChart>
      </ResponsiveContainer>
    </div>
  );
};

// Specified Domain Radar Chart
export const SpecifiedDomainRadarChart = () => {
  return (
    <div style={chartStyles.containerStyle}>
      <ResponsiveContainer width="100%" height="100%">
        <RadarChart cx="50%" cy="50%" outerRadius="80%" data={data}>
          <PolarGrid />
          <PolarAngleAxis dataKey="subject" />
          <PolarRadiusAxis angle={30} domain={[0, 150]} />
          <Tooltip contentStyle={chartStyles.tooltipStyle} />
          <Radar
            name="Student A"
            dataKey="A"
            stroke="hsl(var(--primary))"
            fill="hsl(var(--primary) / 0.5)"
            dot={{ fill: 'hsl(var(--primary))', r: 4 }}
          />
          <Radar
            name="Student B"
            dataKey="B"
            stroke="hsl(var(--destructive))"
            fill="hsl(var(--destructive) / 0.5)"
            dot={{ fill: 'hsl(var(--destructive))', r: 4 }}
          />
          <Legend />
        </RadarChart>
      </ResponsiveContainer>
    </div>
  );
}; 