import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";

interface SearchComponentProps {
  searchValue: string;
  setSearchValue: (value: string) => void;
  placeholder?: string;
  delay?: number;
}

export const SearchComponent = ({
  searchValue,
  setSearchValue,
  placeholder = "Search customers...",
  delay = 500,
}: SearchComponentProps) => {
  const [inputValue, setInputValue] = useState(searchValue);

  useEffect(() => {
    setInputValue(searchValue);
  }, [searchValue]);

  // Debounce search to avoid excessive API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      if (inputValue !== searchValue) {
        setSearchValue(inputValue);
      }
    }, delay);

    return () => clearTimeout(timer);
  }, [inputValue, delay, searchValue, setSearchValue]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleClear = () => {
    setInputValue("");
    setSearchValue("");
  };

  return (
    <div className="relative">
      <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
        <Search className="h-4 w-4 text-gray-400" />
      </div>
      <Input
        type="text"
        value={inputValue}
        onChange={handleChange}
        placeholder={placeholder}
        className="pl-10 pr-10"
      />
      {inputValue && (
        <button
          type="button"
          className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-500"
          onClick={handleClear}
        >
          <span className="text-xl">&times;</span>
        </button>
      )}
    </div>
  );
};
