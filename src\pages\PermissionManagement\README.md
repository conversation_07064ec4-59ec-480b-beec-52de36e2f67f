# Permissions Management System

This document explains the permissions system for the Optiven CRM, with a focus on the newly added sales permissions.

## Sales Permissions

The sales permissions are designed to control access to the sales views API. These permissions allow filtering sales data based on different criteria.

### API Endpoint

```
GET /sales-views/
```

This endpoint returns all new sales based on a start date and end date, with optional filtering parameters.

### Filter Parameters

Note that only one filter can be used at a time:

1. **OFFICE** (string, query)
   - Filter by office. If not provided, all offices are included.
   - Available values: HQ, KAREN, ALL
   - Default value: ALL

2. **MARKETER_EMPLOYEE_NO** (string, query)
   - Filter by marketer. If not provided, all marketers are included.
   - Default value: ALL

3. **DIASPORA_REGION** (string, query)
   - Filter by diaspora region. If not provided, all regions are included.
   - Default value: ALL

4. **ORGANIZATION_TEAM** (string, query)
   - Filter by organization team. If not provided, all teams are included.
   - Available values: DIASPOR<PERSON>, D<PERSON><PERSON><PERSON>, TELEMARKETING, OTHER
   - Default value: ALL

5. **search** (string, query)
   - Search by customer name, lead file number, or plot id.

6. **page** (integer, query)
   - Page number for pagination.
   - Default value: 1

7. **page_size** (integer, query)
   - Number of results per page.
   - Default value: 20

### Permission Codes

#### Office-based Permissions
- **VIEW_SALES_HQ** (1001): Permission to view sales data from HQ office
- **VIEW_SALES_KAREN** (1002): Permission to view sales data from KAREN office
- **VIEW_SALES_ALL_OFFICES** (1003): Permission to view sales data from all offices

#### Marketer-based Permissions
- **VIEW_SALES_OWN_MARKETER** (1004): Permission to view sales data for the logged-in marketer only
- **VIEW_SALES_ALL_MARKETERS** (1005): Permission to view sales data for all marketers

#### Organization Team-based Permissions
- **VIEW_SALES_DIASPORA_TEAM** (1006): Permission to view sales data from DIASPORA team
- **VIEW_SALES_DIGITAL_TEAM** (1007): Permission to view sales data from DIGITAL team
- **VIEW_SALES_TELEMARKETING_TEAM** (1008): Permission to view sales data from TELEMARKETING team
- **VIEW_SALES_OTHER_TEAM** (1009): Permission to view sales data from OTHER team
- **VIEW_SALES_ALL_TEAMS** (1010): Permission to view sales data from all teams

#### Diaspora Region-based Permissions
- **VIEW_SALES_DIASPORA_REGION** (1011): Permission to view sales data filtered by diaspora region
- **VIEW_SALES_ALL_DIASPORA_REGIONS** (1012): Permission to view sales data from all diaspora regions

## Adding Permissions to the Database

You can add these permissions to the database using the SQL script provided in the `sql/sales_permissions.sql` file. This script will add the permissions to the user_permissions, team_permissions, and group_permissions tables.

## Assigning Permissions

Permissions can be assigned at three levels:
1. **User level**: Assign permissions directly to individual users
2. **Team level**: Assign permissions to teams, which will apply to all users in those teams
3. **Group level**: Assign permissions to groups, which will apply to all users in those groups

## Implementation Notes

When implementing these permissions in the backend:

1. Check the user's permissions before returning sales data
2. Apply the appropriate filter based on the user's permissions
3. If a user has multiple permissions, prioritize them in this order:
   - Office-based permissions
   - Marketer-based permissions
   - Team-based permissions
   - Diaspora region-based permissions

## UI Implementation

The permissions management UI now includes:
1. A tab-based interface to manage different types of permissions (user, team, group)
2. A reference section with detailed information about available permissions
3. A template-based system for adding new permissions quickly