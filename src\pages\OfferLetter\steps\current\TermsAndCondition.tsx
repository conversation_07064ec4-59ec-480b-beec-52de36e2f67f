import React from 'react';

interface TermsAndConditionsProps {
  formData: any;
  setFormData: (data: any) => void;
  onNext: () => void;
  onBack: () => void;
}

const TermsAndConditions: React.FC<TermsAndConditionsProps> = ({
  formData,
  setFormData,
  onNext,
  onBack,
}) => {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (formData.termsAccepted) {
      onNext();
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold text-gray-800 mb-2">Terms and Conditions</h2>
        <p className="text-gray-600 mb-6">You're almost done! Please review and agree to the terms and conditions to finish.</p>
      </div>

      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium text-gray-700 mb-4">Terms & Conditions List</h3>
          
          <div className="bg-gray-50 p-6 rounded-lg max-h-96 overflow-y-auto space-y-4 text-sm">
            <div>
              <strong>1.</strong> This letter of offer does not constitute a Sale Agreement.
            </div>
            
            <div>
              <strong>2.</strong> Any amount paid less than the stipulated deposit will be taken as a booking fee and if the purchaser does not top up the balance within 14 days the booked plot will be opened up for sale and 20% of the paid amount will be deducted as administrative cost.
            </div>
            
            <div>
              <strong>3.</strong> Should the Purchaser:
              <div className="ml-4 mt-2 space-y-1">
                <div>a. Cancel/Withdraw from this letter of offer.</div>
                <div>b. Default in making any of the payments due and whether or not the Sale Agreement will have been executed; the vendor shall reserve the right to treat this offer as cancelled and the purchaser shall forfeit 10% per annum of the purchase price plus administrative costs.</div>
              </div>
            </div>
            
            <div>
              <strong>4.</strong> The purchaser should sign the Sale Agreement with full particulars within thirty (30) working days of execution of this letter of offer.
            </div>
            
            <div>
              <strong>5.</strong> The Sale Agreement will be prepared by the vendor's advocates and will incorporate the Law Society of Kenya Conditions of sale (2015 Edition) in so far as they are not inconsistent with this letter of offer.
            </div>
            
            <div>
              <strong>6.</strong> A payment plan shall be provided by the vendor at the point of sale and the same shall not be tied to the execution of the Sale Agreement.
            </div>
            
            <div>
              <strong>7.</strong> The parties hereby agree that by submitting this form you accept the terms and conditions that constitutes a valid contract between the parties.
            </div>
            
            <div>
              <strong>8.</strong> All Payments to be made to ONLY the Optiven bank accounts or official Optiven Paybill, 921225, and the purchaser should request for a receipt within 24hrs.
            </div>
            
            <div>
              <strong>9.</strong> All the MONTHLY INSTALLMENTS MUST be remitted on or before the due date based on the start date.
            </div>
          </div>
        </div>

        <div className="flex items-start space-x-3">
          <input
            type="checkbox"
            id="terms-checkbox"
            checked={formData.termsAccepted || false}
            onChange={(e) => setFormData({ ...formData, termsAccepted: e.target.checked })}
            className="mt-1 text-green-500 focus:ring-green-500"
            required
          />
          <label htmlFor="terms-checkbox" className="text-gray-700 cursor-pointer">
            I/We confirm that I/we have read and agree to the above terms and conditions
          </label>
        </div>
      </div>

      <div className="flex justify-between pt-6">
        <button
          type="button"
          onClick={onBack}
          className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
        >
          ← Go Back
        </button>
        <button
          type="submit"
          disabled={!formData.termsAccepted}
          className="px-6 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed"
        >
          Next Page →
        </button>
      </div>
    </form>
  );
};

export default TermsAndConditions;