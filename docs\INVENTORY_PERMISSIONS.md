# Inventory Permissions Implementation

## Overview

This document describes the implementation of permissions for the inventory sidebar section. The system now supports granular permissions for different inventory features.

## Permission Codes

The following inventory permission codes have been implemented:

| Permission Code | Constant                          | Description                       | Sidebar Item       |
| --------------- | --------------------------------- | --------------------------------- | ------------------ |
| 7112            | VIEW_INVENTORY_FULL_ACCESS        | View Inventory Full Access        | All Sections       |
| 7113            | VIEW_INVENTORY_MARKETER           | View Inventory Marketer           | My Booking         |
| 7114            | VIEW_INVENTORY_GM_HQ              | View Inventory GM HQ              | Special Bookings   |
| 7115            | VIEW_INVENTORY_GM_KAREN           | View Inventory GM KAREN           | Special Bookings   |
| 7116            | VIEW_INVENTORY_ACCOUNTS           | View Inventory Accounts           | Accounts           |
| 7117            | VIEW_INVENTORY_DIASPORA           | View Inventory Diaspora           | Diaspora           |
| 7118            | VIEW_INVENTORY_REPORTS            | View Inventory Reports            | Reports            |
| 7119            | VIEW_INVENTORY_PRICING            | View Inventory Pricing            | Pricing            |
| 7120            | VIEW_INVENTORY_MPESA_TRANSACTIONS | View Inventory Mpesa Transactions | Mpesa Transactions |
| 7121            | VIEW_INVENTORY_LOGS               | View Inventory Logs               | Inventory Logs     |
| 7122            | VIEW_INVENTORY_MAPS               | View Inventory Maps               | Quick Links        |

## Implementation Details

### 1. Permission Constants

- Added `INVENTORY_PERMISSIONS` constant in `src/hooks/useSidebarPermissions.ts`
- Added `hasInventoryPermission` function to check inventory-specific permissions

### 2. Permission Descriptions

- Updated `src/utils/permissionChecker.ts` with descriptions for all inventory permissions

### 3. Sidebar Filtering

- Implemented `filterInventoryItems` function in `src/app-components/sidebar/app-sidebar.tsx`
- The function checks each inventory menu item against its corresponding permission
- Items without permission are hidden from the sidebar

### 4. Route Protection

- Updated route permissions in:
  - `src/components/auth/PermissionGuard.tsx`
  - `src/components/navigation/PermissionAwareLink.tsx`
  - `src/hooks/usePermissionNavigation.ts`

## Sidebar Behavior

### Inventory Section Visibility

**⚠️ Important:** The entire **Inventory** section in the sidebar is **completely hidden** if the user has **no inventory permissions** at all.

### Items Always Visible (No Permission Required)

- Dashboard (`/projects`) - Only visible if user has at least one inventory permission

### Items Requiring Specific Permissions

- **Full Access Override** - Permission 7112 (VIEW_INVENTORY_FULL_ACCESS) grants access to ALL inventory features
- **My Booking** - Requires permission 7113 (VIEW_INVENTORY_MARKETER) OR 7117 (VIEW_INVENTORY_DIASPORA)
- **Special Bookings** - Requires permission 7114 (VIEW_INVENTORY_GM_HQ) OR 7115 (VIEW_INVENTORY_GM_KAREN)
- **Accounts** - Requires permission 7116 (VIEW_INVENTORY_ACCOUNTS)
- **Diaspora** - ❌ **Hidden for all users** (sidebar section removed)
- **Reports** - Requires permission 7118 (VIEW_INVENTORY_REPORTS)
- **Pricing** - Requires permission 7119 (VIEW_INVENTORY_PRICING)
- **Mpesa Transactions** - Requires permission 7120 (VIEW_INVENTORY_MPESA_TRANSACTIONS)
- **Inventory Logs** - Requires permission 7121 (VIEW_INVENTORY_LOGS)

## Testing

### Manual Testing

1. Assign permission 7116 (VIEW_INVENTORY_ACCOUNTS) to a user
2. Login with that user
3. Navigate to the sidebar
4. Verify that the "Accounts" section is visible
5. Remove the permission and verify the section is hidden

### Test Component

A test component has been created at `src/components/test/InventoryPermissionTest.tsx` that can be temporarily added to any page to verify permissions are working correctly.

### Usage Example

```tsx
import InventoryPermissionTest from "@/components/test/InventoryPermissionTest";

// Add this component to any page for testing
<InventoryPermissionTest />;
```

## Route Mappings

The following routes are now protected with inventory permissions:

```typescript
'/mybookings': [7113, 7117], // My Booking - requires VIEW_INVENTORY_MARKETER or VIEW_INVENTORY_DIASPORA
'/inventory/booking-approvals': [7114, 7115], // Special Bookings - requires GM HQ or GM KAREN
'/inventory/accounts': [7116], // VIEW_INVENTORY_ACCOUNTS
'/diaspora-trips': [7117], // VIEW_INVENTORY_DIASPORA
'/diaspora-reservations': [7117], // VIEW_INVENTORY_DIASPORA
'/diaspora-receipts': [7117], // VIEW_INVENTORY_DIASPORA
'/plotmarketreport': [7118], // VIEW_INVENTORY_REPORTS
'/inventory/pricing': [7119], // VIEW_INVENTORY_PRICING
'/inventory/pricing/project-pricing': [7119], // VIEW_INVENTORY_PRICING
'/inventory/pricing/payment-plan-checker': [7119], // VIEW_INVENTORY_PRICING
'/mpesa-transactions': [7120], // VIEW_INVENTORY_MPESA_TRANSACTIONS
'/inventory/logs': [7121], // VIEW_INVENTORY_LOGS
```

## Action-Level Permissions

### Action-Level Permission Controls

#### VIEW_INVENTORY_FULL_ACCESS (7112)

**Complete Override Permission:**

- Grants access to ALL inventory sidebar sections
- Shows ALL plot statuses (Open/Sold/RVD) in both Projects and Project Details
- Shows ALL action buttons in Plot Bookings section
- Shows ALL sections (Diaspora Trips, Quick Links, etc.)
- Overrides any restrictions from other permissions

#### VIEW_INVENTORY_MARKETER (7113)

**Plot Bookings Section - Allowed buttons only:**

- Special Bookings
- M-Pesa Bookings
- Other Bookings
- Plot Payment Options (available for all users)

**OPTIVEN PROJECTS Section - Display restrictions:**

- Shows only **Open plots** in project cards
- Hides **Sold** and **RVD** plot counts
- Percentage sold information is hidden
- Project card shows "X Open Plots" instead of the three-section layout

**Project Details Page - Display restrictions:**

- Shows only **Open plots** section (clickable)
- Hides **Sold** and **RVD** sections
- Percentage sold information is hidden

#### VIEW_INVENTORY_DIASPORA (7117)

**Sidebar Access:**

- ✅ **My Booking** section (shared with marketer users)
- ❌ **Diaspora** section (hidden - no access to Diaspora Trips, Reservations, Receipts)

**Plot Bookings Section - Allowed buttons only:**

- Reserve Plot
- Diaspora Receipting
- Plot Payment Options

**Hidden buttons:**

- Special Bookings
- M-Pesa Bookings
- Other Bookings

**Diaspora Trips Plot Reservations Section:**

- Entire section is hidden without this permission
- Includes View, Reserve, and Receipt Booking buttons

**OPTIVEN PROJECTS Section - Display restrictions:**

- Shows only **Open plots** in project cards
- Hides **Sold** and **RVD** plot counts
- Percentage sold information is hidden
- Project card shows "X Open Plots" instead of the three-section layout

**Project Details Page:**

- Shows only **Open plots** section (clickable)
- Hides **Sold** and **RVD** sections
- Percentage sold information is hidden
- Entire "Diaspora Plots Reservations" section is visible with this permission
- Includes "Reserve Plots for diaspora trip" and "Reserve plot(s)" button

#### VIEW_INVENTORY_MAPS (7122)

**Quick Links Section - Required for:**

- Entire Quick Links section visibility
- Manage Project Visibility
- Map Management
- Forex Management

### Implementation Details

The action-level permissions are implemented in:

- `src/pages/inventory/Projects/index.tsx`

```typescript
// Check marketer permission
const { hasInventoryPermission } = useSidebarPermissions();
const hasMarketerPermission = hasInventoryPermission("VIEW_INVENTORY_MARKETER");

// Filter buttons based on permission
.filter((item) => {
  if (hasMarketerPermission) {
    return item.allowedForMarketer;
  }
  return true;
})

// Conditional project card display
{hasMarketerPermission ? (
  // Show only Open plots
  <p className="px-2 py-1 bg-primary text-white text-center w-full">
    {project?.open_plots} Open Plots
  </p>
) : (
  // Show all plot statuses
  // ... full layout
)}
```

## Next Steps

1. Test the implementation with real user permissions
2. Verify that users without permissions cannot access protected routes
3. Ensure the sidebar correctly shows/hides items based on permissions
4. Test action-level permissions in the Projects page
5. Remove the test component after verification
