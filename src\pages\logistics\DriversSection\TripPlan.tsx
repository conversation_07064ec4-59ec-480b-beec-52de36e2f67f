import { useState } from "react";
import BaseModal from "@/components/custom/modals/BaseModal";
import { Input } from "@/components/ui/input";
import {  SecondaryBadge } from "@/components/custom/badges/badges";
import { Label } from "@/components/ui/label";
import { PrimaryButton, OutlinedButton } from "@/components/custom/buttons/buttons";
import { toast } from "@/components/custom/Toast/MyToast";

interface PlanTripModalProps {
  isOpen: boolean;
  onClose: () => void;
  onPlanTrip: (trip: {
    date: string;
    destination: string;
    vehicle: string;
    distance: string;
  }) => void;
}

const PlanTripModal: React.FC<PlanTripModalProps> = ({ isOpen, onClose, onPlanTrip }) => {
  const [tripData, setTripData] = useState({
    date: "",
    destination: "",
    vehicle: "",
    distance: "",
  });

  const [errors, setErrors] = useState<{
    date?: string;
    destination?: string;
    vehicle?: string;
    distance?: string;
  }>({});

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setTripData((prev) => ({ ...prev, [name]: value }));
    setErrors((prev) => ({ ...prev, [name]: undefined })); 
  };

  const validateTrip = (): boolean => {
    const newErrors: typeof errors = {};

    if (!tripData.date) {
      newErrors.date = "Date is required";
    }
    if (!tripData.destination) {
      newErrors.destination = "Destination is required";
    }
    if (!tripData.vehicle) {
      newErrors.vehicle = "Vehicle is required";
    }
    if (!tripData.distance) {
      newErrors.distance = "Distance is required";
    } else if (!/^\d+\s?(km|miles)?$/.test(tripData.distance)) {
      newErrors.distance = "Distance must be a valid number (e.g., '500 km')";
    }

    setErrors(newErrors);

    if (Object.keys(newErrors).length > 0) {
      toast("error", "Please fix the errors before submitting.");
      return false;
    }

    return true;
  };

  const handleSubmit = () => {
    if (validateTrip()) {
      onPlanTrip(tripData);
      toast("success", "Trip planned successfully!");
      onClose();
    }
  };

  return (
    <BaseModal
      isOpen={isOpen}
      onOpenChange={onClose}
      title="Plan a New Trip"
      description="Fill in the details below to plan a new trip for the driver."
      size="md"
      footer={
        <div className="flex justify-end gap-2">
          <OutlinedButton onClick={onClose}>Cancel</OutlinedButton>
          <SecondaryBadge rounded="md" variant="secondary" size="sm" className="text-sm font-semibold"> Scheduling</SecondaryBadge>
          <PrimaryButton onClick={handleSubmit}>Plan Trip</PrimaryButton>
        </div>
      }
    >
      <div className="space-y-4 py-2">
        <div className="space-y-2">
          <Label htmlFor="date">Date</Label>
          <Input
            id="date"
            name="date"
            type="date"
            value={tripData.date}
            onChange={handleInputChange}
            className={errors.date ? "border-red-500" : ""}
          />
          {errors.date && <p className="text-red-500 text-sm">{errors.date}</p>}
        </div>
        <div className="space-y-2">
          <Label htmlFor="destination">Destination</Label>
          <Input
            id="destination"
            name="destination"
            value={tripData.destination}
            onChange={handleInputChange}
            placeholder="Enter destination (e.g., Nairobi to Mombasa)"
            className={errors.destination ? "border-red-500" : ""}
          />
          {errors.destination && (
            <p className="text-red-500 text-sm">{errors.destination}</p>
          )}
        </div>
        <div className="space-y-2">
          <Label htmlFor="vehicle">Vehicle</Label>
          <Input
            id="vehicle"
            name="vehicle"
            value={tripData.vehicle}
            onChange={handleInputChange}
            placeholder="Enter vehicle (e.g., Truck ABC123)"
            className={errors.vehicle ? "border-red-500" : ""}
          />
          {errors.vehicle && (
            <p className="text-red-500 text-sm">{errors.vehicle}</p>
          )}
        </div>
        <div className="space-y-2">
          <Label htmlFor="distance">Distance</Label>
          <Input
            id="distance"
            name="distance"
            value={tripData.distance}
            onChange={handleInputChange}
            placeholder="Enter distance (e.g., 500 km)"
            className={errors.distance ? "border-red-500" : ""}
          />
          {errors.distance && (
            <p className="text-red-500 text-sm">{errors.distance}</p>
          )}
        </div>
      </div>
    </BaseModal>
  );
};

export default PlanTripModal;