import SimpleTable, {
  ColumnDefinitionST,
} from "@/components/custom/tables/SimpleTable";

import BookingApprovalCard from "../inventory/BookingApproval/BookingApprovalCard";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";

interface BookingType {
  booking_id: string;
  booking_type: string;
  plots: string;
  amount: string;
  marketer_name: string;
  customer_name: string;
  transaction_id: string;
  creation_date: string;
  proof_of_payment: string;
  upload_time: string;
  type: string;
  status: string;
  office: string;
  deadline: string;
}

type Props = {
  data: any;
  loading: boolean;
};

const OtherBookingsReport = ({ data, loading }: Props) => {
  // const columns: ColumnDefinitionST<(typeof data)[0]>[] = [
  //   {
  //     key: "plots",
  //     header: "Plots",
  //     headerClassName: "font-normal px-6 py-5",
  //     cellClassName: "px-6 py-3",
  //   },
  //   {
  //     key: "amount",
  //     header: "Amount",
  //     headerClassName: "font-normal px-6",
  //     cellClassName: "px-6",
  //   },
  //   {
  //     key: "marketer_name",
  //     header: "Marketer",
  //     headerClassName: "font-normal px-6",
  //     cellClassName: "px-6",
  //   },
  //   {
  //     key: "customer_name",
  //     header: "Customer",
  //     headerClassName: "font-normal px-6",
  //     cellClassName: "px-6",
  //   },
  //   {
  //     key: "proof_of_payment",
  //     header: "Payment Document",
  //     headerClassName: "font-normal px-6",
  //     cellClassName: "px-6",
  //     renderCell: (row) => {
  //       return (
  //         <a
  //           href={row?.proof_of_payment}
  //           download
  //           target="_blank"
  //           className="text-blue-600 hover:underline"
  //         >
  //           {row?.proof_of_payment.split("/").pop() || "Download Document"}
  //         </a>
  //       );
  //     },
  //   },
  //   {
  //     key: "transaction_id",
  //     header: "Transaction Ref",
  //     headerClassName: "font-normal px-6",
  //     cellClassName: "px-6",
  //   },
  //   {
  //     key: "status",
  //     header: "Status",
  //     headerClassName: "font-normal px-6",
  //     cellClassName: "px-6",
  //     renderCell: (row) => (
  //       <span
  //         className={`px-2 py-1 rounded-full text-xs font-medium ${
  //           row.status === "OPEN"
  //             ? "bg-blue-100 text-blue-800"
  //             : row.status === "DONE"
  //             ? "bg-green-100 text-green-800"
  //             : row.status === "WAITING"
  //             ? "bg-yellow-100 text-yellow-800"
  //             : "bg-red-100 text-red-800"
  //         }`}
  //       >
  //         {row.status}
  //       </span>
  //     ),
  //   },
  // ];

  return (
    <div className="grid lg:grid-cols-4 md:grid-cols-2 sm:grid-cols-1 gap-3 pt-4 border-t border-t-blue-400">
      {loading ? (
        <div className="flex justify-center items-center w-full h-full">
          <SpinnerTemp type="spinner-double" size="lg" />
        </div>
      ) : data?.data?.results?.length < 1 ? (
        <p>No Other Booking Data Found</p>
      ) : (
        data?.data?.results.map((booking: BookingType) => (
          <BookingApprovalCard key={booking.booking_id} rowData={booking} />
        ))
      )}
    </div>
    // <SimpleTable
    //   data={data?.data}
    //   columns={columns}
    //   containerClassName="rounded-lg overflow-hidden border border-gray-200"
    //   tableClassName="min-w-full border-collapse bg-white"
    //   tBodyClassName="even:bg-gray-50 hover:bg-gray-100 transition-colors duration-150"
    // />
  );
};

export default OtherBookingsReport;
