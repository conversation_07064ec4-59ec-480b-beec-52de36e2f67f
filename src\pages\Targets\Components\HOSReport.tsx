import { useState, useEffect } from 'react';
import LazyModal, { TableColumn } from '@/components/reports/ReportsModal';
import { useGetHOSGMTargetsQuery } from '@/redux/slices/targetsApiSlice';

export interface HOSReport {
  line_no: number;
  marketer_no: string;
  marketer_name: string;
  title: string;
  status: string;
  period_start_date: string;
  period_end_date: string;
  monthly_target: string;
  daily_target: string;
  MIB_achieved: string;
  MIB_Perfomance: string;
  commission_rate: string;
  commission_payable: string;
}

interface HOSReportProps {
  isModalOpen: boolean;
  setIsModalOpen: (open: boolean) => void;
  params?: {
    period_name: string;
    start_date: string;
    end_date: string;
    role?: string;
  };
}

const columns: TableColumn<HOSReport>[] = [
  { key: 'line_no', title: 'Line No' },
  { key: 'marketer_no', title: 'Marketer No' },
  { key: 'marketer_name', title: 'Marketer Name' },
  { key: 'title', title: 'Title' },
  { key: 'status', title: 'Status' },
  { key: 'period_start_date', title: 'Period Start' },
  { key: 'period_end_date', title: 'Period End' },
  { key: 'monthly_target', title: 'Monthly Target', render: v => <span>{Number(v).toLocaleString()}</span> },
  { key: 'daily_target', title: 'Daily Target', render: v => <span>{Number(v).toLocaleString()}</span> },
  { key: 'MIB_achieved', title: 'MIB Achieved', render: v => <span>{Number(v).toLocaleString()}</span> },
  { key: 'MIB_Perfomance', title: 'MIB Performance', render: v => <span>{Number(v).toLocaleString()}%</span> },
  { key: 'commission_rate', title: 'Commission Rate', render: v => <span>{v}</span> },
  { key: 'commission_payable', title: 'Commission Payable', render: v => <span>{Number(v).toLocaleString()}</span> },
];

const HOSReportModal = ({ isModalOpen, setIsModalOpen, params }: HOSReportProps) => {
  // State for date filtering
  const [startDate, setStartDate] = useState<string>(params?.start_date || '');
  const [endDate, setEndDate] = useState<string>(params?.end_date || '');
  const [currentParams, setCurrentParams] = useState(params);

  // Update local state when props change
  useEffect(() => {
    if (params) {
      setStartDate(params.start_date || '');
      setEndDate(params.end_date || '');
      setCurrentParams(params);
    }
  }, [params]);

  // Use the API hook with current parameters
  const { data, isLoading, error, refetch } = useGetHOSGMTargetsQuery({
    period_start_date: currentParams?.start_date,
    period_end_date: currentParams?.end_date,
    page: 1,
    page_size: 20,
  }, {
    skip: !currentParams?.start_date || !currentParams?.end_date,
  });

  console.log("HOS", data);

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  // Handle date changes and trigger data reload
  const handleDateChange = (type: 'start' | 'end', date: string) => {
    if (type === 'start') {
      setStartDate(date);
    } else {
      setEndDate(date);
    }

    // Update current params to trigger API refetch
    const newParams = {
      ...currentParams,
      start_date: type === 'start' ? date : startDate,
      end_date: type === 'end' ? date : endDate,
      period_name: currentParams?.period_name ?? '',
    };

    setCurrentParams(newParams as {
      period_name: string;
      start_date: string;
      end_date: string;
      role?: string;
    });
    
    // Manually trigger refetch for immediate update
    setTimeout(() => {
      refetch();
    }, 100);
  };

  // Mount the data for use in the modal table
  const tableData = (() => {
    if (!data) return [];
    
    if (data.results && Array.isArray(data.results)) {
      return data.results;
    }
    
    if (data.data?.results && Array.isArray(data.data.results)) {
      return data.data.results;
    }
    
    if (Array.isArray(data)) {
      return data;
    }
    
    return [];
  })();

  // Date Filter Component
  const DateFilters = () => (
    <div className="flex gap-4 mb-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
      <div className="flex flex-col">
        <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Start Date
        </label>
        <input
          type="date"
          value={startDate}
          onChange={(e) => handleDateChange('start', e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white"
        />
      </div>
      <div className="flex flex-col">
        <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          End Date
        </label>
        <input
          type="date"
          value={endDate}
          onChange={(e) => handleDateChange('end', e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white"
        />
      </div>
      <div className="flex items-end">
        <button
          onClick={() => refetch()}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          disabled={isLoading}
        >
          {isLoading ? 'Loading...' : 'Refresh'}
        </button>
      </div>
    </div>
  );

  return (
    <LazyModal<HOSReport>
      isOpen={isModalOpen}
      onClose={handleCloseModal}
      title="HOS Reports"
      url="/hosgm-targets/"
      params={currentParams}
      columns={columns}
      size="lg"
      TableComponent={({ ...rest }) => (
        <div>
          <DateFilters />
          
          {isLoading && <div className="p-4 text-center">Loading...</div>}
          {error && <div className="p-4 text-center text-red-600">Error loading data</div>}
          {!isLoading && !error && (
            <div className="overflow-x-auto">
              <table className="min-w-full bg-white dark:bg-gray-800">
                <thead>
                  <tr className="bg-gray-50 dark:bg-gray-700">
                    {columns.map((col) => (
                      <th key={String(col.key)} className="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {col.title}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
                  {tableData.map((row: HOSReport, idx: number) => (
                    <tr key={row.line_no || idx} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                      {columns.map((col) => (
                        <td key={String(col.key)} className="px-2 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          {col.render
                            ? col.render(row[col.key as keyof HOSReport], row, idx)
                            : row[col.key as keyof HOSReport]}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
          {!isLoading && !error && tableData.length === 0 && (
            <div className="p-4 text-center text-gray-500">No data available for the selected date range</div>
          )}
        </div>
      )}
    />
  );
};

export default HOSReportModal;