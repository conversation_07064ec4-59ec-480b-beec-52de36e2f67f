import { Screen } from "@/app-components/layout/screen";
import {
  Eye,
  Pin,
  <PERSON>,
  Clock,
  FileText,
  UserCheck,
  Target,
  TrendingUp,
  Activity,
  BarChart3,
  CheckCircle,
  AlertCircle,
  Filter,
  User,
  Building2,
} from "lucide-react";
import { useState, useEffect } from "react";
import ViewNoteModal from "../../components/servicesdashboard/Notes/VieNotes";
import { useLazyGetNotesQuery } from "@/redux/slices/services";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useSelector } from "react-redux";

// Define RootState for Redux
interface RootState {
  auth: {
    userInfo: {
      id: string; // Adjust based on your actual user object structure
    } | null;
  };
}

// Utility to compute change and changeLabel
const getTimeDiff = (date: string): { change: string; changeLabel: string } => {
  const now = new Date();
  const updated = new Date(date);
  const diffMs = now.getTime() - updated.getTime();
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffHours < 1) {
    return { change: "0h", changeLabel: "Just updated" };
  } else if (diffHours < 24) {
    return { change: `-${diffHours}h`, changeLabel: `Updated ${diffHours} hours ago` };
  } else {
    return { change: `+${diffDays}d`, changeLabel: `Updated ${diffDays} days ago` };
  }
};

type Note = {
  id: string;
  title: string;
  note_type: string;
  content: string;
  is_private: boolean;
  is_pinned: boolean;
  client_status: string;
  entity_type: string;
  entity_name: string;
  created_at: string;
  updated_at: string;
  change: string;
  changeLabel: string;
  created_by?: string; // Added to support filtering
};

// Note type enum values from API
const NOTE_TYPES = [
  { value: "all", label: "All Types", icon: FileText, color: "gray" },
  { value: "General", label: "General", icon: FileText, color: "blue" },
  { value: "Important", label: "Important", icon: AlertCircle, color: "red" },
  { value: "Reminder", label: "Reminder", icon: Clock, color: "yellow" },
  { value: "Follow-up", label: "Follow-up", icon: CheckCircle, color: "green" },
  { value: "Internal", label: "Internal", icon: User, color: "purple" },
  { value: "Customer Facing", label: "Customer Facing", icon: Building2, color: "indigo" },
] as const;

function ViewNotes() {
  const [activeCategory, setActiveCategory] = useState<"Customer" | "Lead File" | "Prospect">("Customer");
  const [hoveredNote, setHoveredNote] = useState<string | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [selectedNote, setSelectedNote] = useState<Note | null>(null);
  const [selectedNoteType, setSelectedNoteType] = useState<string>("all");

  // Get logged-in user's ID
  const userId = useSelector((state: RootState) => state.auth.userInfo?.id);

  // Fetch notes for each category using client_status
  const [triggerCustomer, { data: customerNotes, isLoading: customerLoading }] = useLazyGetNotesQuery();
  const [triggerSales, { data: salesNotes, isLoading: salesLoading }] = useLazyGetNotesQuery();
  const [triggerProspects, { data: prospectsNotes, isLoading: prospectsLoading }] = useLazyGetNotesQuery();

  // Fetch notes for each category on component mount and when note type filter changes
  useEffect(() => {
    if (!userId) return; // Skip if no user ID
    const filters = {
      entity_type: "customer" as const,
      created_by: userId,
      ...(selectedNoteType && selectedNoteType !== "all" ? { note_type: selectedNoteType } : {}),
    };
    triggerCustomer(filters);
  }, [triggerCustomer, selectedNoteType, userId]);

  useEffect(() => {
    if (!userId) return; // Skip if no user ID
    const filters = {
      entity_type: "leadfile" as const,
      created_by: userId,
      ...(selectedNoteType && selectedNoteType !== "all" ? { note_type: selectedNoteType } : {}),
    };
    triggerSales(filters);
  }, [triggerSales, selectedNoteType, userId]);

  useEffect(() => {
    if (!userId) return; // Skip if no user ID
    const filters = {
      entity_type: "prospect" as const,
      created_by: userId,
      ...(selectedNoteType && selectedNoteType !== "all" ? { note_type: selectedNoteType } : {}),
    };
    triggerProspects(filters);
  }, [triggerProspects, selectedNoteType, userId]);

  // Transform notes data for each category
  const transformNotes = (data: any): Note[] => {
    return data?.data?.results
      ? data.data.results.map((note: any) => {
          const { change, changeLabel } = getTimeDiff(note.updated_at || note.created_at);
          return {
            id: note.note_id,
            title: note.title,
            note_type: note.note_type || "General",
            content: note.content || "",
            is_private: note.is_private || false,
            is_pinned: note.is_pinned || false,
            client_status: note.client_status || "",
            entity_type: note.entity_type || "",
            entity_name: note.entity_name || "",
            created_at: note.created_at || "",
            updated_at: note.updated_at || "",
            created_by: note.created_by || "", // Include created_by
            change,
            changeLabel,
          };
        })
      : [];
  };

  const customerNotesData = transformNotes(customerNotes);
  const salesNotesData = transformNotes(salesNotes);
  const prospectsNotesData = transformNotes(prospectsNotes);

  // Calculate stats for each category
  const getStats = (notes: Note[]) => ({
    total: notes.length,
    private: notes.filter((n) => n.is_private).length,
    pinned: notes.filter((n) => n.is_pinned).length,
    recent: notes.filter((n) => {
      const daysDiff = Math.floor(
        (new Date().getTime() - new Date(n.created_at).getTime()) / (1000 * 60 * 60 * 24)
      );
      return daysDiff <= 7;
    }).length,
    byType: {
      general: notes.filter((n) => n.note_type === "General").length,
      important: notes.filter((n) => n.note_type === "Important").length,
      reminder: notes.filter((n) => n.note_type === "Reminder").length,
      followUp: notes.filter((n) => n.note_type === "Follow-up").length,
      internal: notes.filter((n) => n.note_type === "Internal").length,
      customerFacing: notes.filter((n) => n.note_type === "Customer Facing").length,
    },
  });

  const customerStats = getStats(customerNotesData);
  const salesStats = getStats(salesNotesData);
  const prospectsStats = getStats(prospectsNotesData);

  const handleViewNote = (note: Note) => {
    setSelectedNote(note);
    setIsViewModalOpen(true);
  };

  const handleNoteUpdate = () => {
    setIsViewModalOpen(false);
    if (!userId) return;
    const customerFilters = {
      entity_type: "customer" as const,
      created_by: userId,
      ...(selectedNoteType && selectedNoteType !== "all" ? { note_type: selectedNoteType } : {}),
    };
    const salesFilters = {
      entity_type: "leadfile" as const,
      created_by: userId,
      ...(selectedNoteType && selectedNoteType !== "all" ? { note_type: selectedNoteType } : {}),
    };
    const prospectsFilters = {
      entity_type: "prospect" as const,
      created_by: userId,
      ...(selectedNoteType && selectedNoteType !== "all" ? { note_type: selectedNoteType } : {}),
    };

    triggerCustomer(customerFilters);
    triggerSales(salesFilters);
    triggerProspects(prospectsFilters);
  };

  // Helper function to render note cards
  const renderNoteCard = (note: Note, index: number) => {
    const getNoteTypeIcon = (type: string) => {
      switch (type) {
        case "General":
          return FileText;
        case "Important":
          return AlertCircle;
        case "Reminder":
          return Clock;
        case "Follow-up":
          return CheckCircle;
        case "Internal":
          return User;
        case "Customer Facing":
          return Building2;
        default:
          return FileText;
      }
    };

    const getNoteTypeColor = (type: string) => {
      switch (type) {
        case "General":
          return { bg: "bg-blue-100/80", text: "text-blue-700", border: "border-blue-200" };
        case "Important":
          return { bg: "bg-red-100/80", text: "text-red-700", border: "border-red-200" };
        case "Reminder":
          return { bg: "bg-yellow-100/80", text: "text-yellow-700", border: "border-yellow-200" };
        case "Follow-up":
          return { bg: "bg-green-100/80", text: "text-green-700", border: "border-green-200" };
        case "Internal":
          return { bg: "bg-purple-100/80", text: "text-purple-700", border: "border-purple-200" };
        case "Customer Facing":
          return { bg: "bg-indigo-100/80", text: "text-indigo-700", border: "border-indigo-200" };
        default:
          return { bg: "bg-gray-100/80", text: "text-gray-700", border: "border-gray-200" };
      }
    };

    const NoteIcon = getNoteTypeIcon(note.note_type);
    const noteTypeColors = getNoteTypeColor(note.note_type);
    const rotations = ["-rotate-1", "rotate-1", "rotate-0", "-rotate-1", "rotate-1"];
    const randomRotation = rotations[index % rotations.length];

    return (
      <div
        key={note.id}
        className={`group relative bg-white border border-gray-200 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:scale-105 hover:z-10 ${randomRotation}`}
        style={{
          background: "linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)",
          boxShadow: "0 10px 25px rgba(0,0,0,0.1), 0 4px 10px rgba(0,0,0,0.05)",
          transformOrigin: "center center",
        }}
        onMouseEnter={() => setHoveredNote(note.id)}
        onMouseLeave={() => setHoveredNote(null)}
      >
        <div className="absolute top-0 left-0 right-0 h-2 rounded-t-2xl bg-gradient-to-r from-indigo-400 to-purple-400"></div>
        <div className="absolute top-4 right-4 w-3 h-3 bg-gray-100 rounded-full opacity-60"></div>
        <div className="absolute top-6 right-6 w-2 h-2 bg-gray-200 rounded-full opacity-40"></div>
        <div
          className={`absolute top-4 right-12 px-3 py-1 text-xs font-semibold rounded-full backdrop-blur-sm ${noteTypeColors.bg} ${noteTypeColors.text} border ${noteTypeColors.border}`}
        >
          {note.note_type}
        </div>
        <div className="absolute top-4 left-4 flex gap-2">
          {note.is_pinned && (
            <div className="p-1 bg-yellow-100 rounded-full">
              <Pin className="h-3 w-3 text-yellow-600" />
            </div>
          )}
          {note.is_private && (
            <div className="p-1 bg-red-100 rounded-full">
              <Lock className="h-3 w-3 text-red-600" />
            </div>
          )}
        </div>
        <div className="relative px-6 pt-8 pb-4">
          <div className="flex items-start gap-4">
            <div className="relative p-3 rounded-xl shadow-md bg-gradient-to-br from-indigo-50 to-purple-50 border border-indigo-200">
              <NoteIcon className="h-6 w-6 text-indigo-600" />
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="text-xl font-bold text-gray-900 mb-3 leading-tight tracking-tight line-clamp-2">
                {note.title}
              </h3>
              <div className="inline-flex items-center gap-2 px-3 py-1 bg-gray-100 rounded-full text-xs font-medium text-gray-600">
                <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                {note.changeLabel}
              </div>
            </div>
          </div>
        </div>
        <div className="relative px-6 pb-6">
          <p className="mb-6 text-gray-600 text-sm leading-relaxed font-medium line-clamp-3">
            {note.content || "No content available"}
          </p>
          {note.entity_name && (
            <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-xl border border-gray-100 mb-4">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg flex items-center justify-center">
                <UserCheck className="w-4 h-4 text-blue-600" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-1">Entity</p>
                <p className="text-sm font-semibold text-gray-900 truncate">{note.entity_name}</p>
              </div>
            </div>
          )}
          <div
            className={`flex justify-end gap-3 transition-opacity duration-300 ${
              hoveredNote === note.id ? "opacity-100" : "opacity-0"
            }`}
          >
            <button
              onClick={() => handleViewNote(note)}
              className="group flex items-center gap-2 px-4 py-2 bg-blue-50 hover:bg-blue-100 text-blue-600 rounded-xl border border-blue-200 hover:border-blue-300 transition-all duration-200 font-medium text-sm"
            >
              <Eye className="h-4 w-4 group-hover:scale-110 transition-transform duration-200" />
              <span>View</span>
            </button>
          </div>
        </div>
      </div>
    );
  };

  // Handle case where user is not logged in
  if (!userId) {
    return (
      <Screen>
        <div className="min-h-screen bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-500 text-white">
          <div className="container mx-auto px-6 py-12 text-center">
            <div className="relative bg-white/10 backdrop-blur-xl rounded-3xl p-8 max-w-lg mx-auto border border-white/20 shadow-2xl">
              <Activity className="h-12 w-12 text-white mx-auto mb-6" />
              <h3 className="text-2xl font-bold text-white mb-3">Authentication Required</h3>
              <p className="text-indigo-100 text-base">
                Please log in to view notes created by you.
              </p>
            </div>
          </div>
        </div>
      </Screen>
    );
  }

  return (
    <Screen>
      <div className="relative overflow-hidden bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-500 text-white">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-indigo-500/20 to-purple-500/20"></div>
        <div className="absolute top-8 left-8 w-16 h-16 bg-white/10 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute top-20 right-16 w-12 h-12 bg-white/10 rounded-full blur-xl animate-pulse delay-1000"></div>
        <div className="absolute bottom-16 left-1/4 w-20 h-20 bg-white/10 rounded-full blur-xl animate-pulse delay-500"></div>
        <div className="relative container mx-auto px-6 py-12">
          <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
            <div className="space-y-3">
              <div className="flex items-center gap-3 mb-2">
                <div className="p-2 bg-white/20 rounded-xl backdrop-blur-sm">
                  <Activity className="h-6 w-6 text-white" />
                </div>
                <div className="h-1 w-12 bg-gradient-to-r from-white/40 to-transparent rounded-full"></div>
              </div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-white via-purple-100 to-indigo-100 bg-clip-text text-transparent">
                My Notes Dashboard
              </h1>
              <p className="text-lg text-indigo-100 max-w-xl">
                View and manage notes you created across customer, sales, and prospect categories
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-3">
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl px-4 py-3 border border-white/20">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-white/20 rounded-lg">
                    <Filter className="h-4 w-4 text-white" />
                  </div>
                  <div className="min-w-0">
                    <p className="text-white/90 text-xs font-medium mb-1">Filter by Type</p>
                    <Select value={selectedNoteType} onValueChange={setSelectedNoteType}>
                      <SelectTrigger className="w-40 h-8 bg-white/20 border-white/30 text-white text-sm">
                        <SelectValue placeholder="All Types" />
                      </SelectTrigger>
                      <SelectContent>
                        {NOTE_TYPES.map((type) => {
                          const IconComponent = type.icon;
                          return (
                            <SelectItem key={type.value} value={type.value}>
                              <div className="flex items-center gap-2">
                                <IconComponent className="h-4 w-4" />
                                <span>{type.label}</span>
                              </div>
                            </SelectItem>
                          );
                        })}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl px-6 py-4 border border-white/20">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-white/20 rounded-lg">
                    <BarChart3 className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <p className="text-white/90 text-sm font-medium">Total Notes</p>
                    <p className="text-2xl font-bold text-white">
                      {customerNotesData.length + salesNotesData.length + prospectsNotesData.length}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="container mx-auto px-6 -mt-6 relative z-10">
        <Tabs value={activeCategory} onValueChange={(value) => setActiveCategory(value as any)} className="w-full">
          <TabsList className="grid w-full grid-cols-3 mb-8 bg-white rounded-2xl shadow-lg p-2">
            <TabsTrigger
              value="Customer"
              className="flex items-center gap-2 data-[state=active]:bg-indigo-500 data-[state=active]:text-white rounded-xl py-3 px-6 font-semibold transition-all duration-200"
            >
              <UserCheck className="w-5 h-5" />
              Customer ({customerNotesData.length})
            </TabsTrigger>
            <TabsTrigger
              value="Lead File"
              className="flex items-center gap-2 data-[state=active]:bg-purple-500 data-[state=active]:text-white rounded-xl py-3 px-6 font-semibold transition-all duration-200"
            >
              <TrendingUp className="w-5 h-5" />
              Sales ({salesNotesData.length})
            </TabsTrigger>
            <TabsTrigger
              value="Prospect"
              className="flex items-center gap-2 data-[state=active]:bg-pink-500 data-[state=active]:text-white rounded-xl py-3 px-6 font-semibold transition-all duration-200"
            >
              <Target className="w-5 h-5" />
              Prospects ({prospectsNotesData.length})
            </TabsTrigger>
          </TabsList>
          <TabsContent value="Customer" className="space-y-6">
            <div className="bg-gradient-to-br from-white to-indigo-50 rounded-2xl shadow-lg p-6 mb-6 border border-indigo-100">
              <div className="flex items-center gap-4 mb-6">
                <div className="p-3 bg-gradient-to-br from-indigo-100 to-indigo-200 rounded-xl shadow-md">
                  <UserCheck className="w-6 h-6 text-indigo-600" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold bg-gradient-to-r from-indigo-700 to-purple-700 bg-clip-text text-transparent">
                    My Customer Notes
                  </h2>
                  <p className="text-gray-600">Notes you created for existing customers</p>
                </div>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-gradient-to-br from-indigo-50 to-indigo-100 rounded-xl p-4 text-center border border-indigo-200">
                  <div className="flex items-center justify-center mb-2">
                    <BarChart3 className="w-5 h-5 text-indigo-600 mr-2" />
                    <p className="text-2xl font-bold text-indigo-600">{customerStats.total}</p>
                  </div>
                  <p className="text-sm text-gray-600 font-medium">Total</p>
                </div>
                <div className="bg-gradient-to-br from-red-50 to-red-100 rounded-xl p-4 text-center border border-red-200">
                  <div className="flex items-center justify-center mb-2">
                    <Lock className="w-5 h-5 text-red-600 mr-2" />
                    <p className="text-2xl font-bold text-red-600">{customerStats.private}</p>
                  </div>
                  <p className="text-sm text-gray-600 font-medium">Private</p>
                </div>
                <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-xl p-4 text-center border border-yellow-200">
                  <div className="flex items-center justify-center mb-2">
                    <Pin className="w-5 h-5 text-yellow-600 mr-2" />
                    <p className="text-2xl font-bold text-yellow-600">{customerStats.pinned}</p>
                  </div>
                  <p className="text-sm text-gray-600 font-medium">Pinned</p>
                </div>
                <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-4 text-center border border-green-200">
                  <div className="flex items-center justify-center mb-2">
                    <Clock className="w-5 h-5 text-green-600 mr-2" />
                    <p className="text-2xl font-bold text-green-600">{customerStats.recent}</p>
                  </div>
                  <p className="text-sm text-gray-600 font-medium">Recent</p>
                </div>
              </div>
            </div>
            {selectedNoteType === "all" && (
              <div className="bg-white rounded-2xl shadow-lg p-6 mb-6 border border-gray-100">
                <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center gap-2">
                  <Filter className="w-5 h-5 text-indigo-600" />
                  Note Types Breakdown
                </h3>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
                  {NOTE_TYPES.slice(1).map((type) => {
                    const IconComponent = type.icon;
                    const getCount = (noteType: string) => {
                      switch (noteType) {
                        case "General":
                          return customerStats.byType.general;
                        case "Important":
                          return customerStats.byType.important;
                        case "Reminder":
                          return customerStats.byType.reminder;
                        case "Follow-up":
                          return customerStats.byType.followUp;
                        case "Internal":
                          return customerStats.byType.internal;
                        case "Customer Facing":
                          return customerStats.byType.customerFacing;
                        default:
                          return 0;
                      }
                    };
                    const count = getCount(type.value);
                    return (
                      <div
                        key={type.value}
                        className="text-center p-3 bg-gray-50 rounded-xl border border-gray-200 hover:shadow-md transition-shadow"
                      >
                        <div className="flex items-center justify-center mb-2">
                          <div
                            className={`p-2 rounded-lg ${
                              type.color === "blue"
                                ? "bg-blue-100"
                                : type.color === "red"
                                ? "bg-red-100"
                                : type.color === "yellow"
                                ? "bg-yellow-100"
                                : type.color === "green"
                                ? "bg-green-100"
                                : type.color === "purple"
                                ? "bg-purple-100"
                                : "bg-indigo-100"
                            }`}
                          >
                            <IconComponent
                              className={`w-4 h-4 ${
                                type.color === "blue"
                                  ? "text-blue-600"
                                  : type.color === "red"
                                  ? "text-red-600"
                                  : type.color === "yellow"
                                  ? "text-yellow-600"
                                  : type.color === "green"
                                  ? "text-green-600"
                                  : type.color === "purple"
                                  ? "text-purple-600"
                                  : "text-indigo-600"
                              }`}
                            />
                          </div>
                        </div>
                        <p className="text-lg font-bold text-gray-900">{count}</p>
                        <p className="text-xs text-gray-600 font-medium">{type.label}</p>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
            {customerLoading ? (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-8">
                {[...Array(6)].map((_, i) => (
                  <div
                    key={i}
                    className="animate-pulse h-64 bg-gradient-to-br from-gray-50 to-gray-100 border-0 shadow-lg rounded-2xl"
                  >
                    <div className="p-6">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="h-10 w-10 bg-gradient-to-r from-indigo-300 to-purple-300 rounded-xl"></div>
                        <div className="flex-1">
                          <div className="h-4 bg-gradient-to-r from-gray-300 to-gray-400 rounded w-3/4 mb-2"></div>
                          <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-1/2"></div>
                        </div>
                      </div>
                      <div className="space-y-3">
                        <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-full"></div>
                        <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-5/6"></div>
                        <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-4/6"></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : customerNotesData.length === 0 ? (
              <div className="text-center py-16 bg-gradient-to-br from-gray-50 via-indigo-50 to-purple-50 rounded-2xl border border-gray-200 shadow-inner mb-8">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-indigo-400 to-purple-400 rounded-full blur-3xl opacity-20 scale-150"></div>
                  <div className="relative bg-gradient-to-r from-indigo-500 to-purple-500 p-4 rounded-2xl w-20 h-20 mx-auto mb-6 shadow-lg">
                    <UserCheck className="h-12 w-12 text-white" />
                  </div>
                </div>
                <h3 className="text-2xl font-bold bg-gradient-to-r from-gray-700 to-gray-900 bg-clip-text text-transparent mb-2">
                  No customer notes yet
                </h3>
                <p className="text-gray-600 mb-8 max-w-md mx-auto">
                  You haven’t created any customer notes yet.
                </p>
              </div>
            ) : (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {customerNotesData.map((note, index) => renderNoteCard(note, index))}
              </div>
            )}
          </TabsContent>
          <TabsContent value="Lead File" className="space-y-6">
            <div className="bg-gradient-to-br from-white to-purple-50 rounded-2xl shadow-lg p-6 mb-6 border border-purple-100">
              <div className="flex items-center gap-4 mb-6">
                <div className="p-3 bg-gradient-to-br from-purple-100 to-purple-200 rounded-xl shadow-md">
                  <TrendingUp className="w-6 h-6 text-purple-600" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold bg-gradient-to-r from-purple-700 to-pink-700 bg-clip-text text-transparent">
                    My Sales Notes
                  </h2>
                  <p className="text-gray-600">Notes you created for lead files and sales activities</p>
                </div>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-4 text-center border border-purple-200">
                  <div className="flex items-center justify-center mb-2">
                    <BarChart3 className="w-5 h-5 text-purple-600 mr-2" />
                    <p className="text-2xl font-bold text-purple-600">{salesStats.total}</p>
                  </div>
                  <p className="text-sm text-gray-600 font-medium">Total</p>
                </div>
                <div className="bg-gradient-to-br from-red-50 to-red-100 rounded-xl p-4 text-center border border-red-200">
                  <div className="flex items-center justify-center mb-2">
                    <Lock className="w-5 h-5 text-red-600 mr-2" />
                    <p className="text-2xl font-bold text-red-600">{salesStats.private}</p>
                  </div>
                  <p className="text-sm text-gray-600 font-medium">Private</p>
                </div>
                <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-xl p-4 text-center border border-yellow-200">
                  <div className="flex items-center justify-center mb-2">
                    <Pin className="w-5 h-5 text-yellow-600 mr-2" />
                    <p className="text-2xl font-bold text-yellow-600">{salesStats.pinned}</p>
                  </div>
                  <p className="text-sm text-gray-600 font-medium">Pinned</p>
                </div>
                <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-4 text-center border border-green-200">
                  <div className="flex items-center justify-center mb-2">
                    <Clock className="w-5 h-5 text-green-600 mr-2" />
                    <p className="text-2xl font-bold text-green-600">{salesStats.recent}</p>
                  </div>
                  <p className="text-sm text-gray-600 font-medium">Recent</p>
                </div>
              </div>
            </div>
            {salesLoading ? (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-8">
                {[...Array(6)].map((_, i) => (
                  <div
                    key={i}
                    className="animate-pulse h-64 bg-gradient-to-br from-gray-50 to-gray-100 border-0 shadow-lg rounded-2xl"
                  >
                    <div className="p-6">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="h-10 w-10 bg-gradient-to-r from-purple-300 to-pink-300 rounded-xl"></div>
                        <div className="flex-1">
                          <div className="h-4 bg-gradient-to-r from-gray-300 to-gray-400 rounded w-3/4 mb-2"></div>
                          <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-1/2"></div>
                        </div>
                      </div>
                      <div className="space-y-3">
                        <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-full"></div>
                        <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-5/6"></div>
                        <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-4/6"></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : salesNotesData.length === 0 ? (
              <div className="text-center py-16 bg-gradient-to-br from-gray-50 via-purple-50 to-pink-50 rounded-2xl border border-gray-200 shadow-inner mb-8">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full blur-3xl opacity-20 scale-150"></div>
                  <div className="relative bg-gradient-to-r from-purple-500 to-pink-500 p-4 rounded-2xl w-20 h-20 mx-auto mb-6 shadow-lg">
                    <TrendingUp className="h-12 w-12 text-white" />
                  </div>
                </div>
                <h3 className="text-2xl font-bold bg-gradient-to-r from-gray-700 to-gray-900 bg-clip-text text-transparent mb-2">
                  No sales notes yet
                </h3>
                <p className="text-gray-600 mb-8 max-w-md mx-auto">
                  You haven’t created any sales notes yet.
                </p>
              </div>
            ) : (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {salesNotesData.map((note, index) => renderNoteCard(note, index))}
              </div>
            )}
          </TabsContent>
          <TabsContent value="Prospect" className="space-y-6">
            <div className="bg-gradient-to-br from-white to-pink-50 rounded-2xl shadow-lg p-6 mb-6 border border-pink-100">
              <div className="flex items-center gap-4 mb-6">
                <div className="p-3 bg-gradient-to-br from-pink-100 to-pink-200 rounded-xl shadow-md">
                  <Target className="w-6 h-6 text-pink-600" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold bg-gradient-to-r from-pink-700 to-red-700 bg-clip-text text-transparent">
                    My Prospect Notes
                  </h2>
                  <p className="text-gray-600">Notes you created for potential customers and prospects</p>
                </div>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-gradient-to-br from-pink-50 to-pink-100 rounded-xl p-4 text-center border border-pink-200">
                  <div className="flex items-center justify-center mb-2">
                    <BarChart3 className="w-5 h-5 text-pink-600 mr-2" />
                    <p className="text-2xl font-bold text-pink-600">{prospectsStats.total}</p>
                  </div>
                  <p className="text-sm text-gray-600 font-medium">Total</p>
                </div>
                <div className="bg-gradient-to-br from-red-50 to-red-100 rounded-xl p-4 text-center border border-red-200">
                  <div className="flex items-center justify-center mb-2">
                    <Lock className="w-5 h-5 text-red-600 mr-2" />
                    <p className="text-2xl font-bold text-red-600">{prospectsStats.private}</p>
                  </div>
                  <p className="text-sm text-gray-600 font-medium">Private</p>
                </div>
                <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-xl p-4 text-center border border-yellow-200">
                  <div className="flex items-center justify-center mb-2">
                    <Pin className="w-5 h-5 text-yellow-600 mr-2" />
                    <p className="text-2xl font-bold text-yellow-600">{prospectsStats.pinned}</p>
                  </div>
                  <p className="text-sm text-gray-600 font-medium">Pinned</p>
                </div>
                <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-4 text-center border border-green-200">
                  <div className="flex items-center justify-center mb-2">
                    <Clock className="w-5 h-5 text-green-600 mr-2" />
                    <p className="text-2xl font-bold text-green-600">{prospectsStats.recent}</p>
                  </div>
                  <p className="text-sm text-gray-600 font-medium">Recent</p>
                </div>
              </div>
            </div>
            {prospectsLoading ? (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-8">
                {[...Array(6)].map((_, i) => (
                  <div
                    key={i}
                    className="animate-pulse h-64 bg-gradient-to-br from-gray-50 to-gray-100 border-0 shadow-lg rounded-2xl"
                  >
                    <div className="p-6">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="h-10 w-10 bg-gradient-to-r from-pink-300 to-red-300 rounded-xl"></div>
                        <div className="flex-1">
                          <div className="h-4 bg-gradient-to-r from-gray-300 to-gray-400 rounded w-3/4 mb-2"></div>
                          <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-1/2"></div>
                        </div>
                      </div>
                      <div className="space-y-3">
                        <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-full"></div>
                        <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-5/6"></div>
                        <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-300 rounded w-4/6"></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : prospectsNotesData.length === 0 ? (
              <div className="text-center py-16 bg-gradient-to-br from-gray-50 via-pink-50 to-red-50 rounded-2xl border border-gray-200 shadow-inner mb-8">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-pink-400 to-red-400 rounded-full blur-3xl opacity-20 scale-150"></div>
                  <div className="relative bg-gradient-to-r from-pink-500 to-red-500 p-4 rounded-2xl w-20 h-20 mx-auto mb-6 shadow-lg">
                    <Target className="h-12 w-12 text-white" />
                  </div>
                </div>
                <h3 className="text-2xl font-bold bg-gradient-to-r from-gray-700 to-gray-900 bg-clip-text text-transparent mb-2">
                  No prospect notes yet
                </h3>
                <p className="text-gray-600 mb-8 max-w-md mx-auto">
                  You haven’t created any prospect notes yet.
                </p>
              </div>
            ) : (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {prospectsNotesData.map((note, index) => renderNoteCard(note, index))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
      <ViewNoteModal
        isOpen={isViewModalOpen}
        onOpenChange={setIsViewModalOpen}
        noteData={
          selectedNote
            ? {
                id: parseInt(selectedNote.id),
                title: selectedNote.title,
                value: selectedNote.note_type,
                icon: FileText,
                change: selectedNote.change,
                changeLabel: selectedNote.changeLabel,
                description: selectedNote.content,
                isPinned: selectedNote.is_pinned,
                isPrivate: selectedNote.is_private,
                attachment: null,
                createdAt: selectedNote.created_at,
                updatedAt: selectedNote.updated_at,
                author: selectedNote.entity_name || "Unknown",
                category: selectedNote.entity_type,
              }
            : null
        }
        onSubmit={handleNoteUpdate}
      />
    </Screen>
  );
}

export default ViewNotes;