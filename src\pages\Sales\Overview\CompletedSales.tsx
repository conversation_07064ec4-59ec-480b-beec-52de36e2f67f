import React, { useState } from 'react'
import SalesTable from './SalesTable';
import { useGetCompletedSalesQuery } from '@/redux/slices/sales';
import SpinnerTemp from '@/components/custom/spinners/SpinnerTemp';
import { CircleAlert } from 'lucide-react';


const CompletedSales = () => {
    // compeleted sales
    const [completedSalesItemsPerPage, setCompletedSalesItemsPerPage] = useState(20)
    const [completedSalesCurrentPage, setCompletedSalesCurrentPage] = useState(1)
    const [universalSearchValue, setuniversalSearchValue] = useState('')

    const { data: completedSalesData, isLoading, isFetching } = useGetCompletedSalesQuery({ page_size: completedSalesItemsPerPage, page: completedSalesCurrentPage, search: universalSearchValue });

    // Show loader when initially loading or when tab is changing
    const showLoader = isLoading || isFetching;

    const totalItems = completedSalesData?.data?.total_data || 0;

    return (
        <>
            <div className='relative'>
                {showLoader ? (
                    <div className="absolute inset-0 z-10 flex pt-16 justify-center bg-white bg-opacity-70">
                        <SpinnerTemp type="spinner-double" size="md" />
                    </div>
                ) : (!totalItems || totalItems === 0) ? (
                    <div className='flex flex-col gap-2 justify-center items-center h-64'>
                        <CircleAlert size={45} className='text-muted-foreground' />
                        <p className="text-center text-xs">No data available.</p>
                        <p className="text-center text-xs">You may not have permission to view this section.</p>
                    </div>
                ) : (
                    <SalesTable
                        data={completedSalesData}
                        itemsPerPage={completedSalesItemsPerPage}
                        setItemsPerPage={setCompletedSalesItemsPerPage}
                        currentPage={completedSalesCurrentPage}
                        setCurrentPage={setCompletedSalesCurrentPage}
                        SearchComponent={<SearchComponent universalSearchValue={universalSearchValue} setuniversalSearchValue={setuniversalSearchValue} />}
                    />
                )}
            </div>
        </>
    )
}

export default CompletedSales

interface SearchComponentProps {
    universalSearchValue: string,
    setuniversalSearchValue: React.Dispatch<React.SetStateAction<string>>
}

function SearchComponent({ universalSearchValue, setuniversalSearchValue }: SearchComponentProps) {
    return <input
        value={universalSearchValue}
        onChange={e => setuniversalSearchValue(e.target.value)}
        className="px-3 py-2 ml-1 w-full border rounded text-sm focus:outline-none bg-transparent"
        placeholder="Search sales details..."
    />
}





// import React, { useState } from 'react'
// import SalesTable from './SalesTable';
// import { useGetCompletedSalesQuery } from '@/redux/slices/sales';
// import SpinnerTemp from '@/components/custom/spinners/SpinnerTemp';
// import { useSalesPermissions } from '@/hooks/useSalesPermissions';


// const CompletedSales = () => {
//     // Get sales permissions
//     const { 
//         hasAnySalesAccess,
//         apiParams,
//         userDetails
//     } = useSalesPermissions();

//     // compeleted sales
//     const [completedSalesItemsPerPage, setCompletedSalesItemsPerPage] = useState(20)
//     const [completedSalesCurrentPage, setCompletedSalesCurrentPage] = useState(1)
//     const [universalSearchValue, setuniversalSearchValue] = useState('')

//     // Get API parameters based on permissions
//     const getApiParams = () => {
//         // Use the simplified permission-based API parameters
//         return {
//             ...apiParams,
//             page_size: completedSalesItemsPerPage,
//             page: completedSalesCurrentPage,
//             search: universalSearchValue || undefined
//         };
//     };

//     const { data: completedSalesData, isLoading, isFetching } = useGetCompletedSalesQuery(
//         getApiParams(),
//         {
//             skip: !hasAnySalesAccess // Skip the query if user has no access
//         }
//     );

//     // Show loader when initially loading or when tab is changing
//     const showLoader = isLoading || isFetching;

//     return (
//         <>
//             {hasAnySalesAccess ? (
//                 <div className='relative'>
//                     {showLoader && (
//                         <div className="absolute inset-0 z-10 flex pt-16 justify-center bg-white bg-opacity-70">
//                             <SpinnerTemp type="spinner-double" size="md" />
//                         </div>
//                     )}
//                     <SalesTable
//                         data={completedSalesData}
//                         itemsPerPage={completedSalesItemsPerPage}
//                         setItemsPerPage={setCompletedSalesItemsPerPage}
//                         currentPage={completedSalesCurrentPage}
//                         setCurrentPage={setCompletedSalesCurrentPage}
//                         SearchComponent={<SearchComponent universalSearchValue={universalSearchValue} setuniversalSearchValue={setuniversalSearchValue} />}
//                     />
//                 </div>
//             ) : (
//                 <div className="text-center py-8 text-gray-500">
//                     <p>Contact your administrator to request sales viewing permissions.</p>
//                 </div>
//             )}
//         </>
//     )
// }

// export default CompletedSales

// interface SearchComponentProps {
//     universalSearchValue: string,
//     setuniversalSearchValue: React.Dispatch<React.SetStateAction<string>>
// }

// function SearchComponent({ universalSearchValue, setuniversalSearchValue }: SearchComponentProps) {
//     return <input
//         value={universalSearchValue}
//         onChange={e => setuniversalSearchValue(e.target.value)}
//         className="px-3 py-2 ml-1 w-full border rounded text-sm focus:outline-none bg-transparent"
//         placeholder="Search sales details..."
//     />
// }