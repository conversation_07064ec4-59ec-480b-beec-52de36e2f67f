import { useState } from "react";
import { useParams } from "react-router-dom";
import { useGetUserDetailsQuery } from "@/redux/slices/user";
import { Screen } from "@/app-components/layout/screen";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import EditProfileForm from "@/components/profile/EditProfileForm";
import ChangePasswordForm from "@/components/profile/ChangePasswordForm";
import NotificationSettings from "@/components/profile/NotificationsSettings";
import UserPermissions from "@/components/profile/UserPermissions";
import UserGroups from "@/components/profile/UserGroups";
import { ChevronDown } from "lucide-react";

export interface User {
  id: number;
  employee_no: string;
  email: string | null;
  fullnames: string | null;
  department: string | null;
  designation: string | null;
  status: string | null;
  team: string | null;
  region: string | null;
  manager: string | null;
  phone_number: string | null;
  gender: string | null;
  created_date: string | null;
  category: string | null;
}

const Profile = () => {
  const { id } = useParams<{ id: string }>(); // Extract `id` from the route

  const {
    data: userprofile,
    isLoading,
    isError,
    error,
  } = useGetUserDetailsQuery(id || "9");

  const [activeTab, setActiveTab] = useState("edit-profile");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  console.log("userprofile:", userprofile);

  if (isLoading) {
    return (
      <Screen>
        <div>Loading user details...</div>
      </Screen>
    );
  }

  if (isError || !userprofile || userprofile.length === 0) {
    return (
      <Screen>
        <div>
          Error loading user details: {error?.message || "Unknown error"}
        </div>
      </Screen>
    );
  }

  // Handle different data structures
  const user = Array.isArray(userprofile) ? userprofile[0] : userprofile;

  const tabs = [
    { value: "edit-profile", label: "Edit Profile" },
    { value: "change-password", label: "Change Password" },
    { value: "notification-settings", label: "Notification Settings" },
    { value: "user-permissions", label: "User Permissions" },
    { value: "user-groups", label: "User Groups" },
  ];

  const activeTabLabel = tabs.find((tab) => tab.value === activeTab)?.label;

  return (
    <Screen>
      <div className="min-h-screen bg-background p-6">
        <div className="mx-auto max-w-7xl">
          <div className="grid grid-cols-1 md:grid-cols-12 gap-6">
            {/* Profile Info Section */}
            <div className="md:col-span-4">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <div className="flex flex-col items-center">
                  <Avatar className="h-32 w-32 mb-4">
                    <AvatarImage src="https://randomuser.me/api/portraits/men/1.jpg" />
                    <AvatarFallback>
                      {user?.fullnames
                        ?.split(" ")
                        .map((n) => n[0])
                        .join("") || "N/A"}
                    </AvatarFallback>
                  </Avatar>
                  <h1 className="text-2xl font-bold">
                    {userprofile?.fullnames || "Unknown User"}
                  </h1>
                  <p className="text-gray-600 mb-6">
                    {userprofile?.designation || "No Designation"}
                  </p>

                  <div className="w-full space-y-4">
                    <div className="space-y-1">
                      <p className="text-sm text-gray-500">Full Name</p>
                      <p className="font-medium">
                        {userprofile?.fullnames || "N/A"}
                      </p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm text-gray-500">Employee No</p>
                      <p className="font-medium">
                        {userprofile?.employee_no || "N/A"}
                      </p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm text-gray-500">Email</p>
                      <p className="font-medium">
                        {userprofile?.email || "N/A"}
                      </p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm text-gray-500">Phone Number</p>
                      <p className="font-medium">
                        {userprofile?.phone_number || "N/A"}
                      </p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm text-gray-500">Department</p>
                      <p className="font-medium">
                        {userprofile?.department || "N/A"}
                      </p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm text-gray-500">Designation</p>
                      <p className="font-medium">
                        {userprofile?.designation || "N/A"}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Tabs Section */}
            <div className="md:col-span-8">
              <Tabs
                value={activeTab}
                onValueChange={setActiveTab}
                className="w-full"
              >
                <div className="hidden md:block">
                  <TabsList className="w-full justify-start bg-secondary">
                    {tabs.map((tab) => (
                      <TabsTrigger key={tab.value} value={tab.value}>
                        {tab.label}
                      </TabsTrigger>
                    ))}
                  </TabsList>
                </div>

                {/* Mobile Dropdown */}
                <div className="md:hidden relative mb-4">
                  <button
                    className="flex items-center justify-between w-full p-3 bg-white dark:bg-gray-800 rounded-md shadow-sm text-left"
                    onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                  >
                    <span className="font-medium">{activeTabLabel}</span>
                    <ChevronDown
                      className={`transition-transform ${
                        isDropdownOpen ? "rotate-180" : ""
                      }`}
                      size={16}
                    />
                  </button>

                  {isDropdownOpen && (
                    <div className="absolute z-10 mt-1 w-full bg-white dark:bg-gray-800 dark:text-white border border-gray-200 rounded-md shadow-lg py-1">
                      {tabs.map((tab) => (
                        <button
                          key={tab.value}
                          className={`block w-full text-left px-4 py-2 text-sm ${
                            activeTab === tab.value
                              ? "bg-gray-100 text-gray-900 dark:bg-gray-800 dark:text-white"
                              : "text-gray-700 hover:bg-gray-50 dark:bg-gray-800 dark:text-white"
                          }`}
                          onClick={() => {
                            setActiveTab(tab.value);
                            setIsDropdownOpen(false);
                          }}
                        >
                          {tab.label}
                        </button>
                      ))}
                    </div>
                  )}
                </div>

                <TabsContent value="edit-profile">
                  <EditProfileForm  />
                </TabsContent>

                <TabsContent value="change-password">
                  <ChangePasswordForm /> {/* Pass userId as a prop */}
                </TabsContent>

                <TabsContent value="notification-settings">
                  <NotificationSettings  />
                </TabsContent>

                <TabsContent value="user-permissions">
                  <UserPermissions /> {/* Pass userId as a prop */}
                </TabsContent>

                <TabsContent value="user-groups">
                  <UserGroups  />
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </div>
      </div>
    </Screen>
  );
};

export default Profile;
