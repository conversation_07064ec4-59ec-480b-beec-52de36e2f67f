// components/custom/modals/MultiStepModal.tsx
import React, { forwardRef, useState, useEffect, ReactNode } from "react";
import { Button } from "@/components/ui/button";
import { ArrowLeft, ArrowRight, Check } from "lucide-react";
import BaseModal, { BaseModalProps } from "./BaseModal";
import { cn } from "@/lib/utils";

export interface Step {
  title: string;
  content: ReactNode;
  /** return true if this step is valid */
  validate?: () => boolean;
}

export interface MultiStepModalProps extends BaseModalProps {
  steps: Step[];
  currentStep?: number;
  onStepChange?: (step: number) => void;
  onComplete?: () => void;
}

const MultiStepModal = forwardRef<HTMLDivElement, MultiStepModalProps>(
  ({ steps, currentStep: externalStep, onStepChange, onComplete, ...props }, ref) => {
    const [currentStep, setCurrentStep] = useState(externalStep || 0);

    useEffect(() => {
      if (externalStep !== undefined) {
        setCurrentStep(externalStep);
      }
    }, [externalStep]);

    const isValid = steps[currentStep]?.validate?.() ?? true;

    const changeStep = (step: number) => {
      setCurrentStep(step);
      onStepChange?.(step);
    };

    const handleBack = () => {
      if (currentStep > 0) changeStep(currentStep - 1);
    };

    const handleNext = () => {
      if (currentStep < steps.length - 1) {
        changeStep(currentStep + 1);
      } else {
        onComplete?.();
        props.onOpenChange?.(false);
      }
    };

    const footer = (
      <>
        <div className="flex-1">
          {currentStep > 0 && (
            <Button variant="outline" size="sm" onClick={handleBack}>
              <ArrowLeft className="h-4 w-4 mr-2" /> Back
            </Button>
          )}
        </div>
        <Button onClick={handleNext} disabled={!isValid}>
          {currentStep < steps.length - 1 ? (
            <>
              Next <ArrowRight className="h-4 w-4 ml-2" />
            </>
          ) : (
            <>
              Complete <Check className="h-4 w-4 ml-2" />
            </>
          )}
        </Button>
      </>
    );

    return (
      <BaseModal
        ref={ref}
        title={steps[currentStep]?.title || props.title}
        footer={footer}
        footerClassName="flex justify-between w-full"
        {...props}
      >
        {/* step indicator */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            {steps.map((step, i) => (
              <React.Fragment key={i}>
                <div className="flex flex-col items-center">
                  <div
                    className={cn(
                      "flex items-center justify-center w-8 h-8 rounded-full text-xs font-medium border transition-colors",
                      i === currentStep
                        ? "border-primary bg-primary text-primary-foreground"
                        : i < currentStep
                        ? "border-primary/30 bg-primary/10 text-primary"
                        : "border-gray-300 text-gray-500 dark:border-gray-600"
                    )}
                  >
                    {i + 1}
                  </div>
                  <div className="text-xs mt-1 text-center max-w-[60px] truncate">
                    {step.title}
                  </div>
                </div>
                {i < steps.length - 1 && (
                  <div
                    className={cn(
                      "flex-1 h-px mx-1",
                      i < currentStep ? "bg-primary" : "bg-gray-300 dark:bg-gray-600"
                    )}
                  />
                )}
              </React.Fragment>
            ))}
          </div>
        </div>

        {/* step content */}
        <div>{steps[currentStep]?.content}</div>
      </BaseModal>
    );
  }
);

MultiStepModal.displayName = "MultiStepModal";
export default MultiStepModal;
