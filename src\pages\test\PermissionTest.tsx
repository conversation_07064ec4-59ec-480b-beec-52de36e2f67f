import React from "react";
import { Screen } from "@/app-components/layout/screen";
import { useSidebarPermissions } from "@/hooks/useSidebarPermissions";
import { useAuthHook } from "@/utils/useAuthHook";
import { PERMISSION_DESCRIPTIONS } from "@/utils/permissionChecker";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, XCircle, User, Building, Shield } from "lucide-react";

/**
 * Test page to verify permission-based sidebar functionality
 * This page displays current user's permissions and sidebar access status
 */
const PermissionTest: React.FC = () => {
  const { user_details } = useAuthHook();
  const {
    hasPermission,
    hasSidebarAccess,
    accessibleSections,
    userPermissionCodes,
    isLoading,
    userDepartmentId,
  } = useSidebarPermissions();

  const sidebarSections = [
    { key: "MAIN", name: "<PERSON>", code: 111 },
    { key: "PERFORMANCE", name: "Performance", code: 112 },
    { key: "TEAMS", name: "Teams", code: 113 },
    { key: "REPORTS", name: "Reports", code: 114 },
    { key: "ANALYTICS", name: "Analytics", code: 115 },
    { key: "SERVICES", name: "Services", code: 116 },
    { key: "ADMIN", name: "Admin", code: 117 },
  ] as const;

  if (isLoading) {
    return (
      <Screen>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading permissions...</p>
          </div>
        </div>
      </Screen>
    );
  }

  return (
    <Screen>
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Shield className="h-6 w-6 text-blue-600" />
          <h1 className="text-2xl font-bold text-gray-900">Permission Test Page</h1>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* User Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                User Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div>
                <span className="font-medium">Name:</span>{" "}
                {user_details?.fullnames || "Unknown"}
              </div>
              <div>
                <span className="font-medium">Employee No:</span>{" "}
                {user_details?.employee_no || "Unknown"}
              </div>
              <div>
                <span className="font-medium">Email:</span>{" "}
                {user_details?.email || "Unknown"}
              </div>
            </CardContent>
          </Card>

          {/* Department Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                Department Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div>
                <span className="font-medium">Department:</span>{" "}
                {user_details?.department || "Unknown"}
              </div>
              <div>
                <span className="font-medium">Department ID:</span>{" "}
                {userDepartmentId || "Not found"}
              </div>
              <div>
                <span className="font-medium">Office:</span>{" "}
                {user_details?.office || "Unknown"}
              </div>
            </CardContent>
          </Card>

          {/* Permission Codes */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Permission Codes
              </CardTitle>
            </CardHeader>
            <CardContent>
              {userPermissionCodes.length > 0 ? (
                <div className="flex flex-wrap gap-1">
                  {userPermissionCodes.map(code => (
                    <Badge key={code} variant="secondary" className="text-xs">
                      {code}
                    </Badge>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-sm">No permissions found</p>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar Access Status */}
        <Card>
          <CardHeader>
            <CardTitle>Sidebar Access Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {sidebarSections.map(section => {
                const hasAccess = hasSidebarAccess(section.key as any);
                return (
                  <div
                    key={section.key}
                    className={`flex items-center justify-between p-3 rounded-lg border ${
                      hasAccess
                        ? "bg-green-50 border-green-200"
                        : "bg-red-50 border-red-200"
                    }`}
                  >
                    <div>
                      <div className="font-medium text-sm">{section.name}</div>
                      <div className="text-xs text-gray-600">Code: {section.code}</div>
                    </div>
                    {hasAccess ? (
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-600" />
                    )}
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Accessible Sections Summary */}
        <Card>
          <CardHeader>
            <CardTitle>Accessible Sidebar Sections</CardTitle>
          </CardHeader>
          <CardContent>
            {accessibleSections.length > 0 ? (
              <div className="flex flex-wrap gap-2">
                {accessibleSections.map(section => (
                  <Badge key={section} variant="default">
                    {section}
                  </Badge>
                ))}
              </div>
            ) : (
              <p className="text-gray-500">No accessible sections found</p>
            )}
          </CardContent>
        </Card>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>How to Test</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            <p>1. Check your current permissions above</p>
            <p>2. Navigate to the sidebar and verify only accessible sections are shown</p>
            <p>3. Ask an admin to modify your department permissions (codes 111-117)</p>
            <p>4. Refresh the page to see the sidebar update</p>
            <p>5. The sidebar should only show sections you have permission to access</p>
          </CardContent>
        </Card>
      </div>
    </Screen>
  );
};

export default PermissionTest;
