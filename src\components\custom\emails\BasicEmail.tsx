import { Button } from "@/components/ui/button";

type Props = {
  title: string;
};

const BasicEmail = ({ title }: Props) => {
  return (
    <div className="shadow py-3 px-5 text-sm">
      <p className="font-bold text-lg text-accent">{title}</p>

      <div className="w-full border border-dotted my-2 ">
        <div className="w-full flex items-center justify-center p-9">
          <img
            src="https://www.optiven.co.ke/wp-content/uploads/2023/09/optiven-group-logo.png"
            alt="Email Template"
            className="w-[20%] h-auto"
          />
        </div>
        <div className="w-full flex items-center justify-center p-4">
          <h1 className="text-2xl font-bold text-accent">Welcome to Optiven</h1>
        </div>
        <div className="w-full flex flex-col items-start justify-center px-9">
          <p className="text-center">
            Lorem ipsum dolor sit amet consectetur adipisicing elit. Harum alias
            quidem suscipit iste obcaecati, sequi itaque architecto dolor
            voluptates laboriosam maxime consequatur fuga recusandae dignissimos
            omnis nostrum magnam est qui. Lorem ipsum dolor sit amet consectetur
            adipisicing elit. Harum alias quidem suscipit iste obcaecati, sequi
            itaque architecto dolor voluptates laboriosam maxime consequatur
            fuga recusandae dignissimos omnis nostrum magnam est qui.
          </p>
        </div>
        <div className="px-9 flex justify-center">
          <Button className="mt-4" variant="default">
            <span className="text-bold text-xl">Login</span>
          </Button>
        </div>
        <div className="px-9 my-3">
          <p className="text-xs text-muted-foreground italic text-center">
            This is an system auto-generated email. Please do not reply to this
            email.
          </p>
        </div>
        <div className="px-9 my-3">
          <p className="text-center ">
            Warm Regards, <br />
            Optiven Team <br />
          </p>
        </div>
        <div className="px-9 mt-3 bg-primary text-white p-3">
          <p className="text-xs  italic text-center">
            This email was sent to you by Optiven Group. <br />
            Copyright © {new Date().getFullYear()} Optiven Group. All rights
            reserved.
          </p>
        </div>
      </div>
    </div>
  );
};

export default BasicEmail;
