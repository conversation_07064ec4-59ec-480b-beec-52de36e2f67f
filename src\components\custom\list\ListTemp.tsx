import { cn } from "@/lib/utils";
import React from "react";
import { SecondaryBadge } from "../badges/badges";
import { PrimaryButton } from "../buttons/buttons";







type TableProps<T> = {
  data: T[];
  columns: {
    header: string;
    accessor: keyof T;
  }[];
  
};



 const ListTempTable = <T extends object>({ data, columns, className, headerClassName, border }: TableProps<T> & { className?: string; headerClassName?: string; border: boolean }): React.ReactElement => {
  const borderClass = border ? "border-[0.1px] border-slate-200" : "border-none";
  return (
    <div className={cn(" border-collapse rounded-lg primary bg-[#eee]/30 dark:bg-slate-800 ", className, borderClass )}>
        <table className={cn(" border-collapse  overflow-hidden bg-inherit rounded-lg w-full h-full primary ", borderClass)} >
          <tr className={cn(" bg-opacity-10  primary text-sm  bg-opacity-20 overflow-hidden bg-orange-200 ", headerClassName)}>
            {columns.map((col) => (
              <th key={String(col.accessor)} className=" text-left pl-2 py-2 overflow-hidden ">{col.header}</th>
            ))}
            {/* <th className="text-left pl-4">Assigned To</th>
            <th className="text-left pl-4">Due Date</th>
            <th className="text-left pl-4">Status</th>
            <th className="text-left pl-4 pr-2">Action</th> */}
          </tr>
          {data.map((row, i) => (
            <tr key={i} className={cn("primary text-sm border-[0.1px] border-slate-200 overflow-x-hidden ", borderClass)}>
              {columns.map((col) => (
                <td key={String(col.accessor)} className="pl-2 text-left py-2">{col.accessor === 'status' ? ( String(row[col.accessor]) === 'true' ? (
                  <SecondaryBadge className="bg-green-300 self-center margin-" rounded="full">complete</SecondaryBadge>
                ) : (
                  <SecondaryBadge className="bg-red-300" rounded="full">pending</SecondaryBadge>

                )): (String(row[col.accessor]))}</td>
              ))}
            {/* <td className="pl-2 text-left py-2">Hotel Management Syst...</td>
            <td className="text-left pl-4">Mickael Tominae</td>
            <td className="text-left pl-4">27 Mar 2024</td>
            <td className="text-left pl-4"><p className="px-3 rounded-xl pb-0.5  bg-green-200 text-green-600">active</p></td>
            <td className="text-center pl-4"><EllipsisVertical /></td> */}
          </tr>

          ))}
          
          {/* <tr className="primary text-sm border overflow-x-hidden">
            <td className="pl-2 text-left py-2 overflow-x-hidden">Hotel Management Syst...</td>
            <td className="text-left pl-4">John Kimotho</td>
            <td className="text-left pl-4">27 Mar 2024</td>
            <td className="text-left pl-4"><p className="px-3 rounded-xl pb-0.5  bg-green-200 text-green-600">active</p></td>
            <td className="text-center pl-4 "><EllipsisVertical /></td>
          </tr> */}
        </table>
        </div>

  )

}
type buttonProps = 
| {
  button?: true;
  buttonText: string;
  onclick: () => void;

}
| {
  button?: false;
  buttonText?: never;
  onclick?: never;
}




export const ListTemp = <T extends object>({
   heading, 
   data, columns , 
   title = false, border = false, 
   className, 
   button,
   buttonText,
   onclick 
   
  }: 

   {className?: string; 
    border?: boolean; 
    heading?: string; 
    title?: boolean ;
    
    

  } & TableProps<T> & buttonProps): React.ReactElement => {
  const borderClass = border ? "border-[0.1px] border-slate-200" : "border-none";

 switch (title) {
  case true:
    return (
      <div className={cn("flex flex-col rounded-lg  primary bg-[#eee]/25 dark:bg-slate-800 shadow-sm  overflow-hidden", className, borderClass)}>
        <div className="header  py-3 w-full px-3 border-b-[0.1px] border-slate-300 flex justify-between items-center">
          <h1 className="primary font-bold text-sm">{heading}</h1>
          {button && (
            <PrimaryButton
              size="sm"
              onClick={() => {
                onclick?.();
              }}
            >
              {buttonText}
            </PrimaryButton>
          )}
        </div>
        <div className="w-full p-3">
          <ListTempTable data={data} columns={columns} border={border}  />
        </div>
      </div>
    );

    case false:
      return (
        <ListTempTable data={data} columns={columns} border={border} />
      )
    
 }
 

};
