import { Screen } from "@/app-components/layout/screen";
import {
  OutlinedButton,
  PrimaryButton,
} from "@/components/custom/buttons/buttons";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { DataTable } from "@/components/custom/tables/Table1";
import { useGetDepartmentsQuery } from "@/redux/slices/user";
import { ColumnDef } from "@tanstack/react-table";
import { motion } from "framer-motion";
import { KeyRound } from "lucide-react";
import { useEffect, useState } from "react";
import DepartmentPermissionsModal from "../Departments/DepartmentPermissionsModal";

interface DepartmentTypes {
  dp_id: number;
  dp_name: string;
  dep_head_code?: string;
  dep_head_name?: string;
  inactive?: boolean;
}

const Index = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [teamPermissionsData, setTeamPermissionsData] =
    useState<DepartmentTypes | null>(null);

  const { data: teams, isLoading: loadingTeams } = useGetDepartmentsQuery({
    page: currentPage,
    page_size: itemsPerPage,
  });

  const [cols, setCols] = useState<any>([]);

  useEffect(() => {
    setCols(columns);
  }, [loadingTeams]);

  const columns: ColumnDef<DepartmentTypes>[] = [
    {
      accessorKey: "dp_name",
      header: "Department Name",
      cell: (info) => (info.getValue() as string) || "N/A",
      enableColumnFilter: false,
    },
    {
      accessorKey: "dep_head_name",
      header: "Department Head Name",
      cell: (info) => (info.getValue() as string) || "N/A",
      enableColumnFilter: false,
    },
    {
      accessorKey: "dep_head_code",
      header: "Department Head Code",
      cell: (info) => (info.getValue() as string) || "N/A",
      enableColumnFilter: false,
    },
    {
      id: "actions",
      header: "Action",
      cell: ({ row }) => {
        const department = row.original;
        return (
          <div className="flex space-x-2 justify-center">
            <PrimaryButton
              variant="outline"
              size="sm"
              onClick={() => setTeamPermissionsData(department)}
              className="bg-white text-blue-500 border-blue-300 hover:bg-blue-50 flex items-center space-x-1"
            >
              <span title="View Permissions">
                <KeyRound />
              </span>
            </PrimaryButton>
          </div>
        );
      },
      enableColumnFilter: false,
    },
  ];

  return (
    <Screen>
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-3xl font-bold text-gray-800">Departments</h1>
          {/* <OutlinedButton
            variant="primary"
            className="bg-white border-blue-600 text-blue-600 hover:bg-blue-50 font-semibold py-2 px-4 rounded-md transition-colors"
            onClick={() => setIsAddModalOpen(true)}
          >
            Add Team
          </OutlinedButton> */}
        </div>
        <div className="space-y-4">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-x-auto"
          >
            {loadingTeams ? (
              <div className="w-full flex items-center justify-center">
                <SpinnerTemp type="spinner-double" size="md" />
              </div>
            ) : !teams || teams?.length === 0 ? (
              <p className="px-2">No Departments found</p>
            ) : (
              <DataTable<DepartmentTypes>
                data={teams}
                columns={cols}
                enableToolbar={true}
                enableExportToExcel={true}
                enablePagination={true}
                enableColumnFilters={true}
                enableSorting={true}
                // searchInput={<SearchComponent />}
                enablePrintPdf={true}
                tableClassName="border-collapse"
                tHeadClassName="bg-gray-50"
                tHeadCellsClassName="text-xs uppercase text-gray-600 font-semibold"
                tBodyTrClassName="hover:bg-gray-50"
                tBodyCellsClassName="border-t"
                currentPage={currentPage}
                setCurrentPage={setCurrentPage}
                itemsPerPage={itemsPerPage}
                setItemsPerPage={setItemsPerPage}
                totalItems={teams?.length || 0}
              />
            )}
          </motion.div>
        </div>

        {teamPermissionsData && (
          <DepartmentPermissionsModal
            isOpen={!!teamPermissionsData}
            onClose={() => setTeamPermissionsData(null)}
            department={teamPermissionsData}
          />
        )}
      </motion.div>
    </Screen>
  );
};

export default Index;
