import { forwardRef, ComponentPropsWithoutRef, ReactNode } from "react";
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { X } from "lucide-react";
import { cn } from "@/lib/utils";

export type ModalSize = 
  | "xs"  
  | "sm"  
  | "md"  
  | "lg"  
  | "xl"  
  | "2xl" 
  | "full"; 

export interface BaseModalProps {
    isOpen?: boolean;
    onOpenChange?: (isOpen: boolean) => void;
    title?: string;
    description?: string;
    icon?: ReactNode;
    size?: ModalSize;
    position?: "center" | "top";
    showClose?: boolean;
    closeOnOutsideClick?: boolean;
    closeOnEscape?: boolean;
    preventScroll?: boolean;
    className?: string;
    children?: ReactNode;
    footer?: ReactNode;
    footerClassName?: string;
    headerClassName?: string;
    bodyClassName?: string;
    id?: string;
  }

  const sizeClassNames = {
    xs: "sm:max-w-[320px]",
    sm: "sm:max-w-[384px]",
    md: "sm:max-w-[512px]",
    lg: "sm:max-w-[640px]",
    xl: "sm:max-w-[768px]",
    "2xl": "sm:max-w-[896px]",
    full: "sm:max-w-[calc(100vw-40px)] sm:max-h-[calc(100vh-40px)]",
  };
  
  const positionClassNames = {
    center: "sm:align-middle",
    top: "sm:align-top sm:mt-16",
  };
  
const BaseModal = forwardRef<
  HTMLDivElement,
  BaseModalProps & ComponentPropsWithoutRef<typeof Dialog>
>(
  (
    {
      isOpen,
      onOpenChange,
      title,
      description,
      icon,
      size = "md",
      position = "center",
      showClose = false,
      closeOnOutsideClick = true,
      closeOnEscape = true,
      preventScroll = true,
      className,
      children,
      footer,
      footerClassName,
      headerClassName,
      bodyClassName,
      ...props
    },
    ref
  ) => {
    return (
      <Dialog
        open={isOpen}
        onOpenChange={closeOnOutsideClick ? onOpenChange : undefined}
        modal={preventScroll}
        {...props}
      >
        <DialogContent
          ref={ref}
          className={cn(
            "overflow-hidden shadow-lg backdrop-blur-sm animate-in fade-in-0 zoom-in-95 duration-200",
            sizeClassNames[size],
            positionClassNames[position],
            className,
            "max-h-[90vh] overflow-auto" 
          )}
          onEscapeKeyDown={closeOnEscape ? undefined : (e) => e.preventDefault()}
          onPointerDownOutside={
            closeOnOutsideClick ? undefined : (e) => e.preventDefault()
          }
        >
          {(title || icon || description || showClose) && (
            <DialogHeader className={cn("flex flex-row items-start", headerClassName)}>
              <div className="flex-1 flex gap-3 items-center">
                {icon && <div className="flex-shrink-0">{icon}</div>}
                <div className="flex-1">
                  {title && (
                    <DialogTitle className="text-xl font-semibold leading-6">
                      {title}
                    </DialogTitle>
                  )}
                  {description && (
                    <DialogDescription className="mt-1 text-sm text-muted-foreground">
                      {description}
                    </DialogDescription>
                  )}
                </div>
              </div>
              {showClose && (
                <button
                  type="button"
                  onClick={() => onOpenChange?.(false)}
                  className="p-1.5 text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-md transition-colors"
                  aria-label="Close"
                >
                  <X className="h-4 w-4" />
                </button>
              )}
            </DialogHeader>
          )}
          <div className={cn("py-1", bodyClassName)}>{children}</div>
          {footer && (
            <DialogFooter className={cn("flex flex-row gap-2 justify-end", footerClassName)}>
              {footer}
            </DialogFooter>
          )}
        </DialogContent>
      </Dialog>
    );
  }
);

BaseModal.displayName = "BaseModal";
 export default BaseModal;