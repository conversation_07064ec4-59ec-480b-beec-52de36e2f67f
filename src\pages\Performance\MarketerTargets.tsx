import React, { useState, useMemo, useEffect } from 'react';
import { Screen } from "@/app-components/layout/screen";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useAuthHook } from '@/utils/useAuthHook';
import { useGetMarketerPerformanceQuery, useGetMarketerPerformanceByPeriodQuery, useGetMarketingPeriodsQuery } from '@/redux/slices/hrDashboardApiSlice';
import { formatNumberWithCommas } from '@/utils/salesDataFormatter';
import SpinnerTemp from '@/components/custom/spinners/SpinnerTemp';
import {
  TrendingUp,
  TrendingDown,
  Target,
  DollarSign,
  Calendar,
  BarChart3,
  Award,
  Clock,
  Search,
  Filter,
  Download,
  RefreshCw,
  Eye,
  AlertCircle,
  CheckCircle,
  XCircle,
  Activity,
  TrendingUpIcon
} from 'lucide-react';

const MarketerTargets = () => {
  const { user_details } = useAuthHook();
  const currentUserEmployeeNo = user_details?.employee_no;

  // State management
  const [selectedPeriod, setSelectedPeriod] = useState<string>("");
  const [activeTab, setActiveTab] = useState<string>("overview");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [refreshing, setRefreshing] = useState<boolean>(false);

  // Fetch marketing periods
  const {
    data: periodsData,
    isLoading: periodsLoading,
    refetch: refetchPeriods
  } = useGetMarketingPeriodsQuery();

  // Get current period (first in the array)
  const currentPeriod = periodsData?.results?.[0];

  // Use selected period or default to current period
  const activePeriod = selectedPeriod || currentPeriod?.period_start_date || "";

  // Fetch marketer performance data for the logged-in user only
  const {
    data: performanceData,
    isLoading: performanceLoading,
    error: performanceError,
    refetch: refetchPerformance
  } = useGetMarketerPerformanceQuery({
    marketer_employee_no: currentUserEmployeeNo || "",
    page: 1,
    page_size: 100
  }, {
    skip: !currentUserEmployeeNo
  });

  // Fetch ALL performance data for history (using the period-based query with "ALL" periods)
  const {
    data: allPerformanceData,
    isLoading: allPerformanceLoading,
    error: allPerformanceError,
    refetch: refetchAllPerformance
  } = useGetMarketerPerformanceByPeriodQuery({
    marketing_period: "ALL",
    marketer_employee_no: currentUserEmployeeNo || "",
    page: 1,
    page_size: 100
  }, {
    skip: !currentUserEmployeeNo
  });

  // Filter performance data by selected period
  const filteredPerformance = useMemo(() => {
    if (!performanceData?.results || !activePeriod) return [];

    return performanceData.results.filter(item =>
      item.period_start_date === activePeriod
    );
  }, [performanceData?.results, activePeriod]);

  // Get the current marketer's performance data
  const marketerPerformance = filteredPerformance?.[0];

  // Check if we have any historical data even if current period data is missing
  const hasHistoricalData = allPerformanceData?.results && allPerformanceData.results.length > 0;

  // Calculate performance metrics
  const performancePercentage = marketerPerformance?.MIB_Perfomance || 0;
  const isPerformingWell = performancePercentage >= 50;
  const targetAchievement = performancePercentage >= 100 ? "Target Exceeded" :
    performancePercentage >= 75 ? "On Track" :
      performancePercentage >= 50 ? "Needs Improvement" : "Below Expectations";

  // Performance status color and icon
  const getPerformanceStatus = () => {
    if (performancePercentage >= 100) return { color: "text-green-600", bg: "bg-green-50", icon: CheckCircle, status: "Excellent" };
    if (performancePercentage >= 75) return { color: "text-blue-600", bg: "bg-blue-50", icon: TrendingUp, status: "Good" };
    if (performancePercentage >= 50) return { color: "text-yellow-600", bg: "bg-yellow-50", icon: AlertCircle, status: "Fair" };
    return { color: "text-red-600", bg: "bg-red-50", icon: XCircle, status: "Poor" };
  };

  const performanceStatus = getPerformanceStatus();

  // Auto-switch to history tab if no current data but historical data exists
  useEffect(() => {
    if (!marketerPerformance && hasHistoricalData && activeTab === "overview") {
      setActiveTab("history");
    }
  }, [marketerPerformance, hasHistoricalData, activeTab]);

  // Handle refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await Promise.all([refetchPeriods(), refetchPerformance(), refetchAllPerformance()]);
    } finally {
      setRefreshing(false);
    }
  };

  const isLoading = periodsLoading || performanceLoading || allPerformanceLoading;

  if (isLoading) {
    return (
      <Screen>
        <div className="flex items-center justify-center h-64">
          <SpinnerTemp type="spinner-double" size="lg" />
        </div>
      </Screen>
    );
  }

  if (performanceError || allPerformanceError) {
    return (
      <Screen>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-500 mb-2">Error loading performance data</p>
            <p className="text-sm text-gray-500">Please try again later</p>
          </div>
        </div>
      </Screen>
    );
  }



  return (
    <Screen>
      <div className="space-y-6">
        {/* Enhanced Header */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-xl border border-blue-200">
          <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-blue-600 rounded-lg">
                <Target className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">My Performance Targets</h1>
                <p className="text-gray-600 mt-1">Track your performance against monthly and daily targets</p>
                <div className="flex items-center gap-2 mt-2">
                  <Badge variant="secondary" className="text-xs">
                    Employee: {currentUserEmployeeNo}
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    {user_details?.fullnames}
                  </Badge>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={refreshing}
                className="flex items-center gap-2"
              >
                <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
                Refresh
              </Button>

              {/* Period Selector */}
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                  <SelectTrigger className="w-[220px]">
                    <SelectValue placeholder="Select Period" />
                  </SelectTrigger>
                  <SelectContent>
                    {periodsData?.results?.map((period, index) => (
                      <SelectItem key={period.period_start_date} value={period.period_start_date}>
                        {index === 0 && "(Current) "}
                        {new Date(period.period_start_date).toLocaleDateString()} - {new Date(period.period_end_date).toLocaleDateString()}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs Navigation */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="performance" className="flex items-center gap-2">
              <Activity className="h-4 w-4" />
              Performance
            </TabsTrigger>
            <TabsTrigger value="history" className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              History
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            {marketerPerformance ? (
              <>
                {/* Current Period Info */}
                {currentPeriod && (
                  <Card className="bg-gradient-to-r from-emerald-50 to-teal-50 border-emerald-200">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-emerald-900">
                        <Clock className="h-5 w-5" />
                        Current Marketing Period
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <p className="text-sm text-emerald-700">Period Duration</p>
                          <p className="font-semibold text-emerald-900">
                            {new Date(currentPeriod.period_start_date).toLocaleDateString()} - {new Date(currentPeriod.period_end_date).toLocaleDateString()}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-emerald-700">Status</p>
                          <Badge variant="secondary" className="bg-emerald-100 text-emerald-800">
                            Active Period
                          </Badge>
                        </div>
                        <div>
                          <p className="text-sm text-emerald-700">Performance Status</p>
                          <Badge className={`${performanceStatus.bg} ${performanceStatus.color} border-0`}>
                            <performanceStatus.icon className="h-3 w-3 mr-1" />
                            {performanceStatus.status}
                          </Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Enhanced Performance Overview Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {/* Monthly Target */}
                  <Card className="hover:shadow-lg transition-shadow duration-200">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium text-gray-700">Monthly Target</CardTitle>
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <Target className="h-4 w-4 text-blue-600" />
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-gray-900">
                        KSh {formatNumberWithCommas(marketerPerformance.monthly_target)}
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        Daily: KSh {formatNumberWithCommas(marketerPerformance.daily_target)}
                      </p>
                      <div className="mt-2">
                        <div className="flex items-center text-xs text-gray-500">
                          <Calendar className="h-3 w-3 mr-1" />
                          Target Period
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* MIB Achieved */}
                  <Card className="hover:shadow-lg transition-shadow duration-200">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium text-gray-700">MIB Achieved</CardTitle>
                      <div className="p-2 bg-green-100 rounded-lg">
                        <DollarSign className="h-4 w-4 text-green-600" />
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-gray-900">
                        KSh {formatNumberWithCommas(marketerPerformance.MIB_achieved)}
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        Money in Bank achieved
                      </p>
                      <div className="mt-2">
                        <div className="flex items-center text-xs text-green-600">
                          <TrendingUpIcon className="h-3 w-3 mr-1" />
                          Current Achievement
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Performance Percentage */}
                  <Card className="hover:shadow-lg transition-shadow duration-200">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium text-gray-700">Performance</CardTitle>
                      <div className={`p-2 rounded-lg ${isPerformingWell ? 'bg-green-100' : 'bg-red-100'}`}>
                        {isPerformingWell ? (
                          <TrendingUp className="h-4 w-4 text-green-600" />
                        ) : (
                          <TrendingDown className="h-4 w-4 text-red-600" />
                        )}
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className={`text-2xl font-bold ${isPerformingWell ? 'text-green-600' : 'text-red-600'}`}>
                        {performancePercentage.toFixed(2)}%
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        {targetAchievement}
                      </p>
                      <div className="mt-2">
                        <Badge
                          variant="outline"
                          className={`text-xs ${isPerformingWell ? 'border-green-200 text-green-700' : 'border-red-200 text-red-700'}`}
                        >
                          {isPerformingWell ? 'On Track' : 'Needs Attention'}
                        </Badge>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Target Status */}
                  <Card className="hover:shadow-lg transition-shadow duration-200">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium text-gray-700">Target Status</CardTitle>
                      <div className={`p-2 rounded-lg ${performanceStatus.bg}`}>
                        <performanceStatus.icon className={`h-4 w-4 ${performanceStatus.color}`} />
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">
                        <Badge
                          className={`${performanceStatus.bg} ${performanceStatus.color} border-0 text-sm`}
                        >
                          {performanceStatus.status}
                        </Badge>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        Overall performance rating
                      </p>
                      <div className="mt-2">
                        <div className="flex items-center text-xs text-gray-500">
                          <Award className="h-3 w-3 mr-1" />
                          Performance Level
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </>
            ) : (
              <Card className="border-dashed border-2 border-gray-300">
                <CardContent className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <Target className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-700 mb-2">No Current Period Data</h3>
                    <p className="text-gray-500 mb-4">
                      No performance data available for the current period.
                      {hasHistoricalData && " Check the History tab to view past performance."}
                    </p>
                    <div className="flex gap-2 justify-center">
                      <Button variant="outline" onClick={handleRefresh} disabled={refreshing}>
                        <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                        Refresh Data
                      </Button>
                      {hasHistoricalData && (
                        <Button variant="default" onClick={() => setActiveTab("history")}>
                          <Clock className="h-4 w-4 mr-2" />
                          View History
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Performance Tab */}
          <TabsContent value="performance" className="space-y-6">
            {marketerPerformance ? (
              /* Detailed Performance Card */
              <Card className="border-2 border-blue-100">
                <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50">
                  <CardTitle className="flex items-center gap-2 text-blue-900">
                    <BarChart3 className="h-5 w-5" />
                    Performance Analysis
                  </CardTitle>
                  <CardDescription className="text-blue-700">
                    Detailed breakdown of your performance for the selected period
                  </CardDescription>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="space-y-6">
                    {/* Enhanced Progress Bar */}
                    <div>
                      <div className="flex justify-between items-center mb-3">
                        <span className="text-sm font-semibold text-gray-700">Target Achievement Progress</span>
                        <div className="flex items-center gap-2">
                          <span className="text-lg font-bold text-gray-900">{performancePercentage.toFixed(2)}%</span>
                          <Badge className={`${performanceStatus.bg} ${performanceStatus.color} border-0`}>
                            {performanceStatus.status}
                          </Badge>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-3 shadow-inner">
                        <div
                          className={`h-3 rounded-full transition-all duration-500 ${performancePercentage >= 100 ? 'bg-gradient-to-r from-green-500 to-green-600' :
                            performancePercentage >= 75 ? 'bg-gradient-to-r from-blue-500 to-blue-600' :
                              performancePercentage >= 50 ? 'bg-gradient-to-r from-yellow-500 to-yellow-600' : 'bg-gradient-to-r from-red-500 to-red-600'
                            }`}
                          style={{ width: `${Math.min(performancePercentage, 100)}%` }}
                        ></div>
                      </div>
                      <div className="flex justify-between text-xs text-gray-500 mt-1">
                        <span>0%</span>
                        <span>50%</span>
                        <span>100%</span>
                      </div>
                    </div>

                    <Separator />

                    {/* Performance Metrics Grid */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="text-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg border border-blue-200">
                        <Calendar className="h-6 w-6 text-blue-600 mx-auto mb-2" />
                        <p className="text-sm text-blue-700 font-medium">Period Start</p>
                        <p className="font-bold text-blue-900">
                          {new Date(marketerPerformance.period_start_date).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="text-center p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-lg border border-green-200">
                        <Calendar className="h-6 w-6 text-green-600 mx-auto mb-2" />
                        <p className="text-sm text-green-700 font-medium">Period End</p>
                        <p className="font-bold text-green-900">
                          {new Date(marketerPerformance.period_end_date).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="text-center p-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg border border-purple-200">
                        <Award className="h-6 w-6 text-purple-600 mx-auto mb-2" />
                        <p className="text-sm text-purple-700 font-medium">Employee No</p>
                        <p className="font-bold text-purple-900">{marketerPerformance.marketer_no_id}</p>
                      </div>
                    </div>

                    {/* Gap Analysis */}
                    {performancePercentage < 100 && (
                      <div className="p-4 bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg">
                        <div className="flex items-start gap-3">
                          <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
                          <div>
                            <h4 className="font-semibold text-yellow-800 mb-2">Gap to Target</h4>
                            <p className="text-sm text-yellow-700 mb-2">
                              You need <span className="font-bold">KSh {formatNumberWithCommas(marketerPerformance.monthly_target - marketerPerformance.MIB_achieved)}</span> more to reach your monthly target.
                            </p>
                            <div className="text-xs text-yellow-600">
                              Daily requirement: KSh {formatNumberWithCommas((marketerPerformance.monthly_target - marketerPerformance.MIB_achieved) / 30)}
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {performancePercentage >= 100 && (
                      <div className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg">
                        <div className="flex items-start gap-3">
                          <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                          <div>
                            <h4 className="font-semibold text-green-800 mb-2">🎉 Congratulations!</h4>
                            <p className="text-sm text-green-700 mb-2">
                              You have exceeded your monthly target by <span className="font-bold">KSh {formatNumberWithCommas(marketerPerformance.MIB_achieved - marketerPerformance.monthly_target)}</span>.
                            </p>
                            <div className="text-xs text-green-600">
                              Outstanding performance! Keep up the excellent work.
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card className="border-dashed border-2 border-gray-300">
                <CardContent className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <BarChart3 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-700 mb-2">No Performance Data</h3>
                    <p className="text-gray-500 mb-4">
                      No performance analysis available for the current period.
                      {hasHistoricalData && " Check the History tab to view past performance."}
                    </p>
                    <div className="flex gap-2 justify-center">
                      <Button variant="outline" onClick={handleRefresh} disabled={refreshing}>
                        <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                        Refresh Data
                      </Button>
                      {hasHistoricalData && (
                        <Button variant="default" onClick={() => setActiveTab("history")}>
                          <Clock className="h-4 w-4 mr-2" />
                          View History
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* History Tab */}
          <TabsContent value="history" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Performance History
                </CardTitle>
                <CardDescription>
                  View your performance across different marketing periods
                </CardDescription>
              </CardHeader>
              <CardContent>
                {allPerformanceData?.results && allPerformanceData.results.length > 0 ? (
                  <div className="space-y-4">
                    {/* Summary Stats */}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                      <div className="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
                        <div className="text-2xl font-bold text-blue-900">
                          {allPerformanceData.results.length}
                        </div>
                        <div className="text-sm text-blue-700">Total Periods</div>
                      </div>
                      <div className="text-center p-4 bg-green-50 rounded-lg border border-green-200">
                        <div className="text-2xl font-bold text-green-900">
                          {allPerformanceData.results.filter(p => p.MIB_Perfomance >= 100).length}
                        </div>
                        <div className="text-sm text-green-700">Targets Met</div>
                      </div>
                      <div className="text-center p-4 bg-purple-50 rounded-lg border border-purple-200">
                        <div className="text-2xl font-bold text-purple-900">
                          {(allPerformanceData.results.reduce((sum, p) => sum + p.MIB_Perfomance, 0) / allPerformanceData.results.length).toFixed(1)}%
                        </div>
                        <div className="text-sm text-purple-700">Avg Performance</div>
                      </div>
                      <div className="text-center p-4 bg-orange-50 rounded-lg border border-orange-200">
                        <div className="text-2xl font-bold text-orange-900">
                          KSh {formatNumberWithCommas(allPerformanceData.results.reduce((sum, p) => sum + p.MIB_achieved, 0))}
                        </div>
                        <div className="text-sm text-orange-700">Total MIB</div>
                      </div>
                    </div>

                    {/* Historical Performance Table */}
                    <div className="overflow-x-auto">
                      <table className="w-full border-collapse">
                        <thead>
                          <tr className="bg-gray-50 border-b">
                            <th className="text-left p-3 font-semibold text-gray-700">Period</th>
                            <th className="text-right p-3 font-semibold text-gray-700">Target</th>
                            <th className="text-right p-3 font-semibold text-gray-700">Achieved</th>
                            <th className="text-right p-3 font-semibold text-gray-700">Performance</th>
                            <th className="text-center p-3 font-semibold text-gray-700">Status</th>
                          </tr>
                        </thead>
                        <tbody>
                          {[...allPerformanceData.results]
                            .sort((a, b) => new Date(b.period_start_date).getTime() - new Date(a.period_start_date).getTime())
                            .map((period, index) => {
                              const isCurrentPeriod = period.period_start_date === currentPeriod?.period_start_date;
                              const performance = period.MIB_Perfomance;
                              const statusColor = performance >= 100 ? 'text-green-600 bg-green-50' :
                                performance >= 75 ? 'text-blue-600 bg-blue-50' :
                                  performance >= 50 ? 'text-yellow-600 bg-yellow-50' : 'text-red-600 bg-red-50';
                              const statusText = performance >= 100 ? 'Excellent' :
                                performance >= 75 ? 'Good' :
                                  performance >= 50 ? 'Fair' : 'Poor';

                              return (
                                <tr key={period.id} className={`border-b hover:bg-gray-50 ${isCurrentPeriod ? 'bg-blue-50' : ''}`}>
                                  <td className="p-3">
                                    <div>
                                      <div className="font-medium text-gray-900">
                                        {new Date(period.period_start_date).toLocaleDateString()} - {new Date(period.period_end_date).toLocaleDateString()}
                                      </div>
                                      {isCurrentPeriod && (
                                        <Badge variant="secondary" className="mt-1 text-xs bg-blue-100 text-blue-800">
                                          Current Period
                                        </Badge>
                                      )}
                                    </div>
                                  </td>
                                  <td className="p-3 text-right font-medium text-gray-900">
                                    KSh {formatNumberWithCommas(period.monthly_target)}
                                  </td>
                                  <td className="p-3 text-right font-medium text-gray-900">
                                    KSh {formatNumberWithCommas(period.MIB_achieved)}
                                  </td>
                                  <td className="p-3 text-right">
                                    <div className="flex items-center justify-end gap-2">
                                      <span className={`font-bold ${performance >= 50 ? 'text-green-600' : 'text-red-600'}`}>
                                        {performance.toFixed(1)}%
                                      </span>
                                      {performance >= 100 ? (
                                        <TrendingUp className="h-4 w-4 text-green-600" />
                                      ) : performance >= 50 ? (
                                        <TrendingUp className="h-4 w-4 text-yellow-600" />
                                      ) : (
                                        <TrendingDown className="h-4 w-4 text-red-600" />
                                      )}
                                    </div>
                                  </td>
                                  <td className="p-3 text-center">
                                    <Badge className={`${statusColor} border-0 text-xs`}>
                                      {statusText}
                                    </Badge>
                                  </td>
                                </tr>
                              );
                            })}
                        </tbody>
                      </table>
                    </div>

                    {/* Performance Trend Chart Placeholder */}
                    <Card className="mt-6">
                      <CardHeader>
                        <CardTitle className="text-lg">Performance Trend</CardTitle>
                        <CardDescription>Visual representation of your performance over time</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                          <div className="text-center">
                            <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                            <p className="text-gray-500">Performance trend chart</p>
                            <p className="text-sm text-gray-400">Chart visualization coming soon</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-700 mb-2">No Historical Data</h3>
                    <p className="text-gray-500 mb-4">
                      {allPerformanceLoading ?
                        "Loading historical performance data..." :
                        allPerformanceError ?
                          "Error loading historical data. Please try refreshing." :
                          "No historical performance data available yet"
                      }
                    </p>
                    <Button variant="outline" onClick={handleRefresh} disabled={refreshing}>
                      <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                      Refresh Data
                    </Button>
                    {/* Debug info - remove in production */}
                    {process.env.NODE_ENV === 'development' && (
                      <div className="mt-4 text-xs text-gray-400">
                        <p>Debug: allPerformanceData has {allPerformanceData?.results?.length || 0} records</p>
                        <p>Debug: performanceData has {performanceData?.results?.length || 0} records</p>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </Screen>
  );
};

export default MarketerTargets;
