import Tab2 from "@/components/custom/tabs/Tab2";
import { Screen } from "@/app-components/layout/screen";
import OtherBookingsReport from "./otherbookings";
import MpesaBookingsReport from "./mpesabooking";
import SpecialBookingsReport from "./specialbooking";
import WaitingApprovalBookingsReport from "./waitingapproval";
import AllBookingsReport from "./allbookings";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import {
  Share,
  CreditCard,
  Calendar,
  FileCheck,
  BookOpen,
  List,
  Radar,
  Globe2,
  Filter,
} from "lucide-react";
// import NavTab2 from "@/components/custom/tabs/NavTab2";
import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useLazyGetPlotBookingsQuery } from "@/redux/slices/projects";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { useAuthHook } from "@/utils/useAuthHook";
import DiasporaBookingReport from "./diasporabookings";
import { useSidebarPermissions } from "@/hooks/useSidebarPermissions";

const Bookings = () => {
  const { hasInventoryPermission } = useSidebarPermissions();
  const hasFullAccess = hasInventoryPermission("VIEW_INVENTORY_FULL_ACCESS");
  const hasMarketerPermission = hasInventoryPermission(
    "VIEW_INVENTORY_MARKETER"
  );
  const hasDiasporaPermission = hasInventoryPermission(
    "VIEW_INVENTORY_DIASPORA"
  );
  const { user_details } = useAuthHook();
  // const handleExport = () => {};

  const [getBookings, { data: bookings, isLoading, isFetching }] =
    useLazyGetPlotBookingsQuery();

  const navigate = useNavigate();
  const location = useLocation();
  const [filterStatus, setFilterStatus] = useState<string>("");
  const [activeTab, setActiveTab] = useState<string>("ALL");

  const fetchBooking = async (activeTab: string) => {
    let booking_type = activeTab == "ALL" ? "" : activeTab;
    const res = await getBookings({
      booking_type,
      marketer: user_details?.employee_no,
      status: filterStatus,
    }).unwrap();
  };

  useEffect(() => {
    fetchBooking(activeTab);
  }, [activeTab, filterStatus]);

  useEffect(() => {
    const hashItem = location.hash;
    if (hashItem) {
      const hash = hashItem.replace("#", "");
      if (hash) {
        setActiveTab(hash);
        navigate(`#${hash}`, { replace: true });
      }
    } else {
      setActiveTab("ALL");
      navigate(`#ALL`, { replace: true });
    }
  }, [location.hash]);

  // Update URL hash when tab changes
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    navigate(`#${value}`, { replace: true });
  };
  // console.log(window.location)
  return (
    <Screen>
      <div>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-3">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
            <Calendar className="w-8 h-8 text-gray-600" />
            My Bookings
          </h1>
        </div>
        <div className="bg-white shadow-md rounded-lg px-2 py-4 min-h-[50vh]">
          <div className="w-full flex items-center overflow-x-auto sm:overflow-x-auto scrollbar-hide">
            <div
              className={`${
                activeTab == "ALL"
                  ? "bg-accent border-b-0 border border-blue-400 rounded-t-sm"
                  : " border-t-0 border-x-0  text-gray-500"
              } flex  items-center gap-1 justify-start cursor-pointer px-8 py-3`}
              onClick={() => handleTabChange("ALL")}
              title="All Booking"
            >
              <List className="w-5 h-5" />{" "}
              <p className="md:flex hidden">All Booking</p>
            </div>
            {(hasFullAccess || hasMarketerPermission) && (
              <div className="flex">
                <div
                  className={`${
                    activeTab == "SPECIAL"
                      ? "bg-accent border-b-0 border border-blue-400 rounded-t-sm"
                      : " border-t-0 border-x-0  text-gray-500"
                  } flex  items-center gap-1 justify-start cursor-pointer px-8 py-3`}
                  onClick={() => handleTabChange("SPECIAL")}
                  title="Special Booking"
                >
                  <BookOpen className="w-5 h-5" />{" "}
                  <p className="md:flex hidden">Special Booking</p>
                </div>
                <div
                  className={`${
                    activeTab == "MPESA"
                      ? "bg-accent border-b-0 border border-blue-400 rounded-t-sm"
                      : " border-t-0 border-x-0  text-gray-500"
                  } flex  items-center gap-1 justify-start cursor-pointer px-8 py-3`}
                  onClick={() => handleTabChange("MPESA")}
                  title="Mpesa Booking"
                >
                  <CreditCard className="w-5 h-5" />{" "}
                  <p className="md:flex hidden">Mpesa Booking</p>
                </div>
                <div
                  className={`${
                    activeTab == "OTHER"
                      ? "bg-accent border-b-0 border border-blue-400 rounded-t-sm"
                      : " border-t-0 border-x-0  text-gray-500"
                  } flex  items-center gap-1 justify-start cursor-pointer px-8 py-3`}
                  onClick={() => handleTabChange("OTHER")}
                  title="Other Booking"
                >
                  <Radar className="w-5 h-5" />{" "}
                  <p className="md:flex hidden">Other Booking</p>
                </div>
              </div>
            )}
            {(hasFullAccess || hasDiasporaPermission) && (
              <div
                className={`${
                  activeTab == "DIASPORA"
                    ? "bg-accent border-b-0 border border-blue-400 rounded-t-sm"
                    : " border-t-0 border-x-0  text-gray-500"
                } flex  items-center gap-1 justify-start cursor-pointer px-8 py-3`}
                onClick={() => handleTabChange("DIASPORA")}
                title="Diaspora Booking"
              >
                <Globe2 className="w-5 h-5" />{" "}
                <p className="md:flex hidden">Diaspora Booking</p>
              </div>
            )}
          </div>

          {isLoading || isFetching ? (
            <div className="w-full flex items-center justify-center py-4">
              <div>
                <SpinnerTemp type="spinner-double" />
              </div>
            </div>
          ) : (
            bookings?.data?.results && (
              <div className="">
                {activeTab == "ALL" && (
                  <AllBookingsReport
                    filterStatus={filterStatus}
                    setFilterStatus={setFilterStatus}
                    data={bookings}
                    loading={isLoading}
                  />
                )}
                {(hasFullAccess || hasMarketerPermission) && (
                  <>
                    {activeTab == "SPECIAL" && (
                      <SpecialBookingsReport
                        data={bookings}
                        loading={isLoading}
                      />
                    )}
                    {activeTab == "MPESA" && (
                      <MpesaBookingsReport
                        data={bookings}
                        loading={isLoading}
                      />
                    )}
                    {activeTab == "OTHER" && (
                      <OtherBookingsReport
                        data={bookings}
                        loading={isLoading}
                      />
                    )}
                  </>
                )}
                {(hasFullAccess || hasDiasporaPermission) &&
                  activeTab == "DIASPORA" && (
                    <DiasporaBookingReport
                      data={bookings}
                      loading={isLoading}
                    />
                  )}
                {/* {activeTab == "WAITING" && <WaitingApprovalBookingsReport />} */}
              </div>
            )
          )}

          {/* <NavTab2
            tabs={Tabrepo}
            className="text-gray-600 hover:text-blue-600 hover:bg-blue-50 font-medium px-4 py-2 rounded-t-md transition-colors duration-150"
            TabsTriggerClassName='!px-3 !py-3'
            TabsListClassName='!bg-transparent'
          /> */}
        </div>
      </div>
    </Screen>
  );
};

export default Bookings;
