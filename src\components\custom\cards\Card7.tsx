"use client";

import * as React from "react";
import { Card, CardContent } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import { MoreVertical } from "lucide-react";

export interface Card7Props {
  headerBgClass?: string;
  dropdownItems?: Array<{ label: string; onSelect: () => void }>;
  avatarSlot?: React.ReactNode;
  titleSlot?: React.ReactNode;
  subtitleSlot?: React.ReactNode;
  pillSlots?: React.ReactNode;
  actionSlot?: React.ReactNode;
  className?: string;
}

export function Card7({
  headerBgClass = "bg-gray-200",
  dropdownItems = [],
  avatarSlot,
  titleSlot,
  subtitleSlot,
  pillSlots,
  actionSlot,
  className = "",
}: Card7Props) {
  return (
    <Card className={`overflow-visible rounded-lg h-full flex flex-col ${className}`}>
      {/* Top band */}
      <div className={`h-16 ${headerBgClass} relative rounded-t-lg flex-shrink-0`}>
        {dropdownItems.length > 0 && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <button className="absolute top-2 right-2 p-1 rounded-full hover:bg-gray-300">
                <MoreVertical className="h-5 w-5 text-gray-600" />
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {dropdownItems.map((item, i) => (
                <DropdownMenuItem key={i} onSelect={item.onSelect}>
                  {item.label}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>

      {/* Body (lifted up to overlap header) */}
      <CardContent className="flex flex-col items-center -mt-8 px-4 pb-6 flex-grow">
        {/* White “cut-out” so avatar ring is always on white */}
        {avatarSlot && (
          <div className="relative z-10 bg-white p-1 rounded-full">
            {avatarSlot}
          </div>
        )}

        {titleSlot && <div className="mt-2">{titleSlot}</div>}
        {subtitleSlot && <div className="flex-shrink-0">{subtitleSlot}</div>}
        {pillSlots && <div className="mt-4 flex space-x-2 flex-grow flex items-center">{pillSlots}</div>}
        {actionSlot && <div className="mt-4 w-full flex-shrink-0">{actionSlot}</div>}
      </CardContent>
    </Card>
  );
}
