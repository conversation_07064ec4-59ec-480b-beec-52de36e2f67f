import { Calendar, CircleAlert, Clock, DollarSign, FileBarChart, ShieldAlert, ShieldCheck, TriangleAlert } from 'lucide-react'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from '../ui/badge'
import { useSalesStore } from '@/zustand/useSaleStore';
import { formatNumberWithCommas } from '@/utils/salesDataFormatter';
import { TitleStatusStepper } from './TitleStatusStepper';


const OverpaymentsSalesTab = () => {
    const salesData = useSalesStore((state) => state.salesData);
    const {
        lead_file_no,
        lead_file_status_dropped,
        purchase_price,
        selling_price,
        balance_lcy,
        customer_name,
        purchase_type,
        commission_threshold,
        deposit_threshold,
        discount,
        completion_date,
        no_of_installments,
        installment_amount,
        sale_agreement_sent,
        sale_agreement_signed,
        total_paid,
        transfer_cost_charged,
        transfer_cost_paid,
        overpayments,
        refunds,
        refundable_amount,
        penalties_accrued,
        booking_id,
        booking_date,
        additional_deposit_date,
        title_status,
        credit_officer_id,
        plot,
        project,
        marketer,
        customer_id,
        customer_lead_source,
        cat_lead_source,
    } = salesData;

    const sampleSteps = [
        {
            title: "Congrats Message Sent/Skipped",
            description: "Client has been congratulated for completing full payment and instructed to submit ID, KRA PIN, and passport photo for title transfer initiation."
        },
        {
            title: "Cleared To Transfer",
            description: "All required documents have been submitted; the title transfer process has officially started."
        },
        {
            title: "At Client",
            description: "Transfer and consent forms have been sent to the client for signing."
        },
        {
            title: "From Client",
            description: "Signed transfer and consent forms have been received back from the client."
        },
        {
            title: "At Registry",
            description: "The signed documents have been submitted to the land registry for processing."
        },
        {
            title: "From Registry",
            description: "Title deed has been successfully collected from the registry."
        },
        {
            title: "Consent Form / Title Deed Copy Sent",
            description: "A scanned copy of the title deed or consent form has been shared with the client for their records."
        },
        {
            title: "On Transit",
            description: "The original title deed has been dispatched for delivery to the client."
        },
        {
            title: "Title Deed Picked",
            description: "The client has received and acknowledged collection of the title deed."
        },
        {
            title: "Issuance Form Attached",
            description: "The signed issuance acknowledgment form has been filed and attached to the client's records."
        }
    ];

    return (
        <div className='space-y-6'>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
                <div className="flex justify-between items-center shadow-xl shadow-primary/20 px-3 py-8 rounded-md">
                    <div>
                        <p className='text-xs mb-2'>Transfer cost charged</p>
                        <p className='text-1xl font-bold'>{typeof transfer_cost_charged !== 'undefined' ? 'Ksh' : 'Not'}</p>
                        <p className='text-2xl font-bold'>{typeof transfer_cost_charged !== 'undefined' ? formatNumberWithCommas(transfer_cost_charged) : 'Declared'}</p>
                    </div>
                    <div className="h-fit bg-green-100 p-3 rounded-full dark:bg-green-900/30">
                        <DollarSign className="h-6 w-6 text-green-600 dark:text-green-400" />
                    </div>
                </div>

                <div className="flex justify-between items-center shadow-xl shadow-primary/20 px-3 py-8 rounded-md">
                    <div>
                        <p className='text-xs mb-2'>Transfer cost paid</p>
                        <p className='text-1xl font-bold'>{typeof transfer_cost_paid !== 'undefined' ? 'Ksh' : 'Not'}</p>
                        <p className='text-2xl font-bold'>{typeof transfer_cost_paid !== 'undefined' ? formatNumberWithCommas(transfer_cost_paid) : 'Declared'}</p>
                    </div>
                    <div className="h-fit bg-amber-100 p-3 rounded-full dark:bg-amber-900/30">
                        <FileBarChart className="h-6 w-6 text-amber-600 dark:text-amber-400" />
                    </div>
                </div>
            </div>



            <div>
                <p className='font-bold text-[15px] mb-3'>Title Status</p>

                {title_status && title_status.trim() !== '' ? (
                    <TitleStatusStepper
                        steps={sampleSteps}
                        activeStep={
                            sampleSteps.findIndex(
                                step => step.title.toLowerCase() === title_status.toLowerCase()
                            )
                        }
                        className='mx-6 my-4'
                    />
                ) : (
                    <div className='h-[30vh] flex flex-col gap-2 items-center justify-center'>
                        <TriangleAlert size={45} className='text-red-600'/>
                        <p className='text-xs'>Title status is not available.</p>
                    </div>
                )}
            </div>
        </div>
    )
}

export default OverpaymentsSalesTab