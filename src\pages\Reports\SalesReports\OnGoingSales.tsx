import LazyModal, { TableColumn } from '@/components/reports/ReportsModal';
import { formatNumberWithCommas } from '@/utils/salesDataFormatter';

interface OnGoingSale {
    lead_file_no: string
    customer_name: string
    plot: string
    selling_price: string
    total_paid: string
    balance_lcy: string
    project: string
    purchase_type: string
}

interface OnGoingSalesReportProps {
    isModalOpen: boolean;
    setIsModalOpen: (state: boolean) => void;
}

const OnGoingSalesReport = ({ isModalOpen, setIsModalOpen }: OnGoingSalesReportProps) => {

    // Define table columns with proper typing
    const columns: TableColumn<OnGoingSale>[] = [
        {
            key: 'customer_name',
            title: 'Customer',
        },
        {
            key: 'selling_price',
            title: 'Selling Price',
            render: (value: string) => {
                return <span>{formatNumberWithCommas(value)}</span>
            }
        },
        {
            key: 'total_paid',
            title: 'Total Paid',
            render: (value: string) => {
                return <span>{formatNumberWithCommas(value)}</span>
            },
        },
        {
            key: 'balance_lcy',
            title: 'Balance',
            render: (value: string) => {
                return <span>{formatNumberWithCommas(value)}</span>
            },
        },
        {
            key: 'purchase_type',
            title: 'Purchase Type',
        },
        {
            key: 'lead_file_no',
            title: 'Lead File No',
        },
        {
            key: 'plot',
            title: 'Plot',
        },
    ]

    const handleCloseModal = () => {
        setIsModalOpen(false);
    }

    return (
        <LazyModal<OnGoingSale>
            isOpen={isModalOpen}
            onClose={handleCloseModal}
            title="Ongoing Sales Report"
            url="/ongoing-sales"  // Your actual API endpoint
            params={{}}
            columns={columns}
            size="lg"
        />
    )
}

export default OnGoingSalesReport