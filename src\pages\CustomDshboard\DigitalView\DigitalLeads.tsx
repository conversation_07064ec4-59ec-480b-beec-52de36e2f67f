import { SetStateAction, useState, useMemo } from "react";
import BaseModal from "@/components/custom/modals/BaseModal";
import { ColumnDef } from "@tanstack/react-table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Settings, Activity } from "lucide-react";
import { DataTable } from "@/components/custom/tables/Table1";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import { useGetPropectsQuery } from "@/redux/slices/propects";
import { ProspectTypes } from "@/types/prospects";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";


interface ProspectsTableModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function DigitalLeadsTableModal({ open, onOpenChange }: ProspectsTableModalProps) {
  const [activeTab, setActiveTab] = useState<"allocated" | "unallocated">("allocated");

  // Fetch prospects data
  const {
    data: prospectsData,
    isLoading: prospectsLoading,
    error: prospectsError
  } = useGetPropectsQuery({
    page: 1,
    page_size: 1000, // Get all prospects
  });

  // Transform and filter prospects data
  const { allocatedLeads, unallocatedLeads } = useMemo(() => {
    const prospects = prospectsData?.data?.results || [];

    const allocated = prospects.filter(p => p.marketer && p.marketer.trim() !== '');
    const unallocated = prospects.filter(p => !p.marketer || p.marketer.trim() === '');

    return {
      allocatedLeads: allocated,
      unallocatedLeads: unallocated,
    };
  }, [prospectsData]);

  const filteredData = activeTab === "allocated" ? allocatedLeads : unallocatedLeads;

  const columns: ColumnDef<ProspectTypes>[] = [
    {
      accessorKey: "name",
      header: "Name",
      cell: (info) => (
        <span className="font-medium capitalize">
          {info.getValue() as string}
        </span>
      ),
    },
    {
      accessorKey: "phone",
      header: "Phone Number",
      cell: (info) => info.getValue(),
    },
    {
      accessorKey: "email",
      header: "Email",
      cell: (info) => info.getValue() || "N/A",
    },
    {
      accessorKey: "marketer_name",
      header: "Allocated Marketer",
      cell: (info) => info.getValue() || "Unallocated",
    },
    {
      accessorKey: "project_name",
      header: "Project",
      cell: (info) => info.getValue() || "N/A",
    },
    {
      accessorKey: "category",
      header: "Category",
      cell: (info) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          info.getValue() === "Hot"
            ? "bg-red-100 text-red-800"
            : info.getValue() === "Warm"
            ? "bg-yellow-100 text-yellow-800"
            : "bg-blue-100 text-blue-800"
        }`}>
          {info.getValue() as string}
        </span>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: (info) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          info.getValue() === "Active"
            ? "bg-green-100 text-green-800"
            : "bg-gray-100 text-gray-800"
        }`}>
          {info.getValue() as string}
        </span>
      ),
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger>
            <Settings size={20} />
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                console.log(`Edit prospect ${row.original.name}`);
              }}
            >
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                console.log(`View prospect ${row.original.name}`);
              }}
            >
              View Details
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
    },
  ];

  return (
    <BaseModal
      open={open}
      onOpenChange={onOpenChange}
      title="Leads Management"
      description="View and manage allocated and unallocated leads"
      className="max-w-4xl"
      size="full"
    >
        <div className="flex justify-end mb-4">
           
        </div>
      <div className="p-4 bg-white dark:bg-gray-900 rounded-md">
        <div className="flex space-x-4 mb-4">
          <button
            className={`px-4 py-2 rounded-md ${
              activeTab === "allocated" ? "bg-blue-500 text-white" : "bg-gray-200 text-gray-700"
            }`}
            onClick={() => setActiveTab("allocated")}
          >
            Allocated Leads
          </button>
          <button
            className={`px-4 py-2 rounded-md ${
              activeTab === "unallocated" ? "bg-blue-500 text-white" : "bg-gray-200 text-gray-700"
            }`}
            onClick={() => setActiveTab("unallocated")}
          >
            Unallocated Leads
          </button>
        </div>

        {prospectsLoading ? (
          <div className="flex items-center justify-center py-12">
            <SpinnerTemp type="spinner-double" size="md" />
          </div>
        ) : prospectsError ? (
          <div className="text-center py-12">
            <Activity className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Failed to Load Prospects
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Unable to fetch prospects data. Please try again later.
            </p>
          </div>
        ) : filteredData.length === 0 ? (
          <div className="text-center text-gray-500 dark:text-gray-400 py-4">
            No {activeTab === "allocated" ? "allocated" : "unallocated"} leads found.
          </div>
        ) : (
          <div className="w-full overflow-x-auto">
            <DataTable<ProspectTypes>
              data={filteredData}
              columns={columns}
              title={activeTab === "allocated" ? "Allocated Leads" : "Unallocated Leads"}
              enableExportToExcel={true}
              enablePrintPdf={true}
              enableColumnFilters={true}
              enablePagination={true}
              enableSorting={true}
              enableToolbar={true}
              containerClassName="min-w-[700px] bg-white dark:bg-gray-900 rounded-lg shadow-md"
              tableClassName="w-full border-collapse border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 text-sm text-left text-gray-700 dark:text-gray-300"
              tHeadClassName="bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 font-semibold"
              tHeadCellsClassName="px-4 py-2 border-b border-gray-200 dark:border-gray-700"
              tBodyClassName="divide-y divide-gray-200 dark:divide-gray-700"
              tBodyTrClassName="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
              tBodyCellsClassName="px-4 py-2"
            />
          </div>
        )}
        
                 
      </div>
    </BaseModal>
  );
}