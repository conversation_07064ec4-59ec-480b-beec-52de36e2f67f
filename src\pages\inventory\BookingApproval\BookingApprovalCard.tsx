import { ActionButton } from "@/components/custom/buttons/buttons";
import BaseModal from "@/components/custom/modals/BaseModal";
import ConfirmModal from "@/components/custom/modals/ConfirmationModal";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { toast } from "@/components/custom/Toast/MyToast";
import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { useUpdatePlotBookingMutation } from "@/redux/slices/projects";
import { formatDateTime } from "@/utils/formatDate";
import { CheckCircle, FileText, Image } from "lucide-react";
import { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";

type Props = {
  rowData: BookingType;
};

interface BookingType {
  booking_id: string;
  booking_type: string;
  plots: string;
  amount: string;
  lead: string;
  lead_name: string;
  marketer: string;
  marketer_name: string;
  customer: string;
  customer_name: string;
  transaction_id: string;
  creation_date: string;
  proof_of_payment: string;
  upload_time: string;
  type: string;
  status: string;
  office: string;
  deadline: string;
}

const CountdownTimer = ({ targetDate }: { targetDate: string }) => {
  const calculateTimeLeft = () => {
    const difference = +new Date(targetDate) - +new Date();
    let timeLeft: any = {};

    if (difference > 0) {
      timeLeft = {
        days: Math.floor(difference / (1000 * 60 * 60 * 24)),
        hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
        minutes: Math.floor((difference / 1000 / 60) % 60),
        seconds: Math.floor((difference / 1000) % 60),
      };
    } else {
      timeLeft = null;
    }

    return timeLeft;
  };

  const [timeLeft, setTimeLeft] = useState(calculateTimeLeft());

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  if (!timeLeft) {
    return <p>Countdown complete!</p>;
  }

  return (
    <div>
      <span>{timeLeft.days}d </span>
      <span>{timeLeft.hours}h </span>
      <span>{timeLeft.minutes}m </span>
      <span>{timeLeft.seconds}s</span>
    </div>
  );
};

const BookingApprovalCard = ({ rowData }: Props) => {
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [updateBooking, { isLoading: approving }] =
    useUpdatePlotBookingMutation();

  const location = useLocation();
  const path_name = location.pathname.split("/")[1];

  const [reason, setReason] = useState("");

  const [file, setFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isPdf, setIsPdf] = useState(false);

  const handleFileChange = (e: any) => {
    const selected = e.target.files[0];
    if (selected) {
      setFile(selected);
      setIsPdf(selected.type === "application/pdf");

      if (selected.type.startsWith("image/")) {
        setPreviewUrl(URL.createObjectURL(selected));
      } else {
        setPreviewUrl(null);
      }
    } else {
      setFile(null);
      setPreviewUrl(null);
      setIsPdf(false);
    }
  };

  const handleUpload = async () => {
    if (!file) return;

    let formData = {
      action: "special_file_upload",
      id: rowData?.booking_id,
      proof_of_payment: file,
    };

    const newFormData = new FormData();
    Object.entries(formData).forEach(([key, value]: [string, unknown]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          value.forEach((item) => newFormData.append(key, item));
        } else if (value instanceof Date) {
          newFormData.append(key, value.toISOString());
        } else {
          newFormData.append(key, value as string | Blob);
        }
      }
    });

    try {
      const response = await updateBooking({
        id: rowData?.booking_id,
        formData: newFormData,
      }).unwrap();

      if (response.success) {
        toast.success(`Proof of payment uploaded successfully`);
        setShowUploadModal(false);
      } else if (!response.success) {
        toast.error(response?.error);
      }
    } catch (error: any) {
      toast.error(
        error?.data
          ? error?.data?.error
          : "Something went wrong, please try again"
      );
    }
  };

  const handleApprove = async (approval_status: string) => {
    let formData = {
      action: "special_booking_approval",
      approval_action: approval_status,
      id: rowData?.booking_id,
      reason: "",
    };

    if (approval_status === "reject") {
      if (!reason) {
        toast.error("Please provide a reason for rejection");
        return;
      }
      formData = { ...formData, reason: reason };
    }

    const newFormData = new FormData();
    Object.entries(formData).forEach(([key, value]: [string, unknown]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          newFormData.append(key, JSON.stringify(value));
        } else if (value instanceof Date) {
          newFormData.append(key, value.toISOString());
        } else if (typeof value === "string" || value instanceof Blob) {
          newFormData.append(key, value);
        }
      }
    });

    try {
      const response = await updateBooking({
        id: rowData?.booking_id,
        formData: newFormData,
      }).unwrap();

      if (response.success) {
        toast.success(`Booking ${approval_status}d successfully`);
        setShowApprovalModal(false);
        setShowRejectModal(false);
      } else if (!response.success) {
        toast.error(response?.error);
      }
    } catch (error: any) {
      toast.error(
        error?.data
          ? error?.data?.error
          : "Something went wrong, please try again"
      );
    }
  };

  return (
    <Card
      className={`overflow-hidden rounded-lg w-full bg-white dark:bg-gray-800`}
    >
      <div
        className={`h-16 relative rounded-t-lg bg-secondary flex items-center justify-between p-2 px-4 `}
      >
        <p className="text-sm font-bold"> {rowData?.marketer_name}</p>
        <p className="text-[9px] bg-accent px-2 pb-1 pt-1.5 rounded-full">
          {rowData?.status}
        </p>
      </div>

      {/* Body (lifted up to overlap header) */}
      <CardContent className="flex flex-col items-start px-4 pb-6">
        <div className="mt-2 text-xs font-medium flex flex-col gap-1">
          {rowData?.customer && `Customer: ${rowData?.customer_name}`}
          {rowData?.lead && `Lead: ${rowData?.lead_name}`}
        </div>
        <div className="flex flex-col mt-4 gap-1 w-full">
          <div className="flex justify-between items-center w-full ">
            <p className="text-sm text-gray-500">Plots: {rowData?.plots}</p>
          </div>
          <p className="text-xs text-blue-500 font-bold">
            Created at: {formatDateTime(rowData?.creation_date)}
          </p>
          <p className="text-xs text-destructive font-bold">
            Deadline: {formatDateTime(rowData?.deadline)}
          </p>
        </div>

        {rowData?.status === "OPEN" &&
          (path_name == "mybookings" ? (
            <div className="flex flex-wrap items-center justify-center gap-4 mt-4 bg-blue-400/20 text-blue-400 w-full text-sm font-bold p-2 rounded ">
              Awaiting Approval
            </div>
          ) : (
            <div className="flex flex-wrap items-center justify-start gap-4 mt-4 w-full ">
              <ActionButton
                onClick={() => setShowApprovalModal(true)}
                icon={<CheckCircle className="h-4 w-4" />}
                iconPosition="left"
                variant="primary"
                className="text-white border !border-primary "
              >
                Approve
              </ActionButton>
              <ActionButton
                onClick={() => setShowRejectModal(true)}
                icon={<CheckCircle className="h-4 w-4" />}
                iconPosition="left"
                variant="outline"
                className="!text-destructive !border-destructive hover:!bg-destructive hover:!text-white "
              >
                Reject
              </ActionButton>

              <ConfirmModal
                isOpen={showApprovalModal}
                onOpenChange={setShowApprovalModal}
                title="Confirm Approval"
                variant="warning"
                message="Are you sure you want to approve this plot booking."
                confirmText="Approve"
                confirmVariant="default"
                cancelText="Cancel"
                onConfirm={() => handleApprove("approve")}
                isLoading={approving}
              />

              <BaseModal
                isOpen={showRejectModal}
                onOpenChange={setShowRejectModal}
                title="Confirm Rejection"
                description="Are you sure you want to reject this plot booking."
              >
                <div className="flex flex-col gap-4">
                  <p className="text-sm text-gray-500">
                    Please provide a reason for rejection
                  </p>
                  <textarea
                    className="border border-gray-300 rounded-md p-2"
                    rows={4}
                    placeholder="Enter reason for rejection"
                    name="reason"
                    value={reason}
                    onChange={(e) => setReason(e.target.value)}
                    required
                  ></textarea>
                  <div className="flex justify-end">
                    {approving ? (
                      <SpinnerTemp type="spinner-double" size="md" />
                    ) : (
                      <ActionButton
                        onClick={() => handleApprove("reject")}
                        variant="destructive"
                        className="!text-white !border-destructive hover:!bg-destructive/70 text-sm hover:!text-white "
                      >
                        Reject
                      </ActionButton>
                    )}
                  </div>
                </div>
              </BaseModal>
            </div>
          ))}

        <BaseModal
          isOpen={showUploadModal}
          onOpenChange={setShowUploadModal}
          size="lg"
          title="Upload Proof of Payment"
          description="Please upload proof of payment for this booking"
        >
          <div className="flex flex-col gap-4">
            <div className="mx-auto w-full">
              <Label>Attach Proof of Payment document*</Label>
              <label
                htmlFor="file-upload"
                className="flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-2xl cursor-pointer p-6 text-center hover:border-blue-500 transition duration-300"
              >
                <input
                  id="file-upload"
                  type="file"
                  accept="image/*,application/pdf"
                  onChange={handleFileChange}
                  className="hidden"
                />

                {previewUrl && !isPdf ? (
                  <img
                    src={previewUrl}
                    alt="Preview"
                    className="w-full h-64 object-contain rounded-xl shadow-md"
                  />
                ) : file && isPdf ? (
                  <div className="flex flex-col items-center">
                    <FileText className="h-12 w-12 text-gray-400 mb-2" />
                    <p className="text-gray-700 font-medium">PDF Selected</p>
                  </div>
                ) : (
                  <>
                    <Image className="h-12 w-12 text-gray-400 mb-2" />
                    <p className="text-gray-500">
                      Click to upload image or PDF
                    </p>
                  </>
                )}
              </label>

              {file && (
                <p className="mt-2 text-center text-sm text-gray-600">
                  Selected file:{" "}
                  <span className="font-medium">{file.name}</span>
                </p>
              )}
            </div>
            <div className="flex justify-end">
              {approving ? (
                <SpinnerTemp type="spinner-double" size="sm" />
              ) : (
                <ActionButton
                  variant="primary"
                  className="!text-white !border-primary hover:!bg-primary/70 text-sm"
                  onClick={() => handleUpload()}
                >
                  Upload
                </ActionButton>
              )}
            </div>
          </div>
        </BaseModal>

        {rowData?.status === "WAITING" && (
          <div className="flex flex-col w-full">
            <div className="flex flex-row flex-wrap items-start justify-start gap-2 mt-4  text-yellow-600 text-sm font-bold w-full p-2 rounded ">
              Expires in:
              <CountdownTimer targetDate={rowData?.deadline} />
            </div>

            <button
              onClick={() => setShowUploadModal(true)}
              className="text-sm my-2 text-primary hover:text-primary/80 bg-primary/20 font-bold w-full p-2 rounded"
            >
              Upload Proof Of Payment
            </button>
          </div>
        )}
        {rowData?.status === "TIMED" && (
          <div className="flex flex-wrap items-center justify-center gap-4 mt-4 bg-destructive/20 text-destructive w-full text-sm font-bold p-2 rounded ">
            Closed
          </div>
        )}
        {/* {rowData?.booking_type === "REJECTED" && ()} */}
      </CardContent>
    </Card>
  );
};

export default BookingApprovalCard;
