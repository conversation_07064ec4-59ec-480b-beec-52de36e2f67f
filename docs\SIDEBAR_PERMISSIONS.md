# Sidebar Permission System

This document explains how the permission-based sidebar access control works in the CRM system.

## Overview

The sidebar now uses permission codes 111-117 to control access to different sections based on the user's department permissions. Users will only see sidebar sections they have permission to access.

## Permission Codes

| Permission Code | Section     | Description                         |
| --------------- | ----------- | ----------------------------------- |
| 111             | Main        | Access Main on Sidebar              |
| 112             | Performance | Access Performance on the sidebar   |
| 113             | Teams       | Can Access teams on the sidebar     |
| 114             | Reports     | Can Access Reports on the sidebar   |
| 115             | Analytics   | Can access Analytics on the sidebar |
| 116             | Services    | Can access Services on the sidebar  |
| 117             | Admin       | Can access Admin on the sidebar     |

## How It Works

1. **User Authentication**: When a user logs in, their department information is stored in the auth state.

2. **Department Lookup**: The system finds the user's department ID based on their department name.

3. **Permission Fetching**: The system fetches all permissions assigned to the user's department using the `departments_2_departmentspermissions` API endpoint.

4. **Permission Checking**: The sidebar checks if the user has the required permission code for each section.

5. **Conditional Rendering**: Only sections with valid permissions are displayed in the sidebar.

## Implementation Details

### Key Files

- `src/hooks/useSidebarPermissions.ts` - Custom hook for permission checking
- `src/utils/permissionChecker.ts` - Utility functions for permission validation
- `src/app-components/sidebar/app-sidebar.tsx` - Updated sidebar component
- `src/pages/test/PermissionTest.tsx` - Test page for debugging permissions

### Custom Hook: `useSidebarPermissions`

```typescript
const {
  hasPermission, // Check specific permission code
  hasSidebarAccess, // Check sidebar section access
  accessibleSections, // Array of accessible sections
  userPermissionCodes, // Array of user's permission codes
  isLoading, // Loading state
  userDepartmentId, // User's department ID
} = useSidebarPermissions();
```

### Permission Checker Utilities

```typescript
import {
  hasPermissionCode,
  hasSidebarSectionAccess,
} from "@/utils/permissionChecker";

// Check if user has specific permission
const canAccessReports = hasPermissionCode(userPermissions, 114);

// Check sidebar section access
const canAccessTeams = hasSidebarSectionAccess(userPermissions, "TEAMS");
```

## Testing the Implementation

### Current Status (TEMPORARY SETUP)

**⚠️ IMPORTANT**: The system currently includes temporary test permissions for development. In the `useSidebarPermissions.ts` hook, there's a temporary section that adds permissions 111-114 (Main, Performance, Teams, Reports) for any logged-in user.

**To remove the temporary setup and use real permissions:**

1. Remove or comment out the "TEMPORARY" section in `src/hooks/useSidebarPermissions.ts` (lines ~148-162)
2. Ensure users have proper department permissions assigned via the admin interface

### Test Page

Visit `/test/permissions` to see a detailed breakdown of:

- Current user information
- Department details
- Permission codes
- Sidebar access status for each section

### Debug Component

Add the `PermissionDebugger` component to any page for real-time permission debugging:

```typescript
import PermissionDebugger from "@/components/debug/PermissionDebugger";

// Add to your component
<PermissionDebugger />;
```

### Testing Steps

1. **With Temporary Setup (Current)**:

   - All logged-in users should see Main, Performance, Teams, and Reports sections
   - Check browser console for debug logs (development mode only)

2. **After Removing Temporary Setup**:
   - Only users with proper department permissions will see sections
   - Use the admin interface to assign permission codes 111-117 to departments
   - Test with different users from different departments

## Managing Permissions

### For Administrators

1. **Assign Department Permissions**:

   - Go to Admin → Departments
   - Select a department
   - Click "Assign Permission"
   - Add permission codes 111-117 as needed

2. **View User Permissions**:
   - Go to the test page `/test/permissions`
   - Check which sections are accessible
   - Verify permission codes are correctly assigned

### For Developers

1. **Adding New Sidebar Sections**:

   - Add new permission code to `SIDEBAR_PERMISSIONS` in `useSidebarPermissions.ts`
   - Update `PERMISSION_DESCRIPTIONS` in `permissionChecker.ts`
   - Add section to `allSections` array in `app-sidebar.tsx`

2. **Custom Permission Checks**:

   ```typescript
   const { hasPermission } = useSidebarPermissions();

   // Check custom permission
   if (hasPermission(118)) {
     // Show custom feature
   }
   ```

## Fallback Behavior

- If a user has no permissions, the Main section (111) is shown by default
- If permission loading fails, all sections are hidden with a loading message
- The system gracefully handles missing department information

## API Endpoints Used

- `GET /users/departments` - Fetch all departments
- `GET /users/departments_2_departmentspermissions` - Fetch department permissions
- User permissions are also checked from the auth state (`user_permissions` array)

## Troubleshooting

### Common Issues

1. **Sidebar sections not showing**:

   - Check if user's department has the required permissions
   - Verify department name matches exactly in the database
   - Check the test page for permission details

2. **Permission loading errors**:

   - Ensure user is properly authenticated
   - Check network requests in browser dev tools
   - Verify API endpoints are accessible

3. **Department not found**:
   - Check if department exists in the database
   - Verify department name spelling and case sensitivity
   - Ensure user's department field is populated

### Debug Steps

1. Visit `/test/permissions` to see current state
2. Check browser console for errors
3. Verify API responses in Network tab
4. Use the `PermissionDebugger` component for real-time debugging

## Security Notes

- Permissions are checked on the frontend for UI display only
- Backend API endpoints should also validate permissions
- Never rely solely on frontend permission checks for security
- Always implement proper authorization on the server side
