import React, { useState, useEffect } from "react";
import { Home, Mail, Phone, Shield } from "lucide-react";
import { PrimaryButton } from "../buttons/buttons";

interface ServiceUnavailableProps {
  title?: string;
  description?: string;
  showContactInfo?: boolean;
}

const ServiceUnavailable: React.FC<ServiceUnavailableProps> = ({ 
  title = "Access Denied",
  description = "You do not have permission to access this resource.",
  showContactInfo = true 
}) => {
  const [isLoaded, setIsLoaded] = useState<boolean>(false);
  const [isError, setIsError] = useState<boolean>(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoaded(true);
    }, 800);

    return () => clearTimeout(timer);
  }, []);

  const serviceUnavailableSvgPath = "/maintenance.svg";

  const renderFallbackSVG = (): JSX.Element => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 1000 1000"
      className="w-full h-64"
    >
      <rect width="1000" height="1000" fill="#f3f4f6" opacity="0.3" />
      <circle cx="500" cy="400" r="150" fill="#ef4444" opacity="0.2" />
      <text
        x="500"
        y="420"
        fontSize="120"
        fontWeight="bold"
        textAnchor="middle"
        fill="#dc2626"
      >
        503
      </text>
      <text
        x="500"
        y="550"
        fontSize="32"
        textAnchor="middle"
        fill="#6b7280"
      >
        Service Unavailable
      </text>
      <Shield 
        x="450" 
        y="300" 
        width="100" 
        height="100" 
        fill="#dc2626" 
        opacity="0.3"
      />
    </svg>
  );

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50 px-4 py-12">
      <div className="max-w-lg w-full space-y-8 text-center">
        <div className="w-full h-64 relative">
          {!isLoaded && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-12 h-12 border-4 border-red-500 border-t-transparent rounded-full animate-spin"></div>
            </div>
          )}

          {isLoaded && !isError ? (
            <img
              src={serviceUnavailableSvgPath}
              alt="503 Service Unavailable Illustration"
              className="w-full h-full object-contain"
              onError={() => setIsError(true)}
            />
          ) : isLoaded && isError ? (
            renderFallbackSVG()
          ) : null}
        </div>

        <div className="space-y-4">
          <h1 className="text-4xl font-extrabold text-gray-900 tracking-tight sm:text-5xl">
            {title}
          </h1>

          <p className="text-lg text-gray-600 max-w-md mx-auto">
            {description}
          </p>

          {showContactInfo && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mt-6">
              <h3 className="text-lg font-semibold text-blue-900 mb-4 flex items-center justify-center">
                <Shield className="w-5 h-5 mr-2" />
                Need Access?
              </h3>
              <p className="text-blue-800 mb-4">
                If you believe you should have access to this resource, please contact:
              </p>
              <div className="space-y-3 text-sm">
                <div className="flex items-center justify-center text-blue-700">
                  <Mail className="w-4 h-4 mr-2" />
                  <span className="font-medium">Administrator:</span>
                  <a 
                    href="mailto:<EMAIL>" 
                    className="ml-2 underline hover:text-blue-900"
                  >
                    <EMAIL>
                  </a>
                </div>
                <div className="flex items-center justify-center text-blue-700">
                  <Phone className="w-4 h-4 mr-2" />
                  <span className="font-medium">ICT Systems Team:</span>
                  <a 
                    href="mailto:<EMAIL>" 
                    className="ml-2 underline hover:text-blue-900"
                  >
                    <EMAIL>
                  </a>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <a href="/">
            <PrimaryButton variant="primary" className="flex items-center">
              <Home className="w-4 h-4 mr-2" />
              Go to Dashboard
            </PrimaryButton>
          </a>
          <button 
            onClick={() => window.history.back()}
            className="px-6 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    </div>
  );
};

export default ServiceUnavailable;
