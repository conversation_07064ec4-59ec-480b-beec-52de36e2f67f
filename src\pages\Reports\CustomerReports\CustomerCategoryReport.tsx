import LazyModal, { TableColumn } from '@/components/reports/ReportsModal';
import { formatNumberWithCommas } from '@/utils/salesDataFormatter';
import { Badge } from '@/components/ui/badge';
import { useState } from 'react';

interface CustomerCategory {
    customer_id_id: string;
    customer_name: string;
    customer_no: string;
    customer_type: string;
    phone: string;
    email: string;
    Marketer: string;
    total_balance: number;
}

interface CustomerCategoryReportProps {
    isModalOpen: boolean;
    setIsModalOpen: (state: boolean) => void;
}

const CustomerCategoryReport = ({ isModalOpen, setIsModalOpen }: CustomerCategoryReportProps) => {
    const [selectedCategory, setSelectedCategory] = useState<'ACTIVE' | 'COMPLETED' | 'DROPPED'>('ACTIVE');

    // Define table columns with proper typing
    const columns: TableColumn<CustomerCategory>[] = [
        {
            key: 'customer_no',
            title: 'Customer No',
            render: (value: string) => (
                <span className="font-medium text-blue-600">{value}</span>
            )
        },
        {
            key: 'customer_name',
            title: 'Customer Name',
            render: (value: string) => (
                <span className="font-medium">{value}</span>
            )
        },
        {
            key: 'customer_type',
            title: 'Type',
            render: (value: string) => (
                <Badge variant="outline" className="text-xs">
                    {value}
                </Badge>
            )
        },
        {
            key: 'phone',
            title: 'Phone',
            render: (value: string) => (
                <span className="text-sm">{value}</span>
            )
        },
        {
            key: 'email',
            title: 'Email',
            render: (value: string) => (
                <span className="text-sm text-gray-600">{value}</span>
            )
        },
        {
            key: 'Marketer',
            title: 'Marketer',
            render: (value: string) => (
                <span className="text-sm">{value}</span>
            )
        },
        {
            key: 'total_balance',
            title: 'Total Balance',
            render: (value: number) => (
                <span className="font-medium text-green-600">
                    KES {formatNumberWithCommas(value.toString())}
                </span>
            )
        },
    ];

    const handleCloseModal = () => {
        setIsModalOpen(false);
    };

    const getCategoryColor = (category: string) => {
        switch (category) {
            case 'ACTIVE':
                return 'bg-green-100 text-green-800 border-green-200';
            case 'COMPLETED':
                return 'bg-blue-100 text-blue-800 border-blue-200';
            case 'DROPPED':
                return 'bg-red-100 text-red-800 border-red-200';
            default:
                return 'bg-gray-100 text-gray-800 border-gray-200';
        }
    };

    return (
        <LazyModal<CustomerCategory>
            isOpen={isModalOpen}
            onClose={handleCloseModal}
            title={
                <div className="flex items-center gap-3">
                    <span>Customer Category Report</span>
                    <div className="flex gap-2">
                        {(['ACTIVE', 'COMPLETED', 'DROPPED'] as const).map((category) => (
                            <button
                                key={category}
                                onClick={() => setSelectedCategory(category)}
                                className={`px-3 py-1 rounded-full text-xs font-medium border transition-colors ${
                                    selectedCategory === category
                                        ? getCategoryColor(category)
                                        : 'bg-gray-50 text-gray-600 border-gray-200 hover:bg-gray-100'
                                }`}
                            >
                                {category}
                            </button>
                        ))}
                    </div>
                </div>
            }
            url="/Customer-Category/"
            params={{ category: selectedCategory }}
            columns={columns}
            size="xl"
        />
    );
};

export default CustomerCategoryReport;
