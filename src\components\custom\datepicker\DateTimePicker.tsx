import * as React from "react"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"

interface DateTimePickerProps {
  label?: string
  value?: Date
  onChange?: (date: Date) => void
}

export function DateTimePicker({ label = "Pick date & time", value, onChange }: DateTimePickerProps) {
  const [selectedDate, setSelectedDate] = React.useState<Date | null>(value ?? null)
  const [time, setTime] = React.useState<string>("12:00")

  React.useEffect(() => {
    if (onChange && selectedDate) {
      // Merge date + time
      const [hours, minutes] = time.split(":").map(Number)
      const newDate = new Date(selectedDate)
      if (!isNaN(hours)) newDate.setHours(hours)
      if (!isNaN(minutes)) newDate.setMinutes(minutes)
      onChange(newDate)
    }
  }, [selectedDate, time, onChange])

  const displayValue = React.useMemo(() => {
    if (!selectedDate) return "Select date & time"
    const dateStr = selectedDate.toLocaleDateString()
    return `${dateStr} ${time}`
  }, [selectedDate, time])

  return (
    <div className="flex flex-col gap-1 w-full max-w-xs">
      {label && <label className="text-sm font-medium">{label}</label>}

      <Popover>
        <PopoverTrigger asChild>
          <Button variant="outline" className="justify-start text-left font-normal">
            {displayValue}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-2" align="start">
          <div className="flex flex-col items-start gap-2">
            <Calendar
              mode="single"
              selected={selectedDate ?? undefined}
              onSelect={(date) => setSelectedDate(date ?? null)}
              initialFocus
            />
            <div className="flex items-center gap-2 w-full">
              <label className="text-sm">Time:</label>
              <input
                type="time"
                value={time}
                onChange={(e) => setTime(e.target.value)}
                className="border border-gray-300 rounded px-2 py-1 text-sm w-full"
              />
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}
