import * as React from "react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { CalendarIcon } from "lucide-react";

interface DateRange {
  from?: Date;
  to?: Date;
}

interface DateRangePickerProps {
  label?: string;
  value?: DateRange;
  onChange?: (range: DateRange) => void;

  conjunction?: string;

  fromYear?: number;
  toYear?: number;
}

export function DateRangePicker({
  label = "Pick date range",
  value,
  onChange,
  conjunction = " ~ ",
  fromYear = 2020,
  toYear = 2030,
}: DateRangePickerProps) {
  const [dateRange, setDateRange] = React.useState<DateRange>(value || {});

  // Fire external callback whenever local state changes
  React.useEffect(() => {
    if (onChange) {
      onChange(dateRange);
    }
  }, [dateRange, onChange]);

  // Format button label with custom conjunction
  const formattedLabel = React.useMemo(() => {
    if (!dateRange) return "Select date range";
    const { from, to } = dateRange;
    if (from && to) {
      return `${from.toLocaleDateString()}${conjunction}${to.toLocaleDateString()}`;
    } else if (from) {
      return `${from.toLocaleDateString()}${conjunction}...`;
    }
    return "Select date range";
  }, [dateRange, conjunction]);

  return (
    <div className="flex flex-col gap-1 w-full max-w-xs">
      {label && <label className="text-sm font-medium">{label}</label>}

      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className="flex items-center justify-start text-left font-normal"
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {formattedLabel}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Calendar
            mode="range"
            selected={dateRange || { from: undefined, to: undefined }}
            onSelect={(range) => {
              // When the user clicks, DayPicker gives { from?: Date; to?: Date }
              // Just store it in state:
              setDateRange(range || { from: undefined, to: undefined });
            }}
            captionLayout="dropdown"
            fromYear={fromYear}
            toYear={toYear}
            // If you want to control how many months show side by side:
            numberOfMonths={1}
            // Additional props, e.g.:
            // disabled={(date) => date < new Date("2023-01-01")}
            initialFocus
          />
        </PopoverContent>
      </Popover>
    </div>
  );
}
