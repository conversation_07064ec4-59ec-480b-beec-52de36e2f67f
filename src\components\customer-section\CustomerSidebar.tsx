import React from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Star } from "lucide-react";
import { useNavigate } from "react-router-dom";
import {
  useGetCustomerTicketsQuery,
  useGetCustomerTicketDetailsQuery,
  useCreateCustomerTicketMutation,
  useUpdateCustomerTicketMutation,
  useDeleteCustomerTicketMutation,
  useGetCustomerTicketCategoriesQuery,
  useGetCustomerTicketSourcesQuery,
  useGetCustomerTicketMessagesQuery,
  useCreateCustomerTicketMessageMutation,
  useUpdateCustomerTicketMessageMutation,
  useDeleteCustomerTicketMessageMutation,
  useGetCustomerTicketAttachmentsQuery,
  useCreateCustomerTicketAttachmentMutation,
  useDeleteCustomerTicketAttachmentMutation,
  useGetCustomerTicketActionLogsQuery,
  useCreateCustomerTicketActionLogMutation,
  useCreateCustomerTicketCategoryMutation,
  useUpdateCustomerTicketCategoryMutation,
  useDeleteCustomerTicketCategoryMutation,
  useCreateCustomerTicketSourceMutation,
  useUpdateCustomerTicketSourceMutation,
  useDeleteCustomerTicketSourceMutation
} from "@/redux/slices/customerTicketsApiSlice";
import {
  useCreateTicketMutation
} from "@/redux/slices/tickets";
import {
  useGetEngagementsQuery,
  useCreateEngagementMutation,
  usePartialUpdateEngagementMutation,
  useDeleteEngagementMutation,
  useStartEngagementMutation,
  useCompleteEngagementMutation
} from "@/redux/slices/engagementsApiSlice";
import {
  useGetNotificationsQuery,
  useGetUnreadNotificationsQuery,
  useCreateNotificationMutation,
  usePartialUpdateNotificationMutation,
  useDeleteNotificationMutation,
  useMarkNotificationAsReadMutation
} from "@/redux/slices/notificationsApiSlice";
import {
  useGetRemindersQuery,
  useGetOverdueRemindersQuery,
  useGetUpcomingRemindersQuery,
  useCreateReminderMutation,
  useUpdateReminderMutation,
  useDeleteReminderMutation,
  useCompleteReminderMutation,
  useSnoozeReminderMutation
} from "@/redux/slices/remindersApiSlice";
import {
  useGetNotesQuery,
  useCreateNoteMutation,
  usePartialUpdateNoteMutation,
  useDeleteNoteMutation,
  useToggleNotePinMutation
} from "@/redux/slices/notesApiSlice";
import {
  useGetFlagsQuery,
  useCreateFlagMutation,
  usePartialUpdateFlagMutation,
  useDeleteFlagMutation,
  useResolveFlagMutation
} from "@/redux/slices/flagsApiSlice";
import TicketsModals from "./TicketsModals";
import {
  CustomerSidebarProps,
  EngagementFormData,
  ExpandedSections,
  BadgeRenderer,
  TicketFormData,
  TicketItem,
  TicketMessage,
  TicketAttachment,
  TicketCategory,
  TicketSource,
  TicketMessageFormData,
  TicketCategoryFormData,
  TicketSourceFormData,
  TicketActionLogFormData,
  TicketActionLog,
  DateFormatter
} from "./types";
import EngagementsModals from "./EngagementsModals";

// Section components
import RemindersSection from "./RemindersSection";
import TicketsSection from "./TicketsSection";
import NotesSection from "./NotesSection";
import FlagsSection from "./FlagsSection";
import EngagementsSection from "./EngagementsSection";
import NotificationsSection from "./NotificationsSection";

import NotificationsModals from "./NotificationsModals";
import FlagsModals from "./FlagsModal";
import NotesModals from "./NotesModals";
import RemindersModals from "./RemindersModals";

const CustomerSidebar: React.FC<CustomerSidebarProps> = ({
  className,
  customerNo,
  leadfileNo,
  salesCardCustomerId,
  entityType = "customer",
  entityId = "",
  currentUser,
  initialExpandedSection
}) => {
  const navigate = useNavigate();
  const [expandedSections, setExpandedSections] = React.useState<ExpandedSections>(() => {
    // Default state (collapsed)
    const initialState: ExpandedSections = {
      reminders: true,
      tickets: true,
      notes: true,
      flags: true,
      engagements: true,
      notifications: true,
    };

    // Expand all sections if initialExpandedSection is provided
    if (initialExpandedSection) {
      Object.keys(initialState).forEach(key => {
        initialState[key as keyof ExpandedSections] = true;
      });
    } else {
      // Default expanded sections
      initialState.reminders = true;
      initialState.tickets = true;
    }

    return initialState;
  });

  const [activeModal, setActiveModal] = React.useState<string | null>(null);
  const [editingTicket, setEditingTicket] = React.useState<TicketItem | null>(null);
  const [selectedTicket, setSelectedTicket] = React.useState<TicketItem | null>(null);
  const [formErrors, setFormErrors] = React.useState<Record<string, string>>({});
  const [successMessage, setSuccessMessage] = React.useState<string>("");
  const [editingEngagement, setEditingEngagement] = React.useState<any>(null);
  const [editingFlag, setEditingFlag] = React.useState<any>(null);
  const [editingNotification, setEditingNotification] = React.useState<any>(null);
  const [editingReminder, setEditingReminder] = React.useState<any>(null);
  const [editingNote, setEditingNote] = React.useState<any>(null);

  // Tickets form state
  const [ticketForm, setTicketForm] = React.useState<TicketFormData>({
    title: "",
    description: "",
    customer: entityType === "customer" ? customerNo || "" : "",
    user: currentUser?.id || "", // Set current user as default assignee
    category: undefined,
    prospect: entityType === "prospect" ? customerNo : undefined,
    priority: "medium",
    status: "open",
    source: undefined,
    actions: [], // Initialize as empty array
    attachments: [], // Initialize as empty array
    messages: [], // Initialize as empty array
  });

  // File attachment state for tickets
  const [selectedFile, setSelectedFile] = React.useState<File | null>(null);

  // Ticket message form state
  const [messageForm, setMessageForm] = React.useState<TicketMessageFormData>({
    ticket: 0,
    sender: currentUser?.id || "",
    message: "",
  });

  // Category form state
  const [categoryForm, setCategoryForm] = React.useState<TicketCategoryFormData>({
    name: "",
    description: "",
  });

  // Source form state
  const [sourceForm, setSourceForm] = React.useState<TicketSourceFormData>({
    name: "",
    description: "",
  });

  // Action log form state
  const [actionLogForm, setActionLogForm] = React.useState<TicketActionLogFormData>({
    ticket: 0,
    action: "created",
    performed_by: undefined,
    comment: "",
  });

  // Additional state for editing
  const [editingCategory, setEditingCategory] = React.useState<TicketCategory | null>(null);
  const [editingSource, setEditingSource] = React.useState<TicketSource | null>(null);
  const [editingMessage, setEditingMessage] = React.useState<TicketMessage | null>(null);



  // Form state for engagements
  const [engagementForm, setEngagementForm] = React.useState<EngagementFormData>({
    engagement_type: "",
    subject: "",
    description: "",
    status: "Scheduled",
    outcome: "",
    scheduled_at: "",
    assigned_to: "",
    notes: "",
    follow_up_required: false,
    follow_up_date: "",
    entity_type: entityType, // Use the actual entityType prop instead of hardcoding
    entity_id: entityId,
    customer_no: entityType === "customer" ? customerNo : "",
    // prospect_id: entityType === "prospect" ? customerNo : "",
    lead_file_no: entityType === "leadfile" ? leadfileNo : "",
    client_status: entityType === "customer" ? "Customer" : entityType === "prospect" ? "Prospect" : "Lead File",
  });
  // Form state for notes
  const [noteForm, setNoteForm] = React.useState({
    note_type: "General",
    title: "",
    content: "",
    is_private: false,
    is_pinned: false,
    tags: "",
    reminder_date: "",
    is_active: true,
  });

  // Form state for flags
  const [flagForm, setFlagForm] = React.useState({
    entity_type: entityType,
    entity_id: entityId,
    customer_no: entityType === "customer" ? customerNo : "",
    prospect_id: entityType === "prospect" ? customerNo : "",
    lead_file_no: entityType === "leadfile" ? customerNo : "",
    client_status: entityType === "customer" ? "Customer" : entityType === "prospect" ? "Prospect" : "Lead File",
    flag_type: "",
    title: "",
    description: "",
    severity: "Info",
    status: "Active",
    assigned_to: "",
    resolution_notes: "",
  });
  // Form state for notifications
  const [notificationForm, setNotificationForm] = React.useState({
    entity_type: entityType,
    entity_id: entityId,
    customer_no: entityType === "customer" ? customerNo : "",
    prospect_id: entityType === "prospect" ? customerNo : "",
    lead_file_no: entityType === "leadfile" ? customerNo : "",
    client_status: entityType === "customer" ? "Customer" : entityType === "prospect" ? "Prospect" : "Lead File",
    notification_type: "Info",
    priority: "Normal",
    title: "",
    message: "",
    recipient: "",
    sender: "",
    expires_at: "",
    action_url: "",
    is_active: true,
  });

  // Form state for reminders
  const [reminderForm, setReminderForm] = React.useState({
    entity_type: entityType,
    entity_id: entityId,
    customer_no: entityType === "customer" ? customerNo : "",
    prospect_id: entityType === "prospect" ? customerNo : "",
    lead_file_no: entityType === "leadfile" ? leadfileNo : "",
    client_status: entityType === "customer" ? "Customer" : entityType === "prospect" ? "Prospect" : "Lead File",
    reminder_type: "General",
    priority: "Normal",
    title: "",
    description: "",
    remind_at: "",
    advance_notice_minutes: 15,
    repeat_pattern: "None",
    tags: "",
    is_active: true,
  });


  // API calls for tickets - Fetch all tickets and filter client-side
  // The API doesn't support customer/prospect filtering, so we fetch all and filter locally
  const {
    data: allTicketsData,
    isLoading: ticketsLoading,
    isError: ticketsError,
    refetch: refetchTickets,
  } = useGetCustomerTicketsQuery({});

  // Client-side filtering based on entity type
  const ticketsData = React.useMemo(() => {
    if (!allTicketsData?.results) return allTicketsData;

    const filteredResults = allTicketsData.results.filter((ticket: any) => {
      // For customer cards: filter by customer field
      if (customerNo && ticket.customer === customerNo) {
        return true;
      }
      // For sales/lead cards: filter by sales field (should match lead_file_no)
      if (leadfileNo && ticket.sales === leadfileNo) {
        return true;
      }
      return false;
    });


    console.log("Filtered Tickets:", filteredResults);

    return {
      ...allTicketsData,
      results: filteredResults,
      count: filteredResults.length,
      total_data: filteredResults.length,
    };
  }, [allTicketsData, customerNo, leadfileNo, entityType]);

  const [createTicket, { isLoading: isCreatingTicket }] = useCreateTicketMutation();
  const [updateTicket, { isLoading: isUpdatingTicket }] = useUpdateCustomerTicketMutation();
  const [deleteTicket, { isLoading: isDeletingTicket }] = useDeleteCustomerTicketMutation();

  // API calls for ticket categories and sources
  const { data: categoriesData } = useGetCustomerTicketCategoriesQuery({});
  const { data: sourcesData } = useGetCustomerTicketSourcesQuery({});

  // API calls for ticket messages
  const {
    data: ticketMessagesData,
    isLoading: messagesLoading,
    refetch: refetchMessages,
  } = useGetCustomerTicketMessagesQuery(
    { ticket: selectedTicket?.id || 0 },
    { skip: !selectedTicket }
  );

  const [createMessage, { isLoading: isSendingMessage }] = useCreateCustomerTicketMessageMutation();
  const [updateMessage] = useUpdateCustomerTicketMessageMutation();
  const [deleteMessage] = useDeleteCustomerTicketMessageMutation();

  // API calls for ticket attachments
  const [createAttachment, { isLoading: isUploadingAttachment }] = useCreateCustomerTicketAttachmentMutation();
  const [deleteAttachment] = useDeleteCustomerTicketAttachmentMutation();

  // API calls for action logs
  const {
    data: actionLogsData,
    refetch: refetchActionLogs,
  } = useGetCustomerTicketActionLogsQuery(
    { ticket: selectedTicket?.id || 0 },
    { skip: !selectedTicket }
  );
  const [createActionLog] = useCreateCustomerTicketActionLogMutation();

  // API calls for categories CRUD
  const [createCategory, { isLoading: isCreatingCategory }] = useCreateCustomerTicketCategoryMutation();
  const [updateCategory] = useUpdateCustomerTicketCategoryMutation();
  const [deleteCategory] = useDeleteCustomerTicketCategoryMutation();

  // API calls for sources CRUD
  const [createSource, { isLoading: isCreatingSource }] = useCreateCustomerTicketSourceMutation();
  const [updateSource] = useUpdateCustomerTicketSourceMutation();
  const [deleteSource] = useDeleteCustomerTicketSourceMutation();

  // API calls for ticket attachments
  const {
    data: ticketAttachmentsData,
    isLoading: attachmentsLoading,
  } = useGetCustomerTicketAttachmentsQuery(
    { ticket: selectedTicket?.id || 0 },
    { skip: !selectedTicket }
  );

  // API calls for engagements - Build parameters based on entity type and available data
  let engagementsParams: any = {};

  if (entityType === "customer" && customerNo) {
    engagementsParams = { customer_no: customerNo };
  } else if (entityType === "leadfile" && leadfileNo) {
    engagementsParams = { lead_file_no: leadfileNo };
  } else if (entityType === "prospect" && customerNo) {
    // For prospects, we might use prospect_id or customer_no depending on API
    engagementsParams = { prospect_id: customerNo };
  }



  const {
    data: engagementsData,
    isLoading: engagementsLoading,
    isError: engagementsError,
    error: engagementsErrorDetails,
    refetch: refetchEngagements,
  } = useGetEngagementsQuery(engagementsParams, {
    skip: Object.keys(engagementsParams).length === 0,
    refetchOnMountOrArgChange: true // Force fresh data on mount
  });



  const [createEngagement, { isLoading: isCreatingEngagement }] = useCreateEngagementMutation();
  const [updateEngagement] = usePartialUpdateEngagementMutation();
  const [deleteEngagement, { isLoading: isDeletingEngagement }] = useDeleteEngagementMutation();
  const [startEngagement] = useStartEngagementMutation();
  const [completeEngagement] = useCompleteEngagementMutation();



  // API calls for flags - Build parameters based on entity type and available data
  let flagsParams: any = {};

  if (entityType === "customer" && customerNo) {
    flagsParams = { customer_no: customerNo };
  } else if (entityType === "leadfile" && leadfileNo) {
    flagsParams = { lead_file_no: leadfileNo };
  } else if (entityType === "prospect" && customerNo) {
    flagsParams = { prospect_id: customerNo };
  }

  const {
    data: flagsData,
    isLoading: flagsLoading,
    isError: flagsError,
    error: flagsErrorDetails,
    refetch: refetchFlags,
  } = useGetFlagsQuery(flagsParams, {
    skip: Object.keys(flagsParams).length === 0,
    refetchOnMountOrArgChange: true
  });

  const [createFlag, { isLoading: isCreatingFlag }] = useCreateFlagMutation();
  const [updateFlag] = usePartialUpdateFlagMutation();
  const [deleteFlag, { isLoading: isDeletingFlag }] = useDeleteFlagMutation();
  const [resolveFlag] = useResolveFlagMutation();

  // API calls for notifications - Build parameters based on entity type and available data
  let notificationsParams: any = {};

  if (entityType === "customer" && customerNo) {
    notificationsParams = { customer_no: customerNo };
  } else if (entityType === "leadfile" && leadfileNo) {
    notificationsParams = { lead_file_no: leadfileNo };
  } else if (entityType === "prospect" && customerNo) {
    notificationsParams = { prospect_id: customerNo };
  }

  const {
    data: notificationsData,
    isLoading: notificationsLoading,
    isError: notificationsError,
    error: notificationsErrorDetails,
    refetch: refetchNotifications,
  } = useGetNotificationsQuery(notificationsParams, {
    skip: Object.keys(notificationsParams).length === 0,
    refetchOnMountOrArgChange: true
  });

  const {
    data: unreadNotificationsData,
    isLoading: unreadNotificationsLoading,
    isError: unreadNotificationsError,
    refetch: refetchUnreadNotifications,
  } = useGetUnreadNotificationsQuery(notificationsParams, {
    skip: Object.keys(notificationsParams).length === 0,
    refetchOnMountOrArgChange: true
  });

  const [createNotification, { isLoading: isCreatingNotification }] = useCreateNotificationMutation();
  const [updateNotification] = usePartialUpdateNotificationMutation();
  const [deleteNotification, { isLoading: isDeletingNotification }] = useDeleteNotificationMutation();
  const [markAsRead] = useMarkNotificationAsReadMutation();

  // API calls for reminders - Build parameters based on entity type and available data
  let remindersParams: any = {};

  if (entityType === "customer" && customerNo) {
    remindersParams = { customer_no: customerNo };
  } else if (entityType === "leadfile" && leadfileNo) {
    remindersParams = { lead_file_no: leadfileNo };
  } else if (entityType === "prospect" && customerNo) {
    remindersParams = { prospect_id: customerNo };
  }

  const {
    data: remindersData,
    isLoading: remindersLoading,
    isError: remindersError,
    error: remindersErrorDetails,
    refetch: refetchReminders,
  } = useGetRemindersQuery(remindersParams, {
    skip: Object.keys(remindersParams).length === 0,
    refetchOnMountOrArgChange: true
  });

  const {
    data: overdueRemindersData,
    isLoading: overdueRemindersLoading,
    isError: overdueRemindersError,
    refetch: refetchOverdueReminders,
  } = useGetOverdueRemindersQuery(remindersParams, {
    skip: Object.keys(remindersParams).length === 0,
    refetchOnMountOrArgChange: true
  });

  const {
    data: upcomingRemindersData,
    isLoading: upcomingRemindersLoading,
    isError: upcomingRemindersError,
    refetch: refetchUpcomingReminders,
  } = useGetUpcomingRemindersQuery(remindersParams, {
    skip: Object.keys(remindersParams).length === 0,
    refetchOnMountOrArgChange: true
  });

  const [createReminder, { isLoading: isCreatingReminder }] = useCreateReminderMutation();
  const [updateReminder] = useUpdateReminderMutation();
  const [deleteReminder, { isLoading: isDeletingReminder }] = useDeleteReminderMutation();
  const [completeReminder] = useCompleteReminderMutation();
  const [snoozeReminder] = useSnoozeReminderMutation();

  // Debug effect to track reminder data changes
  React.useEffect(() => {
    console.log("🔄 Reminder data changed:");
    console.log("- Reminders Data:", remindersData);
    console.log("- Loading:", remindersLoading);
    console.log("- Error:", remindersError);
    console.log("- Items count:", reminderItems?.length || 0);
  }, [remindersData, remindersLoading, remindersError]);

  // Debug effect to track component mount
  React.useEffect(() => {
    console.log("🚀 CustomerSidebar mounted with reminder system");
    console.log("- Entity Type:", entityType);
    console.log("- Customer No:", customerNo);
    console.log("- Lead File No:", leadfileNo);
  }, []);

  // API calls for notes - Build parameters based on entity type and available data
  let notesParams: any = {};

  if (entityType === "customer" && customerNo) {
    notesParams = { customer_no: customerNo };
  } else if (entityType === "leadfile" && leadfileNo) {
    notesParams = { lead_file_no: leadfileNo };
  } else if (entityType === "prospect" && customerNo) {
    notesParams = { prospect_id: customerNo };
  }



  const {
    data: notesData,
    isLoading: notesLoading,
    isError: notesError,
    error: notesErrorDetails,
    refetch: refetchNotes,
  } = useGetNotesQuery(notesParams, {
    skip: Object.keys(notesParams).length === 0,
    refetchOnMountOrArgChange: true
  });



  const [createNote, { isLoading: isCreatingNote }] = useCreateNoteMutation();
  const [updateNote] = usePartialUpdateNoteMutation();
  const [deleteNote, { isLoading: isDeletingNote }] = useDeleteNoteMutation();
  const [toggleNotePin] = useToggleNotePinMutation();

  // Ticket handlers
  const handleTicketFormChange = (field: keyof TicketFormData, value: string | number) => {
    setTicketForm(prev => ({
      ...prev,
      [field]: value
    }));

    if (formErrors[field]) {
      setFormErrors(prev => ({
        ...prev,
        [field]: ""
      }));
    }
  };

  const validateTicketForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!ticketForm.title.trim()) {
      errors.title = "Title is required";
    }
    if (!ticketForm.description.trim()) {
      errors.description = "Description is required";
    }

    // Check if at least one entity is specified (customer, prospect, or sales)
    const hasCustomer = ticketForm.customer.trim();
    const hasProspect = ticketForm.prospect?.trim();
    const hasSales = entityType === "leadfile" && leadfileNo;

    if (!hasCustomer && !hasProspect && !hasSales) {
      errors.customer = "Customer, prospect, or sales entity is required";
    }

    if (!ticketForm.user?.trim()) {
      errors.user = "User assignment is required";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleCreateTicket = async () => {
    if (!validateTicketForm()) return;

    try {
      // Prepare ticket data using FormData for file upload support
      const formData = new FormData();

      // Add basic fields
      formData.append("title", ticketForm.title);
      formData.append("description", ticketForm.description);
      formData.append("user", ticketForm.user || currentUser?.id || "");
      formData.append("priority", ticketForm.priority);
      formData.append("status", ticketForm.status);

      // Add optional fields if they exist
      if (ticketForm.category) {
        formData.append("category", ticketForm.category.toString());
      }
      if (ticketForm.source) {
        formData.append("source", ticketForm.source.toString());
      }

      // Add entity-specific fields based on entity type
      if (entityType === "customer") {
        formData.append("customer", customerNo || "");
      } else if (entityType === "leadfile") {
        formData.append("sales", leadfileNo || "");
      } else if (entityType === "prospect") {
        formData.append("prospect", customerNo || "");
      }

      // Add file if selected
      if (selectedFile) {
        formData.append("file", selectedFile);
      }

      // Debug: Log the FormData contents
      console.log("=== Ticket Creation Debug ===");
      console.log("Entity Type:", entityType);
      console.log("Customer No:", customerNo);
      console.log("Lead File No:", leadfileNo);
      console.log("Current User:", currentUser);
      console.log("Ticket Form:", ticketForm);

      // Log FormData contents
      console.log("FormData contents:");
      for (let [key, value] of formData.entries()) {
        console.log(`${key}:`, value);
      }

      await createTicket(formData).unwrap();
      setSuccessMessage("Ticket created successfully!");
      refetchTickets();

      // Reset form and file
      setSelectedFile(null);

      setTimeout(() => {
        closeModal();
      }, 1500);
    } catch (error: any) {
      console.error("Failed to create ticket - Full error:", error);
      console.error("Error data:", error?.data);
      console.error("Error status:", error?.status);
      console.error("Error message:", error?.message);

      let errorMessage = "Failed to create ticket. Please try again.";
      if (error?.data?.message) {
        errorMessage = error.data.message;
      } else if (error?.data?.error) {
        errorMessage = error.data.error;
      } else if (error?.data) {
        errorMessage = JSON.stringify(error.data);
      }

      setFormErrors({
        submit: errorMessage
      });
    }
  };

  const handleUpdateTicket = async () => {
    if (!validateTicketForm() || !editingTicket) return;

    try {
      // Prepare ticket data based on entity type
      const ticketData = {
        ...ticketForm,
        customer: entityType === "customer" ? customerNo || "" : "",
        sales: entityType === "leadfile" ? leadfileNo || "" : undefined,
        prospect: entityType === "prospect" ? customerNo : undefined,
      };

      await updateTicket({
        ticketId: editingTicket.id,
        data: ticketData
      }).unwrap();
      setSuccessMessage("Ticket updated successfully!");
      refetchTickets();

      setTimeout(() => {
        closeModal();
      }, 1500);
    } catch (error: any) {
      console.error("Failed to update ticket:", error);
      setFormErrors({
        submit: error?.data?.message || "Failed to update ticket. Please try again."
      });
    }
  };

  const handleDeleteTicket = async (ticketId: number) => {
    if (!confirm("Are you sure you want to delete this ticket? This action cannot be undone.")) {
      return;
    }

    try {
      await deleteTicket(ticketId).unwrap();
      refetchTickets();
      setSuccessMessage("Ticket deleted successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (error) {
      console.error("Failed to delete ticket:", error);
    }
  };

  const handleMessageFormChange = (field: keyof TicketMessageFormData, value: string | number) => {
    setMessageForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSendMessage = async () => {
    if (!messageForm.message.trim() || !selectedTicket) return;

    try {
      await createMessage({
        ...messageForm,
        ticket: selectedTicket.id,
        sender: currentUser?.id || ""
      }).unwrap();
      setMessageForm(prev => ({ ...prev, message: "" }));
      refetchMessages();
      setSuccessMessage("Message sent successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (error) {
      console.error("Failed to send message:", error);
    }
  };

  const openTicketDetails = (ticket: TicketItem) => {
    // Navigate to the ticket details page instead of opening modal
    navigate(`/ticketing/${ticket.id}`);
  };

  const openEditTicket = (ticket: TicketItem) => {
    setEditingTicket(ticket);
    setTicketForm({
      title: ticket.title,
      description: ticket.description,
      customer: ticket.customer,
      user: ticket.user || currentUser?.id || "",
      category: ticket.category,
      prospect: ticket.prospect,
      priority: ticket.priority,
      status: ticket.status,
      source: ticket.source,
      actions: ticket.actions || [],
      attachments: ticket.attachments || [],
      messages: ticket.messages || [],
    });
    openModal("editTicket");
  };

  const navigateToTicket = (ticketId: number) => {
    navigate(`/ticketing/${ticketId}`);
  };

  // Category handlers
  const handleCategoryFormChange = (field: keyof TicketCategoryFormData, value: string) => {
    setCategoryForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleCreateCategory = async () => {
    try {
      await createCategory(categoryForm).unwrap();
      setSuccessMessage("Category created successfully!");
      refetchTickets();
      setTimeout(() => closeModal(), 1500);
    } catch (error: any) {
      console.error("Failed to create category:", error);
      setFormErrors({
        submit: error?.data?.message || "Failed to create category. Please try again."
      });
    }
  };

  const handleUpdateCategory = async () => {
    if (!editingCategory) return;
    try {
      await updateCategory({
        categoryId: editingCategory.id,
        data: categoryForm
      }).unwrap();
      setSuccessMessage("Category updated successfully!");
      refetchTickets();
      setTimeout(() => closeModal(), 1500);
    } catch (error: any) {
      console.error("Failed to update category:", error);
      setFormErrors({
        submit: error?.data?.message || "Failed to update category. Please try again."
      });
    }
  };

  const handleDeleteCategory = async (categoryId: number) => {
    if (!confirm("Are you sure you want to delete this category?")) return;
    try {
      await deleteCategory(categoryId).unwrap();
      setSuccessMessage("Category deleted successfully!");
      refetchTickets();
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (error) {
      console.error("Failed to delete category:", error);
    }
  };

  // Source handlers
  const handleSourceFormChange = (field: keyof TicketSourceFormData, value: string) => {
    setSourceForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleCreateSource = async () => {
    try {
      await createSource(sourceForm).unwrap();
      setSuccessMessage("Source created successfully!");
      refetchTickets();
      setTimeout(() => closeModal(), 1500);
    } catch (error: any) {
      console.error("Failed to create source:", error);
      setFormErrors({
        submit: error?.data?.message || "Failed to create source. Please try again."
      });
    }
  };

  const handleUpdateSource = async () => {
    if (!editingSource) return;
    try {
      await updateSource({
        sourceId: editingSource.id,
        data: sourceForm
      }).unwrap();
      setSuccessMessage("Source updated successfully!");
      refetchTickets();
      setTimeout(() => closeModal(), 1500);
    } catch (error: any) {
      console.error("Failed to update source:", error);
      setFormErrors({
        submit: error?.data?.message || "Failed to update source. Please try again."
      });
    }
  };

  const handleDeleteSource = async (sourceId: number) => {
    if (!confirm("Are you sure you want to delete this source?")) return;
    try {
      await deleteSource(sourceId).unwrap();
      setSuccessMessage("Source deleted successfully!");
      refetchTickets();
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (error) {
      console.error("Failed to delete source:", error);
    }
  };

  // Message handlers
  const handleDeleteMessage = async (messageId: number) => {
    if (!confirm("Are you sure you want to delete this message?")) return;
    try {
      await deleteMessage(messageId).unwrap();
      refetchMessages();
      setSuccessMessage("Message deleted successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (error) {
      console.error("Failed to delete message:", error);
    }
  };

  const handleEditMessage = (message: TicketMessage) => {
    setEditingMessage(message);
    setMessageForm({
      ticket: message.ticket,
      sender: message.sender,
      message: message.message,
    });
    openModal("editMessage");
  };

  // Attachment handlers
  const handleUploadAttachment = async (file: File) => {
    if (!selectedTicket) return;

    const formData = new FormData();
    formData.append('ticket', selectedTicket.id.toString());
    formData.append('uploaded_by', currentUser?.id || '');
    formData.append('file', file);

    try {
      await createAttachment(formData).unwrap();
      setSuccessMessage("Attachment uploaded successfully!");
      // Refetch attachments
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (error) {
      console.error("Failed to upload attachment:", error);
    }
  };

  const handleDeleteAttachment = async (attachmentId: number) => {
    if (!confirm("Are you sure you want to delete this attachment?")) return;
    try {
      await deleteAttachment(attachmentId).unwrap();
      setSuccessMessage("Attachment deleted successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (error) {
      console.error("Failed to delete attachment:", error);
    }
  };

  // Action log handlers
  const handleCreateActionLog = async (action: string, comment?: string) => {
    if (!selectedTicket) return;

    const actionLogData = {
      ticket: selectedTicket.id,
      action: action as TicketActionLogFormData['action'],
      performed_by: parseInt(currentUser?.id || '0'),
      comment: comment || '',
    };

    try {
      await createActionLog(actionLogData).unwrap();
      refetchActionLogs();
    } catch (error) {
      console.error("Failed to create action log:", error);
    }
  };


  const toggleSection = (section: keyof ExpandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  const openModal = (modalName: string) => {
    console.log(`🔓 Opening modal: ${modalName}`);
    if (modalName.includes("reminder") || modalName.includes("Reminder")) {
      console.log("🎯 Reminder modal being opened");
      console.log("Current reminder form state:", reminderForm);
    }
    setActiveModal(modalName);
    setFormErrors({});
    setSuccessMessage("");
  };

  const closeModal = () => {
    console.log(`🔒 Closing modal: ${activeModal}`);
    if (activeModal && (activeModal.includes("reminder") || activeModal.includes("Reminder"))) {
      console.log("🎯 Reminder modal being closed");
    }
    setActiveModal(null);
    setEditingTicket(null);
    setSelectedTicket(null);
    setEditingEngagement(null);
    setEditingFlag(null);
    setEditingNotification(null);
    setEditingReminder(null);
    setEditingNote(null);
    resetForms();
  };

  const resetForms = () => {
    setTicketForm({
      title: "",
      description: "",
      customer: entityType === "customer" ? customerNo || "" : "",
      user: currentUser?.id || "",
      category: undefined,
      prospect: entityType === "prospect" ? customerNo : leadfileNo || undefined,
      priority: "medium",
      status: "open",
      source: undefined,
      actions: [], // Initialize as empty array
      attachments: [], // Initialize as empty array
      messages: [], // Initialize as empty array
    });

    setMessageForm({
      ticket: 0,
      sender: currentUser?.id || "",
      message: "",
    });

    setCategoryForm({
      name: "",
      description: "",
    });

    setSourceForm({
      name: "",
      description: "",
    });

    setActionLogForm({
      ticket: 0,
      action: "created",
      performed_by: undefined,
      comment: "",
    });

    setEngagementForm({
      engagement_type: "",
      subject: "",
      description: "",
      status: "Scheduled",
      outcome: "",
      scheduled_at: "",
      assigned_to: "",
      notes: "",
      follow_up_required: false,
      follow_up_date: "",
      entity_type: entityType, // Use the actual entityType prop
      entity_id: entityId,
      customer_no: entityType === "customer" ? customerNo : "",
      prospect_id: entityType === "prospect" ? customerNo : "",
      lead_file_no: entityType === "leadfile" ? customerNo : "",
      client_status: entityType === "customer" ? "Customer" : entityType === "prospect" ? "Prospect" : "Lead File",
    });
    setNotificationForm({
      entity_type: entityType,
      entity_id: entityId,
      customer_no: entityType === "customer" ? customerNo : "",
      prospect_id: entityType === "prospect" ? customerNo : "",
      lead_file_no: entityType === "leadfile" ? customerNo : "",
      client_status: entityType === "customer" ? "Customer" : entityType === "prospect" ? "Prospect" : "Lead File",
      notification_type: "Info",
      priority: "Normal",
      title: "",
      message: "",
      recipient: "",
      sender: "",
      expires_at: "",
      action_url: "",
      is_active: true,

    });

    setNoteForm({
      note_type: "General",
      title: "",
      content: "",
      is_private: false,
      is_pinned: false,
      tags: "",
      reminder_date: "",
      is_active: true,
    });


    setFormErrors({});
    setSuccessMessage("");
  };



  const validateEngagementForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!engagementForm.engagement_type) {
      errors.engagement_type = "Engagement type is required";
    }
    if (!engagementForm.subject.trim()) {
      errors.subject = "Subject is required";
    }
    if (!engagementForm.description.trim()) {
      errors.description = "Description is required";
    }
    if (!engagementForm.scheduled_at) {
      errors.scheduled_at = "Scheduled date/time is required";
    }
    if (!entityId) {
      errors.entity_id = "Entity ID is required";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleEngagementFormChange = (field: keyof EngagementFormData, value: string | boolean | number) => {
    setEngagementForm(prev => ({
      ...prev,
      [field]: value
    }));

    if (formErrors[field]) {
      setFormErrors(prev => ({
        ...prev,
        [field]: ""
      }));
    }
  };

  const handleCreateEngagement = async () => {
    if (!validateEngagementForm()) return;
    console.log("Form data being sent:", engagementForm);

    try {
      // Format the data before sending
      const formattedData = {
        ...engagementForm,
        // Convert follow_up_date from YYYY-MM-DD to YYYY-MM-DDTHH:MM:SS format
        follow_up_date: engagementForm.follow_up_date
          ? `${engagementForm.follow_up_date}T00:00:00`
          : undefined,
        // Remove empty/undefined fields
        outcome: engagementForm.outcome || undefined,
        duration_minutes: engagementForm.duration_minutes || undefined,
        assigned_to: engagementForm.assigned_to || undefined,
        notes: engagementForm.notes || undefined,
      };

      await createEngagement(formattedData).unwrap();
      setSuccessMessage("Engagement created successfully!");
      console.log("Engagement created:", formattedData);
      refetchEngagements();

      setTimeout(() => {
        closeModal();
      }, 1500);
    } catch (error: any) {
      console.error("Failed to create engagement:", error);
      setFormErrors({
        submit: error?.data?.message || "Failed to create engagement. Please try again."
      });
    }
  };

  const handleUpdateEngagement = async () => {
    if (!validateEngagementForm()) return;

    try {
      // Format the data before sending
      const formattedData = {
        ...engagementForm,
        // Convert follow_up_date from YYYY-MM-DD to YYYY-MM-DDTHH:MM:SS format
        follow_up_date: engagementForm.follow_up_date
          ? `${engagementForm.follow_up_date}T00:00:00`
          : undefined,
        // Remove empty/undefined fields
        outcome: engagementForm.outcome || undefined,
        duration_minutes: engagementForm.duration_minutes || undefined,
        assigned_to: engagementForm.assigned_to || undefined,
        notes: engagementForm.notes || undefined,
      };

      await updateEngagement({
        engagementId: editingEngagement.engagement_id,
        data: formattedData
      }).unwrap();
      setSuccessMessage("Engagement updated successfully!");
      refetchEngagements();

      setTimeout(() => {
        closeModal();
      }, 1500);
    } catch (error: any) {
      console.error("Failed to update engagement:", error);
      setFormErrors({
        submit: error?.data?.message || "Failed to update engagement. Please try again."
      });
    }
  };

  const handleDeleteEngagement = async (engagementId: string, createdBy: string) => {
    if (currentUser?.id !== createdBy) {
      alert("You can only delete engagements that you created.");
      return;
    }

    if (!confirm("Are you sure you want to delete this engagement? This action cannot be undone.")) {
      return;
    }

    try {
      await deleteEngagement(engagementId).unwrap();
      refetchEngagements();
      setSuccessMessage("Engagement deleted successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (error) {
      console.error("Failed to delete engagement:", error);
    }
  };

  const handleEngagementStatusUpdate = async (engagementId: string, action: 'start' | 'complete') => {
    try {
      if (action === 'start') {
        await startEngagement(engagementId).unwrap();
      } else {
        await completeEngagement(engagementId).unwrap();
      }
      refetchEngagements();
      setSuccessMessage(`Engagement ${action === 'start' ? 'started' : 'completed'} successfully!`);
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (error) {
      console.error(`Failed to ${action} engagement:`, error);
    }
  };

  const openEditEngagement = (engagement: any) => {
    setEditingEngagement(engagement);
    setEngagementForm({
      engagement_type: engagement.engagement_type || "",
      subject: engagement.subject || "",
      description: engagement.description || "",
      status: engagement.status || "Scheduled",
      outcome: engagement.outcome || "",
      scheduled_at: engagement.scheduled_at ? engagement.scheduled_at.slice(0, 16) : "",
      started_at: engagement.started_at ? engagement.started_at.slice(0, 16) : "",
      completed_at: engagement.completed_at ? engagement.completed_at.slice(0, 16) : "",
      duration_minutes: engagement.duration_minutes || undefined,
      assigned_to: engagement.assigned_to || "",
      notes: engagement.notes || "",
      follow_up_required: engagement.follow_up_required || false,
      follow_up_date: engagement.follow_up_date ? engagement.follow_up_date.split('T')[0] : "",
      entity_type: engagement.entity_type || entityType, // Use the engagement's entity_type or fallback to prop
      entity_id: engagement.entity_id || entityId,
      customer_no: engagement.customer_no || (entityType === "customer" ? customerNo : ""),
      prospect_id: engagement.prospect_id || (entityType === "prospect" ? customerNo : ""),
      lead_file_no: engagement.lead_file_no || (entityType === "leadfile" ? customerNo : ""),
      client_status: engagement.client_status || (entityType === "customer" ? "Customer" : entityType === "prospect" ? "Prospect" : "Lead File"),
    });
    openModal("editEngagement");
  };
  // Flag form validation
  const validateFlagForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!flagForm.flag_type) {
      errors.flag_type = "Flag type is required";
    }
    if (!flagForm.title.trim()) {
      errors.title = "Title is required";
    }
    if (!flagForm.description.trim()) {
      errors.description = "Description is required";
    }
    if (!entityId) {
      errors.entity_id = "Entity ID is required";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Flag form change handler
  const onFlagFormChange = (field: string, value: string | boolean | number) => {
    setFlagForm(prev => ({
      ...prev,
      [field]: value
    }));

    if (formErrors[field]) {
      setFormErrors(prev => ({
        ...prev,
        [field]: ""
      }));
    }
  };

  // Create flag handler
  const onCreateFlag = async () => {
    if (!validateFlagForm()) return;

    try {
      await createFlag(flagForm).unwrap();
      setSuccessMessage("Flag created successfully!");
      refetchFlags();

      setTimeout(() => {
        closeModal();
      }, 1500);
    } catch (error: any) {
      console.error("Failed to create flag:", error);
      setFormErrors({
        submit: error?.data?.message || "Failed to create flag. Please try again."
      });
    }
  };

  // Update flag handler
  const onUpdateFlag = async () => {
    if (!validateFlagForm()) return;

    try {
      await updateFlag({
        flagId: editingFlag?.flag_id || "",
        data: flagForm
      }).unwrap();
      setSuccessMessage("Flag updated successfully!");
      refetchFlags();

      setTimeout(() => {
        closeModal();
      }, 1500);
    } catch (error: any) {
      console.error("Failed to update flag:", error);
      setFormErrors({
        submit: error?.data?.message || "Failed to update flag. Please try again."
      });
    }
  };

  // Delete flag handler
  const onDeleteFlag = async (flagId: string, createdBy: string) => {
    if (currentUser?.id !== createdBy) {
      alert("You can only delete flags that you created.");
      return;
    }

    if (!confirm("Are you sure you want to delete this flag? This action cannot be undone.")) {
      return;
    }

    try {
      await deleteFlag(flagId).unwrap();
      refetchFlags();
      setSuccessMessage("Flag deleted successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (error) {
      console.error("Failed to delete flag:", error);
    }
  };

  // Resolve flag handler
  const onResolveFlag = async (flagId: string, resolutionNotes: string) => {
    try {
      await resolveFlag({
        flagId,
        data: {
          status: "Resolved",
          resolution_notes: resolutionNotes
        }
      }).unwrap();
      refetchFlags();
      setSuccessMessage("Flag resolved successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (error) {
      console.error("Failed to resolve flag:", error);
    }
  };

  // Open edit flag handler
  const onOpenEditFlag = (flag: any) => {
    setEditingFlag(flag);
    setFlagForm({
      entity_type: flag.entity_type || entityType,
      entity_id: flag.entity_id || entityId,
      customer_no: flag.customer_no || "",
      prospect_id: flag.prospect_id || "",
      lead_file_no: flag.lead_file_no || "",
      client_status: flag.client_status || "",
      flag_type: flag.flag_type || "",
      title: flag.title || "",
      description: flag.description || "",
      severity: flag.severity || "Info",
      status: flag.status || "Active",
      assigned_to: flag.assigned_to || "",
      resolution_notes: flag.resolution_notes || "",
    });
    openModal("editFlag");
  };

  const validateNotificationForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!notificationForm.title.trim()) {
      errors.title = "Title is required";
    }
    if (!notificationForm.message.trim()) {
      errors.message = "Message is required";
    }
    if (!notificationForm.recipient.trim()) {
      errors.recipient = "Recipient is required";
    }
    if (!entityId) {
      errors.entity_id = "Entity ID is required";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };


  const onNotificationFormChange = (field: string, value: string | boolean | number) => {
    setNotificationForm(prev => ({
      ...prev,
      [field]: value
    }));

    if (formErrors[field]) {
      setFormErrors(prev => ({
        ...prev,
        [field]: ""
      }));
    }
  };

  const onReminderFormChange = (field: string, value: string | boolean | number) => {
    console.log(`📝 Reminder form field changed: ${field} = ${value}`);

    setReminderForm(prev => {
      const newForm = {
        ...prev,
        [field]: value
      };
      console.log("Updated reminder form:", newForm);
      return newForm;
    });

    // Clear field-specific errors when user starts typing
    if (formErrors[field]) {
      console.log(`🧹 Clearing error for field: ${field}`);
      setFormErrors(prev => ({
        ...prev,
        [field]: ""
      }));
    }
  };


  // notification handler
  const onCreateNotification = async () => {
    if (!validateNotificationForm()) return;

    try {
      const formattedData = {
        ...notificationForm,
        expires_at: notificationForm.expires_at
          ? `${notificationForm.expires_at}T23:59:59`
          : undefined,
        action_url: notificationForm.action_url || undefined,
        sender: notificationForm.sender || undefined,
      };

      await createNotification(formattedData).unwrap();
      setSuccessMessage("Notification created successfully!");
      refetchNotifications();
      refetchUnreadNotifications();

      setTimeout(() => {
        closeModal();
      }, 1500);
    } catch (error: any) {
      console.error("Failed to create notification:", error);
      setFormErrors({
        submit: error?.data?.message || "Failed to create notification. Please try again."
      });
    }
  };

  // Update notification handler
  const onUpdateNotification = async () => {
    if (!validateNotificationForm()) return;

    try {
      const formattedData = {
        ...notificationForm,
        expires_at: notificationForm.expires_at
          ? `${notificationForm.expires_at}T23:59:59`
          : undefined,
        action_url: notificationForm.action_url || undefined,
        sender: notificationForm.sender || undefined,
      };

      await updateNotification({
        notificationId: editingNotification?.notification_id || "",
        data: formattedData
      }).unwrap();
      setSuccessMessage("Notification updated successfully!");
      refetchNotifications();
      refetchUnreadNotifications();

      setTimeout(() => {
        closeModal();
      }, 1500);
    } catch (error: any) {
      console.error("Failed to update notification:", error);
      setFormErrors({
        submit: error?.data?.message || "Failed to update notification. Please try again."
      });
    }
  };

  // Delete notification handler
  const onDeleteNotification = async (notificationId: string, createdBy: string) => {
    if (currentUser?.id !== createdBy) {
      alert("You can only delete notifications that you created.");
      return;
    }

    if (!confirm("Are you sure you want to delete this notification? This action cannot be undone.")) {
      return;
    }

    try {
      await deleteNotification(notificationId).unwrap();
      refetchNotifications();
      refetchUnreadNotifications();
      setSuccessMessage("Notification deleted successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (error) {
      console.error("Failed to delete notification:", error);
    }
  };

  // Mark notification as read handler
  const onMarkAsRead = async (notificationId: string) => {
    try {
      await markAsRead(notificationId).unwrap();
      refetchNotifications();
      refetchUnreadNotifications();
      setSuccessMessage("Notification marked as read!");
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (error) {
      console.error("Failed to mark notification as read:", error);
    }
  };

  // Reminder CRUD handlers
  const handleCreateReminder = async () => {
    console.log("=== CREATE REMINDER STARTED ===");
    console.log("Reminder Form Data:", reminderForm);

    if (!validateReminderForm()) {
      console.log("❌ Reminder form validation failed");
      return;
    }

    try {
      console.log("📤 Sending create reminder request...");
      const result = await createReminder(reminderForm).unwrap();
      console.log("✅ Reminder created successfully:", result);

      setSuccessMessage("Reminder created successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);
      closeModal();

      console.log("🔄 Refetching reminder data...");
      refetchReminders();
      refetchOverdueReminders();
      refetchUpcomingReminders();
      console.log("=== CREATE REMINDER COMPLETED ===");
    } catch (error) {
      console.error("❌ Failed to create reminder:", error);
      console.log("=== CREATE REMINDER FAILED ===");
    }
  };

  const handleUpdateReminder = async () => {
    console.log("=== UPDATE REMINDER STARTED ===");
    console.log("Editing Reminder:", editingReminder);
    console.log("Updated Form Data:", reminderForm);

    if (!validateReminderForm() || !editingReminder) {
      console.log("❌ Reminder form validation failed or no editing reminder");
      return;
    }

    try {
      console.log("📤 Sending update reminder request...");
      const result = await updateReminder({
        reminderId: editingReminder.reminder_id,
        ...reminderForm
      }).unwrap();
      console.log("✅ Reminder updated successfully:", result);

      setSuccessMessage("Reminder updated successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);
      closeModal();

      console.log("🔄 Refetching reminder data...");
      refetchReminders();
      refetchOverdueReminders();
      refetchUpcomingReminders();
      console.log("=== UPDATE REMINDER COMPLETED ===");
    } catch (error) {
      console.error("❌ Failed to update reminder:", error);
      console.log("=== UPDATE REMINDER FAILED ===");
    }
  };

  const handleDeleteReminder = async (reminderId: string, createdBy: string) => {
    console.log("=== DELETE REMINDER STARTED ===");
    console.log("Reminder ID:", reminderId);
    console.log("Created By:", createdBy);

    try {
      console.log("📤 Sending delete reminder request...");
      await deleteReminder(reminderId).unwrap();
      console.log("✅ Reminder deleted successfully");

      setSuccessMessage("Reminder deleted successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);

      console.log("🔄 Refetching reminder data...");
      refetchReminders();
      refetchOverdueReminders();
      refetchUpcomingReminders();
      console.log("=== DELETE REMINDER COMPLETED ===");
    } catch (error) {
      console.error("❌ Failed to delete reminder:", error);
      console.log("=== DELETE REMINDER FAILED ===");
    }
  };

  const handleCompleteReminder = async (reminderId: string) => {
    console.log("=== COMPLETE REMINDER STARTED ===");
    console.log("Reminder ID:", reminderId);

    try {
      console.log("📤 Sending complete reminder request...");
      await completeReminder(reminderId).unwrap();
      console.log("✅ Reminder completed successfully");

      setSuccessMessage("Reminder completed successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);

      console.log("🔄 Refetching reminder data...");
      refetchReminders();
      refetchOverdueReminders();
      refetchUpcomingReminders();
      console.log("=== COMPLETE REMINDER COMPLETED ===");
    } catch (error) {
      console.error("❌ Failed to complete reminder:", error);
      console.log("=== COMPLETE REMINDER FAILED ===");
    }
  };

  const handleSnoozeReminder = async (reminderId: string, snoozeData: any) => {
    console.log("=== SNOOZE REMINDER STARTED ===");
    console.log("Reminder ID:", reminderId);
    console.log("Snooze Data:", snoozeData);

    try {
      console.log("📤 Sending snooze reminder request...");
      await snoozeReminder({ reminderId, snoozeData }).unwrap();
      console.log("✅ Reminder snoozed successfully");

      setSuccessMessage("Reminder snoozed successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);

      console.log("🔄 Refetching reminder data...");
      refetchReminders();
      refetchOverdueReminders();
      refetchUpcomingReminders();
      console.log("=== SNOOZE REMINDER COMPLETED ===");
    } catch (error) {
      console.error("❌ Failed to snooze reminder:", error);
      console.log("=== SNOOZE REMINDER FAILED ===");
    }
  };

  // Open edit notification handler
  const onOpenEditNotification = (notification: any) => {
    setEditingNotification(notification);
    setNotificationForm({
      entity_type: notification.entity_type || entityType,
      entity_id: notification.entity_id || entityId,
      customer_no: notification.customer_no || "",
      prospect_id: notification.prospect_id || "",
      lead_file_no: notification.lead_file_no || "",
      client_status: notification.client_status || "",
      notification_type: notification.notification_type || "Info",
      priority: notification.priority || "Normal",
      title: notification.title || "",
      message: notification.message || "",
      recipient: notification.recipient || "",
      sender: notification.sender || "",
      expires_at: notification.expires_at ? notification.expires_at.split('T')[0] : "",
      action_url: notification.action_url || "",
      is_active: notification.is_active !== undefined ? notification.is_active : true,
    });
    openModal("editNotification");
  };

  // Note form validation
  const validateNoteForm = () => {
    const errors = {};

    if (!noteForm.title.trim()) {
      errors.title = "Title is required";
    }
    if (!noteForm.content.trim()) {
      errors.content = "Content is required";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Reminder form validation
  const validateReminderForm = () => {
    console.log("=== VALIDATING REMINDER FORM ===");
    console.log("Form Data:", reminderForm);

    const errors: any = {};

    if (!reminderForm.title.trim()) {
      errors.title = "Title is required";
      console.log("❌ Title validation failed");
    }
    if (!reminderForm.remind_at) {
      errors.remind_at = "Reminder date and time is required";
      console.log("❌ Remind at validation failed");
    }
    if (!reminderForm.reminder_type) {
      errors.reminder_type = "Reminder type is required";
      console.log("❌ Reminder type validation failed");
    }
    if (!reminderForm.priority) {
      errors.priority = "Priority is required";
      console.log("❌ Priority validation failed");
    }

    console.log("Validation Errors:", errors);
    setFormErrors(errors);
    const isValid = Object.keys(errors).length === 0;
    console.log("Form is valid:", isValid);
    console.log("=== REMINDER FORM VALIDATION COMPLETED ===");
    return isValid;
  };

  // Open edit reminder handler
  const onOpenEditReminder = (reminder: any) => {
    setEditingReminder(reminder);
    setReminderForm({
      entity_type: reminder.entity_type || entityType,
      entity_id: reminder.entity_id || entityId,
      customer_no: reminder.customer_no || "",
      prospect_id: reminder.prospect_id || "",
      lead_file_no: reminder.lead_file_no || "",
      client_status: reminder.client_status || "",
      reminder_type: reminder.reminder_type || "General",
      priority: reminder.priority || "Normal",
      title: reminder.title || "",
      description: reminder.description || "",
      remind_at: reminder.remind_at ? new Date(reminder.remind_at).toISOString().slice(0, 16) : "",
      advance_notice_minutes: reminder.advance_notice_minutes || 15,
      repeat_pattern: reminder.repeat_pattern || "None",
      tags: reminder.tags || "",
      is_active: reminder.is_active !== undefined ? reminder.is_active : true,
    });
    openModal("editReminder");
  };

  // Note form change handler
  const onNoteFormChange = (field: any, value: any) => {
    setNoteForm(prev => ({
      ...prev,
      [field]: value
    }));

    if (formErrors[field]) {
      setFormErrors(prev => ({
        ...prev,
        [field]: ""
      }));
    }
  };

  // Create note handler
  const onCreateNote = async () => {
    console.log("Creating note with data:", noteForm);
    if (!validateNoteForm()) return;

    try {
      const noteData = {
        ...noteForm,
        reminder_date: noteForm.reminder_date ? `${noteForm.reminder_date}T00:00:00Z` : null,
      };

      await createNote(noteData).unwrap();
      setSuccessMessage("Note created successfully!");
      refetchNotes();

      setTimeout(() => {
        closeModal();
      }, 1500);
    } catch (error) {
      console.error("Failed to create note:", error);
      setFormErrors({
        submit: error?.data?.message || "Failed to create note. Please try again."
      });
    }
  };

  // Update note handler
  const onUpdateNote = async () => {
    if (!validateNoteForm()) return;

    try {
      const noteData = {
        ...noteForm,
        reminder_date: noteForm.reminder_date ? `${noteForm.reminder_date}T00:00:00Z` : null,
      };

      await updateNote({
        noteId: editingNote?.note_id || "",
        data: noteData
      }).unwrap();
      setSuccessMessage("Note updated successfully!");
      refetchNotes();

      setTimeout(() => {
        closeModal();
      }, 1500);
    } catch (error) {
      console.error("Failed to update note:", error);
      setFormErrors({
        submit: error?.data?.message || "Failed to update note. Please try again."
      });
    }
  };

  // Delete note handler
  const onDeleteNote = async (noteId: any, createdBy: any) => {
    if (currentUser?.id !== createdBy) {
      alert("You can only delete notes that you created.");
      return;
    }

    if (!confirm("Are you sure you want to delete this note? This action cannot be undone.")) {
      return;
    }

    try {
      await deleteNote(noteId).unwrap();
      refetchNotes();
      setSuccessMessage("Note deleted successfully!");
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (error) {
      console.error("Failed to delete note:", error);
    }
  };

  // Toggle pin handler
  const onToggleNotePin = async (noteId: any, currentPinStatus: any) => {
    try {
      await toggleNotePin({
        noteId,
        data: { is_pinned: !currentPinStatus }
      }).unwrap();
      refetchNotes();
      setSuccessMessage(`Note ${!currentPinStatus ? 'pinned' : 'unpinned'} successfully!`);
      setTimeout(() => setSuccessMessage(""), 3000);
    } catch (error) {
      console.error("Failed to toggle pin status:", error);
    }
  };

  // Open edit note handler
  const onOpenEditNote = (note: any) => {
    setEditingNote(note);
    setNoteForm({
      note_type: note.note_type || "General",
      title: note.title || "",
      content: note.content || "",
      is_private: note.is_private || false,
      is_pinned: note.is_pinned || false,
      tags: note.tags || "",
      reminder_date: note.reminder_date ? note.reminder_date.split('T')[0] : "",
      is_active: note.is_active !== undefined ? note.is_active : true,
    });
    openModal("editNote");
  };

  // Utility functions
  const renderPriorityBadge: BadgeRenderer = (priority) => {
    const variants: Record<string, string> = {
      High: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",
      Medium: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300",
      Low: "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
      Critical: "bg-red-200 text-red-900 dark:bg-red-800/40 dark:text-red-200"
    };

    return (
      <span className={`text-xs px-2 py-1 rounded-full ${variants[priority] || variants.Medium}`}>
        {priority}
      </span>
    );
  };

  const renderNoteTypeBadge = (type: any) => {
    const variants = {
      "General": "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300",
      "Important": "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",
      "Reminder": "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
      "Follow-up": "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300",
      "Internal": "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300",
      "Customer Facing": "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
    };

    return (
      <span className={`text-xs px-2 py-1 rounded-full ${variants[type] || variants.General}`}>
        {type}
      </span>
    );
  };

  const renderNotificationTypeBadge: BadgeRenderer = (type) => {
    const variants = {
      "Info": "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
      "Warning": "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300",
      "Error": "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",
      "Success": "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
      "Reminder": "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300",
      "Alert": "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300"
    };

    return (
      <span className={`text-xs px-2 py-1 rounded-full ${variants[type] || variants.Info}`}>
        {type}
      </span>
    );
  };

  const renderEngagementTypeBadge: BadgeRenderer = (type) => {
    const variants: Record<string, string> = {
      Call: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
      Email: "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300",
      Meeting: "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
      SMS: "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300",
      Chat: "bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300",
      Visit: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",
      Event: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300",
      "Follow-up": "bg-pink-100 text-pink-800 dark:bg-pink-900/30 dark:text-pink-300"
    };

    return (
      <span className={`text-xs px-2 py-1 rounded-full ${variants[type] || variants.Call}`}>
        {type}
      </span>
    );
  };

  const renderEngagementStatusBadge: BadgeRenderer = (status) => {
    const variants: Record<string, string> = {
      "Scheduled": "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
      "In Progress": "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300",
      "Completed": "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
      "Cancelled": "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",
      "Rescheduled": "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300"
    };

    return (
      <span className={`text-xs px-2 py-1 rounded-full ${variants[status] || variants.Scheduled}`}>
        {status}
      </span>
    );
  };

  const renderStatusBadge: BadgeRenderer = (status) => {
    const variants: Record<string, string> = {
      "Open": "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
      "In Progress": "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300",
      "Resolved": "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
      "Closed": "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300",
      "Escalated": "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"
    };

    return (
      <span className={`text-xs px-2 py-1 rounded-full ${variants[status] || variants.Open}`}>
        {status}
      </span>
    );
  };

  const renderCategoryBadge: BadgeRenderer = (category) => {
    const variants: Record<string, string> = {
      Product: "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300",
      Service: "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
      Billing: "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
      Technical: "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300",
      General: "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300"
    };

    return (
      <span className={`text-xs px-2 py-1 rounded-full ${variants[category] || variants.General}`}>
        {category}
      </span>
    );
  };

  const renderFeedbackTypeBadge: BadgeRenderer = (type) => {
    const variants: Record<string, string> = {
      "General": "bg-gray-100 text-gray-800 dark:bg-gray-800/40 dark:text-gray-300",
      "Product Review": "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
      "Service Review": "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300",
      "Compliment": "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
      "Suggestion": "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300",
      "Complaint": "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"
    };

    return (
      <span className={`text-xs px-2 py-1 rounded-full ${variants[type] || variants.General}`}>
        {type}
      </span>
    );
  };

  const renderRatingStars = (rating?: number): JSX.Element => {
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-3 w-3 ${star <= (rating || 0)
              ? 'fill-yellow-400 text-yellow-400'
              : 'text-gray-300'
              }`}
          />
        ))}
        {rating && (
          <span className="ml-1 text-xs text-gray-500">
            ({rating}/5)
          </span>
        )}
      </div>
    );
  };

  const renderFlagTypeBadge: BadgeRenderer = (type) => {
    const variants = {
      "Customer Issue": "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",
      "Data Quality": "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
      "Security": "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300",
      "Compliance": "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
      "Performance": "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300",
      "Content": "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300"
    };

    return (
      <span className={`text-xs px-2 py-1 rounded-full ${variants[type] || variants.Content}`}>
        {type}
      </span>
    );
  };

  const renderSeverityBadge = (severity: any) => {
    const variants = {
      "Info": "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
      "Warning": "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300",
      "Error": "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300",
      "Critical": "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"
    };

    return (
      <span className={`text-xs px-2 py-1 rounded-full ${variants[severity] || variants.Info}`}>
        {severity}
      </span>
    );
  };


  const formatDate: DateFormatter = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const timeAgo: DateFormatter = (dateString) => {
    if (!dateString) return "Unknown";
    const date = new Date(dateString);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) return "Today";
    if (diffInDays === 1) return "1 day ago";
    if (diffInDays < 7) return `${diffInDays} days ago`;
    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;
    return `${Math.floor(diffInDays / 30)} months ago`;
  };

  // Reminder badge renderers
  const renderReminderTypeBadge = (type: string) => {
    const typeColors = {
      General: "bg-gray-100 text-gray-800",
      Task: "bg-blue-100 text-blue-800",
      "Follow-up": "bg-green-100 text-green-800",
      Meeting: "bg-purple-100 text-purple-800",
      Deadline: "bg-red-100 text-red-800",
      Payment: "bg-yellow-100 text-yellow-800",
      Review: "bg-indigo-100 text-indigo-800",
      Call: "bg-orange-100 text-orange-800",
      Email: "bg-cyan-100 text-cyan-800",
    };
    return (
      <Badge className={`text-xs ${typeColors[type as keyof typeof typeColors] || "bg-gray-100 text-gray-800"}`}>
        {type}
      </Badge>
    );
  };

  const renderReminderPriorityBadge = (priority: string) => {
    const priorityColors = {
      Low: "bg-green-100 text-green-800",
      Normal: "bg-blue-100 text-blue-800",
      High: "bg-yellow-100 text-yellow-800",
      Urgent: "bg-red-100 text-red-800",
    };
    return (
      <Badge className={`text-xs ${priorityColors[priority as keyof typeof priorityColors] || "bg-gray-100 text-gray-800"}`}>
        {priority}
      </Badge>
    );
  };

  const renderReminderStatusBadge = (status: string) => {
    const statusColors = {
      Active: "bg-green-100 text-green-800",
      Completed: "bg-blue-100 text-blue-800",
      Cancelled: "bg-red-100 text-red-800",
      Snoozed: "bg-yellow-100 text-yellow-800",
    };
    return (
      <Badge className={`text-xs ${statusColors[status as keyof typeof statusColors] || "bg-gray-100 text-gray-800"}`}>
        {status}
      </Badge>
    );
  };

  // Sample data for other sections
  const ticketItems: TicketItem[] = ticketsData?.results || [];
  const ticketMessages: TicketMessage[] = ticketMessagesData?.results || [];
  const ticketAttachments: TicketAttachment[] = ticketAttachmentsData?.results || [];
  const actionLogs: TicketActionLog[] = actionLogsData?.results || [];
  const categories: TicketCategory[] = categoriesData?.results || [];
  const sources: TicketSource[] = sourcesData?.results || [];
  // Handle both transformed and raw response formats for notes
  let noteItems: any[] = [];
  if (notesData?.results) {
    // Properly transformed data
    noteItems = notesData.results;
  } else if ((notesData as any)?.data?.results) {
    // Raw untransformed data - extract manually
    noteItems = (notesData as any).data.results;
  }



  // Handle both transformed and raw response formats for flags
  let flagItems: any[] = [];
  if (flagsData?.results) {
    // Properly transformed data
    flagItems = flagsData.results;
  } else if ((flagsData as any)?.data?.results) {
    // Raw untransformed data - extract manually
    flagItems = (flagsData as any).data.results;
  }

  // Handle both transformed and raw response formats
  let engagementItems: any[] = [];
  if (engagementsData?.results) {
    // Properly transformed data
    engagementItems = engagementsData.results;
  } else if ((engagementsData as any)?.data?.results) {
    // Raw untransformed data - extract manually
    engagementItems = (engagementsData as any).data.results;
  }



  // Handle both transformed and raw response formats for notifications
  let notificationItems: any[] = [];
  if (notificationsData?.results) {
    // Properly transformed data
    notificationItems = notificationsData.results;
  } else if ((notificationsData as any)?.data?.results) {
    // Raw untransformed data - extract manually
    notificationItems = (notificationsData as any).data.results;
  }

  // Handle both transformed and raw response formats for reminders
  let reminderItems: any[] = [];
  if (remindersData?.results) {
    // Properly transformed data
    reminderItems = remindersData.results;
  } else if ((remindersData as any)?.data?.results) {
    // Raw untransformed data - extract manually
    reminderItems = (remindersData as any).data.results;
  }

  // Calculate overdue and upcoming counts
  const overdueCount = reminderItems.filter(reminder => reminder.is_overdue).length;
  const upcomingCount = reminderItems.filter(reminder => reminder.is_due && !reminder.is_overdue).length;

  // Debug logging for reminders - placed after all variables are initialized
  console.log("=== REMINDERS DEBUG INFO ===");
  console.log("Entity Type:", entityType);
  console.log("Customer No:", customerNo);
  console.log("Lead File No:", leadfileNo);
  console.log("Reminders Params:", remindersParams);
  console.log("Reminders Data:", remindersData);
  console.log("Reminders Loading:", remindersLoading);
  console.log("Reminders Error:", remindersError);
  console.log("Processed Reminder Items:", reminderItems);
  console.log("Overdue Count:", overdueCount);
  console.log("Upcoming Count:", upcomingCount);
  console.log("Expanded Sections:", expandedSections);
  console.log("Active Modal:", activeModal);
  console.log("=== END REMINDERS DEBUG ===");

  return (
    <>
      <ScrollArea className="h-full overflow-auto">
        <div className={`p-4 space-y-6 pb-16 ${className}`}>
          {successMessage && (
            <Alert className="bg-green-50 border-green-200 text-green-800">
              <AlertDescription>{successMessage}</AlertDescription>
            </Alert>
          )}

          <TicketsSection
            expanded={expandedSections.tickets}
            onToggle={() => toggleSection("tickets")}
            items={ticketItems}
            loading={ticketsLoading}
            error={ticketsError}
            onOpenList={() => openModal("ticketsList")}
            onOpenCreate={() => openModal("createTicket")}
            onOpenTicket={openTicketDetails}
            renderPriorityBadge={renderPriorityBadge}
            renderStatusBadge={renderStatusBadge}
            timeAgo={timeAgo}
          />

          <NotesSection
            expanded={expandedSections.notes}
            onToggle={() => toggleSection("notes")}
            items={noteItems}
            loading={notesLoading}
            error={notesError}
            onOpenList={() => openModal("notes")}
            onOpenCreate={() => openModal("createNote")}
            renderNoteTypeBadge={renderNoteTypeBadge}
            onTogglePin={onToggleNotePin}
            timeAgo={timeAgo}
          />

          <FlagsSection
            expanded={expandedSections.flags}
            onToggle={() => toggleSection("flags")}
            items={flagItems}
            loading={flagsLoading}
            error={flagsError}
            onAdd={() => openModal("createFlag")}
            onOpenList={() => openModal("flags")}
            renderFlagTypeBadge={renderFlagTypeBadge}
            renderSeverityBadge={renderSeverityBadge}
            renderStatusBadge={renderStatusBadge}
            timeAgo={timeAgo}
          />

          <EngagementsSection
            expanded={expandedSections.engagements}
            onToggle={() => toggleSection("engagements")}
            items={engagementItems}
            loading={engagementsLoading}
            error={engagementsError}
            onOpenList={() => openModal("engagements")}
            onOpenCreate={() => openModal("createEngagement")}
            renderEngagementTypeBadge={renderEngagementTypeBadge}
            renderEngagementStatusBadge={renderEngagementStatusBadge}
            formatDate={formatDate}
          />

          <NotificationsSection
            expanded={expandedSections.notifications}
            onToggle={() => toggleSection("notifications")}
            items={notificationItems}
            loading={notificationsLoading}
            error={notificationsError}
            onOpenList={() => openModal("notifications")}
            onOpenCreate={() => openModal("createNotification")}
            renderNotificationTypeBadge={renderNotificationTypeBadge}
            renderPriorityBadge={renderPriorityBadge}
            renderStatusBadge={renderStatusBadge}
            timeAgo={timeAgo}
            onMarkAsRead={onMarkAsRead}
            unreadCount={unreadNotificationsData?.count || 0}
          />

          <RemindersSection
            expanded={expandedSections.reminders}
            onToggle={() => {
              console.log("🔄 Toggling reminders section");
              toggleSection("reminders");
            }}
            items={reminderItems}
            loading={remindersLoading}
            error={remindersError}
            onOpenList={() => {
              console.log("📋 Opening reminders list modal");
              openModal("reminders");
            }}
            onOpenCreate={() => {
              console.log("➕ Opening create reminder modal");
              openModal("createReminder");
            }}
            renderReminderTypeBadge={renderReminderTypeBadge}
            renderPriorityBadge={renderReminderPriorityBadge}
            renderStatusBadge={renderReminderStatusBadge}
            timeAgo={timeAgo}
            onCompleteReminder={(id) => {
              console.log("✅ Completing reminder:", id);
              handleCompleteReminder(id);
            }}
            onSnoozeReminder={(id) => {
              console.log("⏰ Snoozing reminder:", id);
              handleSnoozeReminder(id, { minutes: 15 });
            }}
            overdueCount={overdueCount}
            upcomingCount={upcomingCount}
          />
        </div>
      </ScrollArea>



      {/* Add EngagementsModals component here when created */}
      <EngagementsModals
        activeModal={activeModal}
        onCloseModal={closeModal}
        onOpenModal={openModal}
        engagementForm={engagementForm}
        onEngagementFormChange={handleEngagementFormChange}
        formErrors={formErrors}
        successMessage={successMessage}
        editingEngagement={editingEngagement}
        onOpenEditEngagement={openEditEngagement}
        onCreateEngagement={handleCreateEngagement}
        onUpdateEngagement={handleUpdateEngagement}
        onDeleteEngagement={handleDeleteEngagement}
        onStatusUpdate={handleEngagementStatusUpdate}
        engagementItems={engagementItems}
        currentUser={currentUser}
        isCreating={isCreatingEngagement}
        isDeleting={isDeletingEngagement}
        renderEngagementTypeBadge={renderEngagementTypeBadge}
        renderEngagementStatusBadge={renderEngagementStatusBadge}
        formatDate={formatDate}
      />


      {/* Add TicketsModals component */}
      <TicketsModals
        activeModal={activeModal}
        onClose={closeModal}
        ticketForm={ticketForm}
        onTicketFormChange={handleTicketFormChange}
        onCreateTicket={handleCreateTicket}
        onUpdateTicket={handleUpdateTicket}
        onFileChange={setSelectedFile}
        selectedFile={selectedFile}
        messageForm={messageForm}
        onMessageFormChange={handleMessageFormChange}
        onSendMessage={handleSendMessage}
        onDeleteMessage={handleDeleteMessage}
        onEditMessage={handleEditMessage}
        categoryForm={categoryForm}
        onCategoryFormChange={handleCategoryFormChange}
        onCreateCategory={handleCreateCategory}
        onUpdateCategory={handleUpdateCategory}
        onDeleteCategory={handleDeleteCategory}
        editingCategory={editingCategory}
        sourceForm={sourceForm}
        onSourceFormChange={handleSourceFormChange}
        onCreateSource={handleCreateSource}
        onUpdateSource={handleUpdateSource}
        onDeleteSource={handleDeleteSource}
        editingSource={editingSource}
        onUploadAttachment={handleUploadAttachment}
        onDeleteAttachment={handleDeleteAttachment}
        actionLogs={actionLogs}
        onCreateActionLog={handleCreateActionLog}
        onNavigateToTicket={navigateToTicket}
        editingTicket={editingTicket}
        selectedTicket={selectedTicket}
        ticketMessages={ticketMessages}
        ticketAttachments={ticketAttachments}
        categories={categories}
        sources={sources}
        ticketItems={ticketItems}
        isCreating={isCreatingTicket}
        isUpdating={isUpdatingTicket}
        isSendingMessage={isSendingMessage}
        messagesLoading={messagesLoading}
        attachmentsLoading={attachmentsLoading}
        isDeleting={isDeletingTicket}
        isUploadingAttachment={isUploadingAttachment}
        formErrors={formErrors}
        successMessage={successMessage}
        renderPriorityBadge={renderPriorityBadge}
        renderStatusBadge={renderStatusBadge}
        formatDate={formatDate}
        timeAgo={timeAgo}
        currentUser={currentUser}
      />

      {/* Add FlagsModals component */}
      <FlagsModals
        activeModal={activeModal}
        onCloseModal={closeModal}
        onOpenModal={openModal}
        flagForm={flagForm}
        onFlagFormChange={onFlagFormChange}
        formErrors={formErrors}
        successMessage={successMessage}
        editingFlag={editingFlag}
        onOpenEditFlag={onOpenEditFlag}
        onCreateFlag={onCreateFlag}
        onUpdateFlag={onUpdateFlag}
        onDeleteFlag={onDeleteFlag}
        onResolveFlag={onResolveFlag}
        flagItems={flagItems}
        currentUser={currentUser}
        isCreating={isCreatingFlag}
        isDeleting={isDeletingFlag}
        renderFlagTypeBadge={renderFlagTypeBadge}
        renderSeverityBadge={renderSeverityBadge}
        renderStatusBadge={renderStatusBadge}
        formatDate={formatDate}
      />

      <NotificationsModals
        activeModal={activeModal}
        onCloseModal={closeModal}
        onOpenModal={openModal}
        notificationForm={notificationForm}
        onNotificationFormChange={onNotificationFormChange}
        formErrors={formErrors}
        successMessage={successMessage}
        editingNotification={editingNotification}
        onOpenEditNotification={onOpenEditNotification}
        onCreateNotification={onCreateNotification}
        onUpdateNotification={onUpdateNotification}
        onDeleteNotification={onDeleteNotification}
        onMarkAsRead={onMarkAsRead}
        notificationItems={notificationItems}
        currentUser={currentUser}
        isCreating={isCreatingNotification}
        isDeleting={isDeletingNotification}
        renderNotificationTypeBadge={renderNotificationTypeBadge}
        renderPriorityBadge={renderPriorityBadge}
        renderStatusBadge={renderStatusBadge}
        formatDate={formatDate}
        timeAgo={timeAgo}
      />
      <NotesModals
        activeModal={activeModal}
        onCloseModal={closeModal}
        onOpenModal={openModal}
        noteForm={noteForm}
        onNoteFormChange={onNoteFormChange}
        formErrors={formErrors}
        successMessage={successMessage}
        editingNote={editingNote}
        onOpenEditNote={onOpenEditNote}
        onCreateNote={onCreateNote}
        onUpdateNote={onUpdateNote}
        onDeleteNote={onDeleteNote}
        onToggleNotePin={onToggleNotePin}
        noteItems={noteItems}
        currentUser={currentUser}
        isCreating={isCreatingNote}
        isDeleting={isDeletingNote}
        renderNoteTypeBadge={renderNoteTypeBadge}
        formatDate={formatDate}
        timeAgo={timeAgo}
      />

      <RemindersModals
        activeModal={activeModal}
        onCloseModal={closeModal}
        onOpenModal={openModal}
        reminderForm={reminderForm}
        onReminderFormChange={onReminderFormChange}
        formErrors={formErrors}
        successMessage={successMessage}
        editingReminder={editingReminder}
        onOpenEditReminder={onOpenEditReminder}
        onCreateReminder={handleCreateReminder}
        onUpdateReminder={handleUpdateReminder}
        onDeleteReminder={handleDeleteReminder}
        onCompleteReminder={handleCompleteReminder}
        onSnoozeReminder={handleSnoozeReminder}
        reminderItems={reminderItems}
        currentUser={currentUser}
        isCreating={isCreatingReminder}
        isDeleting={isDeletingReminder}
        renderReminderTypeBadge={renderReminderTypeBadge}
        renderPriorityBadge={renderReminderPriorityBadge}
        renderStatusBadge={renderReminderStatusBadge}
        formatDate={formatDate}
        timeAgo={timeAgo}
      />

    </>
  );
};

export default CustomerSidebar;