import React from 'react';
import { Bell, PlusCircle, ChevronDown, BellRing } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import SpinnerTemp from '@/components/custom/spinners/SpinnerTemp';
import { BadgeRenderer, DateFormatter } from './types';

interface NotificationsSectionProps {
  expanded: boolean;
  onToggle: () => void;
  items: any[];
  loading: boolean;
  error: boolean;
  onOpenList: () => void;
  onOpenCreate: () => void;
  renderNotificationTypeBadge: BadgeRenderer;
  renderPriorityBadge: BadgeRenderer;
  renderStatusBadge: BadgeRenderer;
  timeAgo: DateFormatter;
  onMarkAsRead: (notificationId: string) => void;
  unreadCount: number;
}

const NotificationsSection: React.FC<NotificationsSectionProps> = ({
  expanded,
  onToggle,
  items,
  loading,
  error,
  onOpenList,
  onOpenCreate,
  renderNotificationTypeBadge,
  renderPriorityBadge,
  renderStatusBadge,
  timeAgo,
  onMarkAsRead,
  unreadCount,
}) => {
  return (
    <div className="space-y-2">
      <div
        className="flex items-center justify-between cursor-pointer group transition-colors hover:bg-gray-50 dark:hover:bg-gray-800 p-2 rounded-md"
        onClick={onToggle}
      >
        <div className="flex items-center text-sm font-medium">
          <div className="relative">
            <Bell className="h-4 w-4 mr-2 text-blue-500" />
            {unreadCount > 0 && (
              <div className="absolute -top-1 -right-1 h-2 w-2 bg-red-500 rounded-full animate-pulse" />
            )}
          </div>
          <span>Notifications</span>
          <Badge variant="outline" className="ml-2">
            {items.length}
          </Badge>
          {unreadCount > 0 && (
            <Badge variant="destructive" className="ml-1 text-xs">
              {unreadCount} new
            </Badge>
          )}
        </div>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0 opacity-70 group-hover:opacity-100">
          {expanded ? <ChevronDown className="h-4 w-4 rotate-180" /> : <ChevronDown className="h-4 w-4" />}
        </Button>
      </div>
      
      {expanded && (
        <div className="pl-6 space-y-3">
          {loading ? (
            <div className="flex justify-center py-4">
              <SpinnerTemp type="spinner-double" size="sm" />
            </div>
          ) : error ? (
            <div className="text-sm text-red-500 text-center py-4">
              Failed to load notifications
            </div>
          ) : items.length === 0 ? (
            <div className="text-sm text-muted-foreground text-center py-4">
              No notifications found
            </div>
          ) : (
            <>
              {items.slice(0, 3).map((item) => (
                <div 
                  key={item.notification_id} 
                  className={`border rounded-md p-3 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors ${
                    !item.is_read ? 'bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800' : ''
                  }`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium text-sm">{item.title}</h4>
                      {!item.is_read && (
                        <BellRing className="h-3 w-3 text-blue-500" />
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      {renderNotificationTypeBadge(item.notification_type)}
                      {renderPriorityBadge(item.priority)}
                    </div>
                  </div>
                  
                  <p className="text-xs text-muted-foreground mb-2 line-clamp-2">
                    {item.message}
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-muted-foreground">
                        To: {item.recipient}
                      </span>
                      {item.sender && (
                        <span className="text-xs text-muted-foreground">
                          From: {item.sender}
                        </span>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-muted-foreground">
                        {timeAgo(item.created_at)}
                      </span>
                      {!item.is_read && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 px-2 text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                          onClick={(e) => {
                            e.stopPropagation();
                            onMarkAsRead(item.notification_id);
                          }}
                        >
                          Mark as read
                        </Button>
                      )}
                    </div>
                  </div>
                  
                  {item.expires_at && (
                    <div className="text-xs text-orange-500 mt-1">
                      Expires: {timeAgo(item.expires_at)}
                    </div>
                  )}
                </div>
              ))}
              
              {items.length > 3 && (
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="w-full text-xs justify-center items-center flex gap-1 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                  onClick={onOpenList}
                >
                  <span>View all notifications ({items.length})</span>
                  <ChevronDown className="h-3 w-3" />
                </Button>
              )}
            </>
          )}
          
          <Button 
            variant="outline" 
            size="sm" 
            className="w-full text-xs bg-gray-50 hover:bg-gray-100 dark:bg-gray-800 dark:hover:bg-gray-700 flex gap-1"
            onClick={onOpenCreate}
          >
            <PlusCircle className="h-3 w-3" />
            <span>Add notification</span>
          </Button>
        </div>
      )}
    </div>
  );
};

export default NotificationsSection;