import LazyModal, { TableColumn } from '@/components/reports/ReportsModal';
import { formatNumberWithCommas } from '@/utils/salesDataFormatter';
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useState } from 'react';

interface SalesViews {
    lead_file_no: string
    customer_name: string
    plot_id: string
    selling_price: string
    total_paid: string
    balance_lcy: string
    marketer_id: string
    fullnames: string
}

interface OnSalesViewsReportProps {
    isModalOpen: boolean;
    setIsModalOpen: (state: boolean) => void;
}

const SalesViewsReport = ({ isModalOpen, setIsModalOpen }: OnSalesViewsReportProps) => {

    // Define table columns with proper typing
    const columns: TableColumn<SalesViews>[] = [
        {
            key: 'customer_name',
            title: 'Customer',
        },
        {
            key: 'selling_price',
            title: 'Selling Price',
            render: (value: string) => {
                return <span>{formatNumberWithCommas(value)}</span>
            }
        },
        {
            key: 'total_paid',
            title: 'Total Paid',
            render: (value: string) => {
                return <span>{formatNumberWithCommas(value)}</span>
            },
        },
        {
            key: 'balance_lcy',
            title: 'Balance',
            render: (value: string) => {
                return <span>{formatNumberWithCommas(value)}</span>
            },
        },
        {
            key: 'fullnames',
            title: 'Marketer',
            cellClassName:'uppercase'
        },
        {
            key: 'lead_file_no',
            title: 'Lead File No',
        },
        {
            key: 'plot_id',
            title: 'Plot',
        },
    ]

    const handleCloseModal = () => {
        setIsModalOpen(false);
    }

    const [universalFilterValue, setuniversalFilterValue] = useState('')


    return (
        <LazyModal<SalesViews>
            isOpen={isModalOpen}
            onClose={handleCloseModal}
            title="Sales Views Report"
            url="/sales-views"  // Your actual API endpoint
            params={{ OFFICE: universalFilterValue }}
            columns={columns}
            size="full"
            enableBackendSearch={true}
            enableDateFilter={true}
            FilterComponents={<FilterComponent universalFilterValue={universalFilterValue} setuniversalFilterValue={setuniversalFilterValue} />}
        />
    )
}

export default SalesViewsReport

interface FilterComponentProps {
    universalFilterValue: string,
    setuniversalFilterValue: React.Dispatch<React.SetStateAction<string>>
}

function FilterComponent({ universalFilterValue, setuniversalFilterValue }: FilterComponentProps) {
    return <div>
        <Select onValueChange={(value) => setuniversalFilterValue(value)}>
            <SelectTrigger className="w-[180px] !border border-foreground/40 h-full bg-transparent  focus:!ring-transparent rounded-lg !ring-offset-transparent">
                <SelectValue placeholder="Select Office" />
            </SelectTrigger>
            <SelectContent>
                <SelectGroup>
                    <SelectLabel>Office</SelectLabel>
                    <SelectItem value="ALL">All</SelectItem>
                    <SelectItem value="HQ">HQ</SelectItem>
                    <SelectItem value="KAREN">Karen</SelectItem>
                </SelectGroup>
            </SelectContent>
        </Select>
    </div>
}