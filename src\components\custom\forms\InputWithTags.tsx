import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { X } from "lucide-react";
import { useRef, useState } from "react";

interface TagProps {
  tags: string[];
  setTags: (tags: string[]) => void;

  inputClassName?: string;
  containerClassName?: string;
}

type Props = TagProps;

const InputWithTags = ({
  tags,
  setTags,
  inputClassName,
  containerClassName,
}: Props) => {
  const [plots, setPlots] = useState("");
  const inputRef = useRef<HTMLInputElement>(null);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" || e.key === ",") {
      e.preventDefault();
      const newTags = plots
        .split(",")
        .map((tag) => tag.trim())
        .filter((tag) => tag && !tags.includes(tag));
      setTags([...tags, ...newTags]);
      setPlots("");
    }
  };
  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    e.preventDefault();
    const newTags = plots
      .split(",")
      .map((tag) => tag.trim())
      .filter((tag) => tag && !tags.includes(tag));
    setTags([...tags, ...newTags]);
    setPlots("");
  };

  const handlePlotChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPlots(e.target.value);
  };

  const removeTag = (index: number) => {
    setTags(tags.filter((_, i) => i !== index));
  };

  return (
    <div
      className={`flex min-h-[42px] items-center flex-wrap gap-1 border border-accent  dark:border-white/40 rounded-md px-2 py-1 focus-within:ring-2 focus-within:ring-ring ${containerClassName}`}
      onClick={() => inputRef.current?.focus()}
    >
      {tags.map((tag, index) => (
        <Badge
          key={index}
          variant="default"
          className="flex items-center gap-1 px-2 py-1"
        >
          {tag}
          <Button
            type="button"
            variant="ghost"
            size="icon"
            className="h-4 w-4 p-0"
            onClick={() => removeTag(index)}
          >
            <X className="h-3 w-3" />
          </Button>
        </Badge>
      ))}
      <input
        ref={inputRef}
        value={plots}
        onChange={handlePlotChange}
        onKeyDown={handleKeyDown}
        onBlur={handleBlur}
        className={`flex-1  bg-transparent outline-none text-sm py-1 ${inputClassName}`}
        placeholder="Type and press enter or comma..."
      />
    </div>
  );
};

export default InputWithTags;
