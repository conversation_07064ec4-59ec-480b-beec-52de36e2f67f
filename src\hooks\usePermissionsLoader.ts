import { useState, useEffect } from 'react';
import { useRefreshPermissions } from './useRefreshPermissions';

/**
 * Hook to load permissions before rendering content
 * This can be used in any component that needs to ensure permissions are loaded
 * 
 * @param onPermissionsLoaded - Optional callback to run when permissions are loaded
 * @returns Object containing loading state and refresh function
 */
export const usePermissionsLoader = (onPermissionsLoaded?: () => void) => {
  const [isLoading, setIsLoading] = useState(true);
  const { refreshPermissions } = useRefreshPermissions();

  useEffect(() => {
    // Function to load permissions
    const loadPermissions = async () => {
      setIsLoading(true);
      try {
        // Refresh permissions from the server
        await refreshPermissions();
        
        // Wait a short time to ensure Redux store is updated
        setTimeout(() => {
          setIsLoading(false);
          // Call the callback if provided
          if (onPermissionsLoaded) {
            onPermissionsLoaded();
          }
        }, 100);
      } catch (error) {
        console.error('Error loading permissions:', error);
        setIsLoading(false);
      }
    };

    // Load permissions when the component mounts
    loadPermissions();
  }, [refreshPermissions, onPermissionsLoaded]);

  // Function to manually refresh permissions
  const reloadPermissions = async () => {
    setIsLoading(true);
    try {
      await refreshPermissions();
      setIsLoading(false);
    } catch (error) {
      console.error('Error reloading permissions:', error);
      setIsLoading(false);
    }
  };

  return {
    isLoading,
    reloadPermissions,
  };
};