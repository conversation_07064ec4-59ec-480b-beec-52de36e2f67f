import {
  Sheet,
  Sheet<PERSON>ontent,
  Sheet<PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON>er,
  Sheet<PERSON><PERSON>le,
  Sheet<PERSON><PERSON>ger,
} from "@/components/ui/sheet";
import { X } from "lucide-react";
import EngagementForm from "./forms/EngagementForm";
import ComplaintsForm from "./forms/ComplaintsForm";
import FeedbackForm from "./forms/FeedbackForm";
import NotesForm from "./forms/NotesForm";
import FlagsForm from "./forms/FlagsForm";
import NotificationForm from "./forms/NotificationForm";
import TicketsForm from "./forms/TicketsForm";
import ReminderForm from "./forms/ReminderForm";
import { ScrollArea } from "@radix-ui/react-scroll-area";

type Props = {
  serviceType: string;
  isOpen: boolean;
  onOpenChange: (e: boolean) => void;
  prospect_id: string;
  employee_no: string;
};

const ProspectDrawer = ({
  serviceType,
  isOpen,
  onOpenChange,
  prospect_id,
  employee_no,
}: Props) => {
  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetContent
        // ref={ref}
        side="right"
        // className={cn("p-0 gap-0 bg-background focus:outline-none",)}
      >
        <ScrollArea className="max-h-[100vh] overflow-y-scroll">
          <div className="p-5 border-b sticky top-0 bg-background z-10 flex justify-between items-center">
            <div>
              {serviceType && <SheetTitle>Add {serviceType}</SheetTitle>}
              {serviceType && (
                <SheetDescription>
                  Create {serviceType} this prospect
                </SheetDescription>
              )}
            </div>
            <button
              className="rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
              onClick={() => onOpenChange?.(false)}
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </button>
          </div>
          <div className="p-3 overflow-auto">
            {serviceType == "engagements" && (
              <EngagementForm
                prospect_id={prospect_id}
                employee_no={employee_no}
                onOpenChange={onOpenChange}
              />
            )}
            {serviceType == "reminders" && (
              <ReminderForm
                prospect_id={prospect_id}
                onOpenChange={onOpenChange}
              />
            )}
            {serviceType == "tickets" && (
              <TicketsForm
                prospect_id={prospect_id}
                onOpenChange={onOpenChange}
              />
            )}
            {serviceType == "complaints" && (
              <ComplaintsForm
                prospect_id={prospect_id}
                onOpenChange={onOpenChange}
              />
            )}
            {serviceType == "feedback" && (
              <FeedbackForm
                prospect_id={prospect_id}
                onOpenChange={onOpenChange}
              />
            )}
            {serviceType == "flags" && (
              <FlagsForm
                prospect_id={prospect_id}
                onOpenChange={onOpenChange}
              />
            )}
            {serviceType == "notes" && (
              <NotesForm
                prospect_id={prospect_id}
                onOpenChange={onOpenChange}
              />
            )}
            {serviceType == "notifications" && (
              <NotificationForm
                prospect_id={prospect_id}
                onOpenChange={onOpenChange}
              />
            )}
          </div>
        </ScrollArea>
      </SheetContent>
    </Sheet>
  );
};

export default ProspectDrawer;
