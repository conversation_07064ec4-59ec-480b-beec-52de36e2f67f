// Import Dependencies
import { <PERSON> } from "react-router-dom"; 
import React from "react";


import { ButtonProps } from "./buttons";


interface PolymorphicButtonProps extends ButtonProps {
  as?: React.ElementType; 
}

const PolymorphicButton: React.FC<PolymorphicButtonProps> = ({
  as: Component = 'button', 
  children,
  className = '',
  ...props
}) => {
  return (
    <Component
      {...props}
      className={`px-4 py-2 text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-[hsl(var(--ring))] focus:ring-offset-2 ${className}`}
    >
      {children}
    </Component>
  );
};

export default PolymorphicButton;



