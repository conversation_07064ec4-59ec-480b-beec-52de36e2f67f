import { useState } from "react";
import OLProgressStepper from "./OL-ProgressStepper";
import PersonalDetailsForm from "./PersonalDetailsForm";
import NextOfKinForm from "./NextOfKinForm";
import PaymentPlanForm from "./PaymentPlanForm";
import TermsAndConditions from "./TermsAndCondition";
import ReviewAndSubmit from "./ReviewAndSubmit";
import Logo from '@/assets/logo.svg';
import OnboardingForm from "./OnboardingForm";


const OLMultiStep = () => {
    const [currentStep, setCurrentStep] = useState(2);
    const [formData, setFormData] = useState({});

    const steps = [
        { id: 1, title: 'Onboarding', completed: currentStep > 1, current: currentStep === 1 },
        { id: 2, title: 'Personal Details', completed: currentStep > 2, current: currentStep === 2 },
        { id: 3, title: 'Next of Kin Info', completed: currentStep > 3, current: currentStep === 3 },
        { id: 4, title: 'Payment Plan', completed: currentStep > 4, current: currentStep === 4 },
        { id: 5, title: 'T&Cs', completed: currentStep > 5, current: currentStep === 5 },
        { id: 6, title: 'Review & Submit', completed: currentStep > 6, current: currentStep === 6 },
    ];

    const handleNext = () => {
        if (currentStep < 6) {
            setCurrentStep(currentStep + 1);
        }
    };

    const handleBack = () => {
        if (currentStep > 1) {
            setCurrentStep(currentStep - 1);
        }
    };

    const handleSubmit = () => {
        alert('Application submitted successfully!');
        console.log('Final form data:', formData);
    };

    const renderCurrentStep = () => {
        switch (currentStep) {
            case 1:
                return (
                    <OnboardingForm
                        formData={formData}
                        setFormData={setFormData}
                        onNext={handleNext}
                    />
                );
            case 2:
                return (
                    <PersonalDetailsForm
                        formData={formData}
                        setFormData={setFormData}
                        onNext={handleNext}
                        onBack={handleBack}
                    />
                );
            case 3:
                return (
                    <NextOfKinForm
                        formData={formData}
                        setFormData={setFormData}
                        onNext={handleNext}
                        onBack={handleBack}
                    />
                );
            case 4:
                return (
                    <PaymentPlanForm
                        formData={formData}
                        setFormData={setFormData}
                        onNext={handleNext}
                        onBack={handleBack}
                    />
                );
            case 5:
                return (
                    <TermsAndConditions
                        formData={formData}
                        setFormData={setFormData}
                        onNext={handleNext}
                        onBack={handleBack}
                    />
                );
            case 6:
                return (
                    <ReviewAndSubmit
                        formData={formData}
                        onBack={handleBack}
                        onSubmit={handleSubmit}
                    />
                );
            default:
                return (
                    <OnboardingForm
                    formData={formData}
                    setFormData={setFormData}
                    onNext={handleNext}
                />
                );
        }
    };

    return (
        <div className="min-h-screen">
            <div className="container mx-auto px-4 py-8">
                {/* Optiven Logo */}
                <div className="flex justify-center mb-8">
                    <img src={Logo} alt={'logo'} className="w-64" />
                </div>

                {/* Progress Stepper */}
                {currentStep > 1 && <OLProgressStepper steps={steps} />}
                {/* Form Content */}
                <div className="max-w-4xl mx-auto">
                    <div className=" p-8">
                        {renderCurrentStep()}
                    </div>
                </div>
            </div>
        </div>
    )
}

export default OLMultiStep;