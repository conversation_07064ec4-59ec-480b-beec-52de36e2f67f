import LazyModal, { TableColumn } from '@/components/reports/ReportsModal';
import { formatNumberWithCommas, formatShortDate } from '@/utils/salesDataFormatter';

interface CashOnCash {
    id: number;
    Period: string;
    Transaction_date: string;
    client_no: string;
    client_name: string;
    plot_no: string;
    amount: number;
    bonus: number;
    Regional_Category: string;
    marketer_id: string;
}

interface CashOnCashReportProps {
    isModalOpen: boolean;
    setIsModalOpen: (state: boolean) => void;
}

const CashOnCashReport = ({ isModalOpen, setIsModalOpen }: CashOnCashReportProps) => {

    // Define table columns with proper typing
    const columns: TableColumn<CashOnCash>[] = [
        {
            key: 'client_name',
            title: 'Client',
        },
        {
            key: 'Regional_Category',
            title: 'Region',
        },
        {
            key: 'amount',
            title: 'Amount',
            render: (value: string) => {
                return <span>{formatNumberWithCommas(value)}</span>
            }
        },
        {
            key: 'bonus',
            title: 'Bonus',
            render: (value: string) => {
                return <span>{formatNumberWithCommas(value)}</span>
            },
        },
        {
            key: 'Period',
            title: 'Period',
            render: (value: string) => {
                return <span>{formatShortDate(value)}</span>
            },
        },
        {
            key: 'Transaction_date',
            title: 'Transaction Date',
            render: (value: string) => {
                return <span>{formatShortDate(value)}</span>
            },
        },
        {
            key: 'plot_no',
            title: 'Plot No',
        },
    ]

    const handleCloseModal = () => {
        setIsModalOpen(false);
    }

    return (
        <LazyModal<CashOnCash>
            isOpen={isModalOpen}
            onClose={handleCloseModal}
            title="Cash On Cash Report"
            url="/cash-on-cash-sales"  // Your actual API endpoint
            params={{}}
            columns={columns}
            size="lg"
        />
    )
}

export default CashOnCashReport