import { useState, useEffect } from 'react';
import { useRefreshPermissions } from './useRefreshPermissions';
import { useSalesPermissions } from './useSalesPermissions';
import { useCustomerPermissions } from './useCustomerPermissions';
import { useProspectPermissions } from './useProspectPermissions';

/**
 * Hook to preload all permissions before rendering content
 * This ensures that all permission checks are completed before displaying any content
 * 
 * @returns Object containing loading state and permission hooks
 */
export const usePreloadPermissions = () => {
  const [isLoading, setIsLoading] = useState(true);
  const { refreshPermissions } = useRefreshPermissions();
  
  // Initialize all permission hooks
  const salesPermissions = useSalesPermissions();
  const customerPermissions = useCustomerPermissions();
  const prospectPermissions = useProspectPermissions();

  useEffect(() => {
    // Function to load all permissions
    const loadAllPermissions = async () => {
      setIsLoading(true);
      try {
        // Refresh permissions from the server
        await refreshPermissions();
        
        // Wait a short time to ensure Redux store is updated
        // This helps ensure that the permission hooks have the latest data
        setTimeout(() => {
          setIsLoading(false);
        }, 100);
      } catch (error) {
        console.error('Error loading permissions:', error);
        setIsLoading(false);
      }
    };

    // Load permissions when the component mounts
    loadAllPermissions();
  }, [refreshPermissions]);

  return {
    isLoading,
    salesPermissions,
    customerPermissions,
    prospectPermissions,
    refreshPermissions,
  };
};