import { useState,useEffect } from "react";
import { 
  MapPin,
  Calendar,
  Clock,
  User,
  Car,
  CheckCircle2,
  XCircle,
  Eye,
  Search,
  Filter,
  Download,
  ChevronDown,
  ArrowUpDown,
  Phone,
  Mail,
  Building
} from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardT<PERSON>le, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useGetCustomerSiteVisitsQuery } from "@/redux/slices/customersApiSlice";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface CustomerSiteVisitsTabProps {
  customerId: string;
  customerName?: string;
  customerPhone?: string;
  customerEmail?: string;
}

interface SiteVisit {
  id: number;
  pickup_time: string;
  pickup_date: string;
  pickup_location: string;
  special_assignment_destination?: string;
  remarks?: string;
  created_at: string;
  status: string;
  marketer: string;
  vehicle?: number;
  project: string;
  driver?: string;
  special_assignment_assigned_to?: string;
}

interface SiteVisitsData {
  results: SiteVisit[];
  count: number;
  next: string | null;
  previous: string | null;
}

interface FilterState {
  status: string;
  marketer: string;
  project: string;
  vehicle: string;
  driver: string;
  pickup_location: string;
}

interface SiteVisitSummary {
  totalVisits: number;
  pendingVisits: number;
  completedVisits: number;
  cancelledVisits: number;
  approvedVisits: number;
  inProgressVisits: number;
}

const CustomerSiteVisitsTab = ({ 
  customerId, 
  customerName, 
  customerPhone, 
  customerEmail 
}: CustomerSiteVisitsTabProps) => {

  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [filters, setFilters] = useState<FilterState>({
    status: "",
    marketer: "",
    project: "",
    vehicle: "",
    driver: "",
    pickup_location: ""
  });
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10;

  useEffect(() => {
  const timer = setTimeout(() => {
    setDebouncedSearchTerm(searchTerm);
  }, 500); // 500ms delay

  return () => clearTimeout(timer);
}, [searchTerm]);

useEffect(() => {
  setCurrentPage(1);
}, [debouncedSearchTerm]);

useEffect(() => {
  setIsSearching(true);
  const timer = setTimeout(() => {
    setDebouncedSearchTerm(searchTerm);
    setIsSearching(false);
  }, 500);

  return () => {
    clearTimeout(timer);
    setIsSearching(false);
  };
}, [searchTerm]);

  // Construct query parameters for site visits endpoint
  const queryParams = {
    // Filter by customer details
    ...(customerName && { site_visist_client__name: customerName }),
    ...(customerPhone && { site_visist_client__phone_number: customerPhone }),
    ...(customerEmail && { site_visist_client__email: customerEmail }),
    
    // Additional filters
    search: debouncedSearchTerm || undefined,
    status: filters.status || undefined,
    marketer: filters.marketer || undefined,
    project: filters.project || undefined,
    vehicle: filters.vehicle || undefined,
    driver: filters.driver || undefined,
    pickup_location: filters.pickup_location || undefined,
    page: currentPage,
    page_size: pageSize
  };

  // Fetch customer site visits data
  const { 
    data: siteVisitsData, 
    isLoading, 
    isError,
    refetch 
  } = useGetCustomerSiteVisitsQuery(queryParams);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleNextPage = () => {
    if (siteVisitsData && siteVisitsData.next) {
      setCurrentPage(prev => prev + 1);
    }
  };

  const handlePreviousPage = () => {
    if (siteVisitsData && siteVisitsData.previous && currentPage > 1) {
      setCurrentPage(prev => prev - 1);
    }
  };

  // Calculate summary statistics
  const calculateSummary = (): SiteVisitSummary => {
    if (!siteVisitsData || !siteVisitsData.results || siteVisitsData.results.length === 0) {
      return {
        totalVisits: 0,
        pendingVisits: 0,
        completedVisits: 0,
        cancelledVisits: 0,
        approvedVisits: 0,
        inProgressVisits: 0
      };
    }

    const results = siteVisitsData.results;
    return {
      totalVisits: results.length,
      pendingVisits: results.filter((visit:any) => visit.status === "Pending").length,
      completedVisits: results.filter((visit:any) => visit.status === "Completed").length,
      cancelledVisits: results.filter((visit:any) => visit.status === "Cancelled" || visit.status === "Rejected").length,
      approvedVisits: results.filter((visit:any) => visit.status === "Approved").length,
      inProgressVisits: results.filter((visit:any) => visit.status === "In Progress").length
    };
  };

  const summary = calculateSummary();

  // Status color mapping
  const getStatusColor = (status: string): string => {
    const statusColors: Record<string, string> = {
      "Pending": "bg-yellow-50 text-yellow-800 border-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-300 dark:border-yellow-800",
      "Approved": "bg-blue-50 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800",
      "In Progress": "bg-purple-50 text-purple-800 border-purple-200 dark:bg-purple-900/30 dark:text-purple-300 dark:border-purple-800",
      "Completed": "bg-green-50 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300 dark:border-green-800",
      "Reviewed": "bg-indigo-50 text-indigo-800 border-indigo-200 dark:bg-indigo-900/30 dark:text-indigo-300 dark:border-indigo-800",
      "Cancelled": "bg-red-50 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-300 dark:border-red-800",
      "Rejected": "bg-red-50 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-300 dark:border-red-800"
    };

    return statusColors[status] || "bg-gray-50 text-gray-800 border-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-700";
  };

  // Extract unique values for filter dropdowns
  const getUniqueValues = (field: keyof SiteVisit): string[] => {
    if (!siteVisitsData || !siteVisitsData.results) return [];
    const uniqueValues = new Set<string>(
      siteVisitsData.results
        .map((item: SiteVisit) => item[field])
        .filter(Boolean)
        .map((value: unknown) => String(value))
    );
    return Array.from(uniqueValues);
  };

  // Format date values
  const formatDate = (dateString: string | null): string => {
    if (!dateString) return "N/A";
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return "N/A";
    }
  };

  // Format time values
  const formatTime = (timeString: string | null): string => {
    if (!timeString) return "N/A";
    try {
      return new Date(`2000-01-01T${timeString}`).toLocaleTimeString([], { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } catch {
      return timeString;
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <SpinnerTemp type="spinner-double" size="lg" />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="p-4">
        <Alert variant="destructive">
          <AlertDescription>
            Error loading site visits data. Please try again later.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Site Visits Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="border-0 shadow-sm bg-white dark:bg-gray-900">
          <CardContent className="p-6">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Site Visits</p>
                <h3 className="text-2xl font-bold mt-1 text-gray-900 dark:text-white">
                  {summary.totalVisits}
                </h3>
              </div>
              <div className="bg-blue-100 p-3 rounded-full dark:bg-blue-900/30">
                <MapPin className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm bg-white dark:bg-gray-900">
          <CardContent className="p-6">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Completed Visits</p>
                <h3 className="text-2xl font-bold mt-1 text-gray-900 dark:text-white">
                  {summary.completedVisits}
                </h3>
              </div>
              <div className="bg-green-100 p-3 rounded-full dark:bg-green-900/30">
                <CheckCircle2 className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm bg-white dark:bg-gray-900">
          <CardContent className="p-6">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Pending Visits</p>
                <h3 className="text-2xl font-bold mt-1 text-gray-900 dark:text-white">
                  {summary.pendingVisits}
                </h3>
              </div>
              <div className="bg-yellow-100 p-3 rounded-full dark:bg-yellow-900/30">
                <Clock className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm bg-white dark:bg-gray-900">
          <CardContent className="p-6">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Cancelled Visits</p>
                <h3 className="text-2xl font-bold mt-1 text-gray-900 dark:text-white">
                  {summary.cancelledVisits}
                </h3>
              </div>
              <div className="bg-red-100 p-3 rounded-full dark:bg-red-900/30">
                <XCircle className="h-6 w-6 text-red-600 dark:text-red-400" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Site Visits Table */}
      <Card className="border-0 shadow-sm bg-white dark:bg-gray-900">
        <CardHeader className="border-b bg-gray-50 dark:bg-gray-800/50 dark:border-gray-700 p-4">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div>
              <CardTitle className="text-lg font-medium">Site Visit Records</CardTitle>
              {(customerPhone || customerName || customerEmail) && (
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  Filtered by customer: {customerName && customerName}{customerPhone && ` (${customerPhone})`}
                </p>
              )}
            </div>
            <div className="flex flex-wrap gap-2">
             <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500 dark:text-gray-400" />
              <Input
                type="search"
                placeholder="Search records..."
                className="pl-8 h-9 w-48 md:w-64 bg-secondary"
                value={searchTerm}
                onChange={handleSearchChange}
              />
              {isSearching && (
                <div className="absolute right-2.5 top-2.5">
                  <div className="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
                </div>
              )}
            </div>                 
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="h-9 w-9 p-0 bg-secondary" 
                    title="Export"
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Export Options</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>
                    <Download className="h-4 w-4 mr-2" />
                    Export as CSV
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Download className="h-4 w-4 mr-2" />
                    Export as PDF
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="bg-secondary dark:bg-gray-800/50">
                  <TableHead className="font-medium">
                    <div className="flex items-center">
                      Visit ID
                      <ArrowUpDown className="ml-1 h-4 w-4" />
                    </div>
                  </TableHead>
                  <TableHead className="font-medium">
                    <div className="flex items-center">
                      Date & Time
                      <ArrowUpDown className="ml-1 h-4 w-4" />
                    </div>
                  </TableHead>
                  <TableHead className="font-medium">Pickup Location</TableHead>
                  <TableHead className="font-medium">Project</TableHead>
                  <TableHead className="font-medium">Status</TableHead>
                  <TableHead className="font-medium">Marketer</TableHead>
                  <TableHead className="font-medium">Driver</TableHead>
                  <TableHead className="font-medium">Destination</TableHead>
                  <TableHead className="sr-only">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {siteVisitsData && siteVisitsData.results && siteVisitsData.results.length > 0 ? (
                  siteVisitsData.results.map((visit: SiteVisit) => (
                    <TableRow key={visit.id} className="hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
                      <TableCell className="font-medium text-blue-600 dark:text-blue-400">
                        #{visit.id}
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="font-medium">{formatDate(visit.pickup_date)}</span>
                          <span className="text-sm text-gray-500 dark:text-gray-400">
                            {formatTime(visit.pickup_time)}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <MapPin className="h-4 w-4 mr-2 text-gray-400" />
                          {visit.pickup_location}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Building className="h-4 w-4 mr-2 text-gray-400" />
                          {visit.project}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={`${getStatusColor(visit.status)}`}>
                          {visit.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <User className="h-4 w-4 mr-2 text-gray-400" />
                          {visit.marketer}
                        </div>
                      </TableCell>
                      <TableCell>
                        {visit.driver ? (
                          <div className="flex items-center">
                            <Car className="h-4 w-4 mr-2 text-gray-400" />
                            {visit.driver}
                          </div>
                        ) : (
                          <span className="text-gray-400">Not assigned</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {visit.special_assignment_destination || visit.project || "Standard visit"}
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <ChevronDown className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem>
                              <Eye className="h-4 w-4 mr-2" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <MapPin className="h-4 w-4 mr-2" />
                              View Location
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Phone className="h-4 w-4 mr-2" />
                              Contact Marketer
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem>
                              <Calendar className="h-4 w-4 mr-2" />
                              Reschedule
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={9} className="h-24 text-center">
                      {isLoading ? "Loading site visit records..." : "No site visit records found for this customer"}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
        <CardFooter className="flex items-center justify-between border-t p-4 bg-gray-50 dark:bg-gray-800/50 dark:border-gray-700">
          <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
            <span>
              Showing {siteVisitsData && siteVisitsData.results ? 
                ((currentPage - 1) * pageSize) + 1 : 0} to{' '}
              {siteVisitsData ? 
                Math.min(currentPage * pageSize, siteVisitsData.total_data || siteVisitsData.count || 0) : 0} of{' '}
              {siteVisitsData?.total_data || siteVisitsData?.count || 0} results
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handlePreviousPage}
              disabled={!siteVisitsData?.previous || currentPage === 1}
              className="h-8"
            >
              Previous
            </Button>
            <div className="flex items-center space-x-1">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Page {currentPage}
              </span>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleNextPage}
              disabled={!siteVisitsData?.next}
              className="h-8"
            >
              Next
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};

export default CustomerSiteVisitsTab;