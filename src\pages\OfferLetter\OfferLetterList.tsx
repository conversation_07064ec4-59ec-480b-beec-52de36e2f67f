import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Screen } from '@/app-components/layout/screen';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Pagination, 
  PaginationContent, 
  PaginationItem, 
  PaginationLink, 
  PaginationNext, 
  PaginationPrevious 
} from '@/components/ui/pagination';
import { 
  FileText, 
  Search, 
  Plus, 
  Eye, 
  Download, 
  Mail 
} from 'lucide-react';
import { 
  useGetOfferLettersQuery,
  useGenerateOfferLetterPDFMutation,
  useSendOfferLetterEmailMutation
} from './api/offerLetterApi';
import { CustomerType } from './OfferLetter';

const OfferLetterList: React.FC = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  
  // Query parameters for the API
  const queryParams = {
    page: currentPage,
    page_size: pageSize,
    search: searchTerm || undefined
  };
  
  // Fetch offer letters data
  const { 
    data: offerLettersData, 
    isLoading, 
    isFetching, 
    error,
    isError
  } = useGetOfferLettersQuery(queryParams, {
    // Add error handling
    refetchOnMountOrArgChange: true,
    skip: false
  });
  
  // Debug API response
  React.useEffect(() => {
    if (offerLettersData) {
      console.log('Offer Letters Data:', offerLettersData);
    }
    if (isError) {
      console.error('API Error:', error);
    }
  }, [offerLettersData, isError, error]);
  
  // Mutations for actions
  const [generatePDF, { isLoading: isGeneratingPDF }] = useGenerateOfferLetterPDFMutation();
  const [sendEmail, { isLoading: isSendingEmail }] = useSendOfferLetterEmailMutation();
  
  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1); // Reset to first page when search changes
  };
  
  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  
  // Navigate to create new offer letter
  const handleCreateNew = () => {
    navigate('/offer-letter');
  };
  
  // View offer letter details
  const handleViewDetails = (id: number) => {
    // For now, we'll just navigate to the offer letter page with the ID
    // In the future, you might want to create a dedicated view page
    navigate(`/offer-letter?id=${id}`);
  };
  
  // Generate PDF for an offer letter
  const handleGeneratePDF = async (id: number) => {
    try {
      const response = await generatePDF(id).unwrap();
      // Open the PDF in a new tab
      if (response.pdf_url) {
        window.open(response.pdf_url, '_blank');
      }
    } catch (error) {
      console.error('Failed to generate PDF:', error);
      // You might want to show an error toast here
    }
  };
  
  // Send offer letter via email
  const handleSendEmail = async (id: number) => {
    try {
      await sendEmail({ id }).unwrap();
      // You might want to show a success toast here
    } catch (error) {
      console.error('Failed to send email:', error);
      // You might want to show an error toast here
    }
  };
  
  // Helper function to get customer type label
  const getCustomerTypeLabel = (type?: CustomerType) => {
    switch (type) {
      case 'individual':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700">Individual</Badge>;
      case 'company':
        return <Badge variant="outline" className="bg-purple-50 text-purple-700">Company</Badge>;
      case 'group':
        return <Badge variant="outline" className="bg-amber-50 text-amber-700">Group</Badge>;
      case 'partners':
        return <Badge variant="outline" className="bg-green-50 text-green-700">Partners</Badge>;
      default:
        return <Badge variant="outline" className="bg-gray-50 text-gray-700">Unknown</Badge>;
    }
  };
  
  // Helper function to get completion status label
  const getCompletionStatusLabel = (isCompleted?: boolean) => {
    return isCompleted 
      ? <Badge variant="outline" className="bg-green-50 text-green-700">Completed</Badge>
      : <Badge variant="outline" className="bg-amber-50 text-amber-700">In Progress</Badge>;
  };
  
  // Calculate total pages
  const totalPages = offerLettersData && offerLettersData.count ? Math.ceil(offerLettersData.count / pageSize) : 0;
  
  // Generate pagination items
  const paginationItems = [];
  const maxPagesToShow = 5;
  
  if (totalPages <= maxPagesToShow) {
    // Show all pages if total pages is less than or equal to maxPagesToShow
    for (let i = 1; i <= totalPages; i++) {
      paginationItems.push(i);
    }
  } else {
    // Show a subset of pages with ellipsis
    if (currentPage <= 3) {
      // Near the start
      for (let i = 1; i <= 4; i++) {
        paginationItems.push(i);
      }
      paginationItems.push('ellipsis');
      paginationItems.push(totalPages);
    } else if (currentPage >= totalPages - 2) {
      // Near the end
      paginationItems.push(1);
      paginationItems.push('ellipsis');
      for (let i = totalPages - 3; i <= totalPages; i++) {
        paginationItems.push(i);
      }
    } else {
      // Middle
      paginationItems.push(1);
      paginationItems.push('ellipsis');
      for (let i = currentPage - 1; i <= currentPage + 1; i++) {
        paginationItems.push(i);
      }
      paginationItems.push('ellipsis');
      paginationItems.push(totalPages);
    }
  }
  
  return (
    <Screen>
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div className="flex items-center space-x-2">
              <FileText className="h-6 w-6 text-primary" />
              <CardTitle>Offer Letters</CardTitle>
            </div>
            <Button onClick={handleCreateNew} className="flex items-center space-x-1">
              <Plus className="h-4 w-4" />
              <span>Create New</span>
            </Button>
          </CardHeader>
          
          <CardContent>
            {/* Search and filter section */}
            <div className="mb-6 flex items-center space-x-2">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500" />
                <Input
                  placeholder="Search by plot number, customer name, or booking ID..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                  className="pl-10"
                />
              </div>
            </div>
            
            {/* Table section */}
            {isLoading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : isError ? (
              <div className="py-8 text-center text-red-500">
                Failed to load offer letters. Please try again later.
                {error && typeof error === 'object' && 'status' in error && (
                  <p className="mt-2 text-sm">Error {(error as any).status}: {(error as any).data?.detail || 'Unknown error'}</p>
                )}
              </div>
            ) : !offerLettersData || !offerLettersData.results || offerLettersData.results.length === 0 ? (
              <div className="py-8 text-center text-gray-500">
                No offer letters found. Create your first one!
              </div>
            ) : (
              <>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>ID</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Plot Number</TableHead>
                        <TableHead>Booking ID</TableHead>
                        <TableHead>Customer Type</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {Array.isArray(offerLettersData?.results) && offerLettersData.results.map((offerLetter) => (
                        <TableRow key={offerLetter.id}>
                          <TableCell className="font-medium">{offerLetter.id}</TableCell>
                          <TableCell>{new Date(offerLetter.date || '').toLocaleDateString()}</TableCell>
                          <TableCell>{offerLetter.plot_number || 'N/A'}</TableCell>
                          <TableCell>{offerLetter.booking_id || 'N/A'}</TableCell>
                          <TableCell>{getCustomerTypeLabel(offerLetter.customer_type as CustomerType)}</TableCell>
                          <TableCell>{getCompletionStatusLabel(offerLetter.is_completed)}</TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end space-x-2">
                              <Button 
                                variant="ghost" 
                                size="icon" 
                                onClick={() => handleViewDetails(offerLetter.id!)}
                                title="View Details"
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                              <Button 
                                variant="ghost" 
                                size="icon" 
                                onClick={() => handleGeneratePDF(offerLetter.id!)}
                                disabled={isGeneratingPDF}
                                title="Download PDF"
                              >
                                <Download className="h-4 w-4" />
                              </Button>
                              <Button 
                                variant="ghost" 
                                size="icon" 
                                onClick={() => handleSendEmail(offerLetter.id!)}
                                disabled={isSendingEmail}
                                title="Send Email"
                              >
                                <Mail className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
                
                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="mt-6">
                    <Pagination>
                      <PaginationContent>
                        <PaginationItem>
                          <PaginationPrevious 
                            onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                            className={currentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                          />
                        </PaginationItem>
                        
                        {paginationItems.map((item, index) => (
                          item === 'ellipsis' ? (
                            <PaginationItem key={`ellipsis-${index}`}>
                              <span className="px-4 py-2">...</span>
                            </PaginationItem>
                          ) : (
                            <PaginationItem key={`page-${item}`}>
                              <PaginationLink
                                onClick={() => handlePageChange(item as number)}
                                isActive={currentPage === item}
                                className="cursor-pointer"
                              >
                                {item}
                              </PaginationLink>
                            </PaginationItem>
                          )
                        ))}
                        
                        <PaginationItem>
                          <PaginationNext 
                            onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                            className={currentPage === totalPages ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                          />
                        </PaginationItem>
                      </PaginationContent>
                    </Pagination>
                  </div>
                )}
                
                {/* Results summary */}
                <div className="mt-4 text-sm text-gray-500">
                  Showing {offerLettersData?.results?.length || 0} of {offerLettersData?.count || 0} results
                  {isFetching && !isLoading && (
                    <span className="ml-2 inline-block animate-pulse">Refreshing...</span>
                  )}
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>
    </Screen>
  );
};

export default OfferLetterList;