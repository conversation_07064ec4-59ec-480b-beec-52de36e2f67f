import { useState, useMemo } from "react";
import { Screen } from "@/app-components/layout/screen";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import {
  Search,
  Filter,
  Calendar,
  Clock,
  AlertTriangle,
  AlertCircle,
  Circle,
  CheckCircle2,
  Bell,
  BellOff,
  Tag,
  Repeat,
  Pencil,
  Trash2,
  Plus,
  SortAsc,
  SortDesc,
  List
} from "lucide-react";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import {
  useGetRemindersQuery,
  useDeleteReminderMutation,
  useSnoozeReminderMutation,
} from "@/redux/slices/reminderApiSlice";
import { useToast } from "@/hooks/use-toast";

interface Reminder {
  reminder_id?: string;
  title: string;
  description?: string;
  reminder_type?: string;
  priority?: string;
  status?: string;
  remind_at: string;
  advance_notice_minutes?: number;
  repeat_pattern?: string;
  tags?: string | null;
  is_active?: boolean;
  created_at?: string;
  entity_name?: string;
  is_due?: boolean;
  is_overdue?: boolean;
  related_entity?: any;
}

export default function ReminderList() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [priorityFilter, setPriorityFilter] = useState("all");
  const [typeFilter, setTypeFilter] = useState("all");
  const [sortBy, setSortBy] = useState("remind_at");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const { toast } = useToast();

  // API hooks
  const {
    data: remindersData = [],
    error: remindersError,
    isLoading: remindersLoading,
    refetch: refetchReminders,
  } = useGetRemindersQuery({});

  const [deleteReminder] = useDeleteReminderMutation();
  const [snoozeReminder] = useSnoozeReminderMutation();

  // Debug logging
  console.log("ReminderList - API State:", {
    data: remindersData,
    error: remindersError,
    isLoading: remindersLoading
  });

  // Helper functions
  const getPriorityIcon = (priority?: string) => {
    switch (priority) {
      case 'Critical': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'High': return <AlertCircle className="h-4 w-4 text-orange-500" />;
      case 'Normal': return <Circle className="h-4 w-4 text-blue-500" />;
      case 'Low': return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      default: return <Circle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getPriorityColor = (priority?: string) => {
    switch (priority) {
      case 'Critical': return 'border-red-200 bg-red-50 text-red-700';
      case 'High': return 'border-orange-200 bg-orange-50 text-orange-700';
      case 'Normal': return 'border-blue-200 bg-blue-50 text-blue-700';
      case 'Low': return 'border-green-200 bg-green-50 text-green-700';
      default: return 'border-gray-200 bg-gray-50 text-gray-700';
    }
  };

  const getTypeIcon = (type?: string) => {
    switch (type) {
      case 'Task': return '✅';
      case 'Appointment': return '📅';
      case 'Meeting': return '🤝';
      case 'Birthday': return '🎂';
      case 'Anniversary': return '💝';
      case 'Payment': return '💳';
      case 'Health': return '🏥';
      default: return '📋';
    }
  };

  const getStatusColor = (status?: string) => {
    switch (status?.toLowerCase()) {
      case 'completed': return 'bg-green-100 text-green-800 border-green-200';
      case 'active': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'snoozed': return 'bg-amber-100 text-amber-800 border-amber-200';
      case 'cancelled': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Process and filter reminders
  const reminders = useMemo(() => {
    console.log("ReminderList - Raw reminders data:", remindersData);

    // Handle both possible response structures
    let remindersArray = [];
    if (Array.isArray(remindersData?.results)) {
      remindersArray = remindersData.results;
    } else if (Array.isArray(remindersData?.data)) {
      remindersArray = remindersData.data;
    } else if (Array.isArray(remindersData)) {
      remindersArray = remindersData;
    }

    console.log("ReminderList - Extracted reminders array:", remindersArray);

    if (!Array.isArray(remindersArray)) return [];

    let filtered = remindersArray.filter((reminder: Reminder) => {
      const matchesSearch = reminder.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           reminder.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           (reminder.tags && reminder.tags.toLowerCase().includes(searchTerm.toLowerCase()));

      const matchesStatus = statusFilter === "all" || reminder.status?.toLowerCase() === statusFilter.toLowerCase();
      const matchesPriority = priorityFilter === "all" || reminder.priority?.toLowerCase() === priorityFilter.toLowerCase();
      const matchesType = typeFilter === "all" || reminder.reminder_type?.toLowerCase() === typeFilter.toLowerCase();

      return matchesSearch && matchesStatus && matchesPriority && matchesType;
    });

    console.log("ReminderList - Filtered reminders:", filtered);

    // Sort reminders
    filtered.sort((a: Reminder, b: Reminder) => {
      let aValue, bValue;

      switch (sortBy) {
        case 'title':
          aValue = a.title.toLowerCase();
          bValue = b.title.toLowerCase();
          break;
        case 'priority':
          const priorityOrder = { 'Critical': 4, 'High': 3, 'Normal': 2, 'Low': 1 };
          aValue = priorityOrder[a.priority as keyof typeof priorityOrder] || 0;
          bValue = priorityOrder[b.priority as keyof typeof priorityOrder] || 0;
          break;
        case 'remind_at':
        default:
          aValue = new Date(a.remind_at).getTime();
          bValue = new Date(b.remind_at).getTime();
          break;
      }

      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return filtered;
  }, [remindersData, searchTerm, statusFilter, priorityFilter, typeFilter, sortBy, sortOrder]);

  // Helper to convert a reminder to a calendar event object
  const convertReminderToEvent = (reminder: Reminder) => ({
    id: reminder.reminder_id,
    title: reminder.title,
    start: new Date(reminder.remind_at),
    end: new Date(reminder.remind_at),
    description: reminder.description,
    status: reminder.status,
    priority: reminder.priority,
    type: reminder.reminder_type,
    tags: reminder.tags,
    allDay: false,
  });

  // Convert reminders to events for calendar view
  const events = useMemo(() => {
    // remindersData is now always an array (or empty array)
    const remindersArray = Array.isArray(remindersData) ? remindersData : [];
    const convertedEvents = remindersArray.map(convertReminderToEvent);
    return convertedEvents;
  }, [remindersData]);

  // Event handlers
  const handleDeleteReminder = async (reminderId: string) => {
    try {
      await deleteReminder(reminderId).unwrap();
      toast({
        title: "Reminder deleted",
        description: "The reminder has been successfully deleted.",
      });
      refetchReminders();
    } catch (error) {
      toast({
        title: "Error deleting reminder",
        description: "There was an error deleting the reminder. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleSnoozeReminder = async (reminderId: string) => {
    const snoozeUntil = new Date();
    snoozeUntil.setHours(snoozeUntil.getHours() + 1);

    try {
      await snoozeReminder({
        id: reminderId,
        remind_at: snoozeUntil.toISOString(),
      }).unwrap();
      toast({
        title: "Reminder snoozed",
        description: "The reminder has been snoozed for 1 hour.",
      });
      refetchReminders();
    } catch (error) {
      toast({
        title: "Error snoozing reminder",
        description: "There was an error snoozing the reminder. Please try again.",
        variant: "destructive",
      });
    }
  };

  const toggleSortOrder = () => {
    setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
  };

  if (remindersLoading) {
    return (
      <Screen>
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-700 mx-auto mb-4"></div>
            <p className="text-slate-600 dark:text-slate-400">Loading reminders...</p>
          </div>
        </div>
      </Screen>
    );
  }

  if (remindersError) {
    return (
      <Screen>
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <p className="text-red-600 dark:text-red-400 mb-4">
              Error loading reminders
            </p>
            <PrimaryButton onClick={() => refetchReminders()}>
              Try Again
            </PrimaryButton>
          </div>
        </div>
      </Screen>
    );
  }

  return (
    <Screen>
      {/* Header */}
      <header className="flex-none px-6 py-4 border-b border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-900 sticky top-0 z-10">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <List className="h-6 w-6 text-green-700" />
            <div>
              <h1 className="text-2xl font-bold text-slate-800 dark:text-slate-100">All Reminders</h1>
              <p className="text-sm text-slate-500 dark:text-slate-400">
                {reminders.length} reminder{reminders.length !== 1 ? 's' : ''} found
              </p>
            </div>
          </div>
          <PrimaryButton className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800">
            <Plus className="h-4 w-4 mr-2" />
            Add Reminder
          </PrimaryButton>
        </div>
      </header>

      {/* Filters and Search */}
      <div className="flex-none p-6 bg-gradient-to-r from-green-50 to-emerald-50 border-b border-green-200">
        <div className="space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search reminders by title, description, or tags..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 border-green-200 focus:border-green-400 focus:ring-green-200"
            />
          </div>

          {/* Filters */}
          <div className="flex flex-wrap gap-4">
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-800">Filters:</span>
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-32 border-green-200">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="snoozed">Snoozed</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>

            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
              <SelectTrigger className="w-32 border-green-200">
                <SelectValue placeholder="Priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priority</SelectItem>
                <SelectItem value="critical">Critical</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="normal">Normal</SelectItem>
                <SelectItem value="low">Low</SelectItem>
              </SelectContent>
            </Select>

            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-32 border-green-200">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="general">General</SelectItem>
                <SelectItem value="task">Task</SelectItem>
                <SelectItem value="appointment">Appointment</SelectItem>
                <SelectItem value="meeting">Meeting</SelectItem>
                <SelectItem value="birthday">Birthday</SelectItem>
                <SelectItem value="payment">Payment</SelectItem>
              </SelectContent>
            </Select>

            <div className="flex items-center gap-2">
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-32 border-green-200">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="remind_at">Date</SelectItem>
                  <SelectItem value="title">Title</SelectItem>
                  <SelectItem value="priority">Priority</SelectItem>
                </SelectContent>
              </Select>

              <Button
                variant="outline"
                size="sm"
                onClick={toggleSortOrder}
                className="border-green-200 hover:bg-green-50"
              >
                {sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Reminders List */}
      <div className="flex-1 p-6 overflow-auto">
        <ScrollArea className="h-full">
          {reminders.length > 0 ? (
            <div className="grid gap-4">
              {reminders.map((reminder) => (
                <Card key={reminder.reminder_id} className="border-2 hover:shadow-lg transition-all duration-200 bg-gradient-to-br from-white to-gray-50">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <span className="text-2xl">{getTypeIcon(reminder.reminder_type)}</span>
                        <div>
                          <h3 className="font-semibold text-gray-800 text-lg">{reminder.title}</h3>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge className={`text-xs ${getStatusColor(reminder.status)}`}>
                              {reminder.status}
                            </Badge>
                            {reminder.priority && (
                              <Badge variant="outline" className={`text-xs ${getPriorityColor(reminder.priority)}`}>
                                <div className="flex items-center gap-1">
                                  {getPriorityIcon(reminder.priority)}
                                  {reminder.priority}
                                </div>
                              </Badge>
                            )}
                            {reminder.status === 'Snoozed' && (
                              <Badge className="bg-amber-100 text-amber-800 border-amber-200">
                                <Bell className="h-3 w-3 mr-1" />
                                Snoozed
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="flex gap-2">
                        {reminder.status !== 'Snoozed' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleSnoozeReminder(reminder.reminder_id!)}
                            className="text-amber-600 border-amber-200 hover:bg-amber-50"
                          >
                            <BellOff className="h-4 w-4" />
                          </Button>
                        )}
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-blue-600 border-blue-200 hover:bg-blue-50"
                        >
                          <Pencil className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteReminder(reminder.reminder_id!)}
                          className="text-red-500 border-red-200 hover:bg-red-50"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="pt-0 space-y-3">
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        <span>{new Date(reminder.remind_at).toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        <span>{new Date(reminder.remind_at).toLocaleTimeString('en-US', {
                          hour: '2-digit',
                          minute: '2-digit'
                        })}</span>
                      </div>
                      {reminder.advance_notice_minutes && (
                        <Badge variant="secondary" className="text-xs">
                          {reminder.advance_notice_minutes}min notice
                        </Badge>
                      )}
                    </div>

                    {reminder.description && (
                      <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
                        {reminder.description}
                      </p>
                    )}

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {reminder.tags && reminder.tags.trim() && (
                          <div className="flex items-center gap-1 flex-wrap">
                            <Tag className="h-3 w-3 text-gray-500" />
                            {reminder.tags.split(',').map((tag, index) => (
                              <Badge key={index} variant="secondary" className="text-xs">
                                {tag.trim()}
                              </Badge>
                            ))}
                          </div>
                        )}

                        {reminder.repeat_pattern && reminder.repeat_pattern !== 'None' && (
                          <div className="flex items-center gap-1 text-xs text-gray-500">
                            <Repeat className="h-3 w-3" />
                            <span>Repeats {reminder.repeat_pattern.toLowerCase()}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card className="border-2 border-dashed border-green-200 bg-gradient-to-br from-green-50 to-emerald-50">
              <CardContent className="flex flex-col items-center justify-center h-64 text-center p-6">
                <div className="mb-4 p-4 rounded-full bg-green-100">
                  <List className="h-8 w-8 text-green-600" />
                </div>
                <h3 className="text-lg font-semibold text-green-800 mb-2">No reminders found</h3>
                <p className="text-green-600 mb-4 text-sm">
                  {searchTerm || statusFilter !== 'all' || priorityFilter !== 'all' || typeFilter !== 'all'
                    ? "Try adjusting your search or filters"
                    : "Start organizing your day by adding your first reminder"
                  }
                </p>
                <PrimaryButton className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800">
                  <Plus className="h-4 w-4 mr-2" />
                  Add First Reminder
                </PrimaryButton>
              </CardContent>
            </Card>
          )}
        </ScrollArea>
      </div>
    </Screen>
  );
}