"use client";

import * as React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>er,
  CardTitle,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

// Import your custom progress bar components
import Basic from "@/components/custom/progressBar/Basic";
import PBWithPerc from "@/components/custom/progressBar/PBWithPerc";

export interface Card2Props {
  title: string;
  description?: string;
  featuresCompleted: number;
  featuresTotal: number;
  badges?: Array<{
    label: string;
    variant?: "default" | "secondary" | "destructive" | "outline";
  }>;
  buttonLabel?: string | JSX.Element;
  onButtonClick?: () => void;

  progressStyle?: "basic" | "percentage";

  progressColor?: "primary" | "success" | "destructive";
}

export function Card2({
  title,
  description,
  featuresCompleted,
  featuresTotal,
  badges = [],
  buttonLabel = "View Details",
  onButtonClick,
  progressStyle = "basic",
  progressColor = "primary",
}: Card2Props) {
  const total = featuresTotal || 1; // avoid division by zero
  const progressValue = Math.round((featuresCompleted / total) * 100);

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="space-y-1">
          <p className="text-sm font-medium leading-none">
            Features Completed ({featuresCompleted}/{featuresTotal})
          </p>
          {/* Use either Basic or PBWithPerc depending on progressStyle */}
          {progressStyle === "basic" ? (
            <Basic
              value={progressValue}
              color={progressColor}
              orientation="horizontal"
            />
          ) : (
            <PBWithPerc
              value={progressValue}
              color={progressColor}
              orientation="horizontal"
            />
          )}
        </div>

        <div className="flex flex-wrap gap-2">
          {badges.map((badge, index) => (
            <Badge key={index} variant={badge.variant ?? "secondary"}>
              {badge.label}
            </Badge>
          ))}
        </div>
      </CardContent>
      <CardFooter>
        <Button onClick={onButtonClick}>{buttonLabel}</Button>
      </CardFooter>
    </Card>
  );
}
