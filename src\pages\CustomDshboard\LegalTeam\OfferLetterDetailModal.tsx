import React from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import {
  FileText,
  User,
  Users,
  Building2,
  Calendar,
  CreditCard,
  CheckCircle,
  Clock,
  MapPin,
  Phone,
  Mail,
  DollarSign,
  FileCheck,
  Download,
  Eye,
  X,
  AlertCircle,
  RefreshCw
} from 'lucide-react';
import {
  useGetOfferLetterDetailsQuery,
  OfferLetterCategory
} from '@/pages/OfferLetter/api/offerLetterApi';
import { generateOfferLetterPDF } from './utils/exportUtils';

interface OfferLetterDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  offerLetter: OfferLetterCategory;
}

const OfferLetterDetailModal: React.FC<OfferLetterDetailModalProps> = ({
  isOpen,
  onClose,
  offerLetter
}) => {
  const [isTermsModalOpen, setIsTermsModalOpen] = React.useState(false);

  // Use the actual offer letter ID from the category data
  // The 'id' field from offer-letter-categories API is the offer_letter_id needed for offer-letter-details API
  const offerLetterId = offerLetter.id;

  const {
    data: detailsData,
    isLoading,
    error
  } = useGetOfferLetterDetailsQuery(
    { offer_letter_id: offerLetterId },
    { skip: !isOpen }
  );



  const getCustomerTypeIcon = (type: string) => {
    switch (type) {
      case 'individual':
        return <User className="w-5 h-5" />;
      case 'company':
        return <Building2 className="w-5 h-5" />;
      case 'partner':
        return <Users className="w-5 h-5" />;
      case 'group':
        return <Users className="w-5 h-5" />;
      default:
        return <User className="w-5 h-5" />;
    }
  };

  const getStatusBadge = (isCompleted: boolean) => {
    return isCompleted ? (
      <Badge variant="default" className="bg-green-100 text-green-800">
        <CheckCircle className="w-3 h-3 mr-1" />
        Completed
      </Badge>
    ) : (
      <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
        <Clock className="w-3 h-3 mr-1" />
        Active
      </Badge>
    );
  };

  const formatCurrency = (amount: string) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES'
    }).format(parseFloat(amount));
  };

  const handleDownloadPDF = () => {
    generateOfferLetterPDF(offerLetter, detailsData);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="pb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-xl">
                {getCustomerTypeIcon(detailsData?.customer_type || offerLetter.customer_type)}
              </div>
              <div>
                <DialogTitle className="text-2xl font-bold text-gray-900 dark:text-white">
                  Offer Letter Details
                </DialogTitle>
                <p className="text-gray-600 dark:text-gray-400 mt-1">
                  Plot {detailsData?.plot_number || offerLetter.plot_number} • {detailsData?.lead_file || offerLetter.lead_file}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              {getStatusBadge(detailsData?.is_completed ?? offerLetter.is_completed)}
              <Button variant="ghost" size="sm" onClick={onClose} className="text-gray-400 hover:text-gray-600">
                <X className="w-5 h-5" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        {isLoading ? (
          <div className="flex items-center justify-center py-20">
            <div className="text-center">
              <div className="relative">
                <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600 mx-auto mb-4"></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <FileText className="w-6 h-6 text-blue-600" />
                </div>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Loading Offer Letter Details
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Fetching comprehensive information for {offerLetter.plot_number}...
              </p>
            </div>
          </div>
        ) : error ? (
          <div className="text-center py-20">
            <div className="relative mb-6">
              <div className="w-20 h-20 bg-red-50 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto">
                <AlertCircle className="w-10 h-10 text-red-500" />
              </div>
            </div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
              Unable to Load Details
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
              We encountered an issue while fetching the detailed information for this offer letter.
              The basic information is still available below.
            </p>

            {/* Show basic info as fallback */}
            <Card className="max-w-md mx-auto mb-6">
              <CardContent className="pt-6">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600 dark:text-gray-400">Plot Number:</span>
                    <span className="font-semibold">{offerLetter.plot_number}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600 dark:text-gray-400">Booking ID:</span>
                    <span className="font-semibold">{offerLetter.booking_id}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600 dark:text-gray-400">Lead File:</span>
                    <span className="font-semibold">{offerLetter.lead_file}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600 dark:text-gray-400">Customer Type:</span>
                    <Badge variant="outline" className="capitalize">{offerLetter.customer_type}</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600 dark:text-gray-400">Date:</span>
                    <span className="font-semibold">{new Date(offerLetter.date).toLocaleDateString()}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="flex justify-center space-x-3">
              <Button onClick={() => window.location.reload()} variant="outline">
                <RefreshCw className="w-4 h-4 mr-2" />
                Retry
              </Button>
              <Button onClick={onClose}>
                Close
              </Button>
            </div>
          </div>
        ) : detailsData ? (
          <div className="space-y-6">
            {/* Overview Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card className="border-l-4 border-l-blue-500">
                <CardContent className="pt-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Plot Number</p>
                      <p className="text-xl font-bold text-gray-900 dark:text-white">{detailsData.plot_number}</p>
                    </div>
                    <MapPin className="w-8 h-8 text-blue-500" />
                  </div>
                </CardContent>
              </Card>

              <Card className="border-l-4 border-l-green-500">
                <CardContent className="pt-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Customer Type</p>
                      <p className="text-xl font-bold text-gray-900 dark:text-white capitalize">{detailsData.customer_type}</p>
                    </div>
                    {getCustomerTypeIcon(detailsData.customer_type)}
                  </div>
                </CardContent>
              </Card>

              <Card className="border-l-4 border-l-purple-500">
                <CardContent className="pt-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Status</p>
                      <p className="text-xl font-bold text-gray-900 dark:text-white">
                        {detailsData.is_completed ? 'Completed' : 'Active'}
                      </p>
                    </div>
                    {detailsData.is_completed ? (
                      <CheckCircle className="w-8 h-8 text-green-500" />
                    ) : (
                      <Clock className="w-8 h-8 text-amber-500" />
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="w-5 h-5 text-blue-600" />
                  <span>Basic Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700">
                      <span className="text-gray-600 dark:text-gray-400">Booking ID:</span>
                      <span className="font-semibold text-gray-900 dark:text-white">{detailsData.booking_id}</span>
                    </div>
                    <div className="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700">
                      <span className="text-gray-600 dark:text-gray-400">Lead File:</span>
                      <span className="font-semibold text-gray-900 dark:text-white">{detailsData.lead_file}</span>
                    </div>
                    <div className="flex justify-between items-center py-2">
                      <span className="text-gray-600 dark:text-gray-400">Payment Confirmed:</span>
                      <Badge variant={detailsData.acc_payment_conf ? "default" : "secondary"}>
                        {detailsData.acc_payment_conf ? "Confirmed" : "Pending"}
                      </Badge>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700">
                      <span className="text-gray-600 dark:text-gray-400">Date Created:</span>
                      <div className="flex items-center space-x-2">
                        <Calendar className="w-4 h-4 text-gray-400" />
                        <span className="font-semibold text-gray-900 dark:text-white">
                          {new Date(detailsData.date).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                    <div className="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700">
                      <span className="text-gray-600 dark:text-gray-400">Status:</span>
                      {getStatusBadge(detailsData.is_completed)}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Individual Customer Details */}
            {detailsData.customer_type === 'individual' && detailsData.individuals && detailsData.individuals.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <User className="w-5 h-5 text-blue-600" />
                    <span>Individual Customer Details</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {detailsData.individuals.map((individual, index) => (
                      <div key={index} className="space-y-4">
                        <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                          <h4 className="font-semibold text-lg text-gray-900 dark:text-white mb-3">
                            {individual.first_name} {individual.last_name}
                          </h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <div className="flex items-center space-x-2">
                                <FileText className="w-4 h-4 text-gray-500" />
                                <span className="text-sm text-gray-600 dark:text-gray-400">National ID:</span>
                                <span className="font-medium">{individual.national_id}</span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <FileCheck className="w-4 h-4 text-gray-500" />
                                <span className="text-sm text-gray-600 dark:text-gray-400">KRA PIN:</span>
                                <span className="font-medium">{individual.KRA_Pin}</span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <Phone className="w-4 h-4 text-gray-500" />
                                <span className="text-sm text-gray-600 dark:text-gray-400">Phone:</span>
                                <span className="font-medium">{individual.phone}</span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <Mail className="w-4 h-4 text-gray-500" />
                                <span className="text-sm text-gray-600 dark:text-gray-400">Email:</span>
                                <span className="font-medium">{individual.email}</span>
                              </div>
                            </div>
                            <div className="space-y-2">
                              <div className="flex items-center space-x-2">
                                <MapPin className="w-4 h-4 text-gray-500" />
                                <span className="text-sm text-gray-600 dark:text-gray-400">Location:</span>
                                <span className="font-medium">{individual.city}, {individual.country}</span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <Calendar className="w-4 h-4 text-gray-500" />
                                <span className="text-sm text-gray-600 dark:text-gray-400">Date of Birth:</span>
                                <span className="font-medium">{new Date(individual.DOB).toLocaleDateString()}</span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <Phone className="w-4 h-4 text-gray-500" />
                                <span className="text-sm text-gray-600 dark:text-gray-400">Preferred Contact:</span>
                                <Badge variant="outline" className="capitalize">
                                  {Array.isArray(individual.preferred_contact)
                                    ? individual.preferred_contact.join(', ')
                                    : individual.preferred_contact || 'Not specified'}
                                </Badge>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Next of Kin */}
                        {individual.next_of_kin && individual.next_of_kin.length > 0 && (
                          <div className="mt-4">
                            <h5 className="font-medium text-gray-900 dark:text-white mb-3">Next of Kin</h5>
                            <div className="space-y-3">
                              {individual.next_of_kin.map((kin, kinIndex) => (
                                <div key={kinIndex} className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                    <div>
                                      <p className="font-medium text-gray-900 dark:text-white">{kin.full_name}</p>
                                      <p className="text-sm text-gray-600 dark:text-gray-400">Relationship: {kin.relationship}</p>
                                    </div>
                                    <div className="space-y-1">
                                      <div className="flex items-center space-x-2">
                                        <Phone className="w-3 h-3 text-gray-400" />
                                        <span className="text-sm">{kin.phone}</span>
                                      </div>
                                      <div className="flex items-center space-x-2">
                                        <Mail className="w-3 h-3 text-gray-400" />
                                        <span className="text-sm">{kin.email}</span>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Company Customer Details */}
            {detailsData.customer_type === 'company' && detailsData.companies && detailsData.companies.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Building2 className="w-5 h-5 text-green-600" />
                    <span>Company Customer Details</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {detailsData.companies.map((company, index) => (
                      <div key={index} className="space-y-4">
                        <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                          <h4 className="font-semibold text-lg text-gray-900 dark:text-white mb-3">
                            {company.company_name}
                          </h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <div className="flex items-center space-x-2">
                                <FileText className="w-4 h-4 text-gray-500" />
                                <span className="text-sm text-gray-600 dark:text-gray-400">Registration No:</span>
                                <span className="font-medium">{company.company_registration_number}</span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <FileCheck className="w-4 h-4 text-gray-500" />
                                <span className="text-sm text-gray-600 dark:text-gray-400">KRA PIN:</span>
                                <span className="font-medium">{company.company_kra}</span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <MapPin className="w-4 h-4 text-gray-500" />
                                <span className="text-sm text-gray-600 dark:text-gray-400">Address:</span>
                                <span className="font-medium">{company.address}</span>
                              </div>
                            </div>
                            <div className="space-y-2">
                              <div className="flex items-center space-x-2">
                                <Phone className="w-4 h-4 text-gray-500" />
                                <span className="text-sm text-gray-600 dark:text-gray-400">Phone:</span>
                                <span className="font-medium">{company.phone}</span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <Mail className="w-4 h-4 text-gray-500" />
                                <span className="text-sm text-gray-600 dark:text-gray-400">Email:</span>
                                <span className="font-medium">{company.email}</span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <MapPin className="w-4 h-4 text-gray-500" />
                                <span className="text-sm text-gray-600 dark:text-gray-400">Location:</span>
                                <span className="font-medium">{company.city}, {company.country}</span>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Company Directors */}
                        {company.directors && company.directors.length > 0 && (
                          <div className="mt-4">
                            <h5 className="font-medium text-gray-900 dark:text-white mb-3">Company Directors</h5>
                            <div className="space-y-3">
                              {company.directors.map((director, dirIndex) => (
                                <div key={dirIndex} className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                    <div>
                                      <p className="font-medium text-gray-900 dark:text-white">
                                        {director.first_name} {director.last_name}
                                      </p>
                                      <p className="text-sm text-gray-600 dark:text-gray-400">
                                        ID: {director.national_id}
                                      </p>
                                    </div>
                                    <div className="space-y-1">
                                      <div className="flex items-center space-x-2">
                                        <Phone className="w-3 h-3 text-gray-400" />
                                        <span className="text-sm">{director.phone}</span>
                                      </div>
                                      <div className="flex items-center space-x-2">
                                        <Mail className="w-3 h-3 text-gray-400" />
                                        <span className="text-sm">{director.email}</span>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Partner Customer Details */}
            {detailsData.customer_type === 'partner' && detailsData.partners && detailsData.partners.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Users className="w-5 h-5 text-purple-600" />
                    <span>Partner Customer Details</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {detailsData.partners.map((partner, index) => (
                      <div key={index} className="space-y-4">
                        <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                          <h4 className="font-semibold text-lg text-gray-900 dark:text-white mb-3">
                            Partner {index + 1}: {partner.first_name} {partner.last_name}
                          </h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <div className="flex items-center space-x-2">
                                <FileText className="w-4 h-4 text-gray-500" />
                                <span className="text-sm text-gray-600 dark:text-gray-400">National ID:</span>
                                <span className="font-medium">{partner.national_id}</span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <Phone className="w-4 h-4 text-gray-500" />
                                <span className="text-sm text-gray-600 dark:text-gray-400">Phone:</span>
                                <span className="font-medium">{partner.phone}</span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <Mail className="w-4 h-4 text-gray-500" />
                                <span className="text-sm text-gray-600 dark:text-gray-400">Email:</span>
                                <span className="font-medium">{partner.email}</span>
                              </div>
                            </div>
                            <div className="space-y-2">
                              <div className="flex items-center space-x-2">
                                <MapPin className="w-4 h-4 text-gray-500" />
                                <span className="text-sm text-gray-600 dark:text-gray-400">Location:</span>
                                <span className="font-medium">{partner.city}, {partner.country}</span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <Phone className="w-4 h-4 text-gray-500" />
                                <span className="text-sm text-gray-600 dark:text-gray-400">Preferred Contact:</span>
                                <Badge variant="outline" className="capitalize">
                                  {partner.preferred_contact || 'Not specified'}
                                </Badge>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Next of Kin for Partners */}
                        {partner.next_of_kin && partner.next_of_kin.length > 0 && (
                          <div className="mt-4">
                            <h5 className="font-medium text-gray-900 dark:text-white mb-3">Next of Kin</h5>
                            <div className="space-y-3">
                              {partner.next_of_kin.map((kin, kinIndex) => (
                                <div key={kinIndex} className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                    <div>
                                      <p className="font-medium text-gray-900 dark:text-white">{kin.full_name}</p>
                                      <p className="text-sm text-gray-600 dark:text-gray-400">Relationship: {kin.relationship}</p>
                                    </div>
                                    <div className="space-y-1">
                                      <div className="flex items-center space-x-2">
                                        <Phone className="w-3 h-3 text-gray-400" />
                                        <span className="text-sm">{kin.phone}</span>
                                      </div>
                                      <div className="flex items-center space-x-2">
                                        <Mail className="w-3 h-3 text-gray-400" />
                                        <span className="text-sm">{kin.email}</span>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Group Customer Details */}
            {detailsData.customer_type === 'group' && detailsData.groups && detailsData.groups.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Users className="w-5 h-5 text-orange-600" />
                    <span>Group Customer Details</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {detailsData.groups.map((group, index) => (
                      <div key={index} className="space-y-4">
                        <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg">
                          <h4 className="font-semibold text-lg text-gray-900 dark:text-white mb-3">
                            {group.group_name}
                          </h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <div className="flex items-center space-x-2">
                                <Phone className="w-4 h-4 text-gray-500" />
                                <span className="text-sm text-gray-600 dark:text-gray-400">Phone:</span>
                                <span className="font-medium">{group.group_phone}</span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <Mail className="w-4 h-4 text-gray-500" />
                                <span className="text-sm text-gray-600 dark:text-gray-400">Email:</span>
                                <span className="font-medium">{group.group_email}</span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <FileCheck className="w-4 h-4 text-gray-500" />
                                <span className="text-sm text-gray-600 dark:text-gray-400">KRA PIN:</span>
                                <span className="font-medium">{group.Group_KRA_PIN}</span>
                              </div>
                            </div>
                            <div className="space-y-2">
                              <div className="flex items-center space-x-2">
                                <MapPin className="w-4 h-4 text-gray-500" />
                                <span className="text-sm text-gray-600 dark:text-gray-400">Location:</span>
                                <span className="font-medium">{group.Group_city}, {group.Group_country}</span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <Calendar className="w-4 h-4 text-gray-500" />
                                <span className="text-sm text-gray-600 dark:text-gray-400">Created:</span>
                                <span className="font-medium">{new Date(group.created_at).toLocaleDateString()}</span>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Group Members */}
                        {group.members && group.members.length > 0 && (
                          <div className="mt-4">
                            <h5 className="font-medium text-gray-900 dark:text-white mb-3">Group Members</h5>
                            <div className="space-y-3">
                              {group.members.map((member, memberIndex) => (
                                <div key={memberIndex} className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                    <div>
                                      <p className="font-medium text-gray-900 dark:text-white">
                                        {member.first_name} {member.last_name}
                                      </p>
                                      <p className="text-sm text-gray-600 dark:text-gray-400">
                                        ID: {member.national_id}
                                      </p>
                                    </div>
                                    <div className="space-y-1">
                                      <div className="flex items-center space-x-2">
                                        <Phone className="w-3 h-3 text-gray-400" />
                                        <span className="text-sm">{member.phone}</span>
                                      </div>
                                      <div className="flex items-center space-x-2">
                                        <Mail className="w-3 h-3 text-gray-400" />
                                        <span className="text-sm">{member.email}</span>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* No customer details available */}
            {!detailsData.individuals?.length && 
             !detailsData.companies?.length && 
             !detailsData.partners?.length && 
             !detailsData.groups?.length && (
              <Card>
                <CardContent className="text-center py-8">
                  <User className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                  <p className="text-gray-600 dark:text-gray-400">
                    No customer details available for this offer letter.
                  </p>
                </CardContent>
              </Card>
            )}

            {/* Payment Plan */}
            {detailsData.payment_plan && detailsData.payment_plan.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <CreditCard className="w-5 h-5 text-green-600" />
                    <span>Payment Plan</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {detailsData.payment_plan.map((plan, index) => (
                      <div key={index} className="space-y-4">
                        {/* Plot Number Header */}
                        <div className="flex items-center space-x-2 pb-2 border-b border-gray-200 dark:border-gray-700">
                          <MapPin className="w-5 h-5 text-blue-600" />
                          <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                            Plot: {plan.plot_no}
                          </h4>
                          <Badge variant="outline" className="ml-auto">
                            Plan {index + 1}
                          </Badge>
                        </div>
                        
                        {/* Payment Details Grid */}
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                          <div className="text-center p-6 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-xl">
                            <DollarSign className="w-8 h-8 text-blue-600 mx-auto mb-3" />
                            <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Total Price</p>
                            <p className="text-2xl font-bold text-blue-600">
                              {formatCurrency(plan.total_cash_price)}
                            </p>
                          </div>
                          <div className="text-center p-6 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-xl">
                            <DollarSign className="w-8 h-8 text-green-600 mx-auto mb-3" />
                            <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Deposit</p>
                            <p className="text-2xl font-bold text-green-600">
                              {formatCurrency(plan.deposit)}
                            </p>
                          </div>
                          <div className="text-center p-6 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-xl">
                            <DollarSign className="w-8 h-8 text-purple-600 mx-auto mb-3" />
                            <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Monthly Payment</p>
                            <p className="text-2xl font-bold text-purple-600">
                              {formatCurrency(plan.monthly_installments)}
                            </p>
                          </div>
                          <div className="text-center p-6 bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 rounded-xl">
                            <Calendar className="w-8 h-8 text-orange-600 mx-auto mb-3" />
                            <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Installments</p>
                            <p className="text-2xl font-bold text-orange-600">
                              {plan.no_of_instalments} {plan.no_of_instalments === 1 ? 'Payment' : 'Months'}
                            </p>
                          </div>
                        </div>
                        
                        {/* Additional Plan Info */}
                        <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-gray-600 dark:text-gray-400">Plan Created:</span>
                            <span className="font-medium text-gray-900 dark:text-white">
                              {new Date(plan.created_at).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}










            {/* Terms & Conditions */}
            {detailsData.terms_conditions && detailsData.terms_conditions.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <FileCheck className="w-5 h-5 text-amber-600" />
                    <span>Terms & Conditions</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {detailsData.terms_conditions.map((terms, index) => (
                    <div key={index} className="bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-900/20 dark:to-yellow-900/20 rounded-xl p-6">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="font-semibold text-gray-900 dark:text-white">Agreement Terms</h4>
                        <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                          <Calendar className="w-4 h-4" />
                          <span>Accepted: {new Date(terms.acceptance_date).toLocaleDateString()}</span>
                        </div>
                      </div>
                      <div className="flex items-center justify-between bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                        <div>
                          <p className="text-gray-700 dark:text-gray-300 mb-2">
                            Terms and conditions have been accepted for this offer letter.
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            Click to view the full terms and conditions document.
                          </p>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setIsTermsModalOpen(true)}
                          className="ml-4"
                        >
                          <Eye className="w-4 h-4 mr-2" />
                          View Terms
                        </Button>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            )}

            {/* Action Buttons */}
            <div className="flex justify-between items-center pt-6 border-t border-gray-200 dark:border-gray-700">
              <div className="flex space-x-3">
                <Button
                  variant="outline"
                  size="sm"
                  className="text-blue-600 border-blue-200 hover:bg-blue-50 dark:text-blue-400 dark:border-blue-800 dark:hover:bg-blue-900/20"
                  onClick={handleDownloadPDF}
                >
                  <Download className="w-4 h-4 mr-2" />
                  Download PDF
                </Button>
                {/* <Button
                  variant="outline"
                  size="sm"
                  className="text-green-600 border-green-200 hover:bg-green-50 dark:text-green-400 dark:border-green-800 dark:hover:bg-green-900/20"
                  onClick={handleViewFullDetails}
                >
                  <Eye className="w-4 h-4 mr-2" />
                  View Full Details
                </Button> */}
              </div>
              <Button onClick={onClose} className="bg-blue-600 hover:bg-blue-700">
                Close
              </Button>
            </div>
          </div>
        ) : (
          <div className="text-center py-20">
            <div className="relative mb-8">
              <div className="w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto">
                <FileText className="w-12 h-12 text-gray-400" />
              </div>
              <div className="absolute -top-2 -right-2 w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-full flex items-center justify-center">
                <AlertCircle className="w-4 h-4 text-yellow-600" />
              </div>
            </div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
              No Additional Details Available
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-8 max-w-md mx-auto">
              While we have the basic information for this offer letter, detailed information is not currently available.
              You can still view the essential details below.
            </p>

            {/* Show basic info as fallback */}
            <Card className="max-w-lg mx-auto mb-8">
              <CardHeader>
                <CardTitle className="flex items-center justify-center space-x-2">
                  {getCustomerTypeIcon(offerLetter.customer_type)}
                  <span>Basic Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <p className="text-xs text-gray-600 dark:text-gray-400 mb-1">Plot Number</p>
                      <p className="font-bold text-blue-600">{offerLetter.plot_number}</p>
                    </div>
                    <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <p className="text-xs text-gray-600 dark:text-gray-400 mb-1">Customer Type</p>
                      <p className="font-bold text-green-600 capitalize">{offerLetter.customer_type}</p>
                    </div>
                  </div>
                  <Separator />
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600 dark:text-gray-400">Booking ID:</span>
                      <span className="font-semibold">{offerLetter.booking_id}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600 dark:text-gray-400">Lead File:</span>
                      <span className="font-semibold">{offerLetter.lead_file}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600 dark:text-gray-400">Status:</span>
                      {getStatusBadge(offerLetter.is_completed)}
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600 dark:text-gray-400">Date:</span>
                      <span className="font-semibold">{new Date(offerLetter.date).toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Button onClick={onClose} className="bg-blue-600 hover:bg-blue-700">
              Close
            </Button>
          </div>
        )}
      </DialogContent>

      {/* Terms and Conditions Modal */}
      <Dialog open={isTermsModalOpen} onOpenChange={setIsTermsModalOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-amber-50 dark:bg-amber-900/20 rounded-lg">
                  <FileCheck className="w-6 h-6 text-amber-600" />
                </div>
                <div>
                  <DialogTitle className="text-xl font-bold text-gray-900 dark:text-white">
                    Terms and Conditions
                  </DialogTitle>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    Please review and accept the terms and conditions to complete the final step.
                  </p>
                </div>
              </div>
              <Button variant="ghost" size="sm" onClick={() => setIsTermsModalOpen(false)} className="text-gray-400 hover:text-gray-600">
                <X className="w-5 h-5" />
              </Button>
            </div>
          </DialogHeader>

          <div className="space-y-4">
            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
              <div className="prose prose-sm max-w-none dark:prose-invert">
                <ol className="space-y-4 text-gray-700 dark:text-gray-300">
                  <li>
                    <strong>1.</strong> This letter of offer does not constitute a Sale Agreement.
                  </li>
                  <li>
                    <strong>2.</strong> Any amount paid less than the stipulated deposit will be taken as a booking fee and if the purchaser does not top up the balance within 14 days the booked plot will be opened up for sale and 20% of the paid amount will be deducted as administrative cost.
                  </li>
                  <li>
                    <strong>3.</strong> Should the Purchaser:
                    <div className="ml-6 mt-2 space-y-1">
                      <p><strong>a.</strong> Cancel/Withdraw from this letter of offer.</p>
                      <p><strong>b.</strong> Default in making any payments as and when due and whether or not the Sale Agreement will have been executed;</p>
                    </div>
                    <p className="mt-2">the vendor shall reserve the right to treat this offer as cancelled and the purchaser shall forfeit Ten per cent (10%) of the purchase price as Administrative costs.</p>
                  </li>
                  <li>
                    <strong>4.</strong> The purchaser should sign the Sale Agreement with full particulars within thirty (30) working days of execution of this letter of offer.
                  </li>
                  <li>
                    <strong>5.</strong> The Sale Agreement will be prepared by the vendor's advocates and will incorporate the Law Society of Kenya Conditions of sale (2015 Edition) in so far as they are not inconsistent with this letter of offer.
                  </li>
                  <li>
                    <strong>6.</strong> A payment plan shall be provided by the vendor at the point of sale and the same shall not be tied to the execution of the Sale Agreement.
                  </li>
                  <li>
                    <strong>7.</strong> The parties hereby agree that by submitting this form you accept the terms and conditions that constitutes a valid contract between the parties.
                  </li>
                  <li>
                    <strong>8.</strong> All Payments to be made to ONLY the Optiven bank accounts or official Optiven Paybill, 921225, and the purchaser should request for a receipt within 24hrs.
                  </li>
                  <li>
                    <strong>9.</strong> All the MONTHLY INSTALLMENTS MUST be remitted on or before the due date based on the start date.
                  </li>
                </ol>
              </div>
            </div>

            {/* Acceptance Information */}
            {detailsData?.terms_conditions && detailsData.terms_conditions.length > 0 && (
              <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <div>
                    <p className="font-medium text-green-800 dark:text-green-200">Terms Accepted</p>
                    <p className="text-sm text-green-600 dark:text-green-400">
                      Accepted on: {new Date(detailsData.terms_conditions[0].acceptance_date).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </p>
                  </div>
                </div>
              </div>
            )}

            <div className="flex justify-end pt-4 border-t border-gray-200 dark:border-gray-700">
              <Button onClick={() => setIsTermsModalOpen(false)} className="bg-blue-600 hover:bg-blue-700">
                Close
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </Dialog>
  );
};

export default OfferLetterDetailModal;
