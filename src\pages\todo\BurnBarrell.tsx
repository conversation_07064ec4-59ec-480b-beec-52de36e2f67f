import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Flame, Trash2 } from "lucide-react";
import { CardType } from "./types";


interface BurnBarrelProps {
  setCards: React.Dispatch<React.SetStateAction<CardType[]>>;
}

export const BurnBarrel: React.FC<BurnBarrelProps> = ({ setCards }) => {
  const [active, setActive] = useState(false);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setActive(true);
  };

  const handleDragLeave = () => {
    setActive(false);
  };

  const handleDragEnd = (e: React.DragEvent) => {
    const cardId = e.dataTransfer.getData("cardId");
    setCards((pv) => pv.filter((c) => c.id !== cardId));
    setActive(false);
  };

  return (
    <Card
      onDrop={handleDragEnd}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      className={`mt-10 h-56 w-56 shrink-0 place-content-center ${
        active
          ? "border-destructive bg-destructive/20 text-destructive"
          : "border-muted bg-muted/20 text-muted-foreground"
      }`}
    >
      <CardContent className="flex items-center justify-center h-full">
        {active ? <Flame className="animate-bounce" /> : <Trash2 />}
      </CardContent>
    </Card>
  );
}