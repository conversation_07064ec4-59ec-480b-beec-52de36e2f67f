import LazyModal, { TableColumn } from '@/components/reports/ReportsModal';
import { formatNumberWithCommas } from '@/utils/salesDataFormatter';
import { useGetCommissionHeadersQuery } from '@/redux/slices/commissionApiSlice'; // <-- Import the correct hook
import { useState } from 'react';

export interface CommissionHeader {
  id: number;
  period_start_date: string;
  period_end_date: string;
  role: string;
  Deposit_amount: number;
  deposit_perc: number;
  deposit_commission: number;
  installment_amount: number;
  installment_perc: number;
  installment_commission: number;
  Total_commission: number;
  Tl_gained_comm_from_members: number;
  rm_achieved_MIB: number;
  rm_commission_rate: number;
  rm_commission_amount: number;
  commisison_payable_TL: number;
  emp_no_id: string;
}

interface CommmisionHeaderProps {
  isModalOpen: boolean;
  setIsModalOpen: () => void;
  params?: {
    period_name: string;
    start_date: string;
    end_date: string;
    role?: string; // <-- Add role for office filtering
  };
}

const CommmissionHeaders = ({ isModalOpen, setIsModalOpen, params }: CommmisionHeaderProps) => {
    const [selectedRole, setSelectedRole] = useState<string>("ALL");
    const roles = ["ALL", "HQ", "KAREN", "TEAM-LEADERS", "MARKETER"];

    // Define table columns for CommissionHeader
    const columns: TableColumn<CommissionHeader>[] = [
        { key: 'emp_no_id', title: 'Employee No' },
        { key: 'role', title: 'Role' },
        { key: 'period_start_date', title: 'Period Start' },
        { key: 'period_end_date', title: 'Period End' },
        { key: 'Deposit_amount', title: 'Deposit Amount', render: (v) => <span>{formatNumberWithCommas(v)}</span> },
        { key: 'deposit_perc', title: 'Deposit %', render: (v) => <span>{v}%</span> },
        { key: 'deposit_commission', title: 'Deposit Commission', render: (v) => <span>{formatNumberWithCommas(v)}</span> },
        { key: 'installment_amount', title: 'Installment Amount', render: (v) => <span>{formatNumberWithCommas(v)}</span> },
        { key: 'installment_perc', title: 'Installment %', render: (v) => <span>{v}%</span> },
        { key: 'installment_commission', title: 'Installment Commission', render: (v) => <span>{formatNumberWithCommas(v)}</span> },
        { key: 'Total_commission', title: 'Total Commission', render: (v) => <span>{formatNumberWithCommas(v)}</span> },
        { key: 'Tl_gained_comm_from_members', title: 'TL Gained Comm. from Members', render: (v) => <span>{formatNumberWithCommas(v)}</span> },
        { key: 'rm_achieved_MIB', title: 'RM Achieved MIB', render: (v) => <span>{formatNumberWithCommas(v)}</span> },
        { key: 'rm_commission_rate', title: 'RM Commission Rate', render: (v) => <span>{v}%</span> },
        { key: 'rm_commission_amount', title: 'RM Commission Amount', render: (v) => <span>{formatNumberWithCommas(v)}</span> },
        { key: 'commisison_payable_TL', title: 'Commission Payable TL', render: (v) => <span>{formatNumberWithCommas(v)}</span> },
    ];

    // Fetch data using the headers API
    const { data, isLoading, error } = useGetCommissionHeadersQuery({
        MARKETING_PERIOD: params?.start_date || "ALL",
        MARKETER_EMPLOYEE_NO: "ALL",
        // @ts-expect-error: ROLE is not in the type but required by the API
        ROLE: params?.role || "ALL",
        page: 1,
        page_size: 20,
    }, {
        skip: !params?.start_date,
    });

    // Add these lines for debugging
    console.log("CommissionHeaders API params:", {
        MARKETING_PERIOD: params?.start_date || "ALL",
        MARKETER_EMPLOYEE_NO: "ALL",
        ROLE: params?.role || "ALL",
        page: 1,
        page_size: 20,
    });
    if (error) {
        console.error("CommissionHeaders API error:", error);
    }

    const handleCloseModal = () => {
        setIsModalOpen();
    };

    return (
        <LazyModal<CommissionHeader>
            isOpen={isModalOpen}
            onClose={handleCloseModal}
            title="Commission Headers Report"
            url="/commission-headers-report"
            params={{
                MARKETING_PERIOD: params?.start_date || "ALL",
                MARKETER_EMPLOYEE_NO: "ALL",
                ROLE: params?.role || "ALL", // <-- Pass role to modal/API
                page: 1,
                page_size: 20,
            }}
            columns={columns}
            size="lg"
            TableComponent={({ ...rest }) => (
                <div>
                    {isLoading && <div className="p-4 text-center">Loading...</div>}
                    {error && <div className="p-4 text-center text-red-600">Error loading data</div>}
                    {!isLoading && !error && (
                        <table className="min-w-full bg-white dark:bg-gray-800">
                            <thead>
                                <tr>
                                    {columns.map((col) => (
                                        <th key={String(col.key)} className="px-2 py-2">{col.title}</th>
                                    ))}
                                </tr>
                            </thead>
                            <tbody>
                                {(data?.results || []).map((row, idx) => (
                                    <tr key={row.id || idx}>
                                        {columns.map((col) => (
                                            <td key={String(col.key)} className="px-2 py-2">
                                                {col.render
                                                    ? col.render(row[col.key as keyof CommissionHeader], row, idx)
                                                    : row[col.key as keyof CommissionHeader]}
                                            </td>
                                        ))}
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    )}
                    {!isLoading && !error && (!data?.results || data.results.length === 0) && (
                        <div className="p-4 text-center text-gray-500">No data available</div>
                    )}
                </div>
            )}
        />
    );
};

export default CommmissionHeaders;