import React from 'react';
import { Loader2 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface LoadingButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  isLoading?: boolean;
  loadingText?: string | null;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
}

const LoadingButton = ({
  children,
  isLoading = false,
  loadingText = null,
  disabled = false,
  className = '',
  variant = 'default',
  size = 'default',
  ...props
}: LoadingButtonProps) => {
  return (
    <Button
      disabled={disabled || isLoading}
      variant={variant}
      size={size}
      className={cn(className)}
      {...props}
    >
      {isLoading && (
        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
      )}
      {isLoading && loadingText ? loadingText : children}
    </Button>
  );
};

export { LoadingButton };
export type { LoadingButtonProps };