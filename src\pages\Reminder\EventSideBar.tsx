import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Book,
  Pencil,
  Trash2,
  Clock,
  AlertTriangle,
  AlertCircle,
  Circle,
  CheckCircle2,
  Calendar,
  Tag,
  Repeat,
  Bell,
  X
} from "lucide-react";
import { OutlinedButton, PrimaryButton } from "@/components/custom/buttons/buttons";

interface EventSidebarProps {
  date: Date;
  selectedDayEvents: Array<{
    id: string;
    date: Date;
    title: string;
    description: string;
    time: string;
    status: string;
    priority?: string;
    reminder_type?: string;
    tags?: string | null;
    repeat_pattern?: string;
    advance_notice_minutes?: number;
    snoozed?: boolean;
  }>;
  showEvents: boolean;
  setShowEvents: (show: boolean) => void;
  handleEditEvent: (event: any) => void;
  handleRemoveEvent: (eventId: string) => void;
  handleSnoozeEvent?: (eventId: string, snoozeUntil: Date) => void;
  setIsModalOpen: (open: boolean) => void;
  showAllEvents?: boolean;
}

export default function EventSidebar({
  date,
  selectedDayEvents,
  showEvents,
  setShowEvents,
  handleEditEvent,
  handleRemoveEvent,
  handleSnoozeEvent,
  setIsModalOpen,
  showAllEvents = false,
}: EventSidebarProps) {

  const getPriorityIcon = (priority?: string) => {
    switch (priority) {
      case 'Critical': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'High': return <AlertCircle className="h-4 w-4 text-orange-500" />;
      case 'Normal': return <Circle className="h-4 w-4 text-blue-500" />;
      case 'Low': return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      default: return <Circle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getPriorityColor = (priority?: string) => {
    switch (priority) {
      case 'Critical': return 'border-red-200 bg-red-50 text-red-700';
      case 'High': return 'border-orange-200 bg-orange-50 text-orange-700';
      case 'Normal': return 'border-blue-200 bg-blue-50 text-blue-700';
      case 'Low': return 'border-green-200 bg-green-50 text-green-700';
      default: return 'border-gray-200 bg-gray-50 text-gray-700';
    }
  };

  const getTypeIcon = (type?: string) => {
    switch (type) {
      case 'Task': return '✅';
      case 'Appointment': return '📅';
      case 'Meeting': return '🤝';
      case 'Birthday': return '🎂';
      case 'Anniversary': return '💝';
      case 'Payment': return '💳';
      case 'Health': return '🏥';
      default: return '📋';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed': return 'bg-green-100 text-green-800 border-green-200';
      case 'active': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'snoozed': return 'bg-amber-100 text-amber-800 border-amber-200';
      case 'cancelled': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };
  return (
    showEvents && (
      <div className="w-1/3 border-l-2 border-green-200 bg-gradient-to-b from-green-50 to-white p-6 overflow-auto">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-green-800 mb-2">
              {showAllEvents ? (
                <>🗓️ All Reminders</>
              ) : (
                <>📅 {date.toLocaleDateString('en-US', {
                  weekday: 'long',
                  month: 'long',
                  day: 'numeric'
                })}</>
              )}
            </h2>
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="bg-green-100 text-green-700 border-green-200">
                {selectedDayEvents.length} reminder{selectedDayEvents.length !== 1 ? "s" : ""}
              </Badge>
              {selectedDayEvents.length > 0 && (
                <Badge variant="outline" className="text-xs">
                  {selectedDayEvents.filter(e => e.status === 'Active').length} active
                </Badge>
              )}
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowEvents(false)}
            className="lg:hidden text-green-600 hover:text-green-800 hover:bg-green-100"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>
        <ScrollArea className="h-[calc(100vh-200px)]">
          {selectedDayEvents.length > 0 ? (
            selectedDayEvents
              .sort((a, b) => {
                const timeA = a.time.includes("AM") ? a.time : `${a.time.replace("PM", "")}PM`;
                const timeB = b.time.includes("AM") ? b.time : `${b.time.replace("PM", "")}PM`;
                return (
                  new Date(`1970/01/01 ${timeA}`).getTime() -
                  new Date(`1970/01/01 ${timeB}`).getTime()
                );
              })
              .map((event) => (
                <Card key={event.id} className="mb-4 border-2 hover:shadow-lg transition-all duration-200 bg-gradient-to-br from-white to-gray-50">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-lg">{getTypeIcon(event.reminder_type)}</span>
                        <div>
                          <h3 className="font-semibold text-gray-800 line-clamp-1">{event.title}</h3>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge className={`text-xs ${getStatusColor(event.status)}`}>
                              {event.status}
                            </Badge>
                            {event.priority && (
                              <Badge variant="outline" className={`text-xs ${getPriorityColor(event.priority)}`}>
                                <div className="flex items-center gap-1">
                                  {getPriorityIcon(event.priority)}
                                  {event.priority}
                                </div>
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                      {event.snoozed && (
                        <Badge className="bg-amber-100 text-amber-800 border-amber-200">
                          <Bell className="h-3 w-3 mr-1" />
                          Snoozed
                        </Badge>
                      )}
                    </div>
                  </CardHeader>

                  <CardContent className="pt-0 space-y-3">
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Clock className="h-4 w-4" />
                      <span className="font-medium">{event.time}</span>
                      {event.advance_notice_minutes && (
                        <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                          {event.advance_notice_minutes}min notice
                        </span>
                      )}
                    </div>

                    {event.description && (
                      <p className="text-sm text-gray-600 line-clamp-2 bg-gray-50 p-2 rounded">
                        {event.description}
                      </p>
                    )}

                    {event.tags && event.tags.trim() && (
                      <div className="flex items-center gap-1 flex-wrap">
                        <Tag className="h-3 w-3 text-gray-500" />
                        {event.tags.split(',').map((tag, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {tag.trim()}
                          </Badge>
                        ))}
                      </div>
                    )}

                    {event.repeat_pattern && event.repeat_pattern !== 'None' && (
                      <div className="flex items-center gap-1 text-xs text-gray-500">
                        <Repeat className="h-3 w-3" />
                        <span>Repeats {event.repeat_pattern.toLowerCase()}</span>
                      </div>
                    )}

                    <div className="flex gap-2 pt-2">
                      <PrimaryButton
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditEvent(event)}
                        className="text-blue-600 border-blue-200 hover:bg-blue-50 flex items-center gap-1 flex-1"
                      >
                        <Pencil className="w-3 h-3" />
                        Edit
                      </PrimaryButton>
                      <OutlinedButton
                        variant="outline"
                        size="sm"
                        onClick={() => handleRemoveEvent(event.id)}
                        className="text-red-500 border-red-200 hover:bg-red-50 flex items-center gap-1"
                      >
                        <Trash2 className="w-3 h-3" />
                      </OutlinedButton>
                    </div>
                  </CardContent>
                </Card>
              ))
          ) : (
            <Card className="border-2 border-dashed border-green-200 bg-gradient-to-br from-green-50 to-emerald-50">
              <CardContent className="flex flex-col items-center justify-center h-64 text-center p-6">
                <div className="mb-4 p-4 rounded-full bg-green-100">
                  <Calendar className="h-8 w-8 text-green-600" />
                </div>
                <h3 className="text-lg font-semibold text-green-800 mb-2">No reminders yet</h3>
                <p className="text-green-600 mb-4 text-sm">
                  Start organizing your day by adding your first reminder
                </p>
                <PrimaryButton
                  onClick={() => setIsModalOpen(true)}
                  className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800"
                >
                  <Bell className="h-4 w-4 mr-2" />
                  Add First Reminder
                </PrimaryButton>
              </CardContent>
            </Card>
          )}
        </ScrollArea>
      </div>
    )
  );
}