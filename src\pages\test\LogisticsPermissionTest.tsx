import React from 'react';
import { Screen } from '@/app-components/layout/screen';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useLogisticsPermissions } from '@/hooks/useLogisticsPermissions';
import { useSidebarPermissions } from '@/hooks/useSidebarPermissions';
import { useAuthHook } from '@/utils/useAuthHook';
import { useGetUser2UserPermissionsQuery, useGetTeams2TeamsPermissionsQuery } from '@/redux/slices/permissions';
import { useGetDepartmentsQuery } from '@/redux/slices/user';
import PermissionManager from '@/components/debug/PermissionManager';

const LogisticsPermissionTest: React.FC = () => {
  const { user_details } = useAuthHook();
  const {
    canBookVisit,
    canCompleteTrips,
    canCreateVehicleRequest,
    canCreateSpecialAssignment,
    canAccessLogisticsDashboard,
    canAccessLogisticsStatistics,
    canAccessClients,
    canAccessDrivers,
    canAccessVehicles,
    canAccessLogisticsReports,
    canAccessSitevisitReports,
    userLogisticsPermissions,
    userPermissionCodes,
    isLoading: logisticsLoading,
    LOGISTICS_PERMISSIONS,
  } = useLogisticsPermissions();

  const { userPermissionCodes: allPermissionCodes, isLoading: sidebarLoading } = useSidebarPermissions();

  // Fetch departments to see user's department ID
  const { data: departments = [] } = useGetDepartmentsQuery({
    page: 1,
    page_size: 1000,
  });

  // Find user's department ID
  const userDepartmentId = React.useMemo(() => {
    if (!user_details?.department || !departments.length) return null;
    const userDept = departments.find((dept: any) => dept.dp_name === user_details.department);
    return userDept?.dp_id || null;
  }, [user_details?.department, departments]);

  // Fetch user-specific permissions directly
  const { data: userSpecificPermissions = [], isLoading: loadingUserPerms } = useGetUser2UserPermissionsQuery(
    {
      page: 1,
      page_size: 1000,
      user: user_details?.employee_no || '',
    },
    {
      skip: !user_details?.employee_no,
    }
  );

  // Fetch team permissions
  const { data: teamPermissions = [], isLoading: loadingTeamPerms } = useGetTeams2TeamsPermissionsQuery(
    {
      page: 1,
      page_size: 1000,
      team: userDepartmentId,
    },
    {
      skip: !userDepartmentId,
    }
  );

  const isLoading = logisticsLoading || sidebarLoading || loadingUserPerms || loadingTeamPerms;

  return (
    <Screen>
      <div className="container mx-auto p-6 space-y-6">
        <div className="prose dark:prose-invert max-w-none">
          <h1>Logistics Permission Test</h1>
          <p>This page helps debug logistics permissions for the current user.</p>
        </div>

        {isLoading && (
          <Card>
            <CardContent className="p-6">
              <p>Loading permissions...</p>
            </CardContent>
          </Card>
        )}

        {!isLoading && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* User Info */}
            <Card>
              <CardHeader>
                <CardTitle>User Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div><strong>Name:</strong> {user_details?.fullnames || "Unknown"}</div>
                <div><strong>Employee No:</strong> {user_details?.employee_no || "Unknown"}</div>
                <div><strong>Department:</strong> {user_details?.department || "Unknown"}</div>
                <div><strong>Department ID:</strong> {userDepartmentId || "Not found"}</div>
                <div><strong>Team:</strong> {user_details?.team || "Unknown"}</div>
                <div><strong>User Group:</strong> {user_details?.user_group || "Unknown"}</div>
              </CardContent>
            </Card>

            {/* Raw Permission Data */}
            <Card>
              <CardHeader>
                <CardTitle>Raw Permission Data</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <strong>User Permissions from Login:</strong>
                  <div className="text-sm text-gray-600 mt-1">
                    {user_details?.user_permissions ? 
                      JSON.stringify(user_details.user_permissions, null, 2) : 
                      "None"
                    }
                  </div>
                </div>

                <div>
                  <strong>User-Specific Permissions (API):</strong>
                  <div className="text-sm text-gray-600 mt-1">
                    {userSpecificPermissions.length > 0 ? 
                      JSON.stringify(userSpecificPermissions, null, 2) : 
                      "None found"
                    }
                  </div>
                </div>

                <div>
                  <strong>Team Permissions (API):</strong>
                  <div className="text-sm text-gray-600 mt-1">
                    {teamPermissions.length > 0 ? 
                      JSON.stringify(teamPermissions, null, 2) : 
                      "None found"
                    }
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* All Permission Codes */}
            <Card>
              <CardHeader>
                <CardTitle>All Permission Codes</CardTitle>
                <CardDescription>All permission codes the user has access to</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {allPermissionCodes.length > 0 ? (
                    allPermissionCodes.map(code => (
                      <Badge 
                        key={code} 
                        variant={Object.values(LOGISTICS_PERMISSIONS).includes(code) ? "default" : "secondary"}
                      >
                        {code}
                      </Badge>
                    ))
                  ) : (
                    <span className="text-red-600">No permissions found</span>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Logistics Permission Checks */}
            <Card>
              <CardHeader>
                <CardTitle>Logistics Permission Checks</CardTitle>
                <CardDescription>Specific logistics permission results</CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                {Object.entries(LOGISTICS_PERMISSIONS).map(([key, code]) => {
                  const hasPermission = userPermissionCodes.includes(code);
                  return (
                    <div key={key} className={`flex justify-between p-2 rounded ${hasPermission ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'}`}>
                      <span>{key} ({code})</span>
                      <span>{hasPermission ? '✓ Yes' : '✗ No'}</span>
                    </div>
                  );
                })}
              </CardContent>
            </Card>

            {/* Action Permissions */}
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>Action Permissions</CardTitle>
                <CardDescription>What actions the user can perform</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  <div className={`p-3 rounded border ${canBookVisit ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
                    <div className="font-medium">Book Visit</div>
                    <div className={`text-sm ${canBookVisit ? 'text-green-600' : 'text-red-600'}`}>
                      {canBookVisit ? 'Allowed' : 'Denied'}
                    </div>
                  </div>
                  
                  <div className={`p-3 rounded border ${canCompleteTrips ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
                    <div className="font-medium">Complete Trips</div>
                    <div className={`text-sm ${canCompleteTrips ? 'text-green-600' : 'text-red-600'}`}>
                      {canCompleteTrips ? 'Allowed' : 'Denied'}
                    </div>
                  </div>
                  
                  <div className={`p-3 rounded border ${canCreateVehicleRequest ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
                    <div className="font-medium">Create Vehicle Request</div>
                    <div className={`text-sm ${canCreateVehicleRequest ? 'text-green-600' : 'text-red-600'}`}>
                      {canCreateVehicleRequest ? 'Allowed' : 'Denied'}
                    </div>
                  </div>
                  
                  <div className={`p-3 rounded border ${canCreateSpecialAssignment ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
                    <div className="font-medium">Create Special Assignment</div>
                    <div className={`text-sm ${canCreateSpecialAssignment ? 'text-green-600' : 'text-red-600'}`}>
                      {canCreateSpecialAssignment ? 'Allowed' : 'Denied'}
                    </div>
                  </div>
                  
                  <div className={`p-3 rounded border ${canAccessLogisticsDashboard ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
                    <div className="font-medium">Access Dashboard</div>
                    <div className={`text-sm ${canAccessLogisticsDashboard ? 'text-green-600' : 'text-red-600'}`}>
                      {canAccessLogisticsDashboard ? 'Allowed' : 'Denied'}
                    </div>
                  </div>
                  
                  <div className={`p-3 rounded border ${canAccessLogisticsStatistics ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
                    <div className="font-medium">Access Statistics</div>
                    <div className={`text-sm ${canAccessLogisticsStatistics ? 'text-green-600' : 'text-red-600'}`}>
                      {canAccessLogisticsStatistics ? 'Allowed' : 'Denied'}
                    </div>
                  </div>
                  
                  <div className={`p-3 rounded border ${canAccessClients ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
                    <div className="font-medium">Access Clients</div>
                    <div className={`text-sm ${canAccessClients ? 'text-green-600' : 'text-red-600'}`}>
                      {canAccessClients ? 'Allowed' : 'Denied'}
                    </div>
                  </div>
                  
                  <div className={`p-3 rounded border ${canAccessDrivers ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
                    <div className="font-medium">Access Drivers</div>
                    <div className={`text-sm ${canAccessDrivers ? 'text-green-600' : 'text-red-600'}`}>
                      {canAccessDrivers ? 'Allowed' : 'Denied'}
                    </div>
                  </div>
                  
                  <div className={`p-3 rounded border ${canAccessVehicles ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
                    <div className="font-medium">Access Vehicles</div>
                    <div className={`text-sm ${canAccessVehicles ? 'text-green-600' : 'text-red-600'}`}>
                      {canAccessVehicles ? 'Allowed' : 'Denied'}
                    </div>
                  </div>

                  <div className={`p-3 rounded border ${canAccessLogisticsReports ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
                    <div className="font-medium">Access Logistics Reports</div>
                    <div className={`text-sm ${canAccessLogisticsReports ? 'text-green-600' : 'text-red-600'}`}>
                      {canAccessLogisticsReports ? 'Allowed' : 'Denied'}
                    </div>
                  </div>

                  <div className={`p-3 rounded border ${canAccessSitevisitReports ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
                    <div className="font-medium">Access Site Visit Reports</div>
                    <div className={`text-sm ${canAccessSitevisitReports ? 'text-green-600' : 'text-red-600'}`}>
                      {canAccessSitevisitReports ? 'Allowed' : 'Denied'}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Permission Manager */}
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>Permission Manager</CardTitle>
                <CardDescription>Add permissions for testing (Development only)</CardDescription>
              </CardHeader>
              <CardContent>
                <PermissionManager />
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </Screen>
  );
};

export default LogisticsPermissionTest;
