import { useState } from "react";
import MultiStepModal from "@/components/custom/modals/MultiStepModal";
import { Label } from "@/components/ui/label";
import { AlertCircle, Ticket, User } from "lucide-react";
import { Button } from "@/components/ui/button"; 
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import EscalateComplaintModal from "./TicketEscalate";


interface ComplaintData {
  id: number;
  title: string;
  value: string;
  icon: typeof Ticket;
  change: string;
  changeLabel: string;
  description: string;
  assignee: string;
}

interface ViewComplaintModalProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  complaintData: ComplaintData | null;
  onReassign: (ticketId: number, newAssignee: string) => void;
}

export default function ViewComplaintModal({
  isOpen,
  onOpenChange,
  complaintData,
  onReassign,
}: ViewComplaintModalProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [escalateOpen, setEscalateOpen] = useState(false);
  const [newAssignee, setNewAssignee] = useState("");
  const assignees = ["John Doe", "Jane Smith", "Alex Johnson", "Unassigned"];

  const handleMarkAsSolved = () => {
    console.log("Complaint marked as solved:", complaintData);
    onOpenChange(false);
    setCurrentStep(0);
  };

  const handleEscalationSubmit = (data: any) => {
    console.log("Escalation submitted:", data);
    setEscalateOpen(false);
    onOpenChange(false);
    setCurrentStep(0);
  };

  const handleReassign = () => {
    if (complaintData && newAssignee) {
      onReassign(complaintData.id, newAssignee);
      setNewAssignee("");
    }
  };

  return (
    <>
      <MultiStepModal
        isOpen={isOpen}
        onOpenChange={(open) => {
          onOpenChange(open);
          if (!open) {
            setCurrentStep(0);
            setNewAssignee("");
          }
        }}
        title="View Complaint Details"
        description="Review the details of the selected complaint"
        currentStep={currentStep}
        onStepChange={setCurrentStep}
        onComplete={() => {
          console.log("Complaint review completed!");
          onOpenChange(false);
          setCurrentStep(0);
        }}
        steps={[
          {
            title: "Basic Info",
            content: (
              <div className="space-y-4 py-2">
                <div className="space-y-2">
                  <Label>Title</Label>
                  <p className="text-sm text-muted-foreground">
                    {complaintData?.title || "N/A"}
                  </p>
                </div>
                <div className="space-y-2">
                  <Label>Status</Label>
                  <p className="text-sm text-muted-foreground">
                    {complaintData?.value || "N/A"}
                  </p>
                </div>
                <div className="space-y-2">
                  <Label>Assignee</Label>
                  <p className="text-sm text-muted-foreground">
                    {complaintData?.assignee || "Unassigned"}
                  </p>
                </div>
              </div>
            ),
          },
          {
            title: "Complaint Info",
            content: (
              <div className="space-y-4 py-2">
                <div className="space-y-2">
                  <Label>Description</Label>
                  <p className="text-sm text-muted-foreground">
                    {complaintData?.description || "N/A"}
                  </p>
                </div>
                <div className="space-y-2">
                  <Label>Last Updated</Label>
                  <p className="text-sm text-muted-foreground">
                    {complaintData?.changeLabel || "N/A"} ({complaintData?.change || "N/A"})
                  </p>
                </div>
              </div>
            ),
          },
          {
            title: "Reassign",
            content: (
              <div className="space-y-4 py-2">
                <div className="space-y-2">
                  <Label htmlFor="reassign">Reassign To</Label>
                  <select
                    id="reassign"
                    value={newAssignee}
                    onChange={(e) => setNewAssignee(e.target.value)}
                    className="w-full rounded-md border border-gray-300 p-2 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50"
                  >
                    <option value="" disabled>Select an assignee</option>
                    {assignees.map((assignee) => (
                      <option key={assignee} value={assignee}>{assignee}</option>
                    ))}
                  </select>
                </div>
                <PrimaryButton
                  variant="default"
                  onClick={handleReassign}
                  disabled={!newAssignee}
                  className="w-full"
                >
                  Reassign Ticket
                </PrimaryButton>
              </div>
            ),
          },
          {
            title: "Confirmation",
            content: (
              <div className="py-6 text-center space-y-4">
                <AlertCircle className="w-12 h-12 text-blue-500 mx-auto mb-4" />
                <h3 className="text-lg font-medium">Review Complete</h3>
                <p className="text-muted-foreground">
                  You have reviewed all complaint details.
                </p>
                <div className="flex justify-center gap-4 mt-6">
                  <PrimaryButton variant="outline" onClick={handleMarkAsSolved}>
                    Mark as Solved
                  </PrimaryButton>
                  <PrimaryButton variant="default" onClick={() => setEscalateOpen(true)}>
                    Escalate
                  </PrimaryButton>
                  <PrimaryButton variant="secondary">Add reminder</PrimaryButton>
                </div>
              </div>
            ),
          },
        ]}
      />

      <EscalateComplaintModal
        isOpen={escalateOpen}
        onOpenChange={setEscalateOpen}
        onSubmit={handleEscalationSubmit}
        complaint={{
          id: complaintData?.id || 0,
          title: complaintData?.title || "N/A",
          description: complaintData?.description || "N/A",
        }}
      />
    </>
  );
}