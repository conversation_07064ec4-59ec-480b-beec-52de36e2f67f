import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Shield, Lock, CheckCircle, XCircle, AlertTriangle } from 'lucide-react';
import { useSidebarPermissions } from '@/hooks/useSidebarPermissions';
import { usePermissionNavigation } from '@/hooks/usePermissionNavigation';

const RoutePermissionTest: React.FC = () => {
  const navigate = useNavigate();
  const { hasPermission, userPermissionCodes } = useSidebarPermissions();
  const { canAccessRoute } = usePermissionNavigation();

  const testRoutes = [
    { path: '/logistics-dash', name: 'Logistics Dashboard', requiredPermission: 205 },
    { path: '/logistics', name: 'Logistics Clients', requiredPermission: 207 },
    { path: '/logistics/vehicles', name: 'Vehicles', requiredPermission: 209 },
    { path: '/drivers', name: 'Drivers', requiredPermission: 208 },
    { path: '/logistics-stats', name: 'Logistics Stats', requiredPermission: 206 },
    { path: '/admin/services', name: 'Admin Services', requiredPermission: 116 },
    { path: '/permissions', name: 'Permissions Management', requiredPermission: 117 },
    { path: '/users-list', name: 'Users List', requiredPermission: 117 },
    { path: '/complaints', name: 'Complaints', requiredPermission: 116 },
    { path: '/flags', name: 'Flags', requiredPermission: 116 },
    { path: '/unauthorized', name: 'Unauthorized Page', requiredPermission: null }, // Should be accessible to all
  ];

  const handleDirectNavigation = (path: string) => {
    // This simulates what happens when a user enters a URL directly
    navigate(path);
  };

  const handleTestAccess = (path: string) => {
    // This tests the permission checking without navigation
    const hasAccess = canAccessRoute(path);
    alert(`Access to ${path}: ${hasAccess ? 'ALLOWED' : 'DENIED'}`);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Route Permission Testing
          </CardTitle>
          <CardDescription>
            Test the permission system by attempting to access different routes
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert className="mb-6">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Test Instructions:</strong> Try navigating to restricted routes directly by clicking "Navigate". 
              If permissions are working correctly, you should be redirected to the unauthorized page for routes you don't have access to.
            </AlertDescription>
          </Alert>

          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2">Your Current Permissions:</h3>
            <div className="flex flex-wrap gap-2">
              {Array.from(userPermissionCodes).map((code) => (
                <span key={code} className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm">
                  {code}
                </span>
              ))}
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Test Routes:</h3>
            {testRoutes.map((route) => {
              const hasAccess = route.requiredPermission ? hasPermission(route.requiredPermission) : true;
              const canAccess = canAccessRoute(route.path);
              
              return (
                <div
                  key={route.path}
                  className="flex items-center justify-between p-4 border border-gray-200 rounded-md"
                >
                  <div className="flex items-center gap-3">
                    {hasAccess ? (
                      <CheckCircle className="w-5 h-5 text-green-500" />
                    ) : (
                      <XCircle className="w-5 h-5 text-red-500" />
                    )}
                    <div>
                      <div className="font-medium">{route.name}</div>
                      <div className="text-sm text-gray-500">
                        {route.path} 
                        {route.requiredPermission && ` (Requires permission ${route.requiredPermission})`}
                      </div>
                      <div className="text-xs text-gray-400">
                        Permission Check: {hasAccess ? 'PASS' : 'FAIL'} | 
                        Route Check: {canAccess ? 'PASS' : 'FAIL'}
                      </div>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      onClick={() => handleTestAccess(route.path)}
                      variant="outline"
                      size="sm"
                    >
                      Test Access
                    </Button>
                    <Button
                      onClick={() => handleDirectNavigation(route.path)}
                      variant={hasAccess ? "default" : "destructive"}
                      size="sm"
                    >
                      Navigate
                    </Button>
                  </div>
                </div>
              );
            })}
          </div>

          <div className="mt-6 p-4 bg-gray-50 rounded-md">
            <h4 className="font-semibold mb-2">Expected Behavior:</h4>
            <ul className="text-sm space-y-1 text-gray-600">
              <li>• Routes with green checkmarks should be accessible</li>
              <li>• Routes with red X marks should redirect to /unauthorized</li>
              <li>• The unauthorized page itself should always be accessible</li>
              <li>• Direct URL navigation should respect permissions</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RoutePermissionTest;
