import { useState } from "react";
import BaseModal from "@/components/custom/modals/BaseModal";
import { Card, CardContent } from "@/components/ui/card";
import { X, MapPin, Calendar, CheckCircle, ArrowLeft } from "lucide-react";
import { motion } from "framer-motion";
import { Badge } from "@/components/custom/badges/badges";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import RegionTableModal from "./RegionTableView";
import { useGethrDashboardQuery, useGetPeriodsQuery } from "@/redux/slices/hrDashboardApiSlice";

interface DateRange {
  from?: Date;
  to?: Date;
}

interface ReportsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onViewReport?: (range: DateRange) => void;
}

// TypeScript interfaces for API data (treating offices as regions)
interface RegionData {
  office: string; // We'll treat this as region name
  total_marketers: number;
  current_period: {
    period_name: string;
    start_date: string;
    end_date: string;
    target: number;
    achieved: number;
    progress: number;
  };
}

interface PeriodData {
  period_name: string;
  start_date: string;
  end_date: string;
  target: number;
  achieved: number;
  progress: number;
  period_start_date?: string;
  period_end_date?: string;
}

export default function RegionReportsModal({
  open,
  onOpenChange,
  onViewReport,
}: ReportsModalProps) {
  const [selectedRegion, setSelectedRegion] = useState<string>("");
  const [selectedPeriod, setSelectedPeriod] = useState<PeriodData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showReportsTable, setShowReportsTable] = useState(false);
  const [step, setStep] = useState<"region" | "period">("region");

  // Fetch regions data (using offices as regions)
  const { data: regionsData, isLoading: isLoadingRegions, error: regionsError } = useGethrDashboardQuery({});

  // Fetch periods using the new getPeriods query
  const { data: periodsData, isLoading: isLoadingPeriods, error: periodsError } = useGetPeriodsQuery(
    { page: 1, page_size: 50 }
  );

  const handleRegionSelect = (region: string) => {
    setSelectedRegion(region);
    setSelectedPeriod(null);
    setStep("period");
  };

  const handlePeriodSelect = (period: PeriodData) => {
    setSelectedPeriod(period);
    // Automatically open the report when a period is selected
    handleViewReport(period);
  };

  const handleViewReport = (period?: PeriodData) => {
    const periodToUse = period || selectedPeriod;
    if (!periodToUse) return;

    setIsLoading(true);
    try {
      const dateRange: DateRange = {
        from: new Date(periodToUse.start_date),
        to: new Date(periodToUse.end_date),
      };
      onViewReport?.(dateRange);
      setShowReportsTable(true);
    } finally {
      setTimeout(() => setIsLoading(false), 500);
    }
  };

  const handleReset = () => {
    setSelectedRegion("");
    setSelectedPeriod(null);
    setShowReportsTable(false);
    setStep("region");
  };

  const handleBack = () => {
    if (step === "period") {
      setStep("region");
      setSelectedPeriod(null);
    }
  };

  return (
    <BaseModal
      open={open}
      onOpenChange={onOpenChange}
      title={showReportsTable ? "Region Table" : "Region Reports"}
      description={
        showReportsTable
          ? "View the generated report"
          : step === "region"
            ? "Select a region to view reports"
            : "Select a period to view reports"
      }
      className="max-w-4xl"
    >
      {showReportsTable ? (
        <RegionTableModal
          open={showReportsTable}
          onOpenChange={() => setShowReportsTable(false)}
        />
      ) : (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="flex flex-col gap-6 p-4 bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 rounded-md"
        >
          {/* Header with navigation */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              {step === "period" && (
                <PrimaryButton
                  variant="outline"
                  size="sm"
                  onClick={handleBack}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft className="w-4 h-4" />
                  Back to Regions
                </PrimaryButton>
              )}
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {step === "region" ? "Select Region" : `Select Period for ${selectedRegion}`}
              </h3>
            </div>
            {(selectedRegion || selectedPeriod) && (
              <PrimaryButton
                variant="outline"
                size="sm"
                onClick={handleReset}
                className="flex items-center gap-2"
              >
                <X className="w-4 h-4" />
                Reset
              </PrimaryButton>
            )}
          </div>

          {/* Region Selection Cards */}
          {step === "region" && (
            <div className="space-y-4">
              {isLoadingRegions ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {Array.from({ length: 6 }).map((_, idx) => (
                    <Card key={idx} className="animate-pulse">
                      <CardContent className="p-4">
                        <div className="h-20 bg-gray-200 rounded"></div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : regionsError ? (
                <div className="text-center py-8">
                  <p className="text-red-600">Failed to load regions. Please try again.</p>
                </div>
              ) : regionsData?.offices?.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {regionsData.offices.map((region: RegionData) => (
                    <Card
                      key={region.office}
                      className={cn(
                        "cursor-pointer transition-all duration-200 hover:shadow-lg border-2",
                        selectedRegion === region.office
                          ? "border-green-500 bg-green-50 dark:bg-green-900/20"
                          : "border-gray-200 hover:border-green-300"
                      )}
                      onClick={() => handleRegionSelect(region.office)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex items-center gap-3">
                            <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                              <MapPin className="w-5 h-5 text-green-600 dark:text-green-400" />
                            </div>
                            <div>
                              <h4 className="font-semibold text-gray-900 dark:text-white">
                                {region.office} Region
                              </h4>
                              <p className="text-sm text-gray-600 dark:text-gray-400">
                                {region.total_marketers} Marketers
                              </p>
                            </div>
                          </div>
                          {selectedRegion === region.office && (
                            <CheckCircle className="w-5 h-5 text-green-600" />
                          )}
                        </div>
                        <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                          <div className="flex justify-between items-center text-sm">
                            <span className="text-gray-600 dark:text-gray-400">Current Progress</span>
                            <Badge
                              variant={region.current_period.progress >= 70 ? "default" : "secondary"}
                              className={cn(
                                region.current_period.progress >= 70
                                  ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                                  : "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400"
                              )}
                            >
                              {region.current_period.progress.toFixed(1)}%
                            </Badge>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-600">No regions available.</p>
                </div>
              )}
            </div>
          )}

          {/* Period Selection Cards */}
          {step === "period" && selectedRegion && (
            <div className="space-y-4">
              {isLoadingPeriods ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {Array.from({ length: 4 }).map((_, idx) => (
                    <Card key={idx} className="animate-pulse">
                      <CardContent className="p-4">
                        <div className="h-24 bg-gray-200 rounded"></div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : periodsError ? (
                <div className="text-center py-8">
                  <p className="text-red-600">Failed to load periods. Please try again.</p>
                </div>
              ) : periodsData?.results && periodsData.results.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {periodsData.results.map((period: any) => (
                    <Card
                      key={`${period.period_start_date}-${period.period_end_date}`}
                      className={cn(
                        "cursor-pointer transition-all duration-200 hover:shadow-lg border-2",
                        selectedPeriod?.period_start_date === period.period_start_date
                          ? "border-green-500 bg-green-50 dark:bg-green-900/20"
                          : "border-gray-200 hover:border-green-300"
                      )}
                      onClick={() => handlePeriodSelect({
                        period_name: `${format(new Date(period.period_start_date), "MMM dd")} - ${format(new Date(period.period_end_date), "MMM dd, yyyy")}`,
                        start_date: period.period_start_date,
                        end_date: period.period_end_date,
                        target: 0,
                        achieved: 0,
                        progress: 0,
                        period_start_date: period.period_start_date,
                        period_end_date: period.period_end_date
                      })}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex items-center gap-3">
                            <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                              <Calendar className="w-5 h-5 text-green-600 dark:text-green-400" />
                            </div>
                            <div>
                              <h4 className="font-semibold text-gray-900 dark:text-white">
                                {format(new Date(period.period_start_date), "MMM dd")} - {format(new Date(period.period_end_date), "MMM dd, yyyy")}
                              </h4>
                              <p className="text-sm text-gray-600 dark:text-gray-400">
                                {selectedRegion} Region
                              </p>
                              <p className="text-xs text-gray-500 dark:text-gray-400">
                                Marketing Period
                              </p>
                            </div>
                          </div>
                          {selectedPeriod?.period_start_date === period.period_start_date && (
                            <CheckCircle className="w-5 h-5 text-green-600" />
                          )}
                        </div>
                        <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                          <div className="text-center text-sm text-gray-600 dark:text-gray-400">
                            Click to select this period
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-600">No periods available for this region.</p>
                </div>
              )}
            </div>
          )}

          {/* Action Buttons - Removed since reports open automatically */}

          {/* Selected Summary */}
          {selectedRegion && selectedPeriod && (
            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
              <h4 className="font-semibold text-green-900 dark:text-green-100 mb-2">Selected Report Details</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-green-700 dark:text-green-300">Region:</span>
                  <p className="font-medium text-green-900 dark:text-green-100">{selectedRegion}</p>
                </div>
                <div>
                  <span className="text-green-700 dark:text-green-300">Period:</span>
                  <p className="font-medium text-green-900 dark:text-green-100">{selectedPeriod.period_name}</p>
                </div>
                <div>
                  <span className="text-green-700 dark:text-green-300">Date Range:</span>
                  <p className="font-medium text-green-900 dark:text-green-100">
                    {format(new Date(selectedPeriod.start_date), "MMM dd")} - {format(new Date(selectedPeriod.end_date), "MMM dd, yyyy")}
                  </p>
                </div>
                <div>
                  <span className="text-green-700 dark:text-green-300">Target:</span>
                  <p className="font-medium text-green-900 dark:text-green-100">
                    {new Intl.NumberFormat('en-KE', {
                      style: 'currency',
                      currency: 'KES',
                      minimumFractionDigits: 0,
                    }).format(selectedPeriod.target)}
                  </p>
                </div>
              </div>
            </div>
          )}
        </motion.div>
      )}
    </BaseModal>
  );
}
