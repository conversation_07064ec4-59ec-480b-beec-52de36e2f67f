import { Progress } from "@/components/ui/progress";

type Props = {
  value?: number;
  color?: string;
  orientation?: "horizontal" | "vertical";
};

const PBWithPercOutside = ({ value, color, orientation }: Props) => {
  return (
    <div className="py-3">
      <Progress value={value} color={color} orientation={orientation} />
      <div className="flex items-center justify-end w-full h-full text-xs font-bold text-black">
        {value}%
      </div>
    </div>
  );
};

export default PBWithPercOutside;
