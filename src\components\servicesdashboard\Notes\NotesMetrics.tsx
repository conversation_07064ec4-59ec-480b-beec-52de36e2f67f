import { useMemo } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  FileText,
  Users,
  UserPlus,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Activity,
  Pin,
  Lock,
  Star,
  Clock,
  CheckCircle,
  AlertTriangle,
  BarChart3,
  PieChart,
} from "lucide-react";
import { NotesMetrics } from "@/types/notes";

interface NotesMetricsProps {
  metrics?: NotesMetrics;
  currentNotes?: any[];
  isLoading?: boolean;
}

export default function NotesMetricsComponent({ 
  metrics, 
  currentNotes = [], 
  isLoading = false 
}: NotesMetricsProps) {
  
  // Calculate real-time metrics from current notes if metrics not provided
  const calculatedMetrics = useMemo(() => {
    if (metrics) return metrics;
    
    const totalNotes = currentNotes.length;
    const customerNotes = currentNotes.filter(n => n.entity_type === 'customer').length;
    const prospectNotes = currentNotes.filter(n => n.entity_type === 'prospect').length;
    const salesNotes = currentNotes.filter(n => n.entity_type === 'leadfile').length;
    const generalNotes = currentNotes.filter(n => n.entity_type === 'general' || !n.entity_type).length;
    const privateNotes = currentNotes.filter(n => n.is_private).length;
    const pinnedNotes = currentNotes.filter(n => n.is_pinned).length;
    const draftNotes = currentNotes.filter(n => n.status === 'draft').length;
    const finalizedNotes = currentNotes.filter(n => n.status === 'finalized').length;
    const highPriorityNotes = currentNotes.filter(n => n.priority === 'high').length;
    
    // Calculate recent notes (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    const recentNotes = currentNotes.filter(n => 
      new Date(n.created_at || n.updated_at) > sevenDaysAgo
    ).length;

    return {
      totalNotes,
      customerNotes,
      prospectNotes,
      salesNotes,
      generalNotes,
      privateNotes,
      pinnedNotes,
      draftNotes,
      finalizedNotes,
      highPriorityNotes,
      recentNotes,
    };
  }, [metrics, currentNotes]);

  const metricsCards = [
    {
      title: "Total Notes",
      value: calculatedMetrics.totalNotes,
      icon: FileText,
      color: "text-blue-600",
      bgColor: "bg-gradient-to-br from-blue-50 to-blue-100",
      iconBg: "bg-blue-500",
      change: "+12%",
      changeLabel: "this month",
      trend: "up" as const,
    },
    {
      title: "Customer Notes",
      value: calculatedMetrics.customerNotes,
      icon: Users,
      color: "text-green-600",
      bgColor: "bg-gradient-to-br from-green-50 to-green-100",
      iconBg: "bg-green-500",
      change: "+8%",
      changeLabel: "this week",
      trend: "up" as const,
    },
    {
      title: "Prospect Notes",
      value: calculatedMetrics.prospectNotes,
      icon: UserPlus,
      color: "text-purple-600",
      bgColor: "bg-gradient-to-br from-purple-50 to-purple-100",
      iconBg: "bg-purple-500",
      change: "+15%",
      changeLabel: "this week",
      trend: "up" as const,
    },
    {
      title: "Sales Notes",
      value: calculatedMetrics.salesNotes,
      icon: DollarSign,
      color: "text-emerald-600",
      bgColor: "bg-gradient-to-br from-emerald-50 to-emerald-100",
      iconBg: "bg-emerald-500",
      change: "+5%",
      changeLabel: "this month",
      trend: "up" as const,
    },
    {
      title: "High Priority",
      value: calculatedMetrics.highPriorityNotes,
      icon: AlertTriangle,
      color: "text-red-600",
      bgColor: "bg-gradient-to-br from-red-50 to-red-100",
      iconBg: "bg-red-500",
      change: "-3%",
      changeLabel: "this week",
      trend: "down" as const,
    },
    {
      title: "Recent Notes",
      value: calculatedMetrics.recentNotes,
      icon: Clock,
      color: "text-orange-600",
      bgColor: "bg-gradient-to-br from-orange-50 to-orange-100",
      iconBg: "bg-orange-500",
      change: "+22%",
      changeLabel: "last 7 days",
      trend: "up" as const,
    },
  ];

  const additionalMetrics = [
    {
      title: "Private Notes",
      value: calculatedMetrics.privateNotes,
      icon: Lock,
      color: "text-gray-600",
      percentage: calculatedMetrics.totalNotes > 0 ? 
        Math.round((calculatedMetrics.privateNotes / calculatedMetrics.totalNotes) * 100) : 0,
    },
    {
      title: "Pinned Notes",
      value: calculatedMetrics.pinnedNotes,
      icon: Pin,
      color: "text-yellow-600",
      percentage: calculatedMetrics.totalNotes > 0 ? 
        Math.round((calculatedMetrics.pinnedNotes / calculatedMetrics.totalNotes) * 100) : 0,
    },
    {
      title: "Draft Notes",
      value: calculatedMetrics.draftNotes,
      icon: FileText,
      color: "text-amber-600",
      percentage: calculatedMetrics.totalNotes > 0 ? 
        Math.round((calculatedMetrics.draftNotes / calculatedMetrics.totalNotes) * 100) : 0,
    },
    {
      title: "Finalized Notes",
      value: calculatedMetrics.finalizedNotes,
      icon: CheckCircle,
      color: "text-green-600",
      percentage: calculatedMetrics.totalNotes > 0 ? 
        Math.round((calculatedMetrics.finalizedNotes / calculatedMetrics.totalNotes) * 100) : 0,
    },
  ];

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
        {[1, 2, 3, 4, 5, 6].map((i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-gray-200 rounded-xl"></div>
                <div className="w-16 h-6 bg-gray-200 rounded-full"></div>
              </div>
              <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
              <div className="h-8 bg-gray-200 rounded w-16 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-32"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Main Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
        {metricsCards.map((metric, index) => (
          <Card 
            key={metric.title} 
            className={`${metric.bgColor} border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105`}
            style={{ animationDelay: `${index * 100}ms` }}
          >
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <div className={`p-3 rounded-xl ${metric.iconBg} shadow-lg`}>
                  <metric.icon className="h-6 w-6 text-white" />
                </div>
                <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${
                  metric.trend === 'up' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {metric.trend === 'up' ? (
                    <TrendingUp className="w-3 h-3" />
                  ) : (
                    <TrendingDown className="w-3 h-3" />
                  )}
                </div>
              </div>
              <CardTitle className={`text-sm font-medium ${metric.color} mt-3`}>
                {metric.title}
              </CardTitle>
            </CardHeader>
            
            <CardContent className="pb-2">
              <div className="text-3xl font-bold text-gray-900 mb-1">
                {metric.value}
              </div>
            </CardContent>
            
            <div className="px-6 pb-4">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <span className={`font-medium ${
                  metric.trend === 'up' ? 'text-green-600' : 'text-red-600'
                }`}>
                  {metric.change}
                </span>
                <span>{metric.changeLabel}</span>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Additional Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {additionalMetrics.map((metric, index) => (
          <Card key={metric.title} className="shadow-lg hover:shadow-xl transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <metric.icon className={`h-8 w-8 ${metric.color}`} />
                <Badge variant="secondary" className="text-xs">
                  {metric.percentage}%
                </Badge>
              </div>
              <h3 className={`text-sm font-medium ${metric.color} mb-2`}>
                {metric.title}
              </h3>
              <div className="text-2xl font-bold text-gray-900 mb-2">
                {metric.value}
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full ${
                    metric.color.includes('gray') ? 'bg-gray-500' :
                    metric.color.includes('yellow') ? 'bg-yellow-500' :
                    metric.color.includes('amber') ? 'bg-amber-500' :
                    'bg-green-500'
                  }`}
                  style={{ width: `${metric.percentage}%` }}
                ></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Summary Stats */}
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="w-5 h-5" />
            <span>Notes Overview</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div className="space-y-2">
              <div className="text-2xl font-bold text-blue-600">
                {calculatedMetrics.totalNotes}
              </div>
              <div className="text-sm text-gray-600">Total Notes</div>
            </div>
            <div className="space-y-2">
              <div className="text-2xl font-bold text-green-600">
                {calculatedMetrics.customerNotes + calculatedMetrics.prospectNotes + calculatedMetrics.salesNotes}
              </div>
              <div className="text-sm text-gray-600">Business Notes</div>
            </div>
            <div className="space-y-2">
              <div className="text-2xl font-bold text-purple-600">
                {calculatedMetrics.finalizedNotes}
              </div>
              <div className="text-sm text-gray-600">Completed</div>
            </div>
            <div className="space-y-2">
              <div className="text-2xl font-bold text-orange-600">
                {calculatedMetrics.recentNotes}
              </div>
              <div className="text-sm text-gray-600">This Week</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
