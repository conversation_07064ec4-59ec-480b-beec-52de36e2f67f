import { Screen } from "@/app-components/layout/screen";
import {
  Bell,
  Settings,
  Plus,
  UserPlus,
  Truck,
  MoveUpLeft,
  MoveUp,
  ArrowUp,
  ArrowUpLeft,
  ArrowUpRight,
  TrendingUp,
  TrendingDown,
  Users,
  DollarSign,
  Target,
  Activity,
  Award,
  BarChart3,
  <PERSON><PERSON>hart as PieChartIcon,
  Calendar as CalendarIcon,
  MapPin,
  Phone,
  Mail,
  FileText,
  Briefcase,
} from "lucide-react";
import Icon1 from "@/assets/icon1.svg";
import Icon2 from "@/assets/icon2.svg";
import Icon3 from "@/assets/icon3.svg";
import Icon4 from "@/assets/icon4.svg";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useAuthHook } from "@/utils/useAuthHook";
import { Link } from "react-router-dom";
import { useState, useMemo, useEffect } from "react";
import AddProspects from "./Prospects/addlead";
import { useGetHomeStatsQuery } from "@/redux/slices/home";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Tooltip,
  Legend,
} from "recharts";
import {
  BookOpen,
  SquareLibrary,
  Clock,
  AlertCircle,
  CheckCircle,
  Calendar,
} from "lucide-react";
import { NotificationList } from "@/components/dashboard/NotificationList";
import { useGetDashboardQuery } from "@/redux/slices/dashboard";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import {
  formatShortDate,
  formatNumberWithCommas,
} from "@/utils/salesDataFormatter";
import { useGetDataDashboardStatsQuery } from "@/redux/slices/dashboardStatsApiSlice";
import { useGetAllSalesQuery } from "@/redux/slices/sales";
import {
  useGetMarketerPerformanceQuery,
  useGetTeamsPerformanceQuery,
} from "@/redux/slices/hrDashboardApiSlice";
import { useGetTeleteamDashboardQuery } from "@/redux/slices/telemarketingApiSlice";
import { useGetDigitalDashboardQuery } from "@/redux/slices/digitalApiSlice";

// Helper function to determine user role and permissions
const getUserRoleInfo = (user_details: any) => {
  const userGroup = user_details?.user_group?.toLowerCase() || "";
  const team = user_details?.team?.toLowerCase() || "";
  const department = user_details?.department?.toLowerCase() || "";
  const permissions = user_details?.user_permissions || [];

  // Determine role hierarchy
  const isManager =
    userGroup.includes("manager") ||
    userGroup.includes("head") ||
    team.includes("manager") ||
    department.includes("management");
  const isTeamLead =
    userGroup.includes("team lead") ||
    userGroup.includes("lead") ||
    team.includes("lead") ||
    permissions.some((p: any) => p.permission_name?.includes("team"));
  const isGroupLead =
    userGroup.includes("group lead") ||
    userGroup.includes("supervisor") ||
    permissions.some((p: any) => p.permission_name?.includes("group"));
  const isMarketer =
    userGroup.includes("marketer") ||
    team.includes("marketing") ||
    department.includes("marketing");

  return {
    isManager,
    isTeamLead,
    isGroupLead,
    isMarketer,
    userGroup,
    team,
    department,
    permissions,
    roleLevel: isManager
      ? "manager"
      : isGroupLead
      ? "group_lead"
      : isTeamLead
      ? "team_lead"
      : "marketer",
  };
};

// Helper function to get role-specific greeting
const getRoleGreeting = (roleInfo: any, userName: string) => {
  const timeOfDay =
    new Date().getHours() >= 0 && new Date().getHours() < 12
      ? "Good Morning"
      : new Date().getHours() < 16
      ? "Good Afternoon"
      : new Date().getHours() < 19
      ? "Good Evening"
      : "Good Night";

  if (roleInfo.isManager) return `${timeOfDay}, Manager ${userName}`;
  if (roleInfo.isGroupLead) return `${timeOfDay}, Group Lead ${userName}`;
  if (roleInfo.isTeamLead) return `${timeOfDay}, Team Lead ${userName}`;
  return `${timeOfDay}, ${userName}`;
};

// Helper function to format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat("en-KE", {
    style: "currency",
    currency: "KES",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

// Helper function to calculate percentage change
const calculatePercentageChange = (current: number, previous: number) => {
  if (previous === 0) return current > 0 ? 100 : 0;
  return ((current - previous) / previous) * 100;
};

const Home = () => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  // User authentication and role information
  const { user_details } = useAuthHook();
  const employee_no = user_details?.employee_no || "";
  const employee_name = user_details?.fullnames || "";
  const roleInfo = getUserRoleInfo(user_details);

  // API calls for dashboard data
  const { data, isLoading, isFetching } = useGetDashboardQuery({
    EMPLOYEE_NO: employee_no,
  });

  // Get comprehensive dashboard statistics
  const { data: dashboardStats, isLoading: statsLoading } =
    useGetDataDashboardStatsQuery({});

  // Get sales data with role-based filtering
  const { data: salesData, isLoading: salesLoading } = useGetAllSalesQuery({
    page: 1,
    page_size: 100,
    ...(roleInfo.isMarketer && { marketer_employee_no: employee_no }),
    ...(roleInfo.isTeamLead && { team: user_details?.team }),
  });

  // Get performance data based on role
  const { data: performanceData, isLoading: performanceLoading } =
    useGetMarketerPerformanceQuery({
      marketer_employee_no: roleInfo.isMarketer ? employee_no : undefined,
      page: 1,
      page_size: 50,
    });

  // Get team performance for managers and team leads
  const { data: teamPerformance, isLoading: teamLoading } =
    useGetTeamsPerformanceQuery(
      {
        page: 1,
        page_size: 20,
      },
      {
        skip:
          !roleInfo.isManager && !roleInfo.isTeamLead && !roleInfo.isGroupLead,
      }
    );

  // Get specialized dashboard data based on role
  const { data: teleteamData } = useGetTeleteamDashboardQuery(
    {},
    {
      skip: !user_details?.team?.toLowerCase().includes("telemarketing"),
    }
  );

  const { data: digitalData } = useGetDigitalDashboardQuery(
    {},
    {
      skip: !user_details?.team?.toLowerCase().includes("digital"),
    }
  );

  // Compute real sales chart data from API
  const computedSalesData = useMemo(() => {
    if (!salesData?.data?.results) {
      // Fallback data if no sales data available
      return [
        { month: "Jan", sales: 0, clients: 0 },
        { month: "Feb", sales: 0, clients: 0 },
        { month: "Mar", sales: 0, clients: 0 },
        { month: "Apr", sales: 0, clients: 0 },
        { month: "May", sales: 0, clients: 0 },
        { month: "Jun", sales: 0, clients: 0 },
        { month: "Jul", sales: 0, clients: 0 },
        { month: "Aug", sales: 0, clients: 0 },
        { month: "Sep", sales: 0, clients: 0 },
        { month: "Oct", sales: 0, clients: 0 },
        { month: "Nov", sales: 0, clients: 0 },
        { month: "Dec", sales: 0, clients: 0 },
      ];
    }

    // Group sales by month
    const monthlyData: {
      [key: string]: { sales: number; clients: Set<string> };
    } = {};
    const months = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];

    // Initialize all months
    months.forEach((month) => {
      monthlyData[month] = { sales: 0, clients: new Set() };
    });

    // Process sales data
    salesData.data.results.forEach((sale: any) => {
      if (sale.booking_date) {
        const date = new Date(sale.booking_date);
        const monthIndex = date.getMonth();
        const monthName = months[monthIndex];

        if (monthlyData[monthName]) {
          monthlyData[monthName].sales += sale.selling_price || 0;
          if (sale.customer_no) {
            monthlyData[monthName].clients.add(sale.customer_no);
          }
        }
      }
    });

    return months.map((month) => ({
      month,
      sales: Math.round(monthlyData[month].sales / 1000), // Convert to thousands
      clients: monthlyData[month].clients.size,
    }));
  }, [salesData]);

  // Compute real performance metrics
  const computedPieData = useMemo(() => {
    if (!salesData?.data?.results) {
      return [
        { name: "Completed Sales", value: 0, color: "#16a34a" },
        { name: "Ongoing Sales", value: 0, color: "#84cc16" },
        { name: "Dropped Sales", value: 0, color: "#8b5a2b" },
      ];
    }

    const sales = salesData.data.results;
    const totalSales = sales.length;

    if (totalSales === 0) {
      return [{ name: "No Sales Data", value: 100, color: "#9ca3af" }];
    }

    const completedSales = sales.filter(
      (sale: any) =>
        sale.status?.toLowerCase() === "completed" || sale.balance === 0
    ).length;

    const droppedSales = sales.filter(
      (sale: any) =>
        sale.status?.toLowerCase() === "dropped" ||
        sale.lead_file_dropped === "YES"
    ).length;

    const ongoingSales = totalSales - completedSales - droppedSales;

    const completedPercentage = Math.round((completedSales / totalSales) * 100);
    const ongoingPercentage = Math.round((ongoingSales / totalSales) * 100);
    const droppedPercentage = 100 - completedPercentage - ongoingPercentage;

    return [
      { name: "Completed Sales", value: completedPercentage, color: "#16a34a" },
      { name: "Ongoing Sales", value: ongoingPercentage, color: "#84cc16" },
      { name: "Dropped Sales", value: droppedPercentage, color: "#8b5a2b" },
    ].filter((item) => item.value > 0);
  }, [salesData]);

  // Role-specific KPI calculations
  const roleSpecificKPIs = useMemo(() => {
    const baseKPIs = [
      {
        label: "Reminders",
        value: data?.reminders?.data ? data?.reminders?.data?.length : 0,
        color: "bg-blue-100 dark:bg-blue-900",
        icon: <img src={Icon1} alt="Icon" className="w-6 h-6" />,
      },
      {
        label: "Notifications",
        value: data?.notifications?.data ? data?.notifications?.data.length : 0,
        color: "bg-cyan-100 dark:bg-cyan-900",
        icon: <img src={Icon2} alt="Icon" className="w-6 h-6" />,
      },
      {
        label: "Notes",
        value: data?.notes?.data ? data?.notes?.data?.length : 0,
        color: "bg-green-100 dark:bg-green-900",
        icon: <img src={Icon3} alt="Icon" className="w-6 h-6" />,
      },
      {
        label: "Tickets",
        value: data?.tickets?.data ? data?.tickets?.data?.length : 0,
        color: "bg-pink-100 dark:bg-pink-900",
        icon: <img src={Icon4} alt="Icon" className="w-6 h-6" />,
      },
    ];

    // Add role-specific KPIs
    if (roleInfo.isManager || roleInfo.isGroupLead) {
      baseKPIs.push(
        {
          label: "Total Sales",
          value: dashboardStats?.total_sales
            ? formatNumberWithCommas(dashboardStats.total_sales)
            : "0",
          color: "bg-green-100 dark:bg-green-900",
          icon: <DollarSign className="w-6 h-6 text-green-600" />,
        },
        {
          label: "Total Customers",
          value: dashboardStats?.customers_count || 0,
          color: "bg-purple-100 dark:bg-purple-900",
          icon: <Users className="w-6 h-6 text-purple-600" />,
        }
      );
    }

    if (roleInfo.isTeamLead || roleInfo.isManager) {
      const teamSales = salesData?.data?.results?.length || 0;
      baseKPIs.push({
        label: "Team Sales",
        value: teamSales,
        color: "bg-orange-100 dark:bg-orange-900",
        icon: <Target className="w-6 h-6 text-orange-600" />,
      });
    }

    if (roleInfo.isMarketer) {
      const mySales =
        salesData?.data?.results?.filter(
          (sale: any) => sale.marketer_employee_no === employee_no
        ).length || 0;

      const myPerformance = performanceData?.results?.[0];

      baseKPIs.push({
        label: "My Sales",
        value: mySales,
        color: "bg-indigo-100 dark:bg-indigo-900",
        icon: <Award className="w-6 h-6 text-indigo-600" />,
      });

      if (myPerformance) {
        baseKPIs.push({
          label: "Performance",
          value: `${myPerformance.MIB_Perfomance || 0}%`,
          color: "bg-yellow-100 dark:bg-yellow-900",
          icon: <TrendingUp className="w-6 h-6 text-yellow-600" />,
        });
      }
    }

    return baseKPIs;
  }, [data, dashboardStats, salesData, performanceData, roleInfo, employee_no]);

  const notificationSections =
    !data?.reminders?.data || data?.reminders?.data?.length === 0
      ? [
          {
            title: "Reminders",
            count: "0",
            viewAllText: "View all",
            color: "bg-emerald-50 border-l-emerald-500",
            subtitle: "You currently do not have any reminders.",
            icon: Clock,
            iconColor: "text-emerald-600",
            titleColor: "text-emerald-900 dark:text-emerald-600",
            description: "",
            timestamp: "",
          },
          {
            title: "Notifications",
            count: "0",
            viewAllText: "View all",
            color: "bg-amber-50 border-l-amber-500",
            subtitle: "You currently do not have any notifications.",
            icon: AlertCircle,
            iconColor: "text-amber-600",
            titleColor: "text-amber-900 dark:text-amber-600",
            description: "",
            timestamp: "",
          },
          {
            title: "Notes",
            count: "0",
            viewAllText: "View all",
            color: "bg-green-50 border-l-green-500",
            subtitle: "You currently do not have any notes.",
            icon: CheckCircle,
            iconColor: "text-green-600",
            titleColor: "text-green-900 dark:text-green-600",
            description: "",
            timestamp: "",
          },
          {
            title: "Tickets",
            count: "0",
            viewAllText: "View all",
            color: "bg-red-50 border-l-red-500",
            subtitle: "You currently do not have any tickets.",
            icon: Calendar,
            iconColor: "text-red-600",
            titleColor: "text-red-900 dark:text-red-600",
            description: "",
            timestamp: "",
          },
        ]
      : [
          {
            title: "Reminders",
            count: data.reminders.data.length,
            viewAllText: "View all",
            color: "bg-emerald-50 border-l-emerald-500",
            subtitle: data.reminders.data[0]?.text || "",
            icon: Clock,
            iconColor: "text-emerald-600",
            titleColor: "text-emerald-900 dark:text-emerald-600",
            description: `You have ${data.reminders.data.length} reminders pending`,
            timestamp: data.reminders.data[0]?.date || "",
          },
          {
            title: "To-Do List",
            count: 8,
            viewAllText: "View all",
            color: "bg-amber-50 border-l-amber-500",
            subtitle: data.notifications.data[0]?.text || "",
            icon: AlertCircle,
            iconColor: "text-amber-600",
            titleColor: "text-amber-900 dark:text-amber-600",
            description: "You have 8 tasks pending",
            timestamp: "Today | 5:00 PM",
          },
          {
            title: "Notifications",
            count: 10,
            viewAllText: "View all",
            color: "bg-green-50 border-l-green-500",
            subtitle: data.notes.data[0]?.text || "",
            icon: CheckCircle,
            iconColor: "text-green-600",
            titleColor: "text-green-900 dark:text-green-600",
            description: "You have 10 notifications",
            timestamp: "Today | 5:00 PM",
          },
          {
            title: "Tickets",
            count: 2,
            viewAllText: "View all",
            color: "bg-red-50 border-l-red-500",
            subtitle: data.tickets.data[0]?.text || "",
            icon: Calendar,
            iconColor: "text-red-600",
            titleColor: "text-red-900 dark:text-red-600",
            description: "You have 2 tickets",
            timestamp: "Today | 5:00 PM",
          },
        ];

  // Show loader when initially loading or when tab is changing
  const showLoader = isLoading || isFetching;

  return (
    <Screen>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 relative transition-colors duration-200">
        {showLoader && (
          <div className="absolute inset-0 z-10 flex pt-16 justify-center bg-white bg-opacity-70 dark:bg-gray-900 dark:bg-opacity-70">
            <SpinnerTemp type="spinner-double" size="md" />
          </div>
        )}

        <div className="p-6">
          <div className="flex flex-col lg:flex-row justify-between">
            {/* Welcome Section */}
            <div className="mb-6">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-1">
                {getRoleGreeting(
                  roleInfo,
                  employee_name?.split(" ")[0] || "User"
                )}{" "}
                👋
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Welcome back to Optiven CRM -{" "}
                {roleInfo.roleLevel.replace("_", " ").toUpperCase()} Dashboard
              </p>
              {roleInfo.isManager && (
                <p className="text-sm text-green-600 dark:text-green-400 mt-1">
                  Managing {user_details?.team || "Multiple Teams"} •{" "}
                  {user_details?.department || "Department"}
                </p>
              )}
              {roleInfo.isTeamLead && (
                <p className="text-sm text-blue-600 dark:text-blue-400 mt-1">
                  Leading {user_details?.team || "Team"} •{" "}
                  {user_details?.office || "Office"}
                </p>
              )}
            </div>

            {/* Role-Specific Action Buttons */}
            <div className="flex flex-wrap items-center gap-4 mb-8">
              {/* Common Actions */}
              <button className="bg-green-600 hover:bg-green-700 dark:bg-green-500 dark:hover:bg-green-600 text-white px-4 h-10 rounded-lg flex items-center space-x-2 transition-colors">
                <SquareLibrary size={18} />
                <span>News Feed</span>
              </button>
              <button className="bg-green-600 hover:bg-green-700 dark:bg-green-500 dark:hover:bg-green-600 text-white px-4 h-10 rounded-lg flex items-center space-x-2 transition-colors">
                <BookOpen size={18} />
                <span>Knowledge Center</span>
              </button>

              {/* Marketer Actions */}
              {roleInfo.isMarketer && (
                <>
                  <button
                    onClick={() => setIsAddModalOpen(true)}
                    className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white px-4 h-10 rounded-lg flex items-center space-x-2 transition-colors"
                  >
                    <UserPlus size={18} />
                    <span>Add Prospect</span>
                  </button>
                  <Link
                    to="/book-visit"
                    className="bg-purple-600 hover:bg-purple-700 dark:bg-purple-500 dark:hover:bg-purple-600 text-white px-4 h-10 rounded-lg flex items-center space-x-2 transition-colors"
                  >
                    <MapPin size={18} />
                    <span>Book Site Visit</span>
                  </Link>
                </>
              )}

              {/* Team Lead Actions */}
              {roleInfo.isTeamLead && (
                <>
                  <Link
                    to="/team-performance"
                    className="bg-orange-600 hover:bg-orange-700 dark:bg-orange-500 dark:hover:bg-orange-600 text-white px-4 h-10 rounded-lg flex items-center space-x-2 transition-colors"
                  >
                    <BarChart3 size={18} />
                    <span>Team Reports</span>
                  </Link>
                  <Link
                    to="/prospects"
                    className="bg-indigo-600 hover:bg-indigo-700 dark:bg-indigo-500 dark:hover:bg-indigo-600 text-white px-4 h-10 rounded-lg flex items-center space-x-2 transition-colors"
                  >
                    <Target size={18} />
                    <span>Assign Leads</span>
                  </Link>
                </>
              )}

              {/* Manager Actions */}
              {roleInfo.isManager && (
                <>
                  <Link
                    to="/performance"
                    className="bg-red-600 hover:bg-red-700 dark:bg-red-500 dark:hover:bg-red-600 text-white px-4 h-10 rounded-lg flex items-center space-x-2 transition-colors"
                  >
                    <Activity size={18} />
                    <span>Performance Dashboard</span>
                  </Link>
                  <Link
                    to="/reports"
                    className="bg-gray-600 hover:bg-gray-700 dark:bg-gray-500 dark:hover:bg-gray-600 text-white px-4 h-10 rounded-lg flex items-center space-x-2 transition-colors"
                  >
                    <FileText size={18} />
                    <span>Generate Reports</span>
                  </Link>
                </>
              )}
            </div>
          </div>

          {/* Stats Cards */}
          <NotificationList sections={notificationSections} className="mb-4" />

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border-0 shadow-sm transition-colors">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-800 dark:text-gray-200 mb-2">
                    {roleInfo.isMarketer
                      ? "My Sales Performance"
                      : roleInfo.isTeamLead
                      ? "Team Sales Trend"
                      : "Sales Vs Clients Trend"}
                  </h3>
                  <p className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                    {computedSalesData.reduce(
                      (acc, curr) => acc + curr.sales,
                      0
                    )}
                    K Sales vs{" "}
                    {computedSalesData.reduce(
                      (acc, curr) => acc + curr.clients,
                      0
                    )}{" "}
                    Clients
                  </p>
                  <div className="flex items-center space-x-4 mb-1">
                    <div className="flex items-center space-x-1">
                      <span className="flex flex-row items-center text-green-600 dark:text-green-400 text-sm">
                        <ArrowUpLeft size={18} />
                        {salesData?.data?.results
                          ? calculatePercentageChange(
                              computedSalesData
                                .slice(-3)
                                .reduce((acc, curr) => acc + curr.sales, 0),
                              computedSalesData
                                .slice(-6, -3)
                                .reduce((acc, curr) => acc + curr.sales, 0)
                            ).toFixed(1)
                          : "0"}
                        %
                      </span>
                      <span className="text-gray-600 dark:text-gray-400 text-sm">
                        Sales
                      </span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <span className="flex flex-row items-center text-green-600 dark:text-green-400 text-sm">
                        <ArrowUpRight size={18} />
                        {salesData?.data?.results
                          ? calculatePercentageChange(
                              computedSalesData
                                .slice(-3)
                                .reduce((acc, curr) => acc + curr.clients, 0),
                              computedSalesData
                                .slice(-6, -3)
                                .reduce((acc, curr) => acc + curr.clients, 0)
                            ).toFixed(1)
                          : "0"}
                        %
                      </span>
                      <span className="text-gray-600 dark:text-gray-400 text-sm">
                        Clients
                      </span>
                    </div>
                  </div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {roleInfo.isMarketer
                      ? "Your performance this year"
                      : roleInfo.isTeamLead
                      ? "Team performance this year"
                      : "Sales data for current year"}
                  </p>
                </div>
                <button className="place-self-start text-green-600 dark:text-green-400 border border-green-600 dark:border-green-400 px-4 py-2 rounded-lg text-sm hover:bg-green-50 dark:hover:bg-green-900/20 transition-colors">
                  View Report
                </button>
              </div>
              <div className="h-80 mb-4">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={computedSalesData} barCategoryGap="20%">
                    <XAxis
                      dataKey="month"
                      axisLine={false}
                      tickLine={false}
                      tick={{ fontSize: 12, fill: "#9CA3AF" }}
                    />
                    <YAxis hide />
                    <Tooltip
                      formatter={(value, name) => [
                        name === "sales" ? `${value}K KES` : `${value} clients`,
                        name === "sales" ? "Sales" : "Clients",
                      ]}
                    />
                    <Bar dataKey="sales" fill="#16a34a" radius={[4, 4, 0, 0]} />
                    <Bar
                      dataKey="clients"
                      fill="#7f1d1d"
                      radius={[4, 4, 0, 0]}
                    />
                  </BarChart>
                </ResponsiveContainer>
              </div>
              <div className="flex items-center space-x-6">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-600 rounded-full"></div>
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    Sales
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-red-900 rounded-full"></div>
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    Clients
                  </span>
                </div>
              </div>
            </div>

            {/* Pie Chart */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border-0 shadow-sm transition-colors">
              <div className="flex items-center justify-between mb-6 lg:hidden">
                <h3 className="text-lg font-medium text-gray-800 dark:text-gray-200">
                  Sales Demographics
                </h3>
                <Select>
                  <SelectTrigger className="w-[100px] lg:w-[180px] focus:!ring-0 text-green-600 dark:text-green-400 border border-green-600 dark:border-green-400 bg-transparent hover:bg-green-50 dark:hover:bg-green-900/20">
                    <SelectValue placeholder="Period" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="monthly">Monthly</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-1 lg:grid-cols-3 lg:h-full">
                <div className="h-96 mb-6 col-span-2 self-center">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={computedPieData}
                        cx="50%"
                        cy="50%"
                        innerRadius={40}
                        outerRadius={150}
                        paddingAngle={2}
                        dataKey="value"
                        startAngle={90}
                        endAngle={450}
                      >
                        {computedPieData?.map((entry: any, index: number) => (
                          <Cell key={`cell-${index}`} fill={entry?.color} />
                        ))}
                      </Pie>
                      <Tooltip
                        formatter={(value) => [`${value}%`, "Percentage"]}
                      />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
                <div className="flex flex-col justify-around">
                  <div className="lg:flex hidden flex-col gap-4 justify-between mb-6">
                    <h3 className="text-2xl font-medium text-gray-800 dark:text-gray-200">
                      {roleInfo.isMarketer
                        ? "My Sales Status"
                        : roleInfo.isTeamLead
                        ? "Team Sales Status"
                        : "Sales Demographics"}
                    </h3>
                    <Select>
                      <SelectTrigger className="w-[110px] !h-8 focus:!ring-0 text-green-600 dark:text-green-400 border border-green-600 dark:border-green-400 bg-transparent hover:bg-green-50 dark:hover:bg-green-900/20">
                        <SelectValue placeholder="Period" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="monthly">Monthly</SelectItem>
                        <SelectItem value="quarterly">Quarterly</SelectItem>
                        <SelectItem value="yearly">Yearly</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-3">
                    {computedPieData?.map((dataItem: any, index: number) => (
                      <div
                        key={index}
                        className="flex items-center justify-between"
                      >
                        <div className="flex items-center space-x-3">
                          <span
                            className="text-sm text-gray-50 font-medium px-2.5 py-1.5 rounded-md"
                            style={{ backgroundColor: dataItem?.color }}
                          >
                            {dataItem?.value}%
                          </span>
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            {dataItem?.name}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                  <button className="w-full text-green-600 dark:text-green-400 text-sm mt-6 hover:underline border border-green-600 dark:border-green-400 py-2 rounded-lg hover:bg-green-50 dark:hover:bg-green-900/20 transition-colors">
                    View All
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Team Performance Section - Only for Managers and Team Leads */}
          {(roleInfo.isManager ||
            roleInfo.isTeamLead ||
            roleInfo.isGroupLead) &&
            teamPerformance && (
              <div className="mb-8">
                <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 border-0 shadow-sm transition-colors">
                  <div className="flex items-center justify-between mb-6">
                    <div>
                      <h3 className="text-lg font-medium text-gray-800 dark:text-gray-200 mb-2">
                        {roleInfo.isManager
                          ? "Overall Team Performance"
                          : roleInfo.isGroupLead
                          ? "Group Performance"
                          : "Team Performance Overview"}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Performance metrics and targets for your{" "}
                        {roleInfo.isManager ? "organization" : "team"}
                      </p>
                    </div>
                    <Link
                      to="/team-performance"
                      className="text-green-600 dark:text-green-400 border border-green-600 dark:border-green-400 px-4 py-2 rounded-lg text-sm hover:bg-green-50 dark:hover:bg-green-900/20 transition-colors"
                    >
                      View Details
                    </Link>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                    {teamPerformance.results
                      ?.slice(0, 4)
                      .map((team: any, index: number) => (
                        <div
                          key={index}
                          className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-lg p-4"
                        >
                          <div className="flex items-center justify-between mb-3">
                            <h4 className="font-semibold text-blue-900 dark:text-blue-100 text-sm">
                              {team.team || `Team ${index + 1}`}
                            </h4>
                            <div
                              className={`px-2 py-1 rounded-full text-xs font-medium ${
                                team.progress >= 80
                                  ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
                                  : team.progress >= 60
                                  ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300"
                                  : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"
                              }`}
                            >
                              {team.progress || 0}%
                            </div>
                          </div>

                          <div className="space-y-2">
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-600 dark:text-gray-400">
                                Target:
                              </span>
                              <span className="font-medium text-gray-900 dark:text-gray-100">
                                {formatCurrency(team.monthly_target || 0)}
                              </span>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-600 dark:text-gray-400">
                                Achieved:
                              </span>
                              <span className="font-medium text-gray-900 dark:text-gray-100">
                                {formatCurrency(team.MIB_achieved || 0)}
                              </span>
                            </div>
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-600 dark:text-gray-400">
                                Marketers:
                              </span>
                              <span className="font-medium text-gray-900 dark:text-gray-100">
                                {team.total_marketers || 0}
                              </span>
                            </div>
                          </div>

                          <div className="mt-3">
                            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                              <div
                                className={`h-2 rounded-full transition-all duration-300 ${
                                  team.progress >= 80
                                    ? "bg-green-500"
                                    : team.progress >= 60
                                    ? "bg-yellow-500"
                                    : "bg-red-500"
                                }`}
                                style={{
                                  width: `${Math.min(
                                    team.progress || 0,
                                    100
                                  )}%`,
                                }}
                              ></div>
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>

                  {teamPerformance.results?.length > 4 && (
                    <div className="text-center">
                      <Link
                        to="/team-performance"
                        className="inline-flex items-center space-x-2 text-green-600 dark:text-green-400 hover:underline"
                      >
                        <span>
                          View All {teamPerformance.results.length} Teams
                        </span>
                        <ArrowUpRight size={16} />
                      </Link>
                    </div>
                  )}
                </div>
              </div>
            )}
        </div>

        {/* Bottom Row */}
        <div className="px-6 pb-6">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Reminders */}
            <div className="flex flex-col gap-5 justify-between bg-white dark:bg-gray-800 rounded-lg px-6 py-10 shadow-sm transition-colors">
              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">
                  Reminders
                </h3>
                <div className="space-y-3">
                  {!data?.reminders?.data ||
                  data?.reminders?.data?.length === 0 ? (
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      You currently do not have any reminders.
                    </p>
                  ) : (
                    data?.reminders?.data?.map(
                      (reminder: any, index: number) => (
                        <div
                          key={index}
                          className="flex justify-between items-start"
                        >
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            {reminder?.text}
                          </span>
                          <span className="text-xs text-gray-500 dark:text-gray-500">
                            {reminder?.date}
                          </span>
                        </div>
                      )
                    )
                  )}
                </div>
              </div>
              <div>
                {data?.reminders?.data && data?.reminders?.data?.length > 0 ? (
                  <button className="w-full text-green-600 dark:text-green-400 text-sm mt-4 hover:underline border border-green-600 dark:border-green-400 py-2 rounded-lg hover:bg-green-50 dark:hover:bg-green-900/20 transition-colors">
                    View All
                  </button>
                ) : null}
                <button className="w-full bg-green-600 hover:bg-green-700 dark:bg-green-500 dark:hover:bg-green-600 text-white py-2 px-4 rounded-lg mt-4 transition-colors">
                  Set Reminder
                </button>
              </div>
            </div>

            {/* Tickets */}
            <div className="flex flex-col gap-5 justify-between bg-white dark:bg-gray-800 rounded-lg px-6 py-10 shadow-sm transition-colors">
              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">
                  Tickets
                </h3>
                <div className="space-y-3">
                  {!data?.tickets?.data || data?.tickets?.data?.length === 0 ? (
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      You currently do not have any notifications.
                    </p>
                  ) : (
                    data?.tickets?.data?.map((letter: any, index: number) => (
                      <div
                        key={index}
                        className="flex justify-between items-center"
                      >
                        <div className="flex items-center space-x-3">
                          <span className="text-sm text-gray-800 dark:text-gray-200">
                            {letter?.name}
                          </span>
                          <span className="text-xs text-gray-500 dark:text-gray-500">
                            {letter?.date}
                          </span>
                        </div>
                        <span
                          className={`text-xs px-2 py-1 rounded ${
                            letter?.status === "Accepted"
                              ? "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300"
                              : "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300"
                          }`}
                        >
                          {letter?.status}
                        </span>
                      </div>
                    ))
                  )}
                </div>
              </div>
              <div>
                {data?.tickets?.data && data?.tickets?.data.length > 0 ? (
                  <button className="w-full text-green-600 dark:text-green-400 text-sm mt-4 hover:underline border border-green-600 dark:border-green-400 py-2 rounded-lg hover:bg-green-50 dark:hover:bg-green-900/20 transition-colors">
                    View All
                  </button>
                ) : null}
                <button className="w-full bg-green-600 hover:bg-green-700 dark:bg-green-500 dark:hover:bg-green-600 text-white py-2 px-4 rounded-lg mt-4 transition-colors">
                  Create a Ticket
                </button>
              </div>
            </div>

            {/* Recent Activity Feed */}
            <div className="bg-white dark:bg-gray-800 rounded-lg px-6 py-10 shadow-sm transition-colors">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">
                Recent Activity
              </h3>
              <div className="space-y-4">
                {/* Recent Sales */}
                {salesData?.data?.results
                  ?.slice(0, 3)
                  .map((sale: any, index: number) => (
                    <div
                      key={`sale-${index}`}
                      className="flex items-start space-x-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg"
                    >
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                          <DollarSign className="w-4 h-4 text-white" />
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          New Sale: {sale.customer_name || "Customer"}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {formatCurrency(sale.selling_price || 0)} •{" "}
                          {sale.plot_number || "Plot"} •
                          {sale.booking_date
                            ? new Date(sale.booking_date).toLocaleDateString()
                            : "Recent"}
                        </p>
                      </div>
                    </div>
                  ))}

                {/* Recent Reminders */}
                {data?.reminders?.data
                  ?.slice(0, 2)
                  .map((reminder: any, index: number) => (
                    <div
                      key={`reminder-${index}`}
                      className="flex items-start space-x-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg"
                    >
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                          <Clock className="w-4 h-4 text-white" />
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {reminder.text || "Reminder"}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          Due: {reminder.date || "Soon"}
                        </p>
                      </div>
                    </div>
                  ))}

                {/* Recent Tickets */}
                {data?.tickets?.data
                  ?.slice(0, 1)
                  .map((ticket: any, index: number) => (
                    <div
                      key={`ticket-${index}`}
                      className="flex items-start space-x-3 p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg"
                    >
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                          <AlertCircle className="w-4 h-4 text-white" />
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          Ticket: {ticket.name || "Support Request"}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          Status: {ticket.status || "Open"} •{" "}
                          {ticket.date || "Recent"}
                        </p>
                      </div>
                    </div>
                  ))}

                {/* No Activity Message */}
                {!salesData?.data?.results?.length &&
                  !data?.reminders?.data?.length &&
                  !data?.tickets?.data?.length && (
                    <div className="text-center py-8">
                      <Activity className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        No recent activity to display
                      </p>
                    </div>
                  )}
              </div>

              <div className="mt-6">
                <Link
                  to="/activity"
                  className="w-full text-green-600 dark:text-green-400 text-sm hover:underline border border-green-600 dark:border-green-400 py-2 rounded-lg hover:bg-green-50 dark:hover:bg-green-900/20 transition-colors flex items-center justify-center"
                >
                  View All Activity
                </Link>
              </div>
            </div>

            {/* Overall Summary */}
            <div className="bg-white dark:bg-gray-800 rounded-lg px-6 py-10 shadow-sm transition-colors">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">
                Overall Summary
              </h3>
              <div className="space-y-3">
                {roleSpecificKPIs?.map((item: any, index: number) => (
                  <div
                    key={index}
                    className={`flex items-center justify-between px-4 p-4 rounded-lg ${item?.color} transition-colors`}
                  >
                    <div className="flex items-center space-x-3">
                      {item?.icon}
                      <span className="text-sm text-gray-600 dark:text-gray-200">
                        {item?.label}
                      </span>
                    </div>
                    <span className="text-sm font-medium text-gray-800 dark:text-gray-200 shadow-md px-2 py-1 rounded bg-white dark:bg-transparent dark:border dark:border-foreground/30">
                      {item?.value}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="h-10"> </div>

      {/* Floating Support */}
      {/* <div className="fixed bottom-4 right-4">
                    <div className="bg-green-600 text-white px-3 py-2 rounded-lg text-sm flex items-center space-x-2">
                        <span>Good Morning Setforus 👋</span>
                        <span>| How can I help?</span>
                    </div>
                </div> */}

      {isAddModalOpen && (
        <AddProspects
          isOpen={isAddModalOpen}
          onClose={() => setIsAddModalOpen(false)}
        />
      )}
    </Screen>
  );
};
export default Home;
