import { Screen } from '@/app-components/layout/screen'
import SpinnerTemp from '@/components/custom/spinners/SpinnerTemp'
import CustomerSidebar from '@/components/customer-section/CustomerSidebar'
import PlotView from '@/components/sales/PlotView'
import SalesDetails from '@/components/sales/SalesDetails'
import { useGetSaleDetailsQuery } from '@/redux/slices/sales'
import { useSalesStore } from '@/zustand/useSaleStore'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import React, { useState } from 'react'
import { useParams } from 'react-router-dom'
import { useAuthHook } from '@/utils/useAuthHook'

const SalesCard = () => {
    const { lead_file_no } = useParams<{ lead_file_no: string }>()

    const { data: SalesData, isLoading, isFetching } = useGetSaleDetailsQuery({ saleId: lead_file_no, params: {} })
   
    const setSalesData = useSalesStore((state) => state.setSalesData);
    React.useEffect(() => {
        if (SalesData) {
            setSalesData(SalesData);
        }
    }, [SalesData, setSalesData]);

    const [isSidebarOpen, setIsSidebarOpen] = useState(false);

    // Get current user from auth
    const { user_details } = useAuthHook();
    const currentUser = user_details ? {
        id: user_details.employee_no || "",
        name: user_details.fullnames || ""
    } : undefined;

    const toggleSidebar = () => {
        setIsSidebarOpen(!isSidebarOpen);
    };

    // Show loader when initially loading or when tab is changing
    const showLoader = isLoading || isFetching;

    return (
        <Screen>
            <div className='relative h-auto lg:max-h-[calc(100vh-100px)] overflow-hidden'>
                {showLoader && (
                    <div className="absolute inset-0 z-10 flex pt-16 justify-center bg-white bg-opacity-70">
                        <SpinnerTemp type="spinner-double" size="md" />
                    </div>
                )}
                <div className="">
                    {/* Main grid layout - restructured to stack vertically on mobile */}
                    {/* <div className="grid grid-cols-1 lg:grid-cols-4"> */}
                    <div className="grid grid-cols-1 lg:grid-cols-4 ">
                        {/* On mobile: full width, stacked vertically */}
                        {/* On md+: maintains original grid position */}
                        <div className="border-b lg:border-b-0 lg:border-r w-full lg:max-h-[calc(100vh-100px)] md:col-span-1 ">
                            <SalesDetails {...SalesData} />
                        </div>

                        {/* On mobile: full width, stacked below SalesDetails */}
                        {/* On md+: maintains original grid position */}
                        <div className="border-b lg:border-b-0 lg:border-r w-full lg:col-start-2 lg:col-end-4 lg:max-h-[calc(100vh-100px)]">
                            <PlotView />
                        </div>

                        {/* CustomerSidebar - only visible on desktop by default */}
                        <div className="hidden lg:block lg:col-start-4 lg:col-end-5 max-h-screen overflow-y-auto">
                            <CustomerSidebar
                                leadfileNo={lead_file_no}
                                entityType="leadfile"
                                entityId={lead_file_no}
                                currentUser={currentUser}
                                salesCardCustomerId={SalesData?.customer_id} // Pass customer ID if needed
                            />
                        </div>
                    </div>

                    {/* Mobile toggle button - only visible on mobile */}
                    <button
                        onClick={toggleSidebar}
                        className="lg:hidden fixed top-1/2 right-0 transform -translate-y-1/2 bg-primary text-primary-foreground p-2 rounded-l-md shadow-md z-10"
                        aria-label="Toggle customer sidebar"
                    >
                        <ChevronLeft className={`w-5 h-5 transition-transform ${isSidebarOpen ? 'rotate-180' : ''}`} />
                    </button>

                    {/* Mobile sidebar overlay */}
                    <div
                        className={`lg:hidden fixed inset-0 bg-black/50 z-20 transition-opacity duration-300 ${isSidebarOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
                            }`}
                        onClick={toggleSidebar}
                    />

                    {/* Mobile sliding sidebar */}
                    <div
                        className={`lg:hidden fixed top-0 right-0 h-full w-3/4  bg-background z-30 shadow-xl transition-transform duration-300 transform ${isSidebarOpen ? 'translate-x-0' : 'translate-x-full'
                            }`}
                    >
                        <div className="p-4 h-full overflow-auto">
                            <button
                                onClick={toggleSidebar}
                                className="absolute top-4 left-4 p-1 rounded-full bg-muted hover:bg-muted/80"
                            >
                                <ChevronRight className="w-5 h-5" />
                            </button>
                            <div className="pt-12">
                                <CustomerSidebar
                                    leadfileNo={lead_file_no}
                                    entityType="leadfile"
                                    entityId={lead_file_no}
                                    currentUser={currentUser}
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </Screen>

    )
}

export default SalesCard

export interface SalesDetailsProps {
    lead_file_no: string,
    lead_file_status_dropped: string,
    plot_no: string,
    purchase_price: string,
    selling_price: string,
    balance_lcy: string,
    customer_name: string,
    purchase_type: string,
    commission_threshold: string,
    deposit_threshold: string,
    discount: string,
    completion_date: string,
    no_of_installments: string,
    installment_amount: string,
    sale_agreement_sent: string,
    sale_agreement_signed: string,
    total_paid: string,
    transfer_cost_charged: string,
    transfer_cost_paid: string,
    overpayments: string,
    refunds: string,
    refundable_amount: string,
    penalties_accrued: string,
    booking_id: string,
    booking_date: string,
    additional_deposit_date: string,
    title_status: string,
    credit_officer_id: string,
    plot: string,
    project: string,
    marketer: string,
    customer_id: string,
    customer_lead_source: string,
    cat_lead_source: string,
}