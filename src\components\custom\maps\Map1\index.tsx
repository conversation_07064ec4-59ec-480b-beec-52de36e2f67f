import { useState } from "react";
import { Tooltip } from "react-tooltip";
import "react-tooltip/dist/react-tooltip.css";
import ReactSimpleMap from "../ReactSimpleMap";
import worldMap from "../worldMap.json";

type Props = {
  data: { [key: string]: string };
  title: string;
  description: string;
};

const index = ({ data, title, description }: Props) => {
  const [content, setContent] = useState("");
  return (
    <div className="shadow py-3 px-1">
      <p className="font-bold px-3 text-accent">{title}</p>
      <p className="px-3 text-xs italic">{description}</p>
      <ReactSimpleMap
        map={worldMap}
        setTooltipContent={setContent}
        data={data}
        data-tooltip-id="map1-id"
        data-tooltip-content={content}
      />

      <Tooltip
        id="map1-id"
        variant="success"
        className="text-primary"
        place="top"
      />
    </div>
  );
};

export default index;
