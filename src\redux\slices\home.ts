import { noAuthHeader } from "@/utils/header";
import { apiSlice } from "../apiSlice";

export const homeApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getHomeStats: builder.query({
      query: () => ({
        url: "/main-dashboard",
        method: "GET",
        headers: noAuthHeader(),
      }),

      providesTags: ["Home"],
    }),
  }),
});

export const { useGetHomeStatsQuery } = homeApiSlice;
