import React from 'react';
import { ChevronRight } from 'lucide-react';

interface NotificationSection {
    title: string;
    count: number;
    viewAllText?: string;
    color?: string; // Optional custom color for the section
    icon?: React.ComponentType<any>; // Optional custom icon component
    iconColor?: string; // Optional custom icon color
    titleColor?: string; // Optional custom title color
    timestamp?: string; // Optional timestamp for the section
    subtitle?: string
}

interface NotificationListProps {
    sections: NotificationSection[];
    className?: string;
    onViewAll?: (sectionTitle: string) => void;
}

export const NotificationList: React.FC<NotificationListProps> = ({
    sections,
    className = '',
    onViewAll
}) => {
    return (
        <div className={`grid grid-cols-1 md:grid-cols-4 gap-3 ${className}`}>
            {sections.map((section, sectionIndex) => (
                <div key={sectionIndex} className="bg-white dark:bg-gray-800 rounded-xl pt-6 pb-3 px-4 shadow-sm hover:shadow-md dark:hover:shadow-lg transition-all duration-200">
                    {/* Section Header */}
                    <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-2">
                            <h2 className="text-lg font-bold text-gray-900 dark:text-gray-100">
                                {section.title}
                            </h2>
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-semibold bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300">
                                {section.count}
                            </span>
                        </div>

                        <button
                            onClick={() => onViewAll?.(section.title)}
                            className="flex items-center space-x-1 text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors"
                        >
                            <span>{section.viewAllText || 'View all'}</span>
                            <ChevronRight className="w-4 h-4" />
                        </button>
                    </div>

                    <div className={`bg-white dark:bg-gray-800 h-24 border-l-4 transition-all duration-200 py-4 px-2 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700
                        ${section.color} ${className}
                    `}>
                        <div className="flex items-start space-x-3">
                            <div className={`flex-shrink-0 ${section.iconColor}`}>
                                {section.icon ? (
                                    <section.icon className="w-5 h-5 mt-0.5" />
                                ) : (
                                    <></>
                                )}
                            </div>

                            <div className="flex-1 min-w-0">
                                <h3 className={`text-sm font-semibold leading-tight ${
                                    section.titleColor || 'text-gray-900 dark:text-gray-100'
                                }`}>
                                    {section.subtitle}
                                </h3>

                                <div className="flex items-center justify-between mt-2">
                                    <span className="text-xs text-gray-500 dark:text-gray-400 font-medium">
                                        {section.timestamp}
                                    </span>

                                    {section.count && (
                                        <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium
                                            ${section.iconColor || 'text-gray-600 dark:text-gray-300'} bg-opacity-20 dark:bg-opacity-30
                                        `}>
                                            {section.count}
                                        </span>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            ))}
        </div>
    );
};

// Example usage component to demonstrate the dark mode functionality
export default function NotificationListDemo() {
    const [darkMode, setDarkMode] = React.useState(false);

    const sampleSections = [
        {
            title: "Pending Tasks",
            count: 12,
            subtitle: "Review client documents",
            timestamp: "2 hours ago",
            color: "border-blue-500",
            iconColor: "text-blue-500 dark:text-blue-400",
            icon: ({ className }: { className: string }) => (
                <svg className={className} fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                </svg>
            )
        },
        {
            title: "Messages",
            count: 5,
            subtitle: "New client inquiry",
            timestamp: "1 hour ago",
            color: "border-green-500",
            iconColor: "text-green-500 dark:text-green-400",
            icon: ({ className }: { className: string }) => (
                <svg className={className} fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                </svg>
            )
        },
        {
            title: "Alerts",
            count: 3,
            subtitle: "System maintenance",
            timestamp: "30 minutes ago",
            color: "border-orange-500",
            iconColor: "text-orange-500 dark:text-orange-400",
            icon: ({ className }: { className: string }) => (
                <svg className={className} fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
            )
        },
        {
            title: "Reports",
            count: 8,
            subtitle: "Monthly analytics ready",
            timestamp: "4 hours ago",
            color: "border-purple-500",
            iconColor: "text-purple-500 dark:text-purple-400",
            icon: ({ className }: { className: string }) => (
                <svg className={className} fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
                </svg>
            )
        }
    ];

    const handleViewAll = (sectionTitle: string) => {
        console.log(`View all clicked for: ${sectionTitle}`);
    };

    const toggleDarkMode = () => {
        setDarkMode(!darkMode);
    };

    return (
        <div className={darkMode ? 'dark' : ''}>
            <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200 p-6">
                <div className="max-w-7xl mx-auto">
                    {/* Header with Dark Mode Toggle */}
                    <div className="flex justify-between items-center mb-8">
                        <div>
                            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                                Notification Dashboard
                            </h1>
                            <p className="text-gray-600 dark:text-gray-400 mt-1">
                                Stay updated with your latest notifications and tasks
                            </p>
                        </div>
                        <button
                            onClick={toggleDarkMode}
                            className="flex items-center space-x-2 px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
                        >
                            {darkMode ? (
                                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clipRule="evenodd" />
                                </svg>
                            ) : (
                                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
                                </svg>
                            )}
                            <span>{darkMode ? 'Light' : 'Dark'} Mode</span>
                        </button>
                    </div>

                    {/* Notification List */}
                    <NotificationList
                        sections={sampleSections}
                        onViewAll={handleViewAll}
                        className="mb-6"
                    />

                    {/* Additional Content */}
                    <div className="mt-8 bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm transition-colors">
                        <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                            Recent Activity
                        </h2>
                        <div className="space-y-3">
                            <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                <span className="text-sm text-gray-700 dark:text-gray-300">
                                    Document uploaded successfully
                                </span>
                                <span className="text-xs text-gray-500 dark:text-gray-400">
                                    5 minutes ago
                                </span>
                            </div>
                            <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                <span className="text-sm text-gray-700 dark:text-gray-300">
                                    Meeting scheduled with client
                                </span>
                                <span className="text-xs text-gray-500 dark:text-gray-400">
                                    15 minutes ago
                                </span>
                            </div>
                            <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                <span className="text-sm text-gray-700 dark:text-gray-300">
                                    Task assigned to team member
                                </span>
                                <span className="text-xs text-gray-500 dark:text-gray-400">
                                    1 hour ago
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}