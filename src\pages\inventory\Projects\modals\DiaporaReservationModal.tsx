import InputWithTags from "@/components/custom/forms/InputWithTags";
import Multiselect from "@/components/custom/forms/Multiselect";
import BaseModal from "@/components/custom/modals/BaseModal";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import CustomSelectField from "@/components/CustomSelectField";
import { Button } from "@/components/ui/button";
// import { Form, FormDescription, FormLabel } from "@/components/ui/form";
// import { Label } from "@/components/ui/label";
import {
  useAddReserveDiasporaPlotMutation,
  useEditReserveDiasporaPlotMutation,
} from "@/redux/slices/projects";
import {
  useGetDiaporaTripsQuery,
  useLazyGetDiaporaTripsQuery,
} from "@/redux/slices/propects";
import { useEffect, useState } from "react";
import { toast } from "sonner";

type Props = {
  setOpenModal: (e: boolean) => void;
  openModal: boolean;
  title: string;
  updateData?: any;
};

const DiaporaReservationModal = ({
  setOpenModal,
  openModal,
  title,
  updateData,
}: Props) => {
  const [fetchTrips, { data: trips, isLoading: trips_loading }] =
    useLazyGetDiaporaTripsQuery();
  const [reservePlots, { isLoading: reserving }] =
    useAddReserveDiasporaPlotMutation();
  const [updateReservePlots, { isLoading: updating }] =
    useEditReserveDiasporaPlotMutation();
  const [tags, setTags] = useState<string[]>(
    updateData ? updateData?.plots.split(",") : []
  );

  const [selectedTrip, setSelectedTrip] = useState<string>("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedTrip) {
      toast.error("Trip field is required");
      return;
    }

    if (tags.length < 1) {
      toast.error("Plots field is required");
      return;
    }

    const formData = { plots: tags, trip: selectedTrip };

    try {
      let res;
      if (updateData) {
        res = await updateReservePlots({
          ...formData,
          id: updateData?.id,
        }).unwrap();
      } else {
        res = await reservePlots(formData).unwrap();
      }
      if (res) {
        toast.success(
          `${updateData ? "Updated" : "Reverved"} plots successfully`
        );
        setTags([]);
        setOpenModal(false);
      }
    } catch (error: any) {
      console.log("Error: ", error);
      if (error?.data) {
        toast.error(`${error?.data?.error}`);
      } else {
        toast.error(`Error reserving plot(s)`);
      }
      return;
    }
  };

  return (
    <BaseModal
      isOpen={openModal}
      onOpenChange={setOpenModal}
      size="md"
      title={title}
      description="Fields with (*) are required"
      // footer={<Button onClick={() => setOpenModal(false)}>Close</Button>}
    >
      <div className="py-4">
        <form onSubmit={handleSubmit} className="space-y-8">
          <div className="space-y-2">
            <label htmlFor="marketer">Select Trip *</label>

            <CustomSelectField
              valueField="id"
              labelField="trip_name"
              data={trips?.data?.results}
              queryFunc={fetchTrips}
              setValue={setSelectedTrip}
              useSearchField={true}
              loader={trips_loading}
            />
          </div>
          <div className="space-y-2">
            <div className="flex gap-2 items-center flex-wrap">
              <label>Plot Number(s) *</label>
              <p>
                <small className="text-destructive">
                  ( Use <b>comma</b> to separate your plot numbers or press{" "}
                  <b>Enter</b> )
                </small>
              </p>
            </div>
            <InputWithTags tags={tags} setTags={setTags} />
          </div>

          <div className="w-full flex justify-end">
            {reserving || updating ? (
              <SpinnerTemp size="sm" />
            ) : (
              <Button type="submit" className="justify-end">
                Submit
              </Button>
            )}
          </div>
        </form>
      </div>
    </BaseModal>
  );
};

export default DiaporaReservationModal;
