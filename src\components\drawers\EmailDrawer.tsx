import { useState } from "react";
import { useForm } from "react-hook-form";
import DrawerModal from "../custom/modals/DrawerModal";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { 
  Form, 
  FormControl, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { Paperclip, X } from "lucide-react";

interface EmailDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  customerId: string;
  customerEmail: string;
}

interface EmailFormValues {
  to: string;
  subject: string;
  content: string;
}

const EmailDrawer = ({ isOpen, onClose, customerId, customerEmail }: EmailDrawerProps) => {
  const [isSending, setIsSending] = useState(false);
  const [attachments, setAttachments] = useState<File[]>([]);
  const { toast } = useToast();
  
  const form = useForm<EmailFormValues>({
    defaultValues: {
      to: customerEmail,
      subject: '',
      content: ''
    }
  });

  const handleSubmit = async (data: EmailFormValues) => {
    setIsSending(true);
    
    try {
      console.log("Sending email:", data, "to customer:", customerId);
      console.log("Attachments:", attachments);
      
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      toast({
        title: "Email sent",
        description: "Your email has been sent successfully."
      });
      
      onClose();
      form.reset();
      setAttachments([]);
    } catch (error) {
      console.error("Error sending email:", error);
      toast({
        title: "Error",
        description: "There was a problem sending your email.",
        variant: "destructive"
      });
    } finally {
      setIsSending(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newFiles = Array.from(e.target.files);
      setAttachments(prev => [...prev, ...newFiles]);
    }
  };

  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  return (
    <DrawerModal
      isOpen={isOpen}
      onOpenChange={onClose}
      title="Send Email"
      description="Compose and send an email to this customer"
      size="lg"
    >
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4 sm:space-y-6">
          <FormField
            control={form.control}
            name="to"
            render={({ field }) => (
              <FormItem>
                <FormLabel>To</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="subject"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Subject</FormLabel>
                <FormControl>
                  <Input 
                    placeholder="Email subject" 
                    {...field} 
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="content"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Content</FormLabel>
                <FormControl>
                  <Textarea 
                    placeholder="Compose your email..." 
                    className="min-h-[250px]"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          {/* Attachments */}
          <div className="space-y-2">
            <label className="block text-sm font-medium">Attachments</label>
            
            {attachments.length > 0 && (
              <div className="space-y-2 mb-3">
                {attachments.map((file, index) => (
                  <div 
                    key={index}
                    className="flex items-center justify-between bg-gray-50 p-2 rounded-md"
                  >
                    <div className="flex items-center">
                      <Paperclip className="h-4 w-4 mr-2 text-gray-500" />
                      <span className="text-sm truncate max-w-[300px]">{file.name}</span>
                    </div>
                    <Button 
                      type="button" 
                      variant="ghost" 
                      size="sm"
                      onClick={() => removeAttachment(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
            
            <div>
              <label 
                htmlFor="file-upload" 
                className="cursor-pointer inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <Paperclip className="h-4 w-4 mr-2" />
                Attach Files
              </label>
              <input
                id="file-upload"
                name="file-upload"
                type="file"
                className="sr-only"
                multiple
                onChange={handleFileChange}
              />
            </div>
          </div>
          
          <div className="flex flex-col sm:flex-row justify-end gap-3 pt-4 border-t">
            <Button type="button" variant="outline" onClick={onClose} className="w-full sm:w-auto">
              Cancel
            </Button>
            <Button type="submit" disabled={isSending} className="w-full sm:w-auto">
              {isSending ? "Sending..." : "Send Email"}
            </Button>
          </div>
        </form>
      </Form>
    </DrawerModal>
  );
};

export default EmailDrawer;