import React from 'react';
import { Check } from 'lucide-react';

interface Step {
  title: string;
  description: string;
}

interface StepperProps {
  steps: Step[];
  activeStep: number; // 0-based index
  className?: string;
}

export const TitleStatusStepper: React.FC<StepperProps> = ({ steps, activeStep, className = '' }) => {
  const getStepStatus = (stepIndex: number) => {
    if (stepIndex < activeStep) return 'completed';
    if (stepIndex === activeStep) return 'active';
    return 'pending';
  };

  const getStepStyles = (status: string) => {
    switch (status) {
      case 'completed':
        return {
          circle: 'bg-green-500 border-green-500 text-white shadow-lg',
          connector: 'bg-green-500',
          title: 'text-gray-900 dark:text-gray-100 font-semibold',
          description: 'text-gray-600 dark:text-gray-200'
        };
      case 'active':
        return {
          circle: 'bg-blue-500 border-blue-500 text-white shadow-lg ring-4 ring-blue-100',
          connector: 'bg-gray-200',
          title: 'text-gray-900 dark:text-gray-100 font-semibold',
          description: 'text-gray-700 dark:text-gray-200'
        };
      default:
        return {
          circle: 'bg-gray-100 border-gray-300 text-gray-500',
          connector: 'bg-gray-200',
          title: 'text-gray-500 font-medium',
          description: 'text-gray-400'
        };
    }
  };

  return (
    <div className={` ${className}`}>
      {steps.map((step, index) => {
        const status = getStepStatus(index);
        const styles = getStepStyles(status);
        const isLast = index === steps.length - 1;

        return (
          <div key={index} className="relative flex items-start">
            {/* Step Circle */}
            <div className="relative z-10 flex-shrink-0">
              <div 
                className={`
                  w-10 h-10 rounded-full border-2 flex items-center justify-center
                  transition-all duration-300 ease-in-out
                  ${styles.circle}
                `}
              >
                {status === 'completed' ? (
                  <Check className="w-5 h-5" strokeWidth={2.5} />
                ) : (
                  <span className="text-sm font-semibold">
                    {index + 1}
                  </span>
                )}
              </div>
            </div>

            {/* Step Content */}
            <div className="ml-4 flex-1 pb-8">
              <h3 className={`text-sm leading-tight transition-colors duration-200 ${styles.title}`}>
                {step.title}
              </h3>
              <p className={`mt-1 text-xs leading-relaxed transition-colors duration-200 ${styles.description}`}>
                {step.description}
              </p>
            </div>

            {/* Connecting Line */}
            {!isLast && (
              <div 
                className={`
                  absolute left-5 top-10 w-0.5 h-16 -translate-x-px
                  transition-colors duration-300
                  ${styles.connector}
                `}
              />
            )}
          </div>
        );
      })}
    </div>
  );
};