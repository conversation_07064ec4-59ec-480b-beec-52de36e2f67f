import { useSelector } from 'react-redux';
import { useEffect } from 'react';
import { selectCurrentUserDetails } from '@/redux/authSlice';
import { 
  hasAnySalesPermission, 
  hasSalesPermission, 
  getSalesPermissionLevel,
  getSalesApiParams,
  SALES_PERMISSION_CODES,
  SALES_PERMISSION_DESCRIPTIONS
} from '@/utils/salesPermissions';
import { useRefreshPermissions } from './useRefreshPermissions';
import { useGetUser2UserPermissionsQuery } from '@/redux/slices/permissions';

/**
 * Hook to check sales permissions for the current user
 * Automatically refreshes permissions when the component mounts
 */
export const useSalesPermissions = () => {
  const userDetails = useSelector(selectCurrentUserDetails);
  const { refreshPermissions } = useRefreshPermissions();

  // Fetch user-specific permissions from the API
  const {
    data: userSpecificPermissions = [],
    isLoading: loadingUserPermissions
  } = useGetUser2UserPermissionsQuery(
    {
      page: 1,
      page_size: 1000,
      user: userDetails?.employee_no || '',
    },
    {
      skip: !userDetails?.employee_no, // Skip if no employee number
    }
  );

  // Refresh permissions when the component mounts
  useEffect(() => {
    // Refresh permissions to ensure they're up to date
    refreshPermissions();
    
    // Set up an interval to refresh permissions every 5 minutes
    const intervalId = setInterval(() => {
      refreshPermissions();
    }, 5 * 60 * 1000); // 5 minutes in milliseconds
    
    return () => clearInterval(intervalId); // Clean up on unmount
  }, [refreshPermissions]);

  // Extract permissions from potentially nested arrays
  interface UserPermissionObject {
    code?: string;
    id?: string;
    permission?: number | string;
    [key: string]: any;
  }

  type UserPermission = string | number | UserPermissionObject | UserPermission[];

  interface UserDetails {
    user_permissions?: UserPermission[];
    [key: string]: any;
  }

  const userPermissions: (string | number | undefined)[] = [];
  
  // Add permissions from user2user permissions API
  userSpecificPermissions.forEach((p: any) => {
    if (p.permission !== undefined) {
      userPermissions.push(p.permission);
    }
  });

  // Get API parameters based on permissions
  const apiParams = getSalesApiParams(userDetails, {}, userSpecificPermissions);

  return {
    // Check if user has any sales permissions
    hasAnySalesAccess: hasAnySalesPermission(userPermissions),

    // Check specific permissions
    canViewHQSales: hasSalesPermission(userPermissions, SALES_PERMISSION_CODES.VIEW_SALES_HQ),
    canViewKarenSales: hasSalesPermission(userPermissions, SALES_PERMISSION_CODES.VIEW_SALES_KAREN),
    canViewAllOffices: hasSalesPermission(userPermissions, SALES_PERMISSION_CODES.VIEW_SALES_ALL_OFFICES),
    
    canViewOwnSales: hasSalesPermission(userPermissions, SALES_PERMISSION_CODES.VIEW_SALES_OWN_MARKETER),
    canViewAllMarketers: hasSalesPermission(userPermissions, SALES_PERMISSION_CODES.VIEW_SALES_ALL_MARKETERS),

    canViewDiasporaTeam: hasSalesPermission(userPermissions, SALES_PERMISSION_CODES.VIEW_SALES_DIASPORA_TEAM),
    canViewDigitalTeam: hasSalesPermission(userPermissions, SALES_PERMISSION_CODES.VIEW_SALES_DIGITAL_TEAM),
    canViewTelemarketingTeam: hasSalesPermission(userPermissions, SALES_PERMISSION_CODES.VIEW_SALES_TELEMARKETING_TEAM),   
    canViewOtherTeam: hasSalesPermission(userPermissions, SALES_PERMISSION_CODES.VIEW_SALES_OTHER_TEAM),
    canViewAllTeams: hasSalesPermission(userPermissions, SALES_PERMISSION_CODES.VIEW_SALES_ALL_TEAMS),

    canViewDiasporaRegion: hasSalesPermission(userPermissions, SALES_PERMISSION_CODES.VIEW_SALES_DIASPORA_REGION),
    canViewAllDiasporaRegions: hasSalesPermission(userPermissions, SALES_PERMISSION_CODES.VIEW_SALES_ALL_DIASPORA_REGIONS),

    // Get permission level description
    permissionLevel: getSalesPermissionLevel(userPermissions),

    // Raw permissions array for custom checks
    userPermissions,

    // User details
    userDetails,
    
    // Function to manually refresh permissions
    refreshPermissions,

    // API parameters based on permissions
    apiParams,

    // Permission codes and descriptions for documentation
    SALES_PERMISSION_CODES,
    SALES_PERMISSION_DESCRIPTIONS,

    // Get API parameters with additional query parameters
    getApiParams: (additionalParams: Record<string, any> = {}) => {
      return {
        ...apiParams,
        ...additionalParams
      };
    }
  };
};
