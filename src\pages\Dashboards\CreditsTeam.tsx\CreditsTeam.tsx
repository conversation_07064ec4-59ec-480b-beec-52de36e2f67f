import { Screen } from "@/app-components/layout/screen"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { But<PERSON> } from "@/components/ui/button"
import { ChartBarBig, ChartLine, ChartNoAxesCombined, EllipsisVertical } from "lucide-react"
import CreditsTeamMetrics from "./CreditsTeamMetrics";

const CreditsTeamDashboard = () => {

    return (
        <Screen>
            <div className='space-y-4'>
                <CustomButtons />
                <CreditsTeamMetrics />
            </div>
        </Screen >
    )
}

export default CreditsTeamDashboard

const CustomButtons = () => {
    return <div className=''>
        {/* Desktop Buttons */}
        <div className='hidden md:flex items-center justify-between w-full'>
            <p className='font-bold text-xl'>Credits Team Dashboard</p>
            <div className='flex gap-3 mt-2'>
                <DropdownMenu>
                    <DropdownMenuTrigger>
                        <span className="flex items-center gap-1 h-8 text-green-600 text-sm hover:underline border border-green-600  px-4 rounded-lg hover:bg-green-50">
                            <ChartNoAxesCombined size={17} />Sales Reports
                        </span>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-48">
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>All Sales</DropdownMenuItem>
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>All Customers</DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
                <DropdownMenu>
                    <DropdownMenuTrigger>
                        <span className="flex items-center gap-1 h-8 text-green-600 text-sm hover:underline border border-green-600  px-4 rounded-lg hover:bg-green-50">
                            <ChartBarBig size={17} />Portfolio Reports
                        </span>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-56">
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Portfolio summary</DropdownMenuItem>
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Sales summary</DropdownMenuItem>
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Customer summary</DropdownMenuItem>
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Collection summary</DropdownMenuItem>
                        <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Daily Collection summary</DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
                <span className="flex items-center gap-1 h-8 w-fit text-green-600 text-sm hover:underline border border-green-600  px-4 rounded-lg hover:bg-green-50 cursor-pointer">
                    <ChartLine size={17} />Collections Feedback
                </span>
            </div>
        </div>
        {/* Mobile Dropdown */}
        <div className='flex md:hidden justify-between items-center w-full'>
            <p className='font-bold text-xl'>Credits Team Dashboard</p>
            <DropdownMenu>
                <DropdownMenuTrigger asChild>
                    <Button size='icon' variant='outline'><EllipsisVertical /></Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-72 h-[65vh] overflow-y-scroll">
                    <DropdownMenuLabel className='py-3'>Sales Reports</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>All Sales</DropdownMenuItem>
                    <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>All Customers</DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuLabel className='py-3'>Portfolio reports</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Portfolio summary</DropdownMenuItem>
                    <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Sales summary</DropdownMenuItem>
                    <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Customer summary</DropdownMenuItem>
                    <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Collection summary</DropdownMenuItem>
                    <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Daily Collection summary</DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuLabel className='py-3'>Feedback</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem className='py-2.5 hover:!bg-transparent hover:!text-foreground cursor-pointer'>Collections Feedback</DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>
        </div>
    </div>
}