import { useSelector } from 'react-redux';
import { useEffect } from 'react';
import { selectCurrentUserDetails } from '@/redux/authSlice';
import { 
  hasAnyProspectPermission, 
  hasProspectPermission, 
  getProspectPermissionLevel,
  getProspectApiParams,
  PROSPECT_PERMISSION_CODES,
  PROSPECT_PERMISSION_DESCRIPTIONS
} from '@/utils/prospectPermissions';
import { useRefreshPermissions } from './useRefreshPermissions';
import { useGetUser2UserPermissionsQuery } from '@/redux/slices/permissions';

/**
 * Hook to check prospect permissions for the current user
 * Automatically refreshes permissions when the component mounts
 */
export const useProspectPermissions = () => {
  const userDetails = useSelector(selectCurrentUserDetails);
  const { refreshPermissions } = useRefreshPermissions();

  // Fetch user-specific permissions from the API
  const {
    data: userSpecificPermissions = [],
    isLoading: loadingUserPermissions
  } = useGetUser2UserPermissionsQuery(
    {
      page: 1,
      page_size: 1000,
      user: userDetails?.employee_no || '',
    },
    {
      skip: !userDetails?.employee_no, // Skip if no employee number
    }
  );

  // Refresh permissions when the component mounts
  useEffect(() => {
    // Refresh permissions to ensure they're up to date
    refreshPermissions();
    
    // Set up an interval to refresh permissions every 5 minutes
    const intervalId = setInterval(() => {
      refreshPermissions();
    }, 5 * 60 * 1000); // 5 minutes in milliseconds
    
    return () => clearInterval(intervalId); // Clean up on unmount
  }, [refreshPermissions]);

  // Extract permissions from potentially nested arrays
  interface UserPermissionObject {
    code?: string;
    id?: string;
    permission?: number | string;
    [key: string]: any;
  }

  type UserPermission = string | number | UserPermissionObject | UserPermission[];

  interface UserDetails {
    user_permissions?: UserPermission[];
    [key: string]: any;
  }

  const userPermissions: (string | number)[] = [];
  
  // Add permissions from user2user permissions API
  userSpecificPermissions.forEach((p: any) => {
    if (p.permission !== undefined) {
      userPermissions.push(p.permission);
    }
  });

  // Get API parameters based on permissions
  const apiParams = getProspectApiParams(userDetails, {}, userSpecificPermissions || []);

  return {
    // Check if user has any prospect permissions
    hasAnyProspectAccess: hasAnyProspectPermission(userPermissions),

    // Check specific permissions
    canViewHQProspects: hasProspectPermission(userPermissions, PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_HQ),
    canViewKarenProspects: hasProspectPermission(userPermissions, PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_KAREN),
    canViewAllOfficesProspects: hasProspectPermission(userPermissions, PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_ALL_OFFICES),
    
    canViewOwnProspects: hasProspectPermission(userPermissions, PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_OWN_MARKETER),
    canViewAllMarketersProspects: hasProspectPermission(userPermissions, PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_ALL_MARKETERS),

    canViewDiasporaTeamProspects: hasProspectPermission(userPermissions, PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_DIASPORA_TEAM),
    canViewDigitalTeamProspects: hasProspectPermission(userPermissions, PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_DIGITAL_TEAM),
    canViewTelemarketingTeamProspects: hasProspectPermission(userPermissions, PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_TELEMARKETING_TEAM),   
    canViewOtherTeamProspects: hasProspectPermission(userPermissions, PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_OTHER_TEAM),
    canViewAllTeamsProspects: hasProspectPermission(userPermissions, PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_ALL_TEAMS),

    canViewDiasporaRegionProspects: hasProspectPermission(userPermissions, PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_DIASPORA_REGION),
    canViewAllDiasporaRegionsProspects: hasProspectPermission(userPermissions, PROSPECT_PERMISSION_CODES.VIEW_PROSPECT_ALL_DIASPORA_REGIONS),

    // Get permission level description
    permissionLevel: getProspectPermissionLevel(userPermissions),

    // Raw permissions array for custom checks
    userPermissions,

    // User details
    userDetails,
    
    // Function to manually refresh permissions
    refreshPermissions,

    // API parameters based on permissions
    apiParams,

    // Permission codes and descriptions for documentation
    PROSPECT_PERMISSION_CODES,
    PROSPECT_PERMISSION_DESCRIPTIONS,

    // Get API parameters with additional query parameters
    getApiParams: (additionalParams: Record<string, any> = {}) => {
      return {
        ...apiParams,
        ...additionalParams
      };
    }
  };
};