import React from "react";
import { motion } from "framer-motion";

interface ModernDropIndicatorProps {
  beforeId: string | null;
  column: string;
}

export const ModernDropIndicator: React.FC<ModernDropIndicatorProps> = ({ beforeId, column }) => {
  return (
    <motion.div
      data-before={beforeId || "-1"}
      data-column={column}
      className="my-0.5 h-0.5 w-full bg-gradient-to-r from-blue-400 to-purple-500 opacity-0 rounded-full shadow-lg"
      initial={{ scaleX: 0 }}
      animate={{ scaleX: 1 }}
      transition={{ duration: 0.2 }}
    />
  );
};
