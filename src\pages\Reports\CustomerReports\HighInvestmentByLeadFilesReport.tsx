import LazyModal, { TableColumn } from '@/components/reports/ReportsModal';
import { Badge } from '@/components/ui/badge';

interface HighInvestmentByLeadFiles {
    customer_no: string;
    customer_name: string;
    customer_type: string;
    phone: string;
    primary_email: string;
    total_leadfiles: number;
}

interface HighInvestmentByLeadFilesReportProps {
    isModalOpen: boolean;
    setIsModalOpen: (state: boolean) => void;
}

const HighInvestmentByLeadFilesReport = ({ isModalOpen, setIsModalOpen }: HighInvestmentByLeadFilesReportProps) => {

    // Define table columns with proper typing
    const columns: TableColumn<HighInvestmentByLeadFiles>[] = [
        {
            key: 'customer_no',
            title: 'Customer No',
            render: (value: string) => (
                <span className="font-medium text-blue-600">{value}</span>
            )
        },
        {
            key: 'customer_name',
            title: 'Customer Name',
            render: (value: string) => (
                <span className="font-medium">{value}</span>
            )
        },
        {
            key: 'customer_type',
            title: 'Type',
            render: (value: string) => (
                <Badge variant="outline" className="text-xs">
                    {value}
                </Badge>
            )
        },
        {
            key: 'phone',
            title: 'Phone',
            render: (value: string) => (
                <span className="text-sm">{value || 'N/A'}</span>
            )
        },
        {
            key: 'primary_email',
            title: 'Email',
            render: (value: string) => (
                <span className="text-sm text-gray-600">{value}</span>
            )
        },
        {
            key: 'total_leadfiles',
            title: 'Lead Files',
            render: (value: number) => (
                <div className="flex items-center gap-2">
                    <span className="font-bold text-lg text-purple-600">{value}</span>
                    <Badge 
                        variant={value >= 2 ? "default" : "secondary"}
                        className={value >= 2 ? "bg-purple-100 text-purple-800" : ""}
                    >
                        {value >= 2 ? 'High' : 'Standard'}
                    </Badge>
                </div>
            )
        },
    ];

    const handleCloseModal = () => {
        setIsModalOpen(false);
    };

    return (
        <LazyModal<HighInvestmentByLeadFiles>
            isOpen={isModalOpen}
            onClose={handleCloseModal}
            title="Customer High Investment Report by Number of Lead Files"
            url="/customer-high-invest_no_of_leadfiles/"
            params={{}}
            columns={columns}
            size="xl"
        />
    );
};

export default HighInvestmentByLeadFilesReport;
