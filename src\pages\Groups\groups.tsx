import { Screen } from "@/app-components/layout/screen";
import SimpleTable, { ColumnDefinitionST } from "@/components/custom/tables/SimpleTable";
import { useState, useEffect } from "react";
import AddGroup from "./addgroup";
import DeleteGroup from "./deletegroup";
import {  OutlinedButton } from "@/components/custom/buttons/buttons";
import { toast } from "@/components/custom/Toast/MyToast";
import EditGroup from "./editgroup";
import { Search,Edit, Trash} from "lucide-react";


import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import OnlyIconButton from "@/components/custom/buttons/onlyiconbutton";

const Groups = () => {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState<{
    id: string;
    groupName: string;
    groupHead: string;
    description: string;
  } | null>(null);
  const [searchTerm, setSearchTerm] = useState("");

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(5);

  const [groupsData, setGroupsData] = useState([
    { id: "1", groupName: "Admins", groupHead: "John Doe", description: "Manages system settings" },
    { id: "2", groupName: "Editors", groupHead: "Jane Smith", description: "Handles content editing" },
    { id: "3", groupName: "Viewers", groupHead: "Bob Johnson", description: "Read-only access" },
    { id: "4", groupName: "Managers", groupHead: "Emily Davis", description: "Department management" },
    { id: "5", groupName: "Developers", groupHead: "Michael Wilson", description: "Technical team" },
    { id: "6", groupName: "Marketing", groupHead: "Sarah Lee", description: "Marketing campaigns" },
    { id: "7", groupName: "Finance", groupHead: "David Brown", description: "Financial operations" },
  ]);

 
  const filteredGroups = groupsData.filter(
    (group) =>
      group.groupName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      group.groupHead.toLowerCase().includes(searchTerm.toLowerCase()) ||
      group.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totalPages = Math.ceil(filteredGroups.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredGroups.slice(indexOfFirstItem, indexOfLastItem);

  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);
  
  const columns: ColumnDefinitionST<typeof groupsData[0]>[] = [
    {
      key: "groupName",
      header: "Group Name",
      headerClassName: "font-large px-6 py-5",
      cellClassName: "px-6 py-3",
    },
    {
      key: "groupHead",
      header: "Group Head",
      headerClassName: "font-large px-6",
      cellClassName: "px-6",
    },
    {
      key: "description",
      header: "Description",
      headerClassName: "font-large px-6",
      cellClassName: "px-6",
    },
    {
      key: "actions",
      header: "Actions",
      headerClassName: "font-medium text-center px-6",
      cellClassName: "text-center",
      renderCell: (row) => (
        <div className="flex justify-center space-x-2">
          <OnlyIconButton 
          className=" py-1.5 px-3 rounded-md transition-colors"
            variant="secondary"
            icon={Edit}

            onClick={() => {
              setSelectedGroup(row);
              setIsEditModalOpen(true);
            } } children={undefined}>

            </OnlyIconButton>
          <OnlyIconButton
            className=" py-1.5 px-3 rounded-md transition-colors"
            variant="primary"
            icon={Trash}
            onClick={() => {
              setSelectedGroup(row);
              setIsDeleteModalOpen(true);
            } } children={undefined}>
            </OnlyIconButton>
          
        </div>
      ),
    },
  ];

 
  const validateGroup = (group: {
    groupName: string;
    groupHead: string;
    description: string;
  }) => {
    if (!group.groupName.trim()) {
      toast("error", "Group Name is required");
      return false;
    }
    if (!group.groupHead.trim()) {
      toast("error", "Group Head is required");
      return false;
    }
    if (!group.description.trim()) {
      toast("error", "Description is required");
      return false;
    }
    return true;
  };

  const handleAddGroup = (newGroup: {
    groupName: string;
    groupHead: string;
    description: string;
  }) => {
    
    if (!validateGroup(newGroup)) {
      return;
    }

    const id = (groupsData.length + 1).toString();
    setGroupsData([...groupsData, { id, ...newGroup }]);
    setIsAddModalOpen(false);
    toast("success", "Group added successfully!");
  };

  const handleUpdateGroup = (updatedGroup: {
    id: string;
    groupName: string;
    groupHead: string;
    description: string;
  }) => {
    
    if (!validateGroup(updatedGroup)) {
      return;
    }

    setGroupsData(
      groupsData.map((group) =>
        group.id === updatedGroup.id ? updatedGroup : group
      )
    );
    setIsEditModalOpen(false);
    toast("success", "Group updated successfully!");
  };

  const handleDeleteGroup = () => {
    if (selectedGroup) {
      setGroupsData(
        groupsData.filter((group) => group.id !== selectedGroup.id)
      );
      setIsDeleteModalOpen(false);
      setSelectedGroup(null);
      toast("success", "Group deleted successfully!");
    }
  };

  
  const renderPaginationItems = () => {
    const items = [];
    
   
    items.push(
      <PaginationItem key="first">
        <PaginationLink 
          onClick={() => setCurrentPage(1)}
          isActive={currentPage === 1}
        >
          1
        </PaginationLink>
      </PaginationItem>
    );
    
   
    if (currentPage > 3) {
      items.push(
        <PaginationItem key="ellipsis-1">
          <PaginationEllipsis />
        </PaginationItem>
      );
    }
    
    
    if (currentPage > 2) {
      items.push(
        <PaginationItem key={currentPage - 1}>
          <PaginationLink 
            onClick={() => setCurrentPage(currentPage - 1)}
          >
            {currentPage - 1}
          </PaginationLink>
        </PaginationItem>
      );
    }
    
   
    if (currentPage !== 1 && currentPage !== totalPages) {
      items.push(
        <PaginationItem key={currentPage}>
          <PaginationLink 
            isActive
            onClick={() => setCurrentPage(currentPage)}
          >
            {currentPage}
          </PaginationLink>
        </PaginationItem>
      );
    }
    
    
    if (currentPage < totalPages - 1) {
      items.push(
        <PaginationItem key={currentPage + 1}>
          <PaginationLink 
            onClick={() => setCurrentPage(currentPage + 1)}
          >
            {currentPage + 1}
          </PaginationLink>
        </PaginationItem>
      );
    }
    
    if (currentPage < totalPages - 2) {
      items.push(
        <PaginationItem key="ellipsis-2">
          <PaginationEllipsis />
        </PaginationItem>
      );
    }
    
  
    if (totalPages > 1) {
      items.push(
        <PaginationItem key="last">
          <PaginationLink 
            onClick={() => setCurrentPage(totalPages)}
            isActive={currentPage === totalPages}
          >
            {totalPages}
          </PaginationLink>
        </PaginationItem>
      );
    }
    
    return items;
  };

  return (
    <Screen>
      <div className="min-h-screen space-y-5 py-8">
        <div className="flex justify-between items-center">
          <h1 className="text-4xl font-bold">Groups</h1>
          <OutlinedButton
            variant="primary"
            onClick={() => setIsAddModalOpen(true)}
          >
            Add Group
          </OutlinedButton>
        </div>

        
        <div className="relative max-w-md">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search groups..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
          />
        </div>

        <div className="px-2 border border-secondary rounded">
          <SimpleTable
            data={currentItems}
            columns={columns}
            containerClassName="rounded-lg"
            tableClassName="border-collapse"
            tHeaderClassName=""
            tBodyClassName=""
            tRowClassName=""
            headerCellsClassName=""
            bodyCellsClassName=""
            hoverable={true}
            striped={true}
          />
        </div>

        
        {filteredGroups.length > 0 && (
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-500">
              Showing {indexOfFirstItem + 1}-{Math.min(indexOfLastItem, filteredGroups.length)} of {filteredGroups.length} groups
            </p>
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                    className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                  />
                </PaginationItem>
                
                {renderPaginationItems()}
                
                <PaginationItem>
                  <PaginationNext
                    onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                    className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}

        
        {filteredGroups.length === 0 && (
          <div className="text-center py-8">
            <p className="text-gray-500">No groups found matching your search.</p>
          </div>
        )}

        {isAddModalOpen && (
          <AddGroup
            isOpen={isAddModalOpen}
            onClose={() => setIsAddModalOpen(false)}
            onAdd={handleAddGroup}
          />
        )}

        {isEditModalOpen && selectedGroup && (
          <EditGroup
            isOpen={isEditModalOpen}
            onClose={() => setIsEditModalOpen(false)}
            group={selectedGroup}
            onUpdate={handleUpdateGroup}
          />
        )}

        {isDeleteModalOpen && selectedGroup && (
          <DeleteGroup
            isOpen={isDeleteModalOpen}
            onClose={() => setIsDeleteModalOpen(false)}
            group={selectedGroup}
            onDelete={handleDeleteGroup}
          />
        )}
      </div>
    </Screen>
  );
};

export default Groups;