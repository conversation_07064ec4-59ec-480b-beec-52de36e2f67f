import { apiSlice } from "../apiSlice"

export const projectsApiSlice = apiSlice.injectEndpoints({
    endpoints: (builder) => ({
        getAllSales: builder.query({
            query: (params) => ({
                url: `/sales-views`,
                method: "GET",
                params: params,
            }),
            providesTags: ["Sales"],
        }),
        getSaleDetails: builder.query({
            query: ({saleId:saleId,params:params}) => ({
                url: `/lead-files/${saleId}`,
                method: "GET",
                params: params,
            }),
            providesTags: ["Sales"],
        }),
        getPaymentInstallments: builder.query({
            query: (params) => ({
                url: "/installment-schedules",
                method: "GET",
                params: params,
            }),
            providesTags: ["Sales"],
        }),
         getOngoingSales: builder.query({
            query: (params) => ({
                url: "/ongoing-sales",
                method: "GET",
                params: params,
            }),
            providesTags: ["Sales"],
        }),
        getCompletedSales: builder.query({
            query: (params) => ({
                url: "/completed-sales",
                method: "GET",
                params: params,
            }),
            providesTags: ["Sales"],
        }),
        getDroppedSales: builder.query({
            query: (params) => ({
                url: "/lead-files",
                method: "GET",
                params: params,
            }),
            providesTags: ["Sales"],
        }),
    })
});

export const {
    useGetAllSalesQuery,
    useGetSaleDetailsQuery,
    useGetPaymentInstallmentsQuery,
    useGetOngoingSalesQuery,
    useGetCompletedSalesQuery,
    useGetDroppedSalesQuery
} = projectsApiSlice;




// import { apiSlice } from "../apiSlice";
// import { getSalesApiParams } from "@/utils/salesPermissions";
// import { RootState } from "../store";

// export const projectsApiSlice = apiSlice.injectEndpoints({
//     endpoints: (builder) => ({
//         getAllSales: builder.query({
//             query: (params) => {
//                 // Directly use the params provided by the component
//                 // This simplifies the query function and makes debugging easier
//                 return {
//                     url: "/sales-views/",
//                     method: "GET",
//                     params: params,
//                 };
//             },
//             // Transform the response
//             transformResponse: (response: any) => {
//                 return response;
//             },
//             providesTags: ["Sales"],
//         }),
//         getSaleDetails: builder.query({
//             query: (saleId) => ({ getState }) => {
//                 const state = getState() as RootState;
//                 const userDetails = state.auth.user_details;
//                 const filteredParams = getSalesApiParams(userDetails);

//                 // If user has no access, return 404-like response
//                 if (filteredParams.no_access) {
//                     return {
//                         url: "/sales-views/no-access",
//                         method: "GET",
//                     };
//                 }

//                 return {
//                     url: `/sales-views/${saleId}`,
//                     method: "GET",
//                     params: filteredParams,
//                 };
//             },
//             providesTags: ["Sales"],
//         }),
//         getOngoingSales: builder.query({
//             query: (params) => ({ getState }) => {
//                 const state = getState() as RootState;
//                 const userDetails = state.auth.user_details;
//                 const filteredParams = getSalesApiParams(userDetails, { ...params, status: 'ongoing' });

//                 if (filteredParams.no_access) {
//                     return {
//                         url: "/sales-views/",
//                         method: "GET",
//                         params: { page_size: 0 },
//                     };
//                 }

//                 return {
//                     url: "/sales-views/",
//                     method: "GET",
//                     params: filteredParams,
//                 };
//             },
//             providesTags: ["Sales"],
//         }),
//         getCompletedSales: builder.query({
//             query: (params) => ({ getState }) => {
//                 const state = getState() as RootState;
//                 const userDetails = state.auth.user_details;
//                 const filteredParams = getSalesApiParams(userDetails, { ...params, status: 'completed' });

//                 if (filteredParams.no_access) {
//                     return {
//                         url: "/sales-views/",
//                         method: "GET",
//                         params: { page_size: 0 },
//                     };
//                 }

//                 return {
//                     url: "/sales-views/",
//                     method: "GET",
//                     params: filteredParams,
//                 };
//             },
//             providesTags: ["Sales"],
//         }),
//         getPaymentInstallments: builder.query({
//             query: (params) => ({
//                 url: "/installment-schedules",
//                 method: "GET",
//                 params: params,
//             }),
//             providesTags: ["Sales"],
//         }),
//     })
// });

// export const { 
//     useGetAllSalesQuery, 
//     useGetSaleDetailsQuery, 
//     useGetCompletedSalesQuery, 
//     useGetOngoingSalesQuery,
//     useGetPaymentInstallmentsQuery,
//  } = projectsApiSlice;
