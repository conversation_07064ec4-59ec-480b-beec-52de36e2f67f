import { useState } from "react";
import {
  MessageSquare,
  Star,
  ThumbsUp,
  AlertCircle,
  CheckCircle,
  Calendar,
  Tag,
  Plus,
  ChevronDown,
  Filter,
  Search,
  ArrowUpDown
} from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";


interface CustomerFeedbackTabProps {
  customerId: string;
}

const CustomerFeedbackTab = ({ customerId }: CustomerFeedbackTabProps) => {
  const [filterOpen, setFilterOpen] = useState(false);
  const [addFeedbackOpen, setAddFeedbackOpen] = useState(false);

  // Sample data for feedback
  const feedbackData = [
    {
      id: "1",
      title: "Satisfaction with purchase process",
      rating: 5,
      sentiment: "Positive",
      content: "I'm extremely happy with how smooth the purchase process was. The team was professional and responsive throughout the entire process.",
      source: "Email Survey",
      category: "Purchase Experience",
      date: "2023-04-15",
      project: "Amani Ridge",
      responded: true
    },
    {
      id: "2",
      title: "Site visit experience",
      rating: 4,
      sentiment: "Positive",
      content: "The site visit was informative and well organized. I would have appreciated more detailed information about the surrounding infrastructure plans.",
      source: "In-person Feedback",
      category: "Site Visit",
      date: "2023-05-22",
      project: "Garden of Joy",
      responded: true
    },
    {
      id: "3",
      title: "Documentation delay concerns",
      rating: 2,
      sentiment: "Negative",
      content: "I've been waiting for over three weeks for my documentation to be processed. Multiple follow-ups have not resolved the issue yet.",
      source: "Support Ticket",
      category: "Documentation",
      date: "2023-06-10",
      project: "Sukari Heights",
      responded: false
    },
    {
      id: "4",
      title: "Payment plan flexibility",
      rating: 4,
      sentiment: "Positive",
      content: "I appreciate the flexibility offered in the payment plan. It made it much easier for me to invest in this property.",
      source: "Phone Call",
      category: "Payment",
      date: "2023-07-05",
      project: "Amani Ridge",
      responded: true
    },
    {
      id: "5",
      title: "Unhappy with plot location",
      rating: 2,
      sentiment: "Negative",
      content: "Upon visiting the site again, I realized that the plot location isn't what I expected based on the initial discussions.",
      source: "Email",
      category: "Plot Issue",
      date: "2023-08-12",
      project: "Garden of Joy",
      responded: false
    }
  ];

  // Calculate summary statistics
  const summary = {
    totalFeedback: feedbackData.length,
    averageRating: feedbackData.reduce((acc, item) => acc + item.rating, 0) / feedbackData.length,
    positiveCount: feedbackData.filter(item => item.sentiment === "Positive").length,
    negativeCount: feedbackData.filter(item => item.sentiment === "Negative").length,
    pendingResponses: feedbackData.filter(item => !item.responded).length
  };

  const getSentimentColor = (sentiment: string) => {
    const sentimentColors: Record<string, string> = {
      "Positive": "bg-green-50 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300 dark:border-green-800",
      "Negative": "bg-red-50 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-300 dark:border-red-800",
      "Neutral": "bg-blue-50 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800"
    };

    return sentimentColors[sentiment] || "bg-gray-50 text-gray-800 border-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-700";
  };

  const getRatingColor = (rating: number) => {
    if (rating >= 4) return "text-green-600 dark:text-green-400";
    if (rating >= 3) return "text-yellow-600 dark:text-yellow-400";
    return "text-red-600 dark:text-red-400";
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const color = getRatingColor(rating);

    for (let i = 0; i < 5; i++) {
      stars.push(
        <Star
          key={i}
          className={`h-4 w-4 ${i < rating ? color : "text-gray-300 dark:text-gray-600"}`}
          fill={i < rating ? "currentColor" : "none"}
        />
      );
    }

    return <div className="flex">{stars}</div>;
  };

  return (
    <div className="space-y-6">
      {/* Feedback Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card className="border-0 shadow-sm bg-white dark:bg-gray-900">
          <CardContent className="p-6">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Average Rating</p>
                <h3 className="text-2xl font-bold mt-1 text-gray-900 dark:text-white">{summary.averageRating.toFixed(1)}/5</h3>
              </div>
              <div className="bg-amber-100 p-3 rounded-full dark:bg-amber-900/30">
                <Star className="h-6 w-6 text-amber-600 dark:text-amber-400" fill="currentColor" />
              </div>
            </div>
            <div className="flex items-center mt-4">
              {renderStars(Math.round(summary.averageRating))}
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm bg-white dark:bg-gray-900">
          <CardContent className="p-6">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Positive Feedback</p>
                <h3 className="text-2xl font-bold mt-1 text-gray-900 dark:text-white">{summary.positiveCount}</h3>
              </div>
              <div className="bg-green-100 p-3 rounded-full dark:bg-green-900/30">
                <ThumbsUp className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
            <div className="flex items-center mt-4 text-xs text-gray-500 dark:text-gray-400">
              <span>{((summary.positiveCount / summary.totalFeedback) * 100).toFixed(0)}% of total feedback</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm bg-white dark:bg-gray-900">
          <CardContent className="p-6">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Pending Responses</p>
                <h3 className="text-2xl font-bold mt-1 text-gray-900 dark:text-white">{summary.pendingResponses}</h3>
              </div>
              <div className="bg-blue-100 p-3 rounded-full dark:bg-blue-900/30">
                <MessageSquare className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
            <div className="flex items-center mt-4 text-xs text-gray-500 dark:text-gray-400">
              <span>Requires attention</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Feedback Table */}
      <Card className="border-0 shadow-sm bg-white dark:bg-gray-900">
        <CardHeader className="border-b bg-gray-50 dark:bg-gray-800/50 dark:border-gray-700 p-4">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <CardTitle className="text-lg font-medium">Customer Feedback</CardTitle>
            <div className="flex flex-wrap gap-2">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500 dark:text-gray-400" />
                <Input
                  type="search"
                  placeholder="Search feedback..."
                  className="pl-8 h-9 w-48 md:w-64"
                />
              </div>

              <Dialog open={addFeedbackOpen} onOpenChange={setAddFeedbackOpen}>
                <DialogTrigger asChild>
                  <Button size="sm" className="h-9">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Feedback
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[525px]">
                  <DialogHeader>
                    <DialogTitle>Record Customer Feedback</DialogTitle>
                    <DialogDescription>
                      Document new feedback received from this customer
                    </DialogDescription>
                  </DialogHeader>
                  <div className="py-4 space-y-4">
                    <div>
                      <label className="text-sm font-medium">Feedback Title</label>
                      <Input className="mt-1" placeholder="Enter a concise title" />
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium">Rating</label>
                        <Select>
                          <SelectTrigger className="mt-1">
                            <SelectValue placeholder="Select rating" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="5">5 - Excellent</SelectItem>
                            <SelectItem value="4">4 - Good</SelectItem>
                            <SelectItem value="3">3 - Average</SelectItem>
                            <SelectItem value="2">2 - Poor</SelectItem>
                            <SelectItem value="1">1 - Very Poor</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <label className="text-sm font-medium">Category</label>
                        <Select>
                          <SelectTrigger className="mt-1">
                            <SelectValue placeholder="Select category" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="purchase-experience">Purchase Experience</SelectItem>
                            <SelectItem value="site-visit">Site Visit</SelectItem>
                            <SelectItem value="documentation">Documentation</SelectItem>
                            <SelectItem value="payment">Payment</SelectItem>
                            <SelectItem value="plot-issue">Plot Issue</SelectItem>
                            <SelectItem value="other">Other</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Source</label>
                      <Select>
                        <SelectTrigger className="mt-1">
                          <SelectValue placeholder="Select source" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="email">Email</SelectItem>
                          <SelectItem value="phone">Phone Call</SelectItem>
                          <SelectItem value="survey">Survey</SelectItem>
                          <SelectItem value="in-person">In-person</SelectItem>
                          <SelectItem value="support">Support Ticket</SelectItem>
                          <SelectItem value="social">Social Media</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Project</label>
                      <Select>
                        <SelectTrigger className="mt-1">
                          <SelectValue placeholder="Select project" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="amani-ridge">Amani Ridge</SelectItem>
                          <SelectItem value="garden-of-joy">Garden of Joy</SelectItem>
                          <SelectItem value="sukari-heights">Sukari Heights</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Feedback Content</label>
                      <Textarea className="mt-1" placeholder="Enter detailed feedback from the customer" rows={4} />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setAddFeedbackOpen(false)}>Cancel</Button>
                    <Button onClick={() => setAddFeedbackOpen(false)}>Save Feedback</Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="bg-gray-50 dark:bg-gray-800/50">
                  <TableHead className="font-medium">
                    <div className="flex items-center">
                      Title
                      <ArrowUpDown className="ml-1 h-4 w-4" />
                    </div>
                  </TableHead>
                  <TableHead className="font-medium">Rating</TableHead>
                  <TableHead className="font-medium">Sentiment</TableHead>
                  <TableHead className="font-medium">Category</TableHead>
                  <TableHead className="font-medium">Project</TableHead>
                  <TableHead className="font-medium">
                    <div className="flex items-center">
                      Date
                      <ArrowUpDown className="ml-1 h-4 w-4" />
                    </div>
                  </TableHead>
                  <TableHead className="font-medium">Status</TableHead>
                  <TableHead className="sr-only">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {feedbackData.map((feedback) => (
                  <TableRow key={feedback.id} className="hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
                    <TableCell className="font-medium text-blue-600 dark:text-blue-400">{feedback.title}</TableCell>
                    <TableCell>{renderStars(feedback.rating)}</TableCell>
                    <TableCell>
                      <Badge className={`${getSentimentColor(feedback.sentiment)}`}>
                        {feedback.sentiment}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Tag className="h-3.5 w-3.5 mr-1 text-gray-500 dark:text-gray-400" />
                        <span>{feedback.category}</span>
                      </div>
                    </TableCell>
                    <TableCell>{feedback.project}</TableCell>
                    <TableCell>{new Date(feedback.date).toLocaleDateString()}</TableCell>
                    <TableCell>
                      {feedback.responded ? (
                        <div className="flex items-center text-green-600 dark:text-green-400">
                          <CheckCircle className="h-3.5 w-3.5 mr-1" />
                          <span>Responded</span>
                        </div>
                      ) : (
                        <div className="flex items-center text-amber-600 dark:text-amber-400">
                          <AlertCircle className="h-3.5 w-3.5 mr-1" />
                          <span>Pending</span>
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <ChevronDown className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>View Details</DropdownMenuItem>
                          <DropdownMenuItem>Add Response</DropdownMenuItem>
                          <DropdownMenuItem>Edit Feedback</DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem>Create Task</DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
        <CardFooter className="flex items-center justify-between border-t p-4 bg-gray-50 dark:bg-gray-800/50 dark:border-gray-700">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Showing <span className="font-medium">{feedbackData.length}</span> of <span className="font-medium">{feedbackData.length}</span> records
          </div>
          <div className="flex gap-1">
            <Button variant="outline" size="sm" disabled>
              Previous
            </Button>
            <Button variant="outline" size="sm" disabled>
              Next
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};

export default CustomerFeedbackTab;