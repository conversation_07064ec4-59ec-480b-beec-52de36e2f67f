import { create } from 'zustand';

export interface SalesDetailsProps {
  lead_file_no: string;
  lead_file_status_dropped: string;
  plot_no: string,
  purchase_price: string;
  selling_price: string;
  balance_lcy: string;
  customer_name: string;
  purchase_type: string;
  commission_threshold: string;
  deposit_threshold: string;
  discount: string;
  completion_date: string;
  no_of_installments: string;
  installment_amount: string;
  sale_agreement_sent: string;
  sale_agreement_signed: string;
  total_paid: string;
  transfer_cost_charged: string;
  transfer_cost_paid: string;
  overpayments: string;
  refunds: string;
  refundable_amount: string;
  penalties_accrued: string;
  booking_id: string;
  booking_date: string;
  additional_deposit_date: string;
  title_status: string;
  credit_officer_id: string;
  plot: string;
  project: string;
  marketer: string;
  customer_id: string;
  customer_lead_source: string;
  cat_lead_source: string;
}

interface SalesStore {
  salesData: SalesDetailsProps;
  setSalesData: (data: Partial<SalesDetailsProps>) => void;
  resetSalesData: () => void;
}

const initialSalesData: SalesDetailsProps = {
  lead_file_no: '',
  lead_file_status_dropped: '',
  plot_no: '',
  purchase_price: '',
  selling_price: '',
  balance_lcy: '',
  customer_name: '',
  purchase_type: '',
  commission_threshold: '',
  deposit_threshold: '',
  discount: '',
  completion_date: '',
  no_of_installments: '',
  installment_amount: '',
  sale_agreement_sent: '',
  sale_agreement_signed: '',
  total_paid: '',
  transfer_cost_charged: '',
  transfer_cost_paid: '',
  overpayments: '',
  refunds: '',
  refundable_amount: '',
  penalties_accrued: '',
  booking_id: '',
  booking_date: '',
  additional_deposit_date: '',
  title_status: '',
  credit_officer_id: '',
  plot: '',
  project: '',
  marketer: '',
  customer_id: '',
  customer_lead_source: '',
  cat_lead_source: '',
};

export const useSalesStore = create<SalesStore>((set) => ({
  salesData: initialSalesData,
  
  // Update sales data (merges with existing data)
  setSalesData: (data) => 
    set((state) => ({ 
      salesData: { ...state.salesData, ...data } 
    })),
  
  // Reset to initial state
  resetSalesData: () => set({ salesData: initialSalesData }),
}));