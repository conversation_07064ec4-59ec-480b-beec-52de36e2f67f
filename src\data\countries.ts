export interface Country {
  code: string;
  name: string;
  flag: string;
  phoneCode: string;
}

export const countries: Country[] = [
  { code: 'KE', name: 'Kenya', flag: '🇰🇪', phoneCode: '+254' },
  { code: 'UG', name: 'Uganda', flag: '🇺🇬', phoneCode: '+256' },
  { code: 'TZ', name: 'Tanzania', flag: '🇹🇿', phoneCode: '+255' },
  { code: 'RW', name: 'Rwanda', flag: '🇷🇼', phoneCode: '+250' },
  { code: 'ET', name: 'Ethiopia', flag: '🇪🇹', phoneCode: '+251' },
  { code: 'ZA', name: 'South Africa', flag: '🇿🇦', phoneCode: '+27' },
  { code: 'NG', name: 'Nigeria', flag: '🇳🇬', phoneCode: '+234' },
  { code: 'GH', name: 'Ghana', flag: '🇬🇭', phoneCode: '+233' },
  { code: 'EG', name: 'Egypt', flag: '🇪🇬', phoneCode: '+20' },
  { code: 'MA', name: 'Morocco', flag: '🇲🇦', phoneCode: '+212' },
  { code: 'DZ', name: 'Algeria', flag: '🇩🇿', phoneCode: '+213' },
  { code: 'TN', name: 'Tunisia', flag: '🇹🇳', phoneCode: '+216' },
  { code: 'LY', name: 'Libya', flag: '🇱🇾', phoneCode: '+218' },
  { code: 'SD', name: 'Sudan', flag: '🇸🇩', phoneCode: '+249' },
  { code: 'SS', name: 'South Sudan', flag: '🇸🇸', phoneCode: '+211' },
  { code: 'DJ', name: 'Djibouti', flag: '🇩🇯', phoneCode: '+253' },
  { code: 'SO', name: 'Somalia', flag: '🇸🇴', phoneCode: '+252' },
  { code: 'ER', name: 'Eritrea', flag: '🇪🇷', phoneCode: '+291' },
  { code: 'CM', name: 'Cameroon', flag: '🇨🇲', phoneCode: '+237' },
  { code: 'CF', name: 'Central African Republic', flag: '🇨🇫', phoneCode: '+236' },
  { code: 'TD', name: 'Chad', flag: '🇹🇩', phoneCode: '+235' },
  { code: 'NE', name: 'Niger', flag: '🇳🇪', phoneCode: '+227' },
  { code: 'BF', name: 'Burkina Faso', flag: '🇧🇫', phoneCode: '+226' },
  { code: 'ML', name: 'Mali', flag: '🇲🇱', phoneCode: '+223' },
  { code: 'SN', name: 'Senegal', flag: '🇸🇳', phoneCode: '+221' },
  { code: 'MR', name: 'Mauritania', flag: '🇲🇷', phoneCode: '+222' },
  { code: 'GM', name: 'Gambia', flag: '🇬🇲', phoneCode: '+220' },
  { code: 'GW', name: 'Guinea-Bissau', flag: '🇬🇼', phoneCode: '+245' },
  { code: 'GN', name: 'Guinea', flag: '🇬🇳', phoneCode: '+224' },
  { code: 'SL', name: 'Sierra Leone', flag: '🇸🇱', phoneCode: '+232' },
  { code: 'LR', name: 'Liberia', flag: '🇱🇷', phoneCode: '+231' },
  { code: 'CI', name: 'Ivory Coast', flag: '🇨🇮', phoneCode: '+225' },
  { code: 'TG', name: 'Togo', flag: '🇹🇬', phoneCode: '+228' },
  { code: 'BJ', name: 'Benin', flag: '🇧🇯', phoneCode: '+229' },
  { code: 'GA', name: 'Gabon', flag: '🇬🇦', phoneCode: '+241' },
  { code: 'GQ', name: 'Equatorial Guinea', flag: '🇬🇶', phoneCode: '+240' },
  { code: 'ST', name: 'São Tomé and Príncipe', flag: '🇸🇹', phoneCode: '+239' },
  { code: 'CG', name: 'Republic of the Congo', flag: '🇨🇬', phoneCode: '+242' },
  { code: 'CD', name: 'Democratic Republic of the Congo', flag: '🇨🇩', phoneCode: '+243' },
  { code: 'AO', name: 'Angola', flag: '🇦🇴', phoneCode: '+244' },
  { code: 'ZM', name: 'Zambia', flag: '🇿🇲', phoneCode: '+260' },
  { code: 'ZW', name: 'Zimbabwe', flag: '🇿🇼', phoneCode: '+263' },
  { code: 'BW', name: 'Botswana', flag: '🇧🇼', phoneCode: '+267' },
  { code: 'NA', name: 'Namibia', flag: '🇳🇦', phoneCode: '+264' },
  { code: 'SZ', name: 'Eswatini', flag: '🇸🇿', phoneCode: '+268' },
  { code: 'LS', name: 'Lesotho', flag: '🇱🇸', phoneCode: '+266' },
  { code: 'MW', name: 'Malawi', flag: '🇲🇼', phoneCode: '+265' },
  { code: 'MZ', name: 'Mozambique', flag: '🇲🇿', phoneCode: '+258' },
  { code: 'MG', name: 'Madagascar', flag: '🇲🇬', phoneCode: '+261' },
  { code: 'MU', name: 'Mauritius', flag: '🇲🇺', phoneCode: '+230' },
  { code: 'SC', name: 'Seychelles', flag: '🇸🇨', phoneCode: '+248' },
  { code: 'KM', name: 'Comoros', flag: '🇰🇲', phoneCode: '+269' },
  { code: 'CV', name: 'Cape Verde', flag: '🇨🇻', phoneCode: '+238' },
  // Major international countries
  { code: 'US', name: 'United States', flag: '🇺🇸', phoneCode: '+1' },
  { code: 'GB', name: 'United Kingdom', flag: '🇬🇧', phoneCode: '+44' },
  { code: 'CA', name: 'Canada', flag: '🇨🇦', phoneCode: '+1' },
  { code: 'AU', name: 'Australia', flag: '🇦🇺', phoneCode: '+61' },
  { code: 'DE', name: 'Germany', flag: '🇩🇪', phoneCode: '+49' },
  { code: 'FR', name: 'France', flag: '🇫🇷', phoneCode: '+33' },
  { code: 'IT', name: 'Italy', flag: '🇮🇹', phoneCode: '+39' },
  { code: 'ES', name: 'Spain', flag: '🇪🇸', phoneCode: '+34' },
  { code: 'NL', name: 'Netherlands', flag: '🇳🇱', phoneCode: '+31' },
  { code: 'BE', name: 'Belgium', flag: '🇧🇪', phoneCode: '+32' },
  { code: 'CH', name: 'Switzerland', flag: '🇨🇭', phoneCode: '+41' },
  { code: 'AT', name: 'Austria', flag: '🇦🇹', phoneCode: '+43' },
  { code: 'SE', name: 'Sweden', flag: '🇸🇪', phoneCode: '+46' },
  { code: 'NO', name: 'Norway', flag: '🇳🇴', phoneCode: '+47' },
  { code: 'DK', name: 'Denmark', flag: '🇩🇰', phoneCode: '+45' },
  { code: 'FI', name: 'Finland', flag: '🇫🇮', phoneCode: '+358' },
  { code: 'IE', name: 'Ireland', flag: '🇮🇪', phoneCode: '+353' },
  { code: 'PT', name: 'Portugal', flag: '🇵🇹', phoneCode: '+351' },
  { code: 'GR', name: 'Greece', flag: '🇬🇷', phoneCode: '+30' },
  { code: 'PL', name: 'Poland', flag: '🇵🇱', phoneCode: '+48' },
  { code: 'CZ', name: 'Czech Republic', flag: '🇨🇿', phoneCode: '+420' },
  { code: 'HU', name: 'Hungary', flag: '🇭🇺', phoneCode: '+36' },
  { code: 'RO', name: 'Romania', flag: '🇷🇴', phoneCode: '+40' },
  { code: 'BG', name: 'Bulgaria', flag: '🇧🇬', phoneCode: '+359' },
  { code: 'HR', name: 'Croatia', flag: '🇭🇷', phoneCode: '+385' },
  { code: 'SI', name: 'Slovenia', flag: '🇸🇮', phoneCode: '+386' },
  { code: 'SK', name: 'Slovakia', flag: '🇸🇰', phoneCode: '+421' },
  { code: 'EE', name: 'Estonia', flag: '🇪🇪', phoneCode: '+372' },
  { code: 'LV', name: 'Latvia', flag: '🇱🇻', phoneCode: '+371' },
  { code: 'LT', name: 'Lithuania', flag: '🇱🇹', phoneCode: '+370' },
  { code: 'RU', name: 'Russia', flag: '🇷🇺', phoneCode: '+7' },
  { code: 'UA', name: 'Ukraine', flag: '🇺🇦', phoneCode: '+380' },
  { code: 'BY', name: 'Belarus', flag: '🇧🇾', phoneCode: '+375' },
  { code: 'MD', name: 'Moldova', flag: '🇲🇩', phoneCode: '+373' },
  { code: 'CN', name: 'China', flag: '🇨🇳', phoneCode: '+86' },
  { code: 'JP', name: 'Japan', flag: '🇯🇵', phoneCode: '+81' },
  { code: 'KR', name: 'South Korea', flag: '🇰🇷', phoneCode: '+82' },
  { code: 'IN', name: 'India', flag: '🇮🇳', phoneCode: '+91' },
  { code: 'PK', name: 'Pakistan', flag: '🇵🇰', phoneCode: '+92' },
  { code: 'BD', name: 'Bangladesh', flag: '🇧🇩', phoneCode: '+880' },
  { code: 'LK', name: 'Sri Lanka', flag: '🇱🇰', phoneCode: '+94' },
  { code: 'NP', name: 'Nepal', flag: '🇳🇵', phoneCode: '+977' },
  { code: 'BT', name: 'Bhutan', flag: '🇧🇹', phoneCode: '+975' },
  { code: 'MV', name: 'Maldives', flag: '🇲🇻', phoneCode: '+960' },
  { code: 'AF', name: 'Afghanistan', flag: '🇦🇫', phoneCode: '+93' },
  { code: 'IR', name: 'Iran', flag: '🇮🇷', phoneCode: '+98' },
  { code: 'IQ', name: 'Iraq', flag: '🇮🇶', phoneCode: '+964' },
  { code: 'SY', name: 'Syria', flag: '🇸🇾', phoneCode: '+963' },
  { code: 'LB', name: 'Lebanon', flag: '🇱🇧', phoneCode: '+961' },
  { code: 'JO', name: 'Jordan', flag: '🇯🇴', phoneCode: '+962' },
  { code: 'IL', name: 'Israel', flag: '🇮🇱', phoneCode: '+972' },
  { code: 'PS', name: 'Palestine', flag: '🇵🇸', phoneCode: '+970' },
  { code: 'SA', name: 'Saudi Arabia', flag: '🇸🇦', phoneCode: '+966' },
  { code: 'AE', name: 'United Arab Emirates', flag: '🇦🇪', phoneCode: '+971' },
  { code: 'QA', name: 'Qatar', flag: '🇶🇦', phoneCode: '+974' },
  { code: 'BH', name: 'Bahrain', flag: '🇧🇭', phoneCode: '+973' },
  { code: 'KW', name: 'Kuwait', flag: '🇰🇼', phoneCode: '+965' },
  { code: 'OM', name: 'Oman', flag: '🇴🇲', phoneCode: '+968' },
  { code: 'YE', name: 'Yemen', flag: '🇾🇪', phoneCode: '+967' },
  { code: 'TR', name: 'Turkey', flag: '🇹🇷', phoneCode: '+90' },
  { code: 'CY', name: 'Cyprus', flag: '🇨🇾', phoneCode: '+357' },
  { code: 'GE', name: 'Georgia', flag: '🇬🇪', phoneCode: '+995' },
  { code: 'AM', name: 'Armenia', flag: '🇦🇲', phoneCode: '+374' },
  { code: 'AZ', name: 'Azerbaijan', flag: '🇦🇿', phoneCode: '+994' },
  { code: 'KZ', name: 'Kazakhstan', flag: '🇰🇿', phoneCode: '+7' },
  { code: 'UZ', name: 'Uzbekistan', flag: '🇺🇿', phoneCode: '+998' },
  { code: 'TM', name: 'Turkmenistan', flag: '🇹🇲', phoneCode: '+993' },
  { code: 'TJ', name: 'Tajikistan', flag: '🇹🇯', phoneCode: '+992' },
  { code: 'KG', name: 'Kyrgyzstan', flag: '🇰🇬', phoneCode: '+996' },
  { code: 'MN', name: 'Mongolia', flag: '🇲🇳', phoneCode: '+976' },
  { code: 'TH', name: 'Thailand', flag: '🇹🇭', phoneCode: '+66' },
  { code: 'VN', name: 'Vietnam', flag: '🇻🇳', phoneCode: '+84' },
  { code: 'LA', name: 'Laos', flag: '🇱🇦', phoneCode: '+856' },
  { code: 'KH', name: 'Cambodia', flag: '🇰🇭', phoneCode: '+855' },
  { code: 'MM', name: 'Myanmar', flag: '🇲🇲', phoneCode: '+95' },
  { code: 'MY', name: 'Malaysia', flag: '🇲🇾', phoneCode: '+60' },
  { code: 'SG', name: 'Singapore', flag: '🇸🇬', phoneCode: '+65' },
  { code: 'ID', name: 'Indonesia', flag: '🇮🇩', phoneCode: '+62' },
  { code: 'BN', name: 'Brunei', flag: '🇧🇳', phoneCode: '+673' },
  { code: 'PH', name: 'Philippines', flag: '🇵🇭', phoneCode: '+63' },
  { code: 'TW', name: 'Taiwan', flag: '🇹🇼', phoneCode: '+886' },
  { code: 'HK', name: 'Hong Kong', flag: '🇭🇰', phoneCode: '+852' },
  { code: 'MO', name: 'Macau', flag: '🇲🇴', phoneCode: '+853' },
  { code: 'NZ', name: 'New Zealand', flag: '🇳🇿', phoneCode: '+64' },
  { code: 'FJ', name: 'Fiji', flag: '🇫🇯', phoneCode: '+679' },
  { code: 'PG', name: 'Papua New Guinea', flag: '🇵🇬', phoneCode: '+675' },
  { code: 'SB', name: 'Solomon Islands', flag: '🇸🇧', phoneCode: '+677' },
  { code: 'VU', name: 'Vanuatu', flag: '🇻🇺', phoneCode: '+678' },
  { code: 'NC', name: 'New Caledonia', flag: '🇳🇨', phoneCode: '+687' },
  { code: 'PF', name: 'French Polynesia', flag: '🇵🇫', phoneCode: '+689' },
  { code: 'WS', name: 'Samoa', flag: '🇼🇸', phoneCode: '+685' },
  { code: 'TO', name: 'Tonga', flag: '🇹🇴', phoneCode: '+676' },
  { code: 'TV', name: 'Tuvalu', flag: '🇹🇻', phoneCode: '+688' },
  { code: 'KI', name: 'Kiribati', flag: '🇰🇮', phoneCode: '+686' },
  { code: 'NR', name: 'Nauru', flag: '🇳🇷', phoneCode: '+674' },
  { code: 'PW', name: 'Palau', flag: '🇵🇼', phoneCode: '+680' },
  { code: 'FM', name: 'Micronesia', flag: '🇫🇲', phoneCode: '+691' },
  { code: 'MH', name: 'Marshall Islands', flag: '🇲🇭', phoneCode: '+692' },
  { code: 'BR', name: 'Brazil', flag: '🇧🇷', phoneCode: '+55' },
  { code: 'AR', name: 'Argentina', flag: '🇦🇷', phoneCode: '+54' },
  { code: 'CL', name: 'Chile', flag: '🇨🇱', phoneCode: '+56' },
  { code: 'PE', name: 'Peru', flag: '🇵🇪', phoneCode: '+51' },
  { code: 'CO', name: 'Colombia', flag: '🇨🇴', phoneCode: '+57' },
  { code: 'VE', name: 'Venezuela', flag: '🇻🇪', phoneCode: '+58' },
  { code: 'EC', name: 'Ecuador', flag: '🇪🇨', phoneCode: '+593' },
  { code: 'BO', name: 'Bolivia', flag: '🇧🇴', phoneCode: '+591' },
  { code: 'PY', name: 'Paraguay', flag: '🇵🇾', phoneCode: '+595' },
  { code: 'UY', name: 'Uruguay', flag: '🇺🇾', phoneCode: '+598' },
  { code: 'GY', name: 'Guyana', flag: '🇬🇾', phoneCode: '+592' },
  { code: 'SR', name: 'Suriname', flag: '🇸🇷', phoneCode: '+597' },
  { code: 'GF', name: 'French Guiana', flag: '🇬🇫', phoneCode: '+594' },
  { code: 'FK', name: 'Falkland Islands', flag: '🇫🇰', phoneCode: '+500' },
  { code: 'MX', name: 'Mexico', flag: '🇲🇽', phoneCode: '+52' },
  { code: 'GT', name: 'Guatemala', flag: '🇬🇹', phoneCode: '+502' },
  { code: 'BZ', name: 'Belize', flag: '🇧🇿', phoneCode: '+501' },
  { code: 'SV', name: 'El Salvador', flag: '🇸🇻', phoneCode: '+503' },
  { code: 'HN', name: 'Honduras', flag: '🇭🇳', phoneCode: '+504' },
  { code: 'NI', name: 'Nicaragua', flag: '🇳🇮', phoneCode: '+505' },
  { code: 'CR', name: 'Costa Rica', flag: '🇨🇷', phoneCode: '+506' },
  { code: 'PA', name: 'Panama', flag: '🇵🇦', phoneCode: '+507' },
  { code: 'CU', name: 'Cuba', flag: '🇨🇺', phoneCode: '+53' },
  { code: 'JM', name: 'Jamaica', flag: '🇯🇲', phoneCode: '+1876' },
  { code: 'HT', name: 'Haiti', flag: '🇭🇹', phoneCode: '+509' },
  { code: 'DO', name: 'Dominican Republic', flag: '🇩🇴', phoneCode: '+1809' },
  { code: 'PR', name: 'Puerto Rico', flag: '🇵🇷', phoneCode: '+1787' },
  { code: 'TT', name: 'Trinidad and Tobago', flag: '🇹🇹', phoneCode: '+1868' },
  { code: 'BB', name: 'Barbados', flag: '🇧🇧', phoneCode: '+1246' },
  { code: 'GD', name: 'Grenada', flag: '🇬🇩', phoneCode: '+1473' },
  { code: 'LC', name: 'Saint Lucia', flag: '🇱🇨', phoneCode: '+1758' },
  { code: 'VC', name: 'Saint Vincent and the Grenadines', flag: '🇻🇨', phoneCode: '+1784' },
  { code: 'AG', name: 'Antigua and Barbuda', flag: '🇦🇬', phoneCode: '+1268' },
  { code: 'DM', name: 'Dominica', flag: '🇩🇲', phoneCode: '+1767' },
  { code: 'KN', name: 'Saint Kitts and Nevis', flag: '🇰🇳', phoneCode: '+1869' },
  { code: 'BS', name: 'Bahamas', flag: '🇧🇸', phoneCode: '+1242' },
  { code: 'BM', name: 'Bermuda', flag: '🇧🇲', phoneCode: '+1441' },
];

// Helper function to get country by code
export const getCountryByCode = (code: string): Country | undefined => {
  return countries.find(country => country.code === code);
};

// Helper function to get country by phone code
export const getCountryByPhoneCode = (phoneCode: string): Country | undefined => {
  return countries.find(country => country.phoneCode === phoneCode);
};

// Default country (Kenya)
export const defaultCountry = countries.find(country => country.code === 'KE') || countries[0];
