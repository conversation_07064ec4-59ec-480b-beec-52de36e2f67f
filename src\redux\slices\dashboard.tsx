import { apiSlice } from "../apiSlice";

export const projectsApiSlice = apiSlice.injectEndpoints({
    endpoints: (builder) => ({
        getDashboard: builder.query({
            query: (params) => ({
                url: "/main-dashboard",
                method: "GET",
                params: params,
            }),
            providesTags: ["MainDashboard"],
        }),
    }),
})

export const {useGetDashboardQuery} = projectsApiSlice;