import { useSelector } from 'react-redux';
import { useEffect } from 'react';
import { selectCurrentUserDetails } from '@/redux/authSlice';
import { 
  hasAnyCustomerPermission, 
  hasCustomerPermission, 
  getCustomerPermissionLevel,
  getCustomerApiParams,
  CUSTOMER_PERMISSION_CODES,
  CUSTOMER_PERMISSION_DESCRIPTIONS
} from '@/utils/customerPermissions';
import { useRefreshPermissions } from './useRefreshPermissions';
import { useGetUser2UserPermissionsQuery } from '@/redux/slices/permissions';

/**
 * Hook to check customer permissions for the current user
 * Automatically refreshes permissions when the component mounts
 */
export const useCustomerPermissions = () => {
  const userDetails = useSelector(selectCurrentUserDetails);
  const { refreshPermissions } = useRefreshPermissions();

  // Fetch user-specific permissions from the API
  const {
    data: userSpecificPermissions = [],
    isLoading: loadingUserPermissions
  } = useGetUser2UserPermissionsQuery(
    {
      page: 1,
      page_size: 1000,
      user: userDetails?.employee_no || '',
    },
    {
      skip: !userDetails?.employee_no, // Skip if no employee number
    }
  );

  // Refresh permissions when the component mounts
  useEffect(() => {
    // Refresh permissions to ensure they're up to date
    refreshPermissions();
    
    // Set up an interval to refresh permissions every 5 minutes
    const intervalId = setInterval(() => {
      refreshPermissions();
    }, 5 * 60 * 1000); // 5 minutes in milliseconds
    
    return () => clearInterval(intervalId); // Clean up on unmount
  }, [refreshPermissions]);

  // Extract permissions from potentially nested arrays
  interface UserPermissionObject {
    code?: string;
    id?: string;
    permission?: number | string;
    [key: string]: any;
  }

  type UserPermission = string | number | UserPermissionObject | UserPermission[];

  interface UserDetails {
    user_permissions?: UserPermission[];
    [key: string]: any;
  }

  const userPermissions: (string | number)[] = [];
  
  // Add permissions from user2user permissions API
  userSpecificPermissions.forEach((p: any) => {
    if (p.permission !== undefined) {
      userPermissions.push(p.permission);
    }
  });

  // Get API parameters based on permissions
  const apiParams = getCustomerApiParams(userDetails, {}, userSpecificPermissions || []);
  
  // Temporary debugging to see what permissions are being detected
  // console.log('Customer permissions debug:', {
  //   userPermissions,
  //   apiParams,
  //   hasAnyAccess: hasAnyCustomerPermission(userPermissions),
  //   permissionLevel: getCustomerPermissionLevel(userPermissions)
  // });

  return {
    // Check if user has any customer permissions
    hasAnyCustomerAccess: hasAnyCustomerPermission(userPermissions),

    // Check specific permissions
    canViewHQCustomers: hasCustomerPermission(userPermissions, CUSTOMER_PERMISSION_CODES.VIEW_CUSTOMER_HQ),
    canViewKarenCustomers: hasCustomerPermission(userPermissions, CUSTOMER_PERMISSION_CODES.VIEW_CUSTOMER_KAREN),
    canViewAllOfficesCustomers: hasCustomerPermission(userPermissions, CUSTOMER_PERMISSION_CODES.VIEW_CUSTOMER_ALL_OFFICES),
    
    canViewOwnCustomers: hasCustomerPermission(userPermissions, CUSTOMER_PERMISSION_CODES.VIEW_CUSTOMER_OWN_MARKETER),
    canViewAllMarketersCustomers: hasCustomerPermission(userPermissions, CUSTOMER_PERMISSION_CODES.VIEW_CUSTOMER_ALL_MARKETERS),

    canViewDiasporaTeamCustomers: hasCustomerPermission(userPermissions, CUSTOMER_PERMISSION_CODES.VIEW_CUSTOMER_DIASPORA_TEAM),
    canViewDigitalTeamCustomers: hasCustomerPermission(userPermissions, CUSTOMER_PERMISSION_CODES.VIEW_CUSTOMER_DIGITAL_TEAM),
    canViewTelemarketingTeamCustomers: hasCustomerPermission(userPermissions, CUSTOMER_PERMISSION_CODES.VIEW_CUSTOMER_TELEMARKETING_TEAM),   
    canViewOtherTeamCustomers: hasCustomerPermission(userPermissions, CUSTOMER_PERMISSION_CODES.VIEW_CUSTOMER_OTHER_TEAM),
    canViewAllTeamsCustomers: hasCustomerPermission(userPermissions, CUSTOMER_PERMISSION_CODES.VIEW_CUSTOMER_ALL_TEAMS),

    canViewDiasporaRegionCustomers: hasCustomerPermission(userPermissions, CUSTOMER_PERMISSION_CODES.VIEW_CUSTOMER_DIASPORA_REGION),
    canViewAllDiasporaRegionsCustomers: hasCustomerPermission(userPermissions, CUSTOMER_PERMISSION_CODES.VIEW_CUSTOMER_ALL_DIASPORA_REGIONS),

    // Get permission level description
    permissionLevel: getCustomerPermissionLevel(userPermissions),

    // Raw permissions array for custom checks
    userPermissions,

    // User details
    userDetails,
    
    // Function to manually refresh permissions
    refreshPermissions,

    // API parameters based on permissions
    apiParams,

    // Permission codes and descriptions for documentation
    CUSTOMER_PERMISSION_CODES,
    CUSTOMER_PERMISSION_DESCRIPTIONS,

    // Get API parameters with additional query parameters
    getApiParams: (additionalParams: Record<string, any> = {}) => {
      return {
        ...apiParams,
        ...additionalParams
      };
    }
  };
};