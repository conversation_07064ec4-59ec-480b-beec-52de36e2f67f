import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import {
  useAddNoteMutation,
  useUpdateNoteMutation,
} from "@/redux/slices/services";

type Props = {
  prospect_id?: string;
  onOpenChange?: (e: boolean) => void;
  updateData?: any;
};

const NotesForm = ({ prospect_id, onOpenChange, updateData }: Props) => {
  const [addNote, { isLoading: submitting }] = useAddNoteMutation();
  const [updateNote, { isLoading: updating }] = useUpdateNoteMutation();

  // form schema
  const formSchema = z.object({
    note_type: z.string().min(1, { message: "Note Type is required." }),
    title: z.string().min(1, { message: "title is required." }),
    content: z.string().min(1, { message: "content is required." }),
    is_private: z.boolean().default(false),
    is_pinned: z.boolean().default(false),
  });

  // instantiate react hook form
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      note_type: updateData?.note_type || "",
      title: updateData?.title || "",
      content: updateData?.content || "",
      is_private: updateData?.is_private ?? false,
      is_pinned: updateData?.is_pinned ?? false,
    },
  });

  const handleNoteSubmit = async (data: any) => {
    let formData = updateData
      ? { ...data, note_id: updateData?.note_id }
      : { ...data, customer_status: "prospect", prospect_id: prospect_id };
    try {
      let res;
      if (updateData) {
        res = await updateNote(formData).unwrap();
      } else {
        res = await addNote(formData).unwrap();
      }
      if (res?.note_id) {
        toast.success(`Note ${updateData ? "updated" : "added"} successfully"`);
        onOpenChange && onOpenChange(false);
      } else {
        toast.error("Failed to add");
      }
    } catch (error: any) {
      console.log("error", error);
      toast.error(error?.data ? error?.data?.error : "Something went wrong");
    }
  };

  return (
    <div className="">
      <form onSubmit={handleSubmit(handleNoteSubmit)}>
        <div className="space-y-2">
          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Notes Type*
            </label>
            <div className="relative pt-1">
              <select
                {...register("note_type")}
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              >
                <option value="General">General</option>
                <option value="Important">Important</option>
                <option value="Reminder">Reminder</option>
                <option value="Follow-up">Follow-up</option>
                <option value="Internal">Internal</option>
                <option value="Customer Facing">Customer Facing</option>
              </select>
            </div>
            {errors.note_type && (
              <p className="text-red-500 text-xs pl-3">
                {errors.note_type.message}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Title*
            </label>
            <div className="relative pt-1">
              <input
                {...register("title")}
                placeholder="title"
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              />
            </div>
            {errors.title && (
              <p className="text-red-500 text-xs pl-3">
                {errors.title.message}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Contents*
            </label>
            <div className="relative pt-1">
              <textarea
                {...register("content")}
                rows={3}
                placeholder="content"
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              ></textarea>
            </div>
            {errors.content && (
              <p className="text-red-500 text-xs pl-3">
                {errors.content.message}
              </p>
            )}
          </div>

          <div className="py-1">
            <div className="flex items-center gap-2 pl-2 border py-2 rounded my-3">
              <div className="relative">
                <input type="checkbox" {...register("is_private")} />
              </div>
              <label htmlFor="" className="ml-1 text-sm">
                Mark As Private
              </label>
              {errors.is_private && (
                <p className="text-red-500 text-xs pl-3">
                  {errors.is_private.message}
                </p>
              )}
            </div>
          </div>

          <div className="pb-1">
            <div className="flex items-center gap-2 pl-2 border py-2 rounded mb-3">
              <div className="relative">
                <input type="checkbox" {...register("is_pinned")} />
              </div>
              <label htmlFor="" className="ml-1 text-sm">
                Pin Note
              </label>
              {errors.is_pinned && (
                <p className="text-red-500 text-xs pl-3">
                  {errors.is_pinned.message}
                </p>
              )}
            </div>
          </div>

          <div className="w-full">
            {submitting ? (
              <SpinnerTemp type="spinner-double" size="sm" />
            ) : (
              <Button type="submit" className="w-full">
                Submit
              </Button>
            )}
          </div>
        </div>
      </form>
    </div>
  );
};

export default NotesForm;
