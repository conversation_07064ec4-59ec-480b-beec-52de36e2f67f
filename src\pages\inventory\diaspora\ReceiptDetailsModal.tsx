import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
  Download,
  FileText,
  Mail,
  Printer,
  AlertTriangle,
  Receipt,
} from "lucide-react";
import { format } from "date-fns";
import BaseModal from "@/components/custom/modals/BaseModal";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import { useState } from "react";
import ConfirmationModal from "@/components/custom/modals/ConfirmationModal";
import { Button } from "@/components/ui/button";

export interface PlotBooking {
  id: string;
  plots: string;
  marketer: string;
  marketer_name: string;
  customer_name: string;
  customer: string;
  booking_type: string;
  booking_id: number;
  amount: string;
  transaction_id: string;
  transportation_transaction_id: string;
  description: string;
  office: string;
  proof_of_payment: string;
  upload_time: string;
  status: string;
  deadline: string;
  creation_date: string;
  expected_payment_date: string;
}

interface ReceiptDetailsModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  selectedBooking: any | null;
  handleCancelReciept: () => void;
  setIsOpenCancelModal: (open: boolean) => void;
  handleConfirmCancel: (id: string, plot: string) => void;
  isOpenCancelModal: boolean;
}

export default function ReceiptDetailsModal({
  isOpen,
  onOpenChange,
  selectedBooking,
  handleConfirmCancel,
  setIsOpenCancelModal,
  handleCancelReciept,
  isOpenCancelModal,
}: ReceiptDetailsModalProps) {
  if (!selectedBooking) return null;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "KES",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <BaseModal
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      title={`Diaspora Booking Receipt Details: Plots ${selectedBooking?.plot_no}`}
      size="2xl"
      //   showClose={true}
      position="center"
    >
      <div className="p-6 bg-white rounded-lg dark:bg-gray-800 dark:text-gray-200">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <h2 className="text-xl font-bold text-gray-800 dark:bg-gray-800 dark:text-gray-200">
              Booking ID #{selectedBooking?.booking_id}
            </h2>
            <Badge
              variant="outline"
              className="ml-3 bg-blue-50 text-blue-700 border-blue-200 dark:bg-gray-800 dark:text-gray-200"
            >
              {selectedBooking?.payment_mode}
            </Badge>
          </div>
          <div className="text-gray-500 text-sm">
            Created At:{" "}
            {format(selectedBooking?.created_at, "dd MMM yyyy • HH:mm")}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 dark:bg-gray-800 dark:text-gray-200">
          <div className="space-y-6 ">
            <div className="bg-gray-50 p-5 rounded-lg border border-gray-100 shadow-sm dark:bg-gray-800 dark:text-gray-200">
              <h3 className="text-md font-semibold mb-4 text-gray-800 flex items-center dark:bg-gray-800 dark:text-gray-200">
                <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-2 dark:bg-gray-800 dark:text-gray-200">
                  <FileText className="h-4 w-4 text-blue-600" />
                </div>
                Plot Information
              </h3>

              <div className="space-y-3 ">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600 dark:bg-gray-800 dark:text-gray-200">
                    Plot Number
                  </span>
                  <span className="font-medium text-gray-900 dark:bg-gray-800 dark:text-gray-200">
                    {selectedBooking?.plot_no}
                  </span>
                </div>
                <Separator className="my-2 dark:border border-gray-200" />
                <div className="flex justify-between items-center">
                  <span className="text-gray-600 dark:bg-gray-800 dark:text-gray-200">
                    Marketer
                  </span>
                  <span className="font-medium text-gray-900 dark:bg-gray-800 dark:text-gray-200">
                    {selectedBooking?.marketer}
                  </span>
                </div>

                {/* <div className="flex justify-between items-center">
                  <span className="text-gray-600 dark:bg-gray-800 dark:text-gray-200">Allocated To</span>
                  <span className="font-medium text-gray-900 dark:bg-gray-800 dark:text-gray-200">{selectedBooking?.allocatedMarketer}</span>
                </div> */}
              </div>
            </div>
            <div className="bg-gray-50 p-5 rounded-lg border border-gray-100 shadow-sm dark:bg-gray-800 dark:text-gray-200">
              <h3 className="text-md font-semibold mb-4 text-gray-800 flex items-center dark:bg-gray-800 dark:text-gray-200">
                <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-2 dark:bg-gray-800 dark:text-gray-200">
                  <Receipt className="h-4 w-4 text-green-600" />
                </div>
                Payment Information
              </h3>

              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600 dark:bg-gray-800 dark:text-gray-200">
                    Amount Paid
                  </span>
                  <span className="font-semibold text-gray-900 dark:bg-gray-800 dark:text-gray-200">
                    {formatCurrency(Number(selectedBooking?.amount))}
                  </span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-gray-600 dark:bg-gray-800 dark:text-gray-200">
                    Account No
                  </span>
                  <Badge className="bg-green-50 text-green-700 border-green-200 dark:bg-gray-800 dark:text-gray-200">
                    {selectedBooking?.acc_no}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600 dark:bg-gray-800 dark:text-gray-200">
                    Payment Type
                  </span>
                  <Badge className="bg-blue-50 text-blue-700 border-blue-200 dark:bg-gray-800 dark:text-gray-200">
                    {selectedBooking?.payment_mode}
                  </Badge>
                </div>
              </div>
            </div>
            <div className="space-y-6">
              <div className="bg-gray-50 p-5 rounded-lg border border-gray-100 shadow-sm dark:bg-gray-800 dark:text-gray-200">
                <h3 className="text-md font-semibold mb-4 text-gray-800 flex items-center dark:bg-gray-800 dark:text-gray-200">
                  <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center mr-2 dark:bg-gray-800 dark:text-gray-200 ">
                    <Mail className="h-4 w-4 text-purple-600" />
                  </div>
                  Client Details
                </h3>

                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600 dark:bg-gray-800 dark:text-gray-200">
                      Full Name
                    </span>
                    <span className="font-medium text-gray-900 dark:bg-gray-800 dark:text-gray-200">
                      {selectedBooking?.client_name}
                    </span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-600 dark:bg-gray-800 dark:text-gray-200">
                      Cutomer ID / Passport{" "}
                    </span>
                    <span className="font-medium text-gray-900 dark:bg-gray-800 dark:text-gray-200">
                      {selectedBooking?.client_id_pass}
                    </span>
                  </div>

                  <Separator className="my-2 dark:border border-gray-200" />

                  <div className="flex justify-between items-center">
                    <span className="text-gray-600 dark:bg-gray-800 dark:text-gray-200">
                      Phone
                    </span>
                    <span className="font-medium text-gray-900 dark:bg-gray-800 dark:text-gray-200">
                      {selectedBooking?.client_phone}
                    </span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-600 dark:bg-gray-800 dark:text-gray-200">
                      Email
                    </span>
                    <span className="font-medium text-gray-900 break-all dark:bg-gray-800 dark:text-gray-200">
                      {selectedBooking?.client_email}
                    </span>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-gray-600 dark:bg-gray-800 dark:text-gray-200">
                      Location
                    </span>
                    <span className="font-medium text-gray-900 dark:bg-gray-800 dark:text-gray-200">
                      {selectedBooking?.state}, {selectedBooking?.country}
                    </span>
                  </div>
                </div>
              </div>

              {/* Attachments and Actions */}
              <div className="space-y-6">
                {/* Attachments Section */}
                <div className="bg-gray-50 p-5 rounded-lg border border-gray-100 shadow-sm dark:bg-gray-800 dark:text-gray-200">
                  <h3 className="text-md font-semibold mb-4 text-gray-800 dark:bg-gray-800 dark:text-gray-200">
                    Attachments
                  </h3>

                  <div className="space-y-3">
                    {selectedBooking?.proof_of_payment && (
                      <div className="flex justify-between items-center p-2 bg-white border border-gray-200 rounded-md dark:bg-gray-800 dark:text-gray-200">
                        <div className="flex items-center">
                          <div className="w-8 h-8 bg-blue-50 rounded-md flex items-center justify-center mr-2 dark:bg-gray-800 dark:text-gray-200">
                            <FileText className="h-4 w-4 text-blue-500" />
                          </div>
                          <span className="text-gray-700 dark:bg-gray-800 dark:text-gray-200">
                            proof of payment
                          </span>
                        </div>
                        <PrimaryButton
                          variant="ghost"
                          size="sm"
                          className="text-blue-600 hover:text-blue-800"
                        >
                          <Download className="h-4 w-4 mr-1" /> Download
                        </PrimaryButton>
                      </div>
                    )}

                    {!selectedBooking?.proof_of_payment && (
                      <div className="text-center py-3 text-gray-500 dark:bg-gray-800 dark:text-gray-200">
                        No attachments selectedBookingavailable
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* Action Buttons */}
          <div className="space-y-3 mt-6">
            <PrimaryButton
              className="w-full bg-blue-600 hover:bg-blue-700 flex items-center justify-center"
              size="lg"
            >
              <FileText className="h-4 w-4 mr-2" />
              Generate Receipt
            </PrimaryButton>

            <div className="grid grid-cols-2 gap-3">
              <PrimaryButton
                variant="outline"
                className="flex items-center justify-center dark:border border-gray-200"
                size="sm"
              >
                <Mail className="h-4 w-4 mr-2" />
                Email Client
              </PrimaryButton>

              <PrimaryButton
                variant="outline"
                className="flex items-center justify-center dark:border border-gray-200"
                size="sm"
              >
                <Printer className="h-4 w-4 mr-2" />
                Print Details
              </PrimaryButton>
            </div>

            <Button
              variant="outline"
              className="w-full hover:text-red-600 text-red-600 border-red-200 hover:bg-red-50 flex items-center justify-center"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                handleConfirmCancel(
                  selectedBooking.id,
                  selectedBooking.plot_no
                );
              }}
            >
              <AlertTriangle className="h-4 w-4 mr-2" />
              Cancel Booking
            </Button>
          </div>
        </div>

        <ConfirmationModal
          isOpen={isOpenCancelModal}
          onOpenChange={setIsOpenCancelModal}
          title="Cancel Receipt"
          description={`Are you sure you want to cancel this receipt for plot ${selectedBooking?.plot_no}?`}
          // confirmButtonLabel="Cancel Receipt"
          // confirmButtonColor="red"
          onConfirm={handleCancelReciept}
        />
      </div>
    </BaseModal>
  );
}
