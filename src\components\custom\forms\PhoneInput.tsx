import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { countries, defaultCountry, type Country } from '@/data/countries';
import { Phone } from 'lucide-react';

interface PhoneInputProps {
  value?: string;
  onChange?: (value: string) => void;
  onCountryChange?: (country: Country) => void;
  label?: string;
  placeholder?: string;
  required?: boolean;
  className?: string;
  defaultCountryCode?: string;
}

const PhoneInput: React.FC<PhoneInputProps> = ({
  value = '',
  onChange,
  onCountryChange,
  label = 'Phone Number',
  placeholder = '712345678',
  required = false,
  className = '',
  defaultCountryCode = 'KE'
}) => {
  const [selectedCountry, setSelectedCountry] = useState<Country>(
    countries.find(c => c.code === defaultCountryCode) || defaultCountry
  );

  const handleCountryChange = (countryCode: string) => {
    const country = countries.find(c => c.code === countryCode);
    if (country) {
      setSelectedCountry(country);
      onCountryChange?.(country);
    }
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const phoneNumber = e.target.value.replace(/\D/g, ''); // Remove non-digits
    onChange?.(phoneNumber);
  };

  const formatDisplayValue = () => {
    if (!value) return '';
    // Format the phone number for display
    const digits = value.replace(/\D/g, '');
    if (digits.length <= 3) return digits;
    if (digits.length <= 6) return `${digits.slice(0, 3)} ${digits.slice(3)}`;
    if (digits.length <= 9) return `${digits.slice(0, 3)} ${digits.slice(3, 6)} ${digits.slice(6)}`;
    return `${digits.slice(0, 3)} ${digits.slice(3, 6)} ${digits.slice(6, 9)}`;
  };

  return (
    <div className={`space-y-2 ${className}`}>
      {label && (
        <Label htmlFor="phone-input" className="text-sm font-medium">
          {label} {required && <span className="text-red-500">*</span>}
        </Label>
      )}
      <div className="flex gap-2">
        {/* Country Selector */}
        <Select value={selectedCountry.code} onValueChange={handleCountryChange}>
          <SelectTrigger className="w-[140px]">
            <SelectValue>
              <div className="flex items-center gap-2">
                <span className="text-lg">{selectedCountry.flag}</span>
                <span className="text-sm font-medium">{selectedCountry.phoneCode}</span>
              </div>
            </SelectValue>
          </SelectTrigger>
          <SelectContent className="max-h-[300px]">
            {countries.map((country) => (
              <SelectItem key={country.code} value={country.code}>
                <div className="flex items-center gap-2">
                  <span className="text-lg">{country.flag}</span>
                  <span className="text-sm">{country.phoneCode}</span>
                  <span className="text-sm text-gray-600">{country.name}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Phone Number Input */}
        <div className="flex-1 relative">
          <Phone className="absolute left-3 top-3 w-4 h-4 text-gray-400" />
          <Input
            id="phone-input"
            type="tel"
            placeholder={placeholder}
            value={formatDisplayValue()}
            onChange={handlePhoneChange}
            className="pl-10"
            required={required}
          />
        </div>
      </div>
      
      {/* Full Phone Number Display */}
      {value && (
        <div className="text-xs text-gray-500 mt-1">
          Full number: {selectedCountry.phoneCode} {formatDisplayValue()}
        </div>
      )}
    </div>
  );
};

export default PhoneInput;
