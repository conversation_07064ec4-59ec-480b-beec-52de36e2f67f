import { useState } from "react";
import BaseModal from "@/components/custom/modals/BaseModal";
import { ColumnDef } from "@tanstack/react-table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Settings, AlertCircle, Loader2 } from "lucide-react";
import { DataTable } from "@/components/custom/tables/Table1";
import { useGetHQKarenQuery } from "@/redux/slices/hrDashboardApiSlice";
import { Badge } from "@/components/custom/badges/badges";
import { cn } from "@/lib/utils";
import { MarketerReport } from "@/types/marketer";

interface OfficeTableModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  office?: string;
  start_date?: string;
  end_date?: string;
  period_name?: string;
}

export default function OfficeTableModal({
  open,
  onOpenChange,
  office,
  start_date,
  end_date,
  period_name
}: OfficeTableModalProps) {
  const [officeFilter, setOfficeFilter] = useState("ALL");
  const [periodFilter, setPeriodFilter] = useState("ALL");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(50);

  // Fetch HQ Karen reports data using the provided parameters
  const {
    data: reportsData,
    isLoading,
    error
  } = useGetHQKarenQuery({
    office: officeFilter,
    period: periodFilter,
    page: currentPage,
    page_size: pageSize
  });

  // Extract the results array from the API response and filter by date range
  const allData: MarketerReport[] = reportsData?.results || [];

  // Filter data to show ALL marketers within the specified date range
  const data: MarketerReport[] = allData.filter(record => {
    if (!start_date || !end_date) return true; // If no date range specified, show all

    const recordStartDate = new Date(record.period_start_date);
    const recordEndDate = new Date(record.period_end_date);
    const filterStartDate = new Date(start_date);
    const filterEndDate = new Date(end_date);

    // Check if the record's period overlaps with the filter period
    return recordStartDate <= filterEndDate && recordEndDate >= filterStartDate;
  });

  const columns: ColumnDef<MarketerReport>[] = [
    {
      accessorKey: "marketer_no",
      header: "Marketer No",
      cell: (info) => info.getValue(),
      enableColumnFilter: true,
      filterFn: "includesString",
    },
    {
      accessorKey: "marketer_name",
      header: "Marketer Name",
      cell: (info) => info.getValue(),
      enableColumnFilter: true,
      filterFn: "includesString",
    },
    {
      accessorKey: "title",
      header: "Title",
      cell: (info) => info.getValue(),
      enableColumnFilter: true,
      filterFn: "includesString",
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: (info) => {
        const value = info.getValue() as string;
        return (
          <Badge
            variant={value === "Open" ? "default" : "secondary"}
            className={cn(
              value === "Open"
                ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                : "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"
            )}
          >
            {value}
          </Badge>
        );
      },
      enableColumnFilter: true,
      filterFn: "includesString",
    },
    {
      accessorKey: "period_start_date",
      header: "Period Start",
      cell: (info) => {
        const value = info.getValue() as string;
        return new Date(value).toLocaleDateString('en-KE');
      },
      enableColumnFilter: true,
      filterFn: "includesString",
    },
    {
      accessorKey: "period_end_date",
      header: "Period End",
      cell: (info) => {
        const value = info.getValue() as string;
        return new Date(value).toLocaleDateString('en-KE');
      },
      enableColumnFilter: true,
      filterFn: "includesString",
    },
    {
      accessorKey: "monthly_target",
      header: "Monthly Target",
      cell: (info) => {
        const value = info.getValue() as number;
        return new Intl.NumberFormat('en-KE', {
          style: 'currency',
          currency: 'KES',
          minimumFractionDigits: 0,
        }).format(value);
      },
      enableColumnFilter: true,
      filterFn: "includesString",
    },
    {
      accessorKey: "daily_target",
      header: "Daily Target",
      cell: (info) => {
        const value = info.getValue() as number;
        return new Intl.NumberFormat('en-KE', {
          style: 'currency',
          currency: 'KES',
          minimumFractionDigits: 2,
        }).format(value);
      },
      enableColumnFilter: true,
      filterFn: "includesString",
    },
    {
      accessorKey: "MIB_achieved",
      header: "MIB Achieved",
      cell: (info) => {
        const value = info.getValue() as number;
        return new Intl.NumberFormat('en-KE', {
          style: 'currency',
          currency: 'KES',
          minimumFractionDigits: 0,
        }).format(value);
      },
      enableColumnFilter: true,
      filterFn: "includesString",
    },
    {
      accessorKey: "MIB_Perfomance",
      header: "Performance %",
      cell: (info) => {
        const value = info.getValue() as number;
        const progressColor = value >= 100 ? "bg-green-500" : value >= 75 ? "bg-blue-500" : value >= 50 ? "bg-yellow-500" : "bg-red-500";
        return (
          <div className="flex items-center space-x-2">
            <Badge
              className={cn("text-white", progressColor)}
              variant="secondary"
            >
              {value.toFixed(2)}%
            </Badge>
          </div>
        );
      },
      enableColumnFilter: true,
      filterFn: "includesString",
    },
    {
      accessorKey: "commission_rate",
      header: "Commission Rate",
      cell: (info) => {
        const value = info.getValue() as number;
        return `${(value * 100).toFixed(1)}%`;
      },
      enableColumnFilter: true,
      filterFn: "includesString",
    },
    {
      accessorKey: "commission_payable",
      header: "Commission Payable",
      cell: (info) => {
        const value = info.getValue() as number;
        return new Intl.NumberFormat('en-KE', {
          style: 'currency',
          currency: 'KES',
          minimumFractionDigits: 2,
        }).format(value);
      },
      enableColumnFilter: true,
      filterFn: "includesString",
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger className="">
            <Settings size={20} />
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                console.log(`View details for ${row.original.marketer_no}`);
              }}
            >
              View Details
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={(e) => {
                e.stopPropagation();
                console.log(`Edit marketer ${row.original.marketer_no}`);
              }}
            >
              Edit
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
      enableColumnFilter: false,
    },
  ];

  return (
    <BaseModal
      open={open}
      onOpenChange={onOpenChange}
      title={`Marketer Performance - ${period_name || 'Office Reports'}`}
      description={`View marketer performance for ${office || 'selected office'} ${period_name ? `during ${period_name}` : ''}`}
      className="max-w-[95vw] w-full"
      size="full"
    >
      <div className="p-4 bg-white dark:bg-gray-900 rounded-md max-w-full overflow-hidden">
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin text-green-600 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-400">Loading marketer data...</p>
            </div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <AlertCircle className="h-8 w-8 text-red-600 mx-auto mb-4" />
              <p className="text-red-600 dark:text-red-400">Failed to load marketer data</p>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">Please try again later</p>
            </div>
          </div>
        ) : !office || !start_date || !end_date ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <AlertCircle className="h-8 w-8 text-orange-600 mx-auto mb-4" />
              <p className="text-orange-600 dark:text-orange-400">Missing required parameters</p>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">Office and date range are required</p>
            </div>
          </div>
        ) : (
          <div className="w-full max-w-full overflow-hidden">
            <div className="table-mobile-scroll">
              <DataTable<MarketerReport>
                data={data}
                columns={columns}
                title={`Marketer Performance - ${office} (${reportsData?.count || 0} marketers)`}
                enableExportToExcel={true}
                enablePrintPdf={true}
                enableColumnFilters={true}
                enablePagination={true}
                enableSorting={true}
                enableToolbar={true}
                containerClassName="w-full bg-white dark:bg-gray-900 rounded-lg shadow-md"
              tableClassName="w-full border-collapse border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 text-sm text-left text-gray-700 dark:text-gray-300"
              tHeadClassName="bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 font-semibold"
              tHeadCellsClassName="px-4 py-2 border-b border-gray-200 dark:border-gray-700"
              tBodyClassName="divide-y divide-gray-200 dark:divide-gray-700"
              tBodyTrClassName="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
              tBodyCellsClassName="px-4 py-2"
            />
            </div>
          </div>
        )}
      </div>
    </BaseModal>
  );
}