import React from 'react';
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useCustomerPermissions } from '@/hooks/useCustomerPermissions';
import { Shield, AlertTriangle, Bug } from 'lucide-react';
import { CUSTOMER_PERMISSION_CODES } from '@/utils/customerPermissions';

const CustomerPermissionIndicator: React.FC = () => {
  const {
    hasAnyCustomerAccess,
    permissionLevel,
    apiParams,
    userPermissions,
    userDetails,
    // Keep these for backward compatibility with the indicator
    canViewHQCustomers,
    canViewKarenCustomers,
    canViewAllOfficesCustomers,
    canViewOwnCustomers,
    canViewAllMarketersCustomers,
    canViewDiasporaTeamCustomers,
    canViewDigitalTeamCustomers,
    canViewTelemarketingTeamCustomers,
    canViewOtherTeamCustomers,
    canViewAllTeamsCustomers,
    canViewDiasporaRegionCustomers,
    canViewAllDiasporaRegionsCustomers
  } = useCustomerPermissions();

  // Debug section to show raw permissions
  const DebugPermissions = () => {
    // Extract normalized permissions for display
    const normalizedPermissions = userPermissions.map(p => 
      typeof p === 'string' ? parseInt(p, 10) : p
    );
    
    return (
      <div className="mt-4 p-3 border border-gray-300 rounded bg-gray-50">
        <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
          <Bug className="h-4 w-4 mr-1" /> Debug Information
        </h4>
        <div className="text-xs">
          <p><strong>Raw User Permissions Array:</strong> {JSON.stringify(userPermissions)}</p>
          <p><strong>Normalized Permissions:</strong> {JSON.stringify(normalizedPermissions)}</p>
          <p><strong>Has Permission 2001 (VIEW_CUSTOMER_HQ):</strong> {normalizedPermissions.includes(2001) ? "Yes" : "No"}</p>
          <p><strong>Has Permission 2002 (VIEW_CUSTOMER_KAREN):</strong> {normalizedPermissions.includes(2002) ? "Yes" : "No"}</p>
          <p><strong>hasAnyCustomerAccess:</strong> {hasAnyCustomerAccess ? "Yes" : "No"}</p>
          <p><strong>canViewHQCustomers:</strong> {canViewHQCustomers ? "Yes" : "No"}</p>
          <p><strong>canViewKarenCustomers:</strong> {canViewKarenCustomers ? "Yes" : "No"}</p>
          <p><strong>API Params:</strong> {JSON.stringify(apiParams)}</p>
          <p><strong>Permission Level:</strong> {permissionLevel}</p>
          <p><strong>User Details:</strong> {JSON.stringify(userDetails)}</p>
        </div>
      </div>
    );
  };

  return (
    <Card className={hasAnyCustomerAccess ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"}>
      <CardHeader className="pb-3">
        <CardTitle className={`flex items-center gap-2 ${hasAnyCustomerAccess ? "text-green-700" : "text-red-700"}`}>
          {hasAnyCustomerAccess ? (
            <>
              <Shield className="h-4 w-4" />
              Customer Access Level: {permissionLevel}
            </>
          ) : (
            <>
              <AlertTriangle className="h-4 w-4" />
              No Customer Access
            </>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {!hasAnyCustomerAccess ? (
          <p className="text-sm text-red-600">
            You don't have permission to view customer data. Contact your administrator to request access.
          </p>
        ) : (
          <div className="space-y-3">
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">Your Customer Permissions:</h4>
              <div className="flex flex-wrap gap-2">
                {/* Office Permissions */}
                {canViewAllOfficesCustomers && (
                  <Badge variant="outline" className="bg-blue-100 text-blue-800">All Offices</Badge>
                )}
                {canViewHQCustomers && !canViewAllOfficesCustomers && (
                  <Badge variant="outline" className="bg-blue-100 text-blue-800">HQ Office</Badge>
                )}
                {canViewKarenCustomers && !canViewAllOfficesCustomers && (
                  <Badge variant="outline" className="bg-blue-100 text-blue-800">Karen Office</Badge>
                )}

                {/* Marketer Permissions */}
                {canViewAllMarketersCustomers && (
                  <Badge variant="outline" className="bg-green-100 text-green-800">All Marketers</Badge>
                )}
                {canViewOwnCustomers && !canViewAllMarketersCustomers && (
                  <Badge variant="outline" className="bg-green-100 text-green-800">Own Customers Only</Badge>
                )}

                {/* Team Permissions */}
                {canViewAllTeamsCustomers && (
                  <Badge variant="outline" className="bg-purple-100 text-purple-800">All Teams</Badge>
                )}
                {canViewDiasporaTeamCustomers && !canViewAllTeamsCustomers && (
                  <Badge variant="outline" className="bg-purple-100 text-purple-800">Diaspora Team</Badge>
                )}
                {canViewDigitalTeamCustomers && !canViewAllTeamsCustomers && (
                  <Badge variant="outline" className="bg-purple-100 text-purple-800">Digital Team</Badge>
                )}
                {canViewTelemarketingTeamCustomers && !canViewAllTeamsCustomers && (
                  <Badge variant="outline" className="bg-purple-100 text-purple-800">Telemarketing Team</Badge>
                )}
                {canViewOtherTeamCustomers && !canViewAllTeamsCustomers && (
                  <Badge variant="outline" className="bg-purple-100 text-purple-800">Other Team</Badge>
                )}

                {/* Diaspora Region Permissions */}
                {canViewAllDiasporaRegionsCustomers && (
                  <Badge variant="outline" className="bg-amber-100 text-amber-800">All Diaspora Regions</Badge>
                )}
                {canViewDiasporaRegionCustomers && !canViewAllDiasporaRegionsCustomers && (
                  <Badge variant="outline" className="bg-amber-100 text-amber-800">Your Diaspora Region</Badge>
                )}
              </div>
            </div>

            {userDetails && (
              <div className="text-xs text-gray-500 pt-2 border-t">
                <p>Employee: {userDetails.employee_no} | Office: {userDetails.office} | Team: {userDetails.team}</p>
              </div>
            )}
          </div>
        )}
        
        {/* Uncomment for debugging */}
        {/* <DebugPermissions /> */}
      </CardContent>
    </Card>
  );
};

export default CustomerPermissionIndicator;