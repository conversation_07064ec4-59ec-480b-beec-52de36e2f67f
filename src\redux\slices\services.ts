import { contentHeader, noAuthHeader } from "@/utils/header";
import { apiSlice } from "../apiSlice";
import {
  NotesApiResponse,
  NoteTypes,
  NotesFilters,
  CreateNoteRequest,
  UpdateNoteRequest,
  NotesMetrics,
} from "@/types/notes";

export const servicesApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Engagements
    getEngagements: builder.query({
      query: (params) => ({
        url: "/services/engagements/",
        method: "GET",
        params,
      }),
      providesTags: ["Services"],
    }),

    AddEngagement: builder.mutation({
      query: (data) => ({
        url: "/services/engagements/",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["Services"],
    }),

    // Reminders
    getReminders: builder.query({
      query: (params) => ({
        url: "/services/reminders/",
        method: "GET",
        params,
      }),
      providesTags: ["Services"],
    }),

    AddReminder: builder.mutation({
      query: (data) => ({
        url: "/services/reminders/",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["Services"],
    }),

    EditReminder: builder.mutation({
      query: (data: any) => ({
        url: "/services/reminder/" + data.reminder_id + "/",
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: ["Services"],
    }),

    // Complaints
    getComplaints: builder.query({
      query: (params) => ({
        url: "/services/complaints/",
        method: "GET",
        params,
      }),
      providesTags: ["Services"],
    }),

    AddComplaint: builder.mutation({
      query: (data: any) => ({
        url: "/services/complaints/",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["Services"],
    }),

    // Enhanced Notes API with classification and filtering
    getNotes: builder.query({
      query: (params) => ({
        url: "/services/notes/",
        method: "GET",
        params,
        headers: contentHeader(),
      }),
      transformResponse: (response: NotesApiResponse) => {
        console.log("Notes API Response:", response);
        return response;
      },
      transformErrorResponse: (error) => {
        console.error("Notes API Error:", error);
        return error;
      },
      providesTags: ["Services", "Notes"],
    }),

    getNotesByEntity: builder.query<
      NotesApiResponse,
      { entity_type: string; entity_id?: string; filters?: NotesFilters }
    >({
      query: ({ entity_type, entity_id, filters }) => ({
        url: "/services/notes/",
        method: "GET",
        params: {
          entity_type,
          entity_id,
          ...filters,
        },
        headers: noAuthHeader(),
      }),
      providesTags: ["Services", "Notes"],
    }),

    getCustomerNotes: builder.query<
      NotesApiResponse,
      { customer_id?: string; filters?: NotesFilters }
    >({
      query: ({ customer_id, filters }) => ({
        url: "/services/notes/",
        method: "GET",
        params: {
          entity_type: "customer",
          entity_id: customer_id,
          ...filters,
        },
        headers: noAuthHeader(),
      }),
      providesTags: ["Services", "Notes"],
    }),

    getProspectNotes: builder.query<
      NotesApiResponse,
      { prospect_id?: string; filters?: NotesFilters }
    >({
      query: ({ prospect_id, filters }) => ({
        url: "/services/notes/",
        method: "GET",
        params: {
          entity_type: "prospect",
          entity_id: prospect_id,
          ...filters,
        },
        headers: noAuthHeader(),
      }),
      providesTags: ["Services", "Notes"],
    }),

    getSalesNotes: builder.query<
      NotesApiResponse,
      { sales_id?: string; filters?: NotesFilters }
    >({
      query: ({ sales_id, filters }) => ({
        url: "/services/notes/",
        method: "GET",
        params: {
          entity_type: "leadfile",
          entity_id: sales_id,
          ...filters,
        },
        headers: noAuthHeader(),
      }),
      providesTags: ["Services", "Notes"],
    }),

    getNotesMetrics: builder.query<NotesMetrics, void>({
      query: () => ({
        url: "/services/notes/metrics/",
        method: "GET",
        headers: noAuthHeader(),
      }),
      providesTags: ["Services", "Notes"],
    }),

    getNoteById: builder.query<NoteTypes, string>({
      query: (noteId) => ({
        url: `/services/notes/${noteId}/`,
        method: "GET",
        headers: noAuthHeader(),
      }),
      providesTags: ["Services", "Notes"],
    }),

    AddNote: builder.mutation<NoteTypes, CreateNoteRequest>({
      query: (data) => ({
        url: "/services/notes/",
        method: "POST",
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Services", "Notes"],
    }),

    updateNote: builder.mutation<NoteTypes, UpdateNoteRequest>({
      query: ({ note_id, ...data }) => ({
        url: `/services/notes/${note_id}/`,
        method: "PATCH",
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Services", "Notes"],
    }),

    // Provide only ONE update endpoint. If you need a separate "EditNote", name it differently and give a comment why.
    // If you want to keep EditNote (untyped), you could do:
    // editNote: builder.mutation<any, any>({
    //   query: (data: any) => ({
    //     url: `/services/notes/${data.note_id}/`,
    //     method: "PATCH",
    //     body: data,
    //   }),
    //   invalidatesTags: ["Services", "Notes"],
    // }),

    deleteNote: builder.mutation<void, string>({
      query: (noteId) => ({
        url: `/services/notes/${noteId}/`,
        method: "DELETE",
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Services", "Notes"],
    }),

    bulkUpdateNotes: builder.mutation<
      void,
      { note_ids: string[]; updates: Partial<NoteTypes> }
    >({
      query: ({ note_ids, updates }) => ({
        url: "/services/notes/bulk-update/",
        method: "POST",
        body: { note_ids, updates },
        headers: contentHeader(),
      }),
      invalidatesTags: ["Services", "Notes"],
    }),

    searchNotes: builder.query<
      NotesApiResponse,
      { query: string; filters?: NotesFilters }
    >({
      query: ({ query, filters }) => ({
        url: "/services/notes/search/",
        method: "GET",
        params: {
          q: query,
          ...filters,
        },
        headers: noAuthHeader(),
      }),
      providesTags: ["Services", "Notes"],
    }),

    EditNote: builder.mutation({
      query: (data: any) => ({
        url: "/services/notes/" + data.note_id + "/",
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: ["Services"],
    }),

    getFlags: builder.query({
      query: (params) => ({
        url: "/services/flags/",
        method: "GET",
        params,
      }),
      providesTags: ["Services"],
    }),

    AddFlag: builder.mutation({
      query: (data: any) => ({
        url: "/services/flags/",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["Services"],
    }),

    // Feedbacks
    getFeedbacks: builder.query({
      query: (params) => ({
        url: "/services/feedback/",
        method: "GET",
        params,
      }),
      providesTags: ["Services"],
    }),

    AddFeedback: builder.mutation({
      query: (data: any) => ({
        url: "/services/feedback/",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["Services"],
    }),

    // Notifications
    getNotifications: builder.query({
      query: (params) => ({
        url: "/services/notifications/",
        method: "GET",
        params,
      }),
      providesTags: ["Services"],
    }),

    AddNotification: builder.mutation({
      query: (data: any) => ({
        url: "/services/notifications/",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["Services"],
    }),

    EditNotification: builder.mutation({
      query: (data: any) => ({
        url: "/services/notifications/" + data.notification_id + "/",
        method: "PATCH",
        body: data,
      }),
      invalidatesTags: ["Services"],
    }),

    // Tickets
    getTickets: builder.query({
      query: (params) => ({
        url: "/services/tickets/",
        method: "GET",
        params,
      }),
      providesTags: ["Services"],
    }),

    AddTicket: builder.mutation({
      query: (data: any) => ({
        url: "/services/tickets/",
        method: "POST",
        body: data,
      }),
      invalidatesTags: ["Services"],
    }),
  }),
});

// All hooks (no change needed)
export const {
  // Mutations
  useAddEngagementMutation,
  useAddReminderMutation,
  useAddComplaintMutation,
  useAddNoteMutation,
  useUpdateNoteMutation,
  useDeleteNoteMutation,
  useBulkUpdateNotesMutation,
  useAddFlagMutation,
  useAddFeedbackMutation,
  useAddNotificationMutation,
  useAddTicketMutation,

  useEditNoteMutation,
  useEditNotificationMutation,
  useEditReminderMutation,

  // Queries
  useGetEngagementsQuery,
  useLazyGetEngagementsQuery,
  useLazyGetRemindersQuery,
  useLazyGetComplaintsQuery,
  useLazyGetFeedbacksQuery,
  useLazyGetFlagsQuery,

  useGetNotesQuery,
  useLazyGetNotesQuery,
  useGetNotesByEntityQuery,
  useLazyGetNotesByEntityQuery,
  useGetCustomerNotesQuery,
  useLazyGetCustomerNotesQuery,
  useGetProspectNotesQuery,
  useLazyGetProspectNotesQuery,
  useGetSalesNotesQuery,
  useLazyGetSalesNotesQuery,
  useGetNotesMetricsQuery,
  useGetNoteByIdQuery,
  useSearchNotesQuery,
  useLazySearchNotesQuery,

  useLazyGetNotificationsQuery,
  useLazyGetTicketsQuery,
} = servicesApiSlice;
