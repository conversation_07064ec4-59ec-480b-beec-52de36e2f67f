import { contentHeader, noAuthHeader } from "@/utils/header";
import { apiSlice } from "../apiSlice";

export const notificationsApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getNotifications: builder.query({
      query: (params) => ({
        url: "/services/notifications/",
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      transformResponse: (response) => {
        // The API returns data nested under response.data
        const apiData = response.data || {};
        return {
          results: apiData.results || [],
          count: apiData.count || 0,
          current_page: apiData.current_page || 1,
          num_pages: apiData.num_pages || 1,
          total_data: apiData.count || 0,
          per_page: apiData.per_page || Math.ceil(
            (apiData.count || 0) / (apiData.num_pages || 1)
          ),
        };
      },
      transformErrorResponse: (error) => {
        console.error("Notifications API Error:", error);
        return error;
      },
      providesTags: ["Notifications"],
    }),

    getUnreadNotifications: builder.query({
      query: (params) => ({
        url: "/services/notifications/unread/",
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      transformResponse: (response) => {
        // The API returns data nested under response.data
        const apiData = response.data || {};
        return {
          results: apiData.results || [],
          count: apiData.count || 0,
          current_page: apiData.current_page || 1,
          num_pages: apiData.num_pages || 1,
          total_data: apiData.count || 0,
          per_page: apiData.per_page || Math.ceil(
            (apiData.count || 0) / (apiData.num_pages || 1)
          ),
        };
      },
      transformErrorResponse: (error) => {
        console.error("Unread Notifications API Error:", error);
        return error;
      },
      providesTags: ["Notifications"],
    }),

    getNotificationDetails: builder.query({
      query: (notificationId) => ({
        url: `/services/notifications/${notificationId}/`,
        method: "GET",
        headers: noAuthHeader(),
      }),
      providesTags: ["Notifications"],
    }),

    createNotification: builder.mutation({
      query: (data) => ({
        url: "/services/notifications/",
        method: "POST",
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Notifications"],
    }),

    updateNotification: builder.mutation({
      query: ({ notificationId, data }) => ({
        url: `/services/notifications/${notificationId}/`,
        method: "PUT",
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Notifications"],
    }),

    partialUpdateNotification: builder.mutation({
      query: (data) => ({
        url: `/services/notifications/${data?.notification_id}/`,
        method: "PATCH",
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Notifications"],
    }),

    deleteNotification: builder.mutation({
      query: (notificationId) => ({
        url: `/services/notifications/${notificationId}/`,
        method: "DELETE",
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Notifications"],
    }),

    markNotificationAsRead: builder.mutation({
      query: (notificationId) => ({
        url: `/services/notifications/${notificationId}/mark_read/`,
        method: "PATCH",
        headers: contentHeader(),
      }),
      invalidatesTags: ["Notifications"],
    }),
  }),
});

export const {
  useGetNotificationsQuery,
  useGetUnreadNotificationsQuery,
  useGetNotificationDetailsQuery,
  useCreateNotificationMutation,
  useUpdateNotificationMutation,
  usePartialUpdateNotificationMutation,
  useDeleteNotificationMutation,
  useMarkNotificationAsReadMutation,
} = notificationsApiSlice;