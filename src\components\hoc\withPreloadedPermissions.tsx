import React, { ComponentType } from 'react';
import { usePreloadPermissions } from '@/hooks/usePreloadPermissions';
import { Card, CardContent } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';

/**
 * Higher-order component that ensures permissions are loaded before rendering the wrapped component
 * 
 * @param WrappedComponent - The component to wrap
 * @returns A new component that loads permissions before rendering the wrapped component
 */
export const withPreloadedPermissions = <P extends object>(
  WrappedComponent: ComponentType<P>
) => {
  // Create a new component that wraps the original component
  const WithPreloadedPermissions: React.FC<P> = (props) => {
    // Use the preload permissions hook to load all permissions
    const { 
      isLoading, 
      salesPermissions, 
      customerPermissions, 
      prospectPermissions 
    } = usePreloadPermissions();

    // Show loading indicator while permissions are being loaded
    if (isLoading) {
      return (
        <div className="flex items-center justify-center min-h-[50vh]">
          <Card className="w-full max-w-md">
            <CardContent className="p-6 flex flex-col items-center">
              <Loader2 className="h-8 w-8 text-primary animate-spin mb-4" />
              <p className="text-center text-muted-foreground">
                Loading permissions...
              </p>
            </CardContent>
          </Card>
        </div>
      );
    }

    // Once permissions are loaded, render the wrapped component with all permissions
    return (
      <WrappedComponent 
        {...props} 
        salesPermissions={salesPermissions}
        customerPermissions={customerPermissions}
        prospectPermissions={prospectPermissions}
      />
    );
  };

  // Set display name for debugging
  const wrappedComponentName = WrappedComponent.displayName || WrappedComponent.name || 'Component';
  WithPreloadedPermissions.displayName = `withPreloadedPermissions(${wrappedComponentName})`;

  return WithPreloadedPermissions;
};

export default withPreloadedPermissions;