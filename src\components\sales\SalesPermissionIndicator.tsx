import React from 'react';
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useSalesPermissions } from '@/hooks/useSalesPermissions';
import { Shield, AlertTriangle, Bug } from 'lucide-react';
import { SALES_PERMISSION_CODES } from '@/utils/salesPermissions';

const SalesPermissionIndicator: React.FC = () => {
  const {
    hasAnySalesAccess,
    permissionLevel,
    apiParams,
    userPermissions,
    userDetails,
    // Keep these for backward compatibility with the indicator
    canViewHQSales,
    canViewKarenSales,
    canViewAllOffices,
    canViewOwnSales,
    canViewAllMarketers,
    canViewDiasporaTeam,
    canViewDigitalTeam,
    canViewTelemarketingTeam,
    canViewOtherTeam,
    canViewAllTeams,
    canViewDiasporaRegion,
    canViewAllDiasporaRegions
  } = useSalesPermissions();

  // Debug section to show raw permissions
  const DebugPermissions = () => {
    // Extract normalized permissions for display
    const normalizedPermissions = userPermissions.map(p => 
      typeof p === 'string' ? parseInt(p, 10) : p
    );
    
    return (
      <div className="mt-4 p-3 border border-gray-300 rounded bg-gray-50">
        <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
          <Bug className="h-4 w-4 mr-1" /> Debug Information
        </h4>
        <div className="text-xs">
          <p><strong>Raw User Permissions Array:</strong> {JSON.stringify(userPermissions)}</p>
          <p><strong>Normalized Permissions:</strong> {JSON.stringify(normalizedPermissions)}</p>
          <p><strong>Has Permission 1001 (VIEW_SALES_HQ):</strong> {normalizedPermissions.includes(1001) ? "Yes" : "No"}</p>
          <p><strong>Has Permission 1002 (VIEW_SALES_KAREN):</strong> {normalizedPermissions.includes(1002) ? "Yes" : "No"}</p>
          <p><strong>hasAnySalesAccess:</strong> {hasAnySalesAccess ? "Yes" : "No"}</p>
          <p><strong>canViewHQSales:</strong> {canViewHQSales ? "Yes" : "No"}</p>
          <p><strong>canViewKarenSales:</strong> {canViewKarenSales ? "Yes" : "No"}</p>
          <p><strong>API Params:</strong> {JSON.stringify(apiParams)}</p>
          <p><strong>Permission Level:</strong> {permissionLevel}</p>
          <p><strong>User Details:</strong> {JSON.stringify(userDetails)}</p>
        </div>
      </div>
    );
  };

  return (
    <Card className={hasAnySalesAccess ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"}>
      <CardHeader className="pb-3">
        <CardTitle className={`flex items-center gap-2 ${hasAnySalesAccess ? "text-green-700" : "text-red-700"}`}>
          {hasAnySalesAccess ? (
            <>
              <Shield className="h-4 w-4" />
              Sales Access Level: {permissionLevel}
            </>
          ) : (
            <>
              <AlertTriangle className="h-4 w-4" />
              No Sales Access
            </>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {!hasAnySalesAccess ? (
          <p className="text-sm text-red-600">
            You don't have permission to view sales data. Contact your administrator to request access.
          </p>
        ) : (
          <div className="space-y-3">
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">Your Sales Permissions:</h4>
              <div className="flex flex-wrap gap-2">
                {/* Office Permissions */}
                {canViewAllOffices && (
                  <Badge variant="outline" className="bg-blue-100 text-blue-800">All Offices</Badge>
                )}
                {canViewHQSales && !canViewAllOffices && (
                  <Badge variant="outline" className="bg-blue-100 text-blue-800">HQ Office</Badge>
                )}
                {canViewKarenSales && !canViewAllOffices && (
                  <Badge variant="outline" className="bg-blue-100 text-blue-800">Karen Office</Badge>
                )}

                {/* Marketer Permissions */}
                {canViewAllMarketers && (
                  <Badge variant="outline" className="bg-green-100 text-green-800">All Marketers</Badge>
                )}
                {canViewOwnSales && !canViewAllMarketers && (
                  <Badge variant="outline" className="bg-green-100 text-green-800">Own Sales Only</Badge>
                )}

                {/* Team Permissions */}
                {canViewAllTeams && (
                  <Badge variant="outline" className="bg-purple-100 text-purple-800">All Teams</Badge>
                )}
                {canViewDiasporaTeam && !canViewAllTeams && (
                  <Badge variant="outline" className="bg-purple-100 text-purple-800">Diaspora Team</Badge>
                )}
                {canViewDigitalTeam && !canViewAllTeams && (
                  <Badge variant="outline" className="bg-purple-100 text-purple-800">Digital Team</Badge>
                )}
                {canViewTelemarketingTeam && !canViewAllTeams && (
                  <Badge variant="outline" className="bg-purple-100 text-purple-800">Telemarketing Team</Badge>
                )}
                {canViewOtherTeam && !canViewAllTeams && (
                  <Badge variant="outline" className="bg-purple-100 text-purple-800">Other Team</Badge>
                )}

                {/* Diaspora Region Permissions */}
                {canViewAllDiasporaRegions && (
                  <Badge variant="outline" className="bg-amber-100 text-amber-800">All Diaspora Regions</Badge>
                )}
                {canViewDiasporaRegion && !canViewAllDiasporaRegions && (
                  <Badge variant="outline" className="bg-amber-100 text-amber-800">Your Diaspora Region</Badge>
                )}
              </div>
            </div>

            {userDetails && (
              <div className="text-xs text-gray-500 pt-2 border-t">
                <p>Employee: {userDetails.employee_no} | Office: {userDetails.office} | Team: {userDetails.team}</p>
              </div>
            )}
          </div>
        )}
        
        {/* Always show debug information */}
        <DebugPermissions />
      </CardContent>
    </Card>
  );
};

export default SalesPermissionIndicator;
