import { Screen } from '@/app-components/layout/screen'
import { useState } from 'react';
import { registerReportComponent, ReportConfig, ReportsModal } from '../../../components/reports/ReportsModal';
import { ColumnDef } from '@tanstack/react-table';
import { DataTable } from '@/components/custom/tables/Table1';

const SalesReportDemo = () => {

  const [isModalOpen, setIsModalOpen] = useState(false);

  // Example config - notice we only pass the component type string
  const reportConfig: ReportConfig = {
    moduleId: 'sales',
    endpoint: '/logistics/vehicles',
    params: { date: '2025-05-13' },
    title: 'Sales Report',
    componentType: 'sales' // Just a string identifier, not a component
  };

  return (
    <Screen>
      <div className=" !m-0 min-h-screen w-full border rounded">
        <div className=' px-3 py-5   border-b'>
          <h2 className=' font-bold text-lg'>Sales Report Demo</h2>
        </div>
        <div className=''>

          <button onClick={() => setIsModalOpen(true)}>
            Open Report
          </button>

          <ReportsModal
            config={reportConfig}
            isOpen={isModalOpen}
            onClose={() => setIsModalOpen(false)}
          // Optional: Pass a custom component directly
          // customComponent={MySpecialReportComponent}
          />

        </div>

      </div>
    </Screen>
  )
}

export default SalesReportDemo

// register component to be rendered in the modal
registerReportComponent('sales', SalesReportView);

interface Vehicle {
  id: number;
  make: string;
  model: string;
}

const data: Vehicle[] = [
  {
    id: 1,
    make: 'Lexus',
    model: '570',
  }
]

const columns: ColumnDef<Vehicle>[] = [
  {
    accessorKey: 'make',
    header: 'Make',
    cell: info => info.getValue(),
    enableColumnFilter: true,
    filterFn: 'includesString',
  },
  {
    accessorKey: 'model',
    header: 'Model',
    cell: info => info.getValue(),
    enableColumnFilter: true,
    filterFn: 'includesString',
  },
  {
    accessorKey: 'body_type',
    header: 'Body Type',
    cell: info => info.getValue(),
    enableColumnFilter: true,
    filterFn: 'includesString',
  },
]

function SalesReportView({ data }: { data: any }) {

  return (
    <div className="px-2">
      <DataTable<Vehicle>
        data={data?.data?.results || []}
        columns={columns}
        title="Vehicles"
        enableSelectColumn={false}
        enableColumnFilters={true}
        enableSorting={true}
        enableToolbar={true}
        tableClassName='border-none'
        containerClassName=' py-2 '
        tHeadClassName='border-t'
        tHeadCellsClassName="border-r px-2"
        tBodyCellsClassName="text-xs border-r px-2"
        />
    </div>
  );
}
