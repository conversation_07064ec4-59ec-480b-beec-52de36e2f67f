"use client";

import * as React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>T<PERSON><PERSON>,
  CardDescription,
  CardContent,
} from "@/components/ui/card";

export interface Card4Props {
  title: string;
  description?: string;
  content?: string;
  /** Custom action nodes, e.g., buttons or links */
  actions?: React.ReactNode;
  /** Custom class for the Card wrapper */
  className?: string;
  /** Custom class for the header section */
  headerClassName?: string;
  /** Custom class for the content section */
  contentClassName?: string;
  /** Custom class for the footer section */
  footerClassName?: string;
}

export function Card4({
  title,
  description,
  content,
  actions,
  className,
  headerClassName,
  contentClassName,
  footerClassName,
}: Card4Props) {
  return (
    <Card className={className}>
      <CardHeader className={headerClassName}>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent className={contentClassName}>
        {content && <p className="text-sm">{content}</p>}
      </CardContent>
      <CardFooter className={`justify-end ${footerClassName}`}> 
        {actions}
      </CardFooter>
    </Card>
  );
}
