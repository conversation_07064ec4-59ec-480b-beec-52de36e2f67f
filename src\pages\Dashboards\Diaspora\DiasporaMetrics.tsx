import SpinnerTemp from '@/components/custom/spinners/SpinnerTemp'
import { Badge } from '@/components/ui/badge'
import { useGetDiasporaDashboardQuery } from '@/redux/slices/teams'
import { formatNumberWithCommas } from '@/utils/salesDataFormatter'
import { BarChartBig, Calendar, ChartArea, ChartColumnBig, ChartColumnIncreasing, ChartGantt, ChartLine, ChartNoAxesCombined, ChartNoAxesGantt, ChartPie, ChartScatter, ChartSpline, CircleCheck, Globe, TrendingUp } from 'lucide-react'

const GMKarenMetrics = () => {
    const { data, isLoading, isFetching } = useGetDiasporaDashboardQuery({})

    const expectedCollectionRate = 10

    // Show loader when initially loading or when tab is changing
    const showLoader = isLoading || isFetching;

    return (
        <div className='relative space-y-4 mt-2'>
            {showLoader && (
                <div className="absolute inset-0 z-10 flex pt-16 justify-center bg-white bg-opacity-70">
                    <SpinnerTemp type="spinner-double" size="md" />
                </div>
            )}
            <div className='grid grid-cols-1 md:grid-cols-6 gap-5'>
                <div className='bg-emerald-50 px-3 py-12 rounded-xl shadow-md'>
                    <div className='flex justify-between items-center'>
                        <div className='bg-emerald-800 text-white p-2 rounded-lg'>
                            <ChartLine size={30} className='' />
                        </div>
                        <TrendingUp size={16} />
                    </div>
                    <p className='text-xs mt-4'>Ksh</p>
                    <p className='font-bold text-lg'>{formatNumberWithCommas(data?.MIB?.Daily_MIB) || 0}</p>
                    <p className='text-sm'>Daily MIB</p>
                </div>
                <div className='bg-cyan-50 px-3 py-12 rounded-xl shadow-md'>
                    <div className='flex justify-between items-center'>
                        <div className='bg-cyan-800 text-white p-2 rounded-lg'>
                            <ChartNoAxesCombined size={30} className='' />
                        </div>
                        <TrendingUp size={16} />
                    </div>
                    <p className='text-xs mt-4'>Ksh</p>
                    <p className='font-bold text-lg'>{formatNumberWithCommas(data?.MIB?.Monthly_MIB) || 0}</p>
                    <p className='text-sm trancate'>Monthly MIB</p>
                </div>
                <div className='bg-red-50 px-3 py-12 rounded-xl shadow-md'>
                    <div className='flex justify-between items-center'>
                        <div className='bg-red-800 text-white p-2 rounded-lg'>
                            <ChartGantt size={30} className='' />
                        </div>
                        <TrendingUp size={16} />
                    </div>
                    <p className='text-xs mt-4'>Ksh</p>
                    <p className='font-bold text-lg'>{formatNumberWithCommas(data?.MIB?.Monthly_Transfer_Costs) || 0}</p>
                    <p className='text-sm trancate'>Transfer Cost</p>
                </div>
                <div className='bg-blue-50 px-3 py-12 rounded-xl shadow-md'>
                    <div className='flex justify-between items-center'>
                        <div className='bg-blue-800 text-white p-2 rounded-lg'>
                            <ChartPie size={30} className='' />
                        </div>
                        <TrendingUp size={16} />
                    </div>
                    <p className='text-xs mt-4'>Ksh</p>
                    <p className='font-bold text-lg'>{formatNumberWithCommas(data?.MIB?.Monthly_Deposits) || 0}</p>
                    <p className='text-sm trancate'>Deposits</p>
                </div>
                <div className='bg-teal-50 cyan-50 px-3 py-12 rounded-xl shadow-md'>
                    <div className='flex justify-between items-center'>
                        <div className='bg-teal-800 text-white p-2 rounded-lg'>
                            <ChartColumnBig size={30} className='' />
                        </div>
                        <TrendingUp size={16} />
                    </div>
                    <p className='text-xs mt-4'>Ksh</p>
                    <p className='font-bold text-lg'>{formatNumberWithCommas(data?.MIB?.Monthly_Additional_Deposits) || 0}</p>
                    <p className='text-sm trancate'>Addit.. Deposits</p>
                </div>
                <div className='bg-fuchsia-50 -50 px-3 py-12 rounded-xl shadow-md'>
                    <div className='flex justify-between items-center'>
                        <div className='bg-fuchsia-800 text-white p-2 rounded-lg'>
                            <ChartColumnIncreasing size={30} className='' />
                        </div>
                        <TrendingUp size={16} />
                    </div>
                    <p className='text-xs mt-4'>Ksh</p>
                    <p className='font-bold text-lg'>{formatNumberWithCommas(data?.MIB?.Monthly_Installments) || 0}</p>
                    <p className='text-sm trancate'>Installments</p>
                </div>
            </div>

            {/* desktop  */}
            <div className='hidden md:contents'>
                <div className='grid grid-cols-5 py-14 '>
                    <div className='flex flex-col items-center border-r'>
                        <Calendar size={40} className='text-emerald-600' />
                        <p className='text-[25px] font-bold'>{formatNumberWithCommas(data?.Collections?.Installments_Due_Today) || 0}</p>
                        <p className='text-sm'>Installments due today</p>
                    </div>
                    <div className='flex flex-col items-center border-r'>
                        <BarChartBig size={40} className='text-cyan-600' />
                        <p className='text-[25px] font-bold'>{formatNumberWithCommas(data?.Collections?.ALL_Overdue_Collections) || 0}</p>
                        <p className='text-sm'>Overdue Collections</p>
                    </div>
                    <div className='flex flex-col items-center border-r'>
                        <ChartArea size={40} className='text-red-600' />
                        <p className='text-[25px] font-bold'>{formatNumberWithCommas(data?.Collections?.Overdue_Collections_Collected) || 0}</p>
                        <p className='text-sm'>Overdue Collections Collected</p>
                    </div>
                    <div className='flex flex-col items-center border-r'>
                        <ChartSpline size={40} className='text-blue-600' />
                        <p className='text-[25px] font-bold'>{formatNumberWithCommas(data?.Collections?.Sales_Deposits_Below_Threshold) || 0}</p>
                        <p className='text-sm'>Deposit Below Threshold</p>
                    </div>
                    <div className='flex flex-col items-center'>
                        <ChartNoAxesGantt size={40} className='text-fuchsia-600' />
                        <p className='text-[25px] font-bold'>{formatNumberWithCommas(data?.Collections?.Overdue_Below_Threshold) || 0}</p>
                        <p className='text-sm'>Overdue Below Threshold</p>
                    </div>
                </div>
            </div>

            {/* Mobile */}
            <div className='md:hidden overflow-x-auto scrollbar-hide'>
                <div className='flex py-14'>
                    {/* First set of items */}
                    <div className='flex-shrink-0 w-48 flex flex-col items-center border-r px-4'>
                        <Calendar size={40} className='text-emerald-600' />
                        <p className='text-[25px] font-bold'>{formatNumberWithCommas(data?.Collections?.Installments_Due_Today) || 0}</p>
                        <p className='text-sm text-center'>Installments due today</p>
                    </div>
                    <div className='flex-shrink-0 w-48 flex flex-col items-center border-r px-4'>
                        <BarChartBig size={40} className='text-cyan-600' />
                        <p className='text-[25px] font-bold'>{formatNumberWithCommas(data?.Collections?.ALL_Overdue_Collections) || 0}</p>
                        <p className='text-sm text-center'>Overdue Collections</p>
                    </div>
                    <div className='flex-shrink-0 w-48 flex flex-col items-center border-r px-4'>
                        <ChartArea size={40} className='text-red-600' />
                        <p className='text-[25px] font-bold'>{formatNumberWithCommas(data?.Collections?.Overdue_Collections_Collected) || 0}</p>
                        <p className='text-sm text-center'>Overdue Collections Collected</p>
                    </div>
                    <div className='flex-shrink-0 w-48 flex flex-col items-center border-r px-4'>
                        <ChartSpline size={40} className='text-blue-600' />
                        <p className='text-[25px] font-bold'>{formatNumberWithCommas(data?.Collections?.Sales_Deposits_Below_Threshold) || 0}</p>
                        <p className='text-sm text-center'>Deposit Below Threshold</p>
                    </div>
                    <div className='flex-shrink-0 w-48 flex flex-col items-center px-4'>
                        <ChartNoAxesGantt size={40} className='text-fuchsia-600' />
                        <p className='text-[25px] font-bold'>{formatNumberWithCommas(data?.Collections?.Overdue_Below_Threshold) || 0}</p>
                        <p className='text-sm text-center'>Overdue Below Threshold</p>
                    </div>
                </div>
            </div>

            <div className='grid grid-cols-1 md:grid-cols-3 gap-5'>

                {/* collections  */}
                <div className='border border-border/60   px-2 py-4 rounded-xl space-y-3 '>
                    <div className='flex items-center gap-2 mb-4'>
                        <div className='bg-emerald-600 p-1.5 rounded-lg text-white'>
                            <CircleCheck size={24} />
                        </div>
                        <div>
                            <p className='font-bold text-sm'>Sales Overview</p>
                            <p className='text-xs'>Details</p>
                        </div>
                        <Badge className='bg-emerald-600 ml-auto px-4'>
                            <p>4/4</p>
                        </Badge>
                    </div>
                    <div>
                        <div className='flex items-center gap-2 shadow-md rounded-xl p-3'>
                            <div className='bg-red-600/30 p-1.5 rounded-lg text-emerald-800'>
                                <CircleCheck size={18} />
                            </div>
                            <div>
                                <p className='text-sm'>Total Active Sales</p>
                            </div>
                            <Badge className='bg-red-600 ml-auto px-4'>
                                <p className='font-bold'>{formatNumberWithCommas(data?.SalesOverview?.TotalActiveSales) || 0}</p>
                            </Badge>
                        </div>
                    </div>
                    <div>
                        <div className='flex items-center gap-2 shadow-md rounded-xl p-3'>
                            <div className='bg-cyan-600/30 p-1.5 rounded-lg text-emerald-800'>
                                <CircleCheck size={18} />
                            </div>
                            <div>
                                <p className='text-sm'>Total Purchase Prices</p>
                            </div>
                            <Badge className='bg-cyan-600 ml-auto px-4'>
                                <p className='font-bold'>Ksh {formatNumberWithCommas(data?.SalesOverview?.TotalPurchasePrices) || 0}</p>
                            </Badge>
                        </div>
                    </div>
                    <div>
                        <div className='flex items-center gap-2 shadow-md rounded-xl p-3'>
                            <div className='bg-fuchsia-600/30 p-1.5 rounded-lg text-emerald-800'>
                                <CircleCheck size={18} />
                            </div>
                            <div>
                                <p className='text-sm'>Total Paid</p>
                            </div>
                            <Badge className='bg-fuchsia-600 ml-auto px-4'>
                                <p className='font-bold'>Ksh {formatNumberWithCommas(data?.SalesOverview?.TotalPaid) || 0}</p>
                            </Badge>
                        </div>
                    </div>
                    <div>
                        <div className='flex items-center gap-2 shadow-md rounded-xl p-3'>
                            <div className='bg-blue-600/30 p-1.5 rounded-lg text-emerald-800'>
                                <CircleCheck size={18} />
                            </div>
                            <div>
                                <p className='text-sm'>Total Outstanding Balances</p>
                            </div>
                            <Badge className='bg-blue-600 ml-auto px-4'>
                                <p className='font-bold'>Ksh {formatNumberWithCommas(data?.SalesOverview?.TotalOutstandingBalances) || 0}</p>
                            </Badge>
                        </div>
                    </div>
                </div>

                {/* Expected Monthly Installments Card */}
                <div className="border border-border/60 rounded-xl p-6 ">
                    <div className="flex items-center justify-between mb-4">
                        <h3 className=" text-sm font-bold">Collections</h3>
                        <div className="flex items-center gap-2">
                            <ChartGantt className={`w-5 h-5 ${getStatusColor(expectedCollectionRate)}`} />

                        </div>
                    </div>

                    <div className="space-y-2 mb-4">
                        <div className="flex justify-between text-xs">
                            <span>Overdue Collections</span> <span className='font-bold'> Ksh {formatNumberWithCommas(data?.Collections?.ALL_Overdue_Collections) || 0}</span>
                        </div>
                        <div className="flex justify-between text-xs">
                            <span>Overdue Collected</span> <span className='font-bold'> Ksh {formatNumberWithCommas(data?.Collections?.Overdue_Collections_Collected) || 0}</span>
                        </div>
                    </div>

                    <div className="mt-4">
                        <div className="w-full bg-slate-200  rounded-full h-1.5">
                            <div
                                className={`h-1.5 rounded-full transition-all duration-500 ${expectedCollectionRate >= 80 ? 'bg-green-400' :
                                    expectedCollectionRate >= 60 ? 'bg-yellow-400' :
                                        expectedCollectionRate >= 40 ? 'bg-orange-400' : 'bg-red-400'
                                    }`}
                                style={{ width: `${Math.min(expectedCollectionRate, 100)}%` }}
                            />
                        </div>
                        <div className="flex justify-between text-xs  mt-1">
                            <span>0%</span>
                            <span>100%</span>
                        </div>
                    </div>

                    <div className="mt-4 flex items-center justify-center">
                        <div className={`px-3 py-1 rounded-full text-xs font-medium ${expectedCollectionRate >= 80 ? 'bg-green-400/20 text-green-400' :
                            expectedCollectionRate >= 60 ? 'bg-yellow-400/20 text-yellow-400' :
                                expectedCollectionRate >= 40 ? 'bg-orange-400/20 text-orange-400' :
                                    'bg-red-400/20 text-red-400'
                            }`}>
                            {getStatusText(expectedCollectionRate).toUpperCase()} PERFORMANCE
                        </div>
                    </div>
                </div>

                {/* collections  */}
                <div className=' border border-border/60  px-2 py-4 rounded-xl space-y-3'>
                    <div className='flex items-center gap-2 mb-4'>
                        <div className='bg-emerald-600 p-1.5 rounded-lg text-white'>
                            <CircleCheck size={24} />
                        </div>
                        <div>
                            <p className='font-bold text-sm'>Monthly Collection</p>
                            <p className='text-xs'>Details</p>
                        </div>
                        <Badge className='bg-emerald-600 ml-auto px-4'>
                            <p>4/4</p>
                        </Badge>
                    </div>
                    <div>
                        <div className='flex items-center gap-2 shadow-md rounded-xl p-3'>
                            <div className='bg-red-600/30 p-1.5 rounded-lg text-emerald-800'>
                                <CircleCheck size={18} />
                            </div>
                            <div>
                                <p className='text-sm'>Total Expected Collections</p>
                            </div>
                            <Badge className='bg-red-600 ml-auto px-4'>
                                <p className='font-bold'>Ksh {formatNumberWithCommas(data?.MonthlyExpectedCollections?.Total_Expected_Collections) || 0}</p>
                            </Badge>
                        </div>
                    </div>
                    <div>
                        <div className='flex items-center gap-2 shadow-md rounded-xl p-3'>
                            <div className='bg-cyan-600/30 p-1.5 rounded-lg text-emerald-800'>
                                <CircleCheck size={18} />
                            </div>
                            <div>
                                <p className='text-sm'>Current Expected Installments</p>
                            </div>
                            <Badge className='bg-cyan-600 ml-auto px-4'>
                                <p className='font-bold'>Ksh {formatNumberWithCommas(data?.MonthlyExpectedCollections?.CurrentExpectedInstallments) || 0}</p>
                            </Badge>
                        </div>
                    </div>
                    <div>
                        <div className='flex items-center gap-2 shadow-md rounded-xl p-3'>
                            <div className='bg-fuchsia-600/30 p-1.5 rounded-lg text-emerald-800'>
                                <CircleCheck size={18} />
                            </div>
                            <div>
                                <p className='text-sm'>Accrued Missed Installments</p>
                            </div>
                            <Badge className='bg-fuchsia-600 ml-auto px-4'>
                                <p className='font-bold'>Ksh {formatNumberWithCommas(data?.MonthlyExpectedCollections?.AccruedMissedInstallments) || 0}</p>
                            </Badge>
                        </div>
                    </div>
                    <div>
                        <div className='flex items-center gap-2 shadow-md rounded-xl p-3'>
                            <div className='bg-blue-600/30 p-1.5 rounded-lg text-emerald-800'>
                                <CircleCheck size={18} />
                            </div>
                            <div>
                                <p className='text-sm'>Overdue Installments</p>
                            </div>
                            <Badge className='bg-blue-600 ml-auto px-4'>
                                <p className='font-bold'>Ksh {formatNumberWithCommas(data?.MonthlyExpectedCollections?.OverdueInstallments) || 0}</p>
                            </Badge>
                        </div>
                    </div>
                </div>
            </div>

            <div className='border border-border/60 rounded-xl'>
                <div className='py-6 border-b border-border/60 px-4'>
                    <p className='font-bold text-sm'>Team Performance</p>
                </div>
                <div>
                    {/* Diaspora Regions */}
                    <div className="p-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {data?.DiasporaRegions?.map((region: any, index: number) => (
                                <RegionCard key={index} region={region} />
                            ))}
                        </div>
                    </div>
                </div>
            </div>

            <div className='h-5'></div>
        </div>
    )
}

export default GMKarenMetrics

const getStatusText = (percentage: any) => {
    if (percentage >= 80) return 'excellent';
    if (percentage >= 60) return 'good';
    if (percentage >= 40) return 'fair';
    return 'critical';
};

const getStatusColor = (percentage: any) => {
    if (percentage >= 80) return 'text-green-400';
    if (percentage >= 60) return 'text-yellow-400';
    if (percentage >= 40) return 'text-orange-400';
    return 'text-red-400';
};

const RegionCard = ({ region }: any) => (
    <div className="rounded-xl px-2 py-6 border border-border/60 hover:shadow-lg transition-shadow">
        <div className="flex items-start justify-between mb-2">
            <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                    <Globe className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                    <h3 className="font-semibold text-sm ">{region?.name}</h3>
                    <p className="text-xs">{region?.manager_name}</p>
                </div>
            </div>
            <span className="bg-blue-100 text-blue-800 text-xs font-medium px-3 py-1 rounded-full cursor-pointer hover:underline">
                Reports
            </span>
        </div>
        <div className="space-y-1">
            <div className="flex justify-between items-center">
                <span className="text-xs">Total Paid</span>
                <span className="text-sm font-semibold">Ksh {formatNumberWithCommas(region?.total_paid_sum)}</span>
            </div>
            <div className="flex justify-between items-cfenter">
                <span className="text-xs">Lead Sources</span>
                <span className="text-sm font-semibold">{formatNumberWithCommas(region?.leadsource_count)}</span>
            </div>
        </div>
    </div>
);