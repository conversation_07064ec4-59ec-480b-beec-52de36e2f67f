import  { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, <PERSON><PERSON>Content } from "@/components/ui/tabs";
import { Eye,  FileText, Calendar,  DollarSign, File } from "lucide-react";
import BaseModal from "@/components/custom/modals/BaseModal";
import SimpleTable, { ColumnDefinitionST } from "@/components/custom/tables/SimpleTable";
import { Screen } from "@/app-components/layout/screen";
import { PrimaryButton } from "@/components/custom/buttons/buttons";

const reports = [
  {
    id: "REP-001",
    date: "2025-03-15",
    client: "<PERSON>",
    name: "Residential Plot Sale",
    marketer: "<PERSON>",
    plotno: "PLT-123",
    amountreceipted: "KES 500,000",
    cashprice: "KES 2,000,000",
    purchaseprice: "KES 1,800,000",
    totalpayment: "KES 500,000",
    balance: "KES 1,300,000",
    agreement: "Signed",
    account: "ACC-456789",
    region: "Nairobi",
  },
  {
    id: "REP-002",
    date: "2025-04-05",
    client: "<PERSON>",
    name: "Commercial Plot Sale",
    marketer: "<PERSON>",
    plotno: "PLT-456",
    amountreceipted: "KES 1,000,000",
    cashprice: "KES 5,000,000",
    purchaseprice: "KES 4,500,000",
    totalpayment: "KES 1,500,000",
    balance: "KES 3,000,000",
    agreement: "Pending",
    account: "ACC-123456",
    region: "Mombasa",
  },
];

const Reports = () => {
  const [selectedReport, setSelectedReport] = useState<any>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleViewReport = (report: any) => {
    setSelectedReport(report);
    setIsModalOpen(true);
  };

  
  console.log("Reports data:", reports);

  const columns: ColumnDefinitionST<typeof reports[0]>[] = [
    {
      key: "marketer",
      header: "Marketer",
      headerClassName: "font-semibold text-gray-700 px-6 py-4 bg-gray-50 text-left",
      cellClassName: "px-6 py-4 text-gray-600",
    },
    {
      key: "client",
      header: "Client Name",
      headerClassName: "font-semibold text-gray-700 px-6 py-4 bg-gray-50 text-left",
      cellClassName: "px-6 py-4 text-gray-600",
    },
    {
      key: "plotno",
      header: "Plot No",
      headerClassName: "font-semibold text-gray-700 px-6 py-4 bg-gray-50 text-left",
      cellClassName: "px-6 py-4 text-gray-600",
    },
    {
      key: "actions",
      header: "Actions",
      headerClassName: "font-semibold text-gray-700 px-6 py-4 bg-gray-50 text-right",
      cellClassName: "px-6 py-4 text-right",
      renderCell: (row: any) => (
        <button
          onClick={() => handleViewReport(row)}
          className="inline-flex items-center gap-2 px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-150"
        >
          <Eye className="w-4 h-4" />
          View
        </button>
      ),
    },
  ];

  return (
    <Screen>
        
    <div className="min-h-screen py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-8">
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
            <FileText className="w-8 h-8 text-gray-600" />
            Plot Sales Reports
          </h1>
          <PrimaryButton variant="primary" size="md"> Export</PrimaryButton>
        </div>
        <div className="bg-white shadow-md rounded-lg p-6">
          {reports.length === 0 ? (
            <p className="text-sm text-gray-500">No reports available.</p>
          ) : (
            <SimpleTable
              data={reports}
              columns={columns}
              containerClassName="rounded-lg overflow-x-auto border border-gray-200 min-h-[200px]"
              tableClassName="min-w-full border-collapse bg-white"
              tHeaderClassName="bg-gray-50"
              tBodyClassName=""
              tRowClassName="even:bg-gray-50 hover:bg-gray-100 transition-colors duration-150"
              headerCellsClassName=""
              bodyCellsClassName=""
              hoverable={true}
              striped={true}
            />
          )}
        </div>
      </div>
      <ReportDetailModal report={selectedReport} isOpen={isModalOpen} onOpenChange={setIsModalOpen} />
    </div>
    </Screen>
  );
};


const ReportDetailModal = ({
  report,
  isOpen,
  onOpenChange,
}: {
  report: any;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}) => {
  if (!report) return null;

  return (
    <BaseModal
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      title={`Plot Sale: ${report.plotno}`}
      description={`Client: ${report.client} • Date: ${report.date}`}
      size="lg"
      showClose
    >
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="flex border-b border-gray-200 mb-6">
          <TabsTrigger
            value="overview"
           
          >
            <Calendar className="w-4 h-4 mr-2" />
            Overview
          </TabsTrigger>
          <TabsTrigger
            value="financial"
            
          >
            <DollarSign className="w-4 h-4 mr-2" />
            Financial Details
          </TabsTrigger>
          <TabsTrigger
            value="additional"
            >
            <File className="w-4 h-4 mr-2" />
            Additional Info
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            <div>
              <h3 className="text-sm font-medium text-gray-500">Client Name</h3>
              <p className="text-gray-900">{report.client}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500">Plot Number</h3>
              <p className="text-gray-900">{report.plotno}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500">Marketer</h3>
              <p className="text-gray-900">{report.marketer}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500">Date</h3>
              <p className="text-gray-900">{report.date}</p>
            </div>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-500">Name</h3>
            <p className="text-sm text-gray-600 mt-1">{report.name}</p>
          </div>
        </TabsContent>

        <TabsContent value="financial" className="space-y-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            <div>
              <h3 className="text-sm font-medium text-gray-500">Amount Receipted</h3>
              <p className="text-gray-900">{report.amountreceipted}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500">Cash Price</h3>
              <p className="text-gray-900">{report.cashprice}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500">Purchase Price</h3>
              <p className="text-gray-900">{report.purchaseprice}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500">Total Payment</h3>
              <p className="text-gray-900">{report.totalpayment}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500">Balance</h3>
              <p className="text-gray-900">{report.balance}</p>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="additional" className="space-y-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            <div>
              <h3 className="text-sm font-medium text-gray-500">Agreement</h3>
              <p className="text-gray-900">{report.agreement}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500">Account</h3>
              <p className="text-gray-900">{report.account}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500">Region</h3>
              <p className="text-gray-900">{report.region}</p>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </BaseModal>
  );
};

export default Reports;
