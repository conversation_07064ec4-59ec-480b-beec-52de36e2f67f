import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { useAddFlagMutation } from "@/redux/slices/services";
import { usePartialUpdateFlagMutation } from "@/redux/slices/flagsApiSlice";

type Props = {
  prospect_id?: string;
  onOpenChange?: (e: boolean) => void;
  updateData?: any;
};

const FlagsForm = ({ prospect_id, onOpenChange, updateData }: Props) => {
  const [addFlag, { isLoading: submitting }] = useAddFlagMutation();
  const [updateFlag, { isLoading: updating }] = usePartialUpdateFlagMutation();
  // form schema
  const formSchema = z.object({
    flag_type: z.string().min(1, { message: "Flag Type is required." }),
    title: z.string().min(1, { message: "title is required." }),
    description: z.string().min(1, { message: "description is required." }),
    severity: z.string().min(1, { message: "severity is required." }),
    status: z.string().min(1, { message: "status is required." }),
  });

  // instantiate react hook form
  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      flag_type: updateData?.flag_type || "",
      title: updateData?.title || "",
      description: updateData?.description || "",
      severity: updateData?.severity || "Info",
      status: updateData?.status || "Active",
    },
  });

  const handleFlagSubmit = async (data: any) => {
    let formData = updateData
      ? { ...data, flag_id: updateData?.flag_id }
      : { ...data, customer_status: "prospect", prospect_id: prospect_id };
    try {
      let res;
      if (updateData) {
        res = await updateFlag(formData).unwrap();
      } else {
        res = await addFlag(formData).unwrap();
      }
      if (res?.flag_id) {
        toast.success(`Flag ${updateData ? "updated" : "added"} successfully`);
        onOpenChange && onOpenChange(false);
      } else {
        toast.error("Failed to add");
      }
    } catch (error: any) {
      console.log("error", error);
      toast.error(error?.data ? error?.data?.error : "Somethe went wrong");
    }
  };

  return (
    <div className="">
      <form onSubmit={handleSubmit(handleFlagSubmit)}>
        <div className="space-y-2">
          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Flag Type*
            </label>
            <div className="relative pt-1">
              <select
                {...register("flag_type")}
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              >
                <option value="Customer Issue">Customer Issue</option>
                <option value="Data Quality">Data Quality</option>
                <option value="Security">Security</option>
                <option value="Compliance">Compliance</option>
                <option value="Performance">Performance</option>
                <option value="Content">Content</option>
              </select>
            </div>
            {errors.flag_type && (
              <p className="text-red-500 text-xs pl-3">
                {errors.flag_type.message}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Title*
            </label>
            <div className="relative pt-1">
              <input
                {...register("title")}
                placeholder="title"
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              />
            </div>
            {errors.title && (
              <p className="text-red-500 text-xs pl-3">
                {errors.title.message}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Descriptions*
            </label>
            <div className="relative pt-1">
              <textarea
                {...register("description")}
                rows={3}
                placeholder="Description"
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              ></textarea>
            </div>
            {errors.description && (
              <p className="text-red-500 text-xs pl-3">
                {errors.description.message}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Severity
            </label>
            <div className="relative pt-1">
              <select
                {...register("severity")}
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              >
                <option value="Info">Info</option>
                <option value="Warning">Warning</option>
                <option value="Error">Error</option>
                <option value="Critical">Critical</option>
              </select>
            </div>
            {errors.severity && (
              <p className="text-red-500 text-xs pl-3">
                {errors.severity.message}
              </p>
            )}
          </div>

          <div>
            <label htmlFor="" className="ml-1 text-sm">
              Status
            </label>
            <div className="relative pt-1">
              <select
                {...register("status")}
                className="w-full p-4 py-2 border border-gray-300 rounded-lg text-gray-400 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all"
              >
                <option value="Active">Active </option>
                <option value="Resolved">Resolved</option>
                <option value="Dismissed">Dismissed</option>
                <option value="Under Review">Under Review</option>
              </select>
            </div>
            {errors.status && (
              <p className="text-red-500 text-xs pl-3">
                {errors.status.message}
              </p>
            )}
          </div>

          <div className="w-full">
            {submitting || updating ? (
              <SpinnerTemp type="spinner-double" size="sm" />
            ) : (
              <Button type="submit" className="w-full">
                Submit
              </Button>
            )}
          </div>
        </div>
      </form>
    </div>
  );
};

export default FlagsForm;
