import { contentHeader, noAuthHeader } from "@/utils/header";
import { apiSlice } from "../apiSlice";

export const engagementsApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getEngagements: builder.query({
      query: (params) => ({
        url: "/services/engagements/",
        method: "GET",
        params: params,
        headers: noAuthHeader(),
      }),
      transformResponse: (response: any) => {
        // The API returns data nested under response.data
        const apiData = response.data || {};
        const count = apiData.total_data || 0;
        const num_pages = apiData.last_page || 1;
        return {
          results: apiData.results || [],
          count,
          current_page: apiData.current_page || 1,
          num_pages,
          total_data: count,
          per_page: apiData.per_page || (count === 0 ? 0 : Math.ceil(count / num_pages)),
        };
      },
      transformErrorResponse: (error) => {
        console.error("Engagements API Error:", error);
        return error;
      },
      providesTags: ["Engagements"],
    }),

    getEngagementDetails: builder.query({
      query: (engagementId) => ({
        url: `/services/engagements/${engagementId}/`,
        method: "GET",
        headers: noAuthHeader(),
      }),
      providesTags: ["Engagements"],
    }),

    createEngagement: builder.mutation({
      query: (data) => ({
        url: "/services/engagements/",
        method: "POST",
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Engagements"],
    }),

    updateEngagement: builder.mutation({
      query: ({ engagementId, data }) => ({
        url: `/services/engagements/${engagementId}/`,
        method: "PUT",
        body: data,
        headers: contentHeader(),
      }),
      invalidatesTags: ["Engagements"],
    }),

    partialUpdateEngagement: builder.mutation({
      query: (data) => ({
        url: `/services/engagements/${data?.engagement_id}/`,
        method: "PATCH",
        body: data,
        // headers: contentHeader(),
      }),
      invalidatesTags: ["Engagements"],
    }),

    deleteEngagement: builder.mutation({
      query: (engagementId) => ({
        url: `/services/engagements/${engagementId}/`,
        method: "DELETE",
        headers: noAuthHeader(),
      }),
      invalidatesTags: ["Engagements"],
    }),

    startEngagement: builder.mutation({
      query: (engagementId) => ({
        url: `/services/engagements/${engagementId}/start/`,
        method: "PATCH",
        headers: contentHeader(),
      }),
      invalidatesTags: ["Engagements"],
    }),

    completeEngagement: builder.mutation({
      query: (engagementId) => ({
        url: `/services/engagements/${engagementId}/complete/`,
        method: "PATCH",
        headers: contentHeader(),
      }),
      invalidatesTags: ["Engagements"],
    }),
  }),
});

export const {
  useGetEngagementsQuery,
  useGetEngagementDetailsQuery,
  useCreateEngagementMutation,
  useUpdateEngagementMutation,
  usePartialUpdateEngagementMutation,
  useDeleteEngagementMutation,
  useStartEngagementMutation,
  useCompleteEngagementMutation,
} = engagementsApiSlice;
