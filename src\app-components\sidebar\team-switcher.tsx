import * as React from "react";
import { ChevronsUpDown, Plus } from "lucide-react";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";

export function TeamSwitcher({
  teams,
}: {
  teams: {
    name: string;
    logo: React.ElementType;
    plan: string;
  }[];
}) {
  const { isMobile } = useSidebar();
  const [activeTeam, setActiveTeam] = React.useState(teams[0]);

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
          <SidebarMenuButton
              size="lg"
              className="flex items-center justify-between px-4 py-3 text-sidebar-foreground hover:bg-sidebar-accent"
            >
                <div className="flex aspect-square items-center justify-center w-8 h-8 rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                <activeTeam.logo className="w-4 h-4" />
              </div>
              <div className="grid flex-1 ml-3 text-left text-sm leading-tight">
                <span className="truncate font-semibold">
                  {activeTeam.name}
                </span>
                <span className="truncate text-xs text-sidebar-foreground/70">
                  {activeTeam.plan}
                </span>
              </div>
              <ChevronsUpDown className="ml-auto text-sidebar-foreground/70" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg bg-sidebar text-sidebar-foreground"
            align="start"
            side={isMobile ? "bottom" : "right"}
            sideOffset={4}
          >
            <DropdownMenuLabel className="text-xs text-sidebar-foreground/70 px-3 py-2">
              Companies
            </DropdownMenuLabel>
            {teams.map((team) => (
              <DropdownMenuItem
                key={team.name}
                onClick={() => setActiveTeam(team)}
                className="gap-2 p-2 cursor-pointer font-bold hover:bg-sidebar-accent"
              >
                <div className="flex w-6 h-6 items-center justify-center rounded-sm border border-gray-600 bg-gray-800">
                <team.logo className="w-4 h-4 shrink-0 text-sidebar-primary-foreground" />
                </div>
                {team.name}
              </DropdownMenuItem>
            ))}
            <DropdownMenuSeparator className="bg-sidebar-border" />
            <DropdownMenuItem className="gap-2 p-2 cursor-pointer hover:bg-sidebar-accent">
              <div className="flex w-6 h-6 items-center justify-center rounded-md border border-sidebar-border bg-sidebar-primary">
                <Plus className="w-4 h-4 text-sidebar-primary-foreground" />
              </div>
              <div className="font-medium text-sidebar-foreground">Add company</div>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
