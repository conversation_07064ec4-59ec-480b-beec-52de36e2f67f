import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import { Dialog, DialogTrigger } from "@radix-ui/react-dialog";
import { Activity, MapPin, Badge, Eye, GitBranch } from "lucide-react";

function RecentActivity() {
  
  const mockVisits = [
    {
      id: 1,
      project: { name: "Project Alpha" },
      marketer: { fullnames: "<PERSON> Doe" },
      pickup_location: "Downtown Office",
      status: "Completed",
    },
    {
      id: 2,
      project: { name: "Project Beta" },
      marketer: { fullnames: "<PERSON>" },
      pickup_location: "Central Hub",
      status: "Pending",
    },
    {
      id: 3,
      project: { name: "Project Gamma" },
      marketer: { fullnames: "<PERSON> Brown" },
      pickup_location: "North Branch",
      status: "Scheduled",
    },
  ];

  const visitsLoading = false; // Set to false for UI-only

  return (
    <div>
      <Card className="border-0 shadow-xl bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700">
        <CardHeader className="bg-gradient-to-r from-green-500 to-indigo-500 dark:from-blue-900/20 dark:to-indigo-900/20 border-b border-gray-200 dark:border-gray-700">
          <CardTitle className="flex items-center text-lg text-gray-900 dark:text-gray-100">
            <GitBranch className="w-5 h-5 mr-2 text-blue-600 dark:text-blue-400" />
            Recent Activity
          </CardTitle>
          <CardDescription className="text-gray-600 dark:text-gray-400">
            Latest scheduled visits with status management
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          {visitsLoading ? (
            <div className="space-y-3">
              {[1, 2, 3].map((i) => (
                <div key={i} className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gray-200 dark:bg-gray-600 animate-pulse rounded-full"></div>
                  <div className="flex-1 space-y-2">
                    <div className="w-3/4 h-4 bg-gray-200 dark:bg-gray-600 animate-pulse rounded"></div>
                    <div className="w-1/2 h-3 bg-gray-200 dark:bg-gray-600 animate-pulse rounded"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {mockVisits.slice(0, 5).map((visit) => (
                <div
                  key={visit.id}
                  className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                >
                  <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center">
                    <MapPin className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div className="flex-1">
                    <div className="font-medium text-sm text-gray-900 dark:text-gray-100">
                      {visit.project?.name || "N/A"}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {visit.marketer?.fullnames || "N/A"} •{" "}
                      {visit.pickup_location || "N/A"}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge
                     
                      className={`text-xs ${
                        visit.status === "Completed"
                          ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                          : visit.status === "Pending"
                          ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
                          : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"
                      }`}
                    >
                      {visit.status}
                    </Badge>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 hover:bg-blue-100 dark:hover:bg-blue-900/50"
                        >
                          <Eye className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                        </Button>
                      </DialogTrigger>
                      {/* Dialog content can be added here if needed */}
                    </Dialog>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

export default RecentActivity;