import React, { useState } from "react";
import { CardType } from "./types";

// Backward compatible interface for existing cards
interface LegacyCard {
  id: string;
  title: string;
  column: string;
  assignee?: string;
  assigneeInitials?: string;
}

interface KanbanCardProps extends LegacyCard {
  handleDragStart: (e: React.DragEvent, card: LegacyCard) => void;
  onKeyDown: (e: React.KeyboardEvent, card: LegacyCard) => void;
  setCards?: React.Dispatch<React.SetStateAction<any[]>>;
}

const getInitials = (name?: string): string => {
  if (!name) return "NA"; // Default for "No Assignee"
  const names = name.trim().split(" ");
  if (names.length === 1) return names[0].charAt(0).toUpperCase();
  return `${names[0].charAt(0)}${names[names.length - 1].charAt(0)}`.toUpperCase();
};

export const KanbanCard: React.FC<KanbanCardProps> = ({
  id,
  title,
  assignee,
  column,
  handleDragStart,
  onKeyDown,
}) => {
  const initials = getInitials(assignee);

  return (
    <div
      draggable
      onDragStart={(e) => handleDragStart(e, { id, title, assignee, column })}
      onKeyDown={(e) => onKeyDown(e, { id, title, assignee, column })}
      tabIndex={0}
      className="bg-card border rounded-md p-3 cursor-move hover:shadow-md transition-shadow"
      role="button"
      aria-label={`Task: ${title}, Assigned to: ${assignee || "No Assignee"}`}
    >
      <div className="flex items-center justify-between">
        <span className="text-sm font-medium">{title}</span>
        <div
          className="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center text-xs font-semibold"
          title={assignee || "No Assignee"}
        >
          {initials}
        </div>
      </div>
    </div>
  );
};